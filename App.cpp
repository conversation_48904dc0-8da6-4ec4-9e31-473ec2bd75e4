//
// Created by <PERSON><PERSON><PERSON> on 18.12.21.
//
#define IMGUI_DEFINE_MATH_OPERATORS



#include <sstream>
#include <regex>
#include "App.h"
#include "view/MenuView.h"

#include "ameliteui/AMEliteLoggerController.h"
#include "controller/UserDegreeController.h"
#include "controller/CategoryController.h"
#include "controller/PersonController.h"
#include "model/ChangePasswordDialog.h"
#include "amdialogs/AMUIDialogLoginView.h"
#include "amuitable/AMUIHeartbeat.h"

#include "AppLogin.hpp"
#include "svr/AMSvrStatus.h"

App::~App()
{
}

//#include <string>
//#include <iostream>
//#include <filesystem>

App::App():
    AMEliteApp(),
    menu(),
    view(),
    showTestWindow(false),
    m_session(),
    m_sessionForCustomer(),
    m_openedCustomer(),
    m_loginDialog(nullptr),
    m_changePasswordDialog(nullptr),
    m_loginRequests(),
    m_changePasswordRequests(),
    //m_runLoginMessage(),
    //m_runLoginDomain(),
    m_saw_icon(std::numeric_limits<GLuint>::min())
{
}


App& App::instance()
{
    return static_cast<App&>(AMEliteApp::instance());
}

App& App::initInstance()
{
    return *new App();
}
/*
void App::onProcessEvent(SDL_Event *event)
{
    AMEliteApp::onProcessEvent(event);
}*/
/*
void App::onRender()
{
    AMEliteApp::onRender();
}
*/
void App::onPerform()
{

    AMEliteApp::onPerform();
    menu.Perform();
}

void App::onCreate()
{
    AMUIApp::onCreate();

    std::locale::global(std::locale("cs_CZ.UTF-8"));
    std::setlocale(LC_ALL, "cs_CZ.UTF-8");

    AMUITable::AMUIHeartbeat<SAWEnum, SAWString>::instance();
}

void App::onStart()
{
    AMUIApp::onStart();

    menu.Init(this);
    m_status.PushNetworkMessage("Synchronizováno", AMEliteUI::EStatusLevelPositive);
    //m_status.PushMessage("Příliš žluťoučký kůň úpěl ďábelské ódy", EStatusLevelInfo, 3000);
    m_status.PushMessage("Je to tu!", AMEliteUI::EStatusLevelPositive, 3000);

}

void App::onResume()
{
    view.Init(this);
    AMEliteApp::onResume();
    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(Logo_SAW_png),
        &m_saw_icon,
        nullptr,
        nullptr
                                                                   );
    AMAssert(rv);

    if (sessionId().empty()) {
        login(session().domain);
    }
}

void App::onPause()
{
    AMEliteApp::onPause();
    AMEliteUI::AMEliteImageManager::unloadTexture(m_saw_icon);
    m_saw_icon = std::numeric_limits<GLuint>::min();
}
/*
void App::onDestroy()
{
    AMEliteApp::onDestroy();
}*/

AMUI::AMUIIView &App::getMainView()
{
    return view;
}

void App::runLogger()
{
    AMEliteUI::AMEliteLoggerController& lc = AMEliteUI::AMEliteLoggerController::instance();
    lc.bringToForeground();
}

AMUI::AMUIRect<float> App::getMainViewPort()
{
    return view.getMainViewPort();
}

void App::runUserDegrees()
{
    UserDegreeController* c = new UserDegreeController();
    c->init();
}

void App::runCategories()
{
    CategoryController* c = new CategoryController();
    c->init();
}

void App::runPeople()
{
    PersonController *c = new PersonController();
    c->init();
}




void App::handleError(int status, std::string message)
{
    printf("Handle error %i [%s]\n", status, message.c_str());
    switch ((AMSvr::AMSvrStatus)status) {
        case AMSvr::UNAUTHORIZED_ACCESS: {
            if (message == m_session.domain) {
                m_session.sessionId.clear();
            } else if (message.empty()) {
                m_session.sessionId.clear();
                message = m_session.domain;
            } else {
                auto it = m_session.customers.find(message);
                if(it == m_session.customers.end()) {
                    break;
                }
                m_session.customers.erase(it);
            }
            auto data = new LoginRunData(message, "Vaše sezení vypršelo.", false);
            runDelayedLogin(data);
            break;
        }
        case AMSvr::FORBIDDEN: {
            std::string thing = "tuto operaci";
            if (message == "table") {
                thing = "čtení této tabulky";
            } else if (message == "record") {
                thing = "čtení tohoto záznamu";
            } else if (message == "insert") {
                thing = "vložení nového záznamu";
            } else if (message == "update") {
                thing = "úpravu tohoto záznamu";
            } else if (message == "delete") {
                thing = "mazání tohoto záznamu";
            } else if (message == "flashback") {
                thing = "čtení tohoto flashbacku";
            } else if (message == "flashback record") {
                thing = "čtení tohoto flachback záznamu";
            } else if (message == "history") {
                thing = "čtení této historie";
            } else if (message == "reset password by self") {
                thing = "reset vlastního účtu";
            } else if (message == "reset password") {
                thing = "reset cizího účtu";
            }
            AMUIApp::errorMessageBox("Oprávnění", "Nemáte oprávnění pro " + thing + ".");
            break;
        }
        case AMSvr::NAME_OR_PASSWORD_INVALID: {
            //handle in APP::parseLogin because need domain
            std::string s;
            if (message == "Name or password invalid") {
                s = "Jméno nebo heslo nejsou správné.";
            } else if (message == "No attempts - need reset password") {
                s = "Již nemáte žádné pokusy. Nechte si zresetovat\n"
                    "přístupové údaje Vaším SAWAPP administrátorem.";
            }
            AMUIApp::errorMessageBox("Chyba zalogování", s);
            break;
        }
        case AMSvr::UNABLE_INSERT: {
            std::string s = message;
            std::cmatch cm;
            if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  duplicate key value violates unique constraint \"dbo_user_degrees_degree_uindex\"\\nDETAIL:  Key \\(degree, after_name\\)=\\((.+), (.+)\\) already exists\\.\\n"))) {
                s = "V tabulce již existuje stejný titul se stejnou polohou.\nUdělejte nějakou změnu a zkuste uložit titul znovu.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  duplicate key value violates unique constraint \"users_name_uk\"\\nDETAIL:  Key \\(name\\)=\\((.+)\\) already exists\\.\\n"))) {
                s = "Již existuje jeden uživatel se stejným uživatelským jménem.\nZvolte jiné uživatelské jméno.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  insert or update on table \"people\" violates foreign key constraint \"dbo_people_leader_dbo_people_id_fk\"\nDETAIL:  Key \\(leader\\)=\\((.+)\\) is not present in table \"people\"\\.\\n"))) {
                s = "Nelze uložit osobu bez vyplněného nadřízeného vedoucího.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
               "Value on row (.+) column (.+) does not match with any enumeration value."))) {
                s = "Hodnota na řádku " + cm[1].str() + " sloupci " + cm[2].str() + " nevychází z žádné hodnoty výčtového typu.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
               "Column \\\"(.*)\\\" found in csv many times."))) {
                s = "Sloupec \"" + cm[1].str() + "\" je nalezen v CSV souboru několikrát.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
               "Column \\\"(.*)\\\" not found in csv file."))) {
                s = "Sloupec \"" + cm[1].str() + "\" nebyl nalezen v CSV souboru.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
               "Columns \\[(.*)\\] from csv does not exists."))) {
                s = "Sloupce [" + cm[1].str() + "] z CSV souboru neexistují.";
            } else if (std::regex_search(message.c_str(), cm, std::regex(
                "Invalid value \\\"(.*)\\\" at row (.*) column (.*)."))) {
                s = "Nevalidní hodnota \"" + cm[1].str() + "\" na řádku " + cm[2].str() + ", sloupci " + cm[3].str() + ".";
            }


            AMUIApp::errorMessageBox("Nemohu vložit", s);
            break;
        }
        case AMSvr::UNABLE_UPDATE: {
            std::string s = message;
            std::cmatch cm;
            if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  duplicate key value violates unique constraint \"dbo_user_degrees_degree_uindex\"\\nDETAIL:  Key \\(degree, after_name\\)=\\((\\w+), (\\w+)\\) already exists\\.\\n"))) {
                s = "V tabulce již existuje stejný titul se stejnou polohou.\nUdělejte nějakou změnu a zkuste uložit titul znovu.";
                }
            if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  Position of degree cannot be changed\\nCONTEXT:  PL\\/pgSQL function trigger_user_degrees_prevent_change_position\\(\\) line (\\d+) at RAISE\\n"))) {
                s = "Pokud se titul používá,\ntak jeho polohu změnit nemůžete.";
            }
            if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  duplicate key value violates unique constraint \"users_name_uk\"\\nDETAIL:  Key \\(name\\)=\\((.+)\\) already exists\\.\\n"))) {
                s = "Již existuje jeden uživatel se stejným uživatelským jménem.\nZvolte jiné uživatelské jméno.";
                }

            if (std::regex_search(message.c_str(), cm, std::regex(
                "ERROR:  insert or update on table \"people\" violates foreign key constraint \"dbo_people_leader_dbo_people_id_fk\"\nDETAIL:  Key \\(leader\\)=\\((.+)\\) is not present in table \"people\"\\.\\n"))) {
                s = "Nelze uložit osobu bez vyplněného nadřízeného vedoucího.";
            }
            AMUIApp::errorMessageBox("Nemohu upravit", s);
            break;
        }
        case AMSvr::UNABLE_DELETE: {
            std::string s = message;
            if (message == "users") {
                s = "Uživatele smazat nemůžete.\nDeaktivujte jej přepínačem \"Existuje\".";
            } else if (message.find("ERROR:  update or delete on table \"providers_wms\" violates foreign key constraint \"dbo_medical_checks_provider_dbo_providers_wms_id_fk\" on table \"medical_checks\"")) {
                s = "Nemohu tohoto poskytovatele lékařských služeb smazat,\nprotože má referenci v lékařských prohlídkách.";
            }
            AMUIApp::errorMessageBox("Nemohu smazat", s);
            break;
        }
        case AMSvr::CLIENT_ERROR: {
            if (message.empty()) {
                message = "Zkontrolujte zadané údaje.";
            }
            AMUIApp::errorMessageBox("Chybný vstup uživatele", message);
            break;
        }
        case AMSvr::ASSIGNMENT_PROBLEM: {
            std::string s;
            if (message == "Incorrect domains this/remote settings") {
                s = "Chybné přiřazení domény v propojeném účtu.";
            } else if (message == "Too many records") {
                s = "Příliš mnoho odpovídajících záznamů.";
            } else if (message == "No email set") {
                s = "Účet uživatele nemá přiřazený žádný email.";
            } else {
                // rest of messages is in czech language!!
                s = message;
            }
            AMUIApp::errorMessageBox("Chyba přiřazení", s);
            break;
        }
        case AMSvr::VERSION_INVALID: {
            //"Inactual version"
            AMUIApp::errorMessageBox("Neplatná verze klienta", "Verze klienta ve Vašem prohlížečič již není aktální.\n\nProveďte občerstvení pomocí kláves CTRL-R nebo CMD-R.");
            break;
        }
        case AMSvr::APPLICATION_ACCOUNT_SUSPENDED: {
            AMUIApp::errorMessageBox("Aplikace", "Účet aplikace byl pozastaven.\n\nZvažte prodloužení vašeho\nzákaznického účtu.");
            break;
        }
        case AMSvr::DATABASE_NOT_EXISTS: {
            AMUIApp::errorMessageBox("Doména", "Doména(účet vaší aplikace) neexistuje.\n\nZkontrolujte URL v adresním řádku.");
            break;
        }
        case AMSvr::SERVER_ERROR: {
            AMUIApp::errorMessageBox("Chyba serveru", message);
            break;
        }
        case AMSvr::RECORD_NOT_FOUND: {
            std::string s = message;
            if (message == "Record not exists") {
                s = "Záznam již neexistuje.";
            }
            AMUIApp::errorMessageBox("Neexistuje", s);
            break;
        }
        case AMSvr::BAD_URL_FORMAT: {//generovan z url utils přičtením 100 k error 413
            AMUIApp::errorMessageBox("Dotaz", "Dotaz na server je přiliš dlouhý.\nZkraťte texty ve formuláři.");
            break;
        }
        case AMSvr::NO_RESPONSE: {
            AMUIApp::errorMessageBox("Chyba serveru", "Server neodpovídá.\nZkontrolujte připojení k internetu.");
            break;
        }
        default:
            AMUIApp::errorMessageBox("Chyba "+ std::to_string(status), message);
    }

}
