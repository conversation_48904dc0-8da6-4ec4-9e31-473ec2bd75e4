/* automatically generated file, do not edit. */

#include "CLegalRequirementsForMedicalFitnessEnum.h"  

CLegalRequirementsForMedicalFitnessEnum::CLegalRequirementsForMedicalFitnessEnum(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      //color(),
      description()//,
      //titledesc(),
      //ord()
    {           
    };
    
CLegalRequirementsForMedicalFitnessEnum::CLegalRequirementsForMedicalFitnessEnum()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      //color(),
      description()//,
      //titledesc(),
      //ord()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CLegalRequirementsForMedicalFitnessEnum::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CLegalRequirementsForMedicalFitnessEnum>(
    "dbe.legal_req_for_med_fitness",
    {  
        {&CLegalRequirementsForMedicalFitnessEnum::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CLegalRequirementsForMedicalFitnessEnum::title, "title", AMCore::AMDataType_string, {}, nullptr},
        //{&CLegalRequirementsForMedicalFitnessEnum::color, "color", AMCore::AMDataType_string, {}, nullptr},
        {&CLegalRequirementsForMedicalFitnessEnum::description, "description", AMCore::AMDataType_string, {}, nullptr},
        //{&CLegalRequirementsForMedicalFitnessEnum::titledesc, "titledesc", AMCore::AMDataType_string, {}, "t.title || ' ' || SPLIT_PART(t.description, E'\n', 1) AS titledesc"},
        //{&CLegalRequirementsForMedicalFitnessEnum::ord, "ord", AMCore::AMDataType_integer, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CLegalRequirementsForMedicalFitnessEnum::context()
{
    return *g_context;
}

CLegalRequirementsForMedicalFitnessEnumMap::CLegalRequirementsForMedicalFitnessEnumMap()
    :std::map<int, CLegalRequirementsForMedicalFitnessEnum>(),
     loaded(false)
{
}


bool CLegalRequirementsForMedicalFitnessEnumMap::loadCLegalRequirementsForMedicalFitnessEnum(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    CLegalRequirementsForMedicalFitnessEnum::loadSelect(sql, "dbe.legal_req_for_med_fitness");
    try {
        pqxx::result R(db.query(sql.str(), W));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CLegalRequirementsForMedicalFitnessEnum lr;
            lr.loadFromData(it);
            insert({lr.id, lr});
        }
        loaded = true;
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}