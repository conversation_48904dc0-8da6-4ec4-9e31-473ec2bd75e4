/* automatically generated file, do not edit. */

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CConnectedAccounts: public AMSvr::AMSvrDbObject<int>
{
public:
    CConnectedAccounts();
    CConnectedAccounts(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<SAWString, int> domain;
    AMSvr::AMSvrProp<std::tm, int> validFrom;
    AMSvr::AMSvrProp<std::tm, int> validUntil;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};
