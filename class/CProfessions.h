/* automatically generated file, do not edit. */


#ifndef CPROFESSIONS_H
#define CPROFESSIONS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CProfessions: public AMSvr::AMSvrDbObject<int>
{
public:
    CProfessions();
    CProfessions(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<SAWString, int> typeOfWork;
    AMSvr::AMSvrProp<int, int> czIsco;
    AMSvr::AMSvrProp<SAWEnum, int> object;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> objectAddress_;
    AMSvr::AMSvrProp<SAWEnum, int> workplace;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> workplace_;
    AMSvr::AMSvrProp<SAWString, int> workingTime;
    AMSvr::AMSvrProp<SAWString, int> interruptMode;
    AMSvr::AMSvrProp<SAWEnum, int> workingMode;
    AMSvr::AMSvrProp<SAWString, int> workload;
    AMSvr::AMSvrProp<SAWString, int> addInfo;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPROFESSIONS_H
