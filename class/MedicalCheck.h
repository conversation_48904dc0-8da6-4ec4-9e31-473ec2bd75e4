//
// Created by <PERSON><PERSON><PERSON> on 9.3.24.
//

#ifndef SAW_MEDICALCHECK_H
#define SAW_MEDICALCHECK_H

#include "svr/AMSvrDB.h"
#include "SAWSettings.h"
#include "svr/AMSvrSessionForTunnel.h"
//#include

class MedicalCheck {
public:
    static void fillRecord(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request& req, httplib::Response& res);
    static void updateStatus(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request& req, httplib::Response& res, std::string &body);
};


#endif //SAW_MEDICALCHECK_H
