/* automatically generated file, do not edit. */

#ifndef CPDFTYPES_H
#define CPDFTYPES_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CPdfTypes: public AMSvr::AMSvrDbObject<int>
{
public:
    CPdfTypes();
    CPdfTypes(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    AMSvr::AMSvrProp<std::string, int> title;
    AMSvr::AMSvrProp<SAWEnum , int> classtype;
    AMSvr::AMSvrProp<std::string, int> version;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPDFTYPES_H
