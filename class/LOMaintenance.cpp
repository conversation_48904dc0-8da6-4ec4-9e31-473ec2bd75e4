//
// Created by <PERSON><PERSON><PERSON> on 24.10.23.
//

#include <pthread.h>
#include <pqxx/pqxx>
#include "LOMaintenance.h"

void LOMaintenance::run()
{
    printf("MAINTENANCE!\n");
    std::string sql = "SELECT datname FROM pg_database WHERE datname <> 'rdsadmin' AND datname <> 'postgres' AND datname <> '_saw' AND datname NOT LIKE 'template%';";
    AMSvr::AMSvrDB db("_saw");
    std::set<std::string> dbNames;
    try {
        pqxx::result R1(db.nonTransactQuery(sql));
        for (auto it: R1) {
            dbNames.insert(it.at(0).template as<std::string>());
        }
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log("", "", AMLog::Error, "LOMaintenance::run list dbs fail");
    }
    for (auto dbName: dbNames) {
        AMSvr::AMSvrDB dbPriv(dbName);
        pqxx::work W(dbPriv.conn());
        try {
            runPriv(dbPriv, W);
            W.commit();
            AMLog::AMLoggerSvr::Log("", "", AMLog::Info,"LOMaintenance::run db %s maintenance success\n", dbName.c_str());
        } catch (std::exception e) {
            //W.abort();
            AMLog::AMLoggerSvr::Log("", "", AMLog::Error, "LOMaintenance::run db %s maintenance fail", dbName.c_str());
            printf("LOMaintenance::run db %s maintenance fail\n", dbName.c_str());
        }
    }

    pqxx::work W2(db.conn());
    time_t t = time(nullptr);
    finished = *gmtime(&t);
    try {
        save(db, W2);
        W2.commit();
    } catch (std::exception e) {
        //W2.abort();
        AMLog::AMLoggerSvr::Log("", "", AMLog::Warn, "LOMaintenance::run save query failed");
    }
}

LOMaintenance::LOMaintenance()
    : LongOpItem()
{
    task = loq_maintenance;
}


void LOMaintenance::initThread()
{
    time_t now = time(0);
    std::tm *ltm = gmtime(&now);
    g_lastTime = *ltm;
    int result = pthread_create(&g_timerTid, NULL, threadLoop, nullptr);
    if (result != 0) {
        AMLog::AMLoggerSvr::Log("", "", AMLog::Error, "LOMaintenance::run thread fail");
    }
}


void *LOMaintenance::threadLoop(void*)
{
    while(1) {
        time_t now = time(0);
        std::tm *ltm = gmtime(&now);

        if ((g_lastTime.tm_year != ltm->tm_year || g_lastTime.tm_mon != ltm->tm_mon || g_lastTime.tm_mday != ltm->tm_mday/*|| g_lastTime.tm_min != ltm->tm_min*/) && ltm->tm_hour == 1) {
            g_lastTime = *ltm;
            auto m = new LOMaintenance();
            m->input = "";
            m->customer = "_saw";
            const std::time_t t = time(nullptr);
            m->created = *gmtime(&t);
            AMSvr::AMSvrDB db("_saw");
            pqxx::work W(db.conn());
            try {
                m->save(db, W);
                W.commit();
            } catch (std::exception e) {
                AMLog::AMLoggerSvr::Log("", "", AMLog::Error, "LOMaintenance::run threadloop fail");
            }
            LongOpManager::instance().add(m);
        }
        sleep(600);
    }

}

pthread_t LOMaintenance::g_timerTid = 0LL;
std::tm LOMaintenance::g_lastTime = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};

void LOMaintenance::runPriv(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::string sql = "UPDATE dbo.healthy_restrictions t SET modified_by=0, person__ = mc.person FROM dbo.medical_checks mc WHERE mc.id = t.med_check AND t.person__ <> mc.person;"
                      "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_restrictions;"
                      "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_check_status;";

    db.query(sql, W);

    sql = "UPDATE dbo.medical_checks umc SET modified_by=0, status = t.st "
            "FROM (SELECT mc.id, "
                "CASE "
                    "WHEN con.conclusion_date IS NOT NULL AND con.completeness = 0 THEN 1 "
                    "WHEN con.conclusion_date IS NULL AND mc.order_date >= NOW() + INTERVAL '90 DAYS' THEN 2 "
                    "WHEN con.conclusion_date IS NULL AND mc.order_date < NOW() + INTERVAL '90 DAYS' AND mc.order_date >= NOW() AND mc.sent = false THEN 3 "
                    "WHEN con.conclusion_date IS NULL AND mc.order_date < NOW() + INTERVAL '90 DAYS' AND mc.order_date >= NOW() AND mc.sent = true THEN 4 "
                    "WHEN con.conclusion_date IS NULL AND mc.order_date < NOW() THEN 5 "
                    "WHEN con.conclusion_date IS NOT NULL AND con.completeness = 1 THEN 6 "
                    "WHEN con.conclusion_date IS NOT NULL AND con.completeness = 2 THEN 7 "
                "END AS st "
                "FROM dbo.medical_checks mc LEFT JOIN dbo.med_conclusions con ON con.id = mc.id) t "
            "WHERE t.id = umc.id AND st <> umc.status ;";

    db.query(sql, W);
}
