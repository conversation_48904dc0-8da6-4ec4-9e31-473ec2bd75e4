//
// Created by <PERSON><PERSON><PERSON> on 14.1.24.
//
#include "svr/Pdf.h"
#include "model/TextUtils.h"
#include "CMedicalChecks.h"
#include "CPeople.h"
#include "CPeopleData.h"
#include "CEmployers.h"
#include "CEmployerOsvcs.h"
#include "CProfessions.h"
#include "CWorkingModes.h"
#include "CCategorizations.h"
#include "COccupationalHazards.h"
#include "CRiskFactorsWorkingConditions.h"
#include "CLegalRequirementsForMedicalFitness.h"
#include "CProfessionEducationAreas.h"
#include "CProvidersWMS.h"
#include "CExaminations.h"
#include "amdialogs/AMUISynthesizers.h"
#include "generated/job_categorization_factors.h"
#include "generated/job_category.h"
#include "generated/occupational_hazards.h"
#include "generated/examination_types.h"
#include "COccupationalHazardsEnum.h"
#include "CRiskFactorsWorkingConditionsEnum.h"
#include "CLegalRequirementsForMedicalFitnessEnum.h"
#include "CExaminationTypes.h"

#include "generated/CWorkingInjuriesRecords.h"
#include "generated/CWorkingInjuries.h"
#include "generated/sex.h"
#include "generated/citizenships.h"
#include "CCitizenships.h"

AMSvr::AMSvrStatus Pdf::creaWIRecord(AMSvr::AMSvrDB &db, pqxx::work &W, const httplib::Request &req, int document, nlohmann::json &j)
{
    std::stringstream errors;
    CWorkingInjuriesRecords wir(document);
    if (!wir.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid document id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    CWorkingInjuries wi(wir.workingInjury.get());
    if (!wi.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid wi id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["injury_type_stat"] = (int)wi.injuryTypeStat.get();
    j["reg_number"] = (std::string)wi.regNumber.get();
    j["reg_number_employer"] = (std::string)wi.regNumberEmployer.get();
    j["regular_workplace"] = (bool)wi.regularWorkplace.get();
    j["activity"] = (std::string)wi.activity.get();

    CPeople employee(wi.person.get());
    if (!employee.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid employee id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    CEmployers empOfEmployee(employee.employer.get());
    if (!empOfEmployee.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid empOfEmployee id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    if (employee.employer.get() == wi.employer.get()) {
        j["employer_ico"] = TextUtils::formatICO(empOfEmployee.ico.get());
        j["employer_name_address"] = (std::string)empOfEmployee.name.get() + "\n" + (std::string)empOfEmployee.address.get();
        j["cz_nace"] = (std::string)wi.czNace.get();
        j["site"] = (std::string)wi.site.get();
        j["wi_employer_ico"] = "";
        j["wi_employer_name_address"] = "";
        j["wi_cz_nace"] = "";
        j["wi_site"] = "";
    } else {
        j["employer_ico"] = TextUtils::formatICO(empOfEmployee.ico.get());
        j["employer_name_address"] = (std::string)empOfEmployee.name.get() + "\n" + (std::string)empOfEmployee.address.get();
        j["cz_nace"] = "";
        j["site"] = "";
        CEmployers empOfWI(wi.employer.get());
        if (!empOfWI.load(db, W)) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid empOfWI id");
            return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
        }
        j["wi_employer_ico"] = TextUtils::formatICO(empOfWI.ico.get());
        j["wi_employer_name_address"] = (std::string)empOfWI.name.get() + "\n" + (std::string)empOfWI.address.get();
        j["wi_cz_nace"] = (std::string)wi.czNace.get();
        j["wi_site"] = (std::string)wi.site.get();
    }
    j["name"] = (std::string)employee.cname();
    j["sex"] = (bool)employee.sex.get() == SAW_sex::male;
    CPeopleData employeeData(employee.id.get());
    if (!employeeData.load(db, W)) {
        errors << "Není vyplněno personal info. Vyplňte jej a vygenerujte dokument znovu.";
        j["address"] = "";
        j["date_birth"] = "";
        j["citizenship"] = "";
        j["duration_years"] = "";
        j["duration_months"] = "";
    } else {
        j["address"] = (std::string)employeeData.permanentAddress.get();
        std::tm t = employeeData.dateBirth.get();
        j["date_birth"] = AMDialogs::AMUISynthesizeDatetime<std::string>("%Y-%m-%d", &t);
        CCitizenships cititizenship(employeeData.citizenship.get());
        if (!cititizenship.load(db, W)) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid cititizenship id");
            return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
        }
        j["citizenship"] = (std::string)cititizenship.title.get();
        std::tm boatding_date = employeeData.onboardDate.get();

        std::time_t atm = time(nullptr);
        std::tm actTm = *localtime(&atm);
        std::tm difft = subractTime(actTm, boatding_date);
        j["duration_years"] = std::to_string(difft.tm_year);
        j["duration_months"] = std::to_string(difft.tm_mon);
    }
    CProfessions profession(employee.profession.get());
    if (!profession.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaWIRecord invalid profession id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["prof_cz_isco"] = std::tostring(profession.czIsco.get());


    /*
    j["mc_addinfo"] = (std::string)mc.medicalInfo.get();
    j["mc_purpose"] = (std::string)mc.purpose.get();

    char requestbuf[64];
    snprintf(requestbuf, sizeof(requestbuf) - 1, "%04d%02d%02d-%08d", mc.orderDate.get().tm_year + 1900, mc.orderDate.get().tm_mon + 1, mc.orderDate.get().tm_mday, mc.id.get());
    j["request_no"] = requestbuf;

    CPeople per(mc.person.get());
    if (!per.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaMedicalCheck invalid person id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["per_cname"] = (std::string)per.cname();
    j["per_centre"] = (std::string)per.centrecode_.get();

    CPeople perReq(mc.privilegedPerson.get());
    if (!perReq.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaMedicalCheck invalid person id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    CPeopleData perReqData(mc.privilegedPerson.get());
    if (perReqData.load(db, W)) {
        j["request_cname"] = (std::string)perReq.cname() + (!perReqData.identityCard.get().empty() ? " (doklad č. " + (std::string)perReqData.identityCard.get() + ")" : std::string());
    } else {
        j["request_cname"] = (std::string)perReq.cname();
    }
    CPeople perAppl(mc.appliedPerson.get());
    if (perAppl.load(db, W)) {
        j["appl_cname"] = (std::string)perAppl.cname();
    } else {
        j["appl_cname"] = "";
    }



    CProfessions prof(per.profession.get());
    if (!prof.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaMedicalCheck invalid profession id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["prof_object"] = (std::string)prof.objectAddress_.get();
    j["prof_workplace"] = (std::string)prof.workplace_.get();
    j["prof_name"] = (std::string)prof.name.get();
    j["prof_type"] = (std::string)prof.typeOfWork.get();
    j["prof_load"] = TextUtils::nl2br(prof.workload.get());
    j["prof_addinfo"] = TextUtils::nl2br(prof.addInfo.get());
    j["prof_mode"] = std::string("Pracovní doba: ") + TextUtils::nl2br(prof.workingTime.get()) + "<br/>Režim přestávek: " + TextUtils::nl2br(prof.interruptMode.get());
    char iscobuf[32];
    snprintf(iscobuf, sizeof(iscobuf) - 1, "%05i", prof.czIsco.get());
    j["prof_isco"] = iscobuf;
    CWorkingModes profwm(prof.workingMode.get());
    if (!profwm.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaMedicalCheck invalid working mode id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["prof_workingmode"] = (std::string)profwm.title.get();

    CCategorizationsMap catmap;
    std::vector<std::string> catarr = {"1", "2", "2R", "3", "4"};
    catmap.loadCategorizations(db, W, prof.id.get());
    auto it = catmap.find(SAW_job_categorization_factors::risk_factor_dust);
    try {
        j["cat_dust"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_dust"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_chemicals);
    try {
        j["cat_chemicals"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_chemicals"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_noise);
    try {
        j["cat_noise"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_noise"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_vibration);
    try {
        j["cat_vibration"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_vibration"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_nonionizing_radiatio);
    try {
        j["cat_nonionizing_radiatio"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_nonionizing_radiatio"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_physical_load);
    try {
        j["cat_physical_load"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_physical_load"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_working_position);
    try {
        j["cat_working_position"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_working_position"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_heat_load);
    try {
        j["cat_heat_load"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_heat_load"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_cold_stress);
    try {
        j["cat_cold_stress"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_cold_stress"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_mental_stress);
    try {
        j["cat_mental_stress"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_mental_stress"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_visual_stress);
    try {
        j["cat_visual_stress"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_visual_stress"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_biological_agents);
    try {
        j["cat_biological_agents"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_biological_agents"] = "";
    }
    it = catmap.find(SAW_job_categorization_factors::risk_factor_increased_pressure);
    try {
        j["cat_increased_pressure"] = (it == catmap.end()) ? "" : catarr[it->second.category.get() - 1];
    } catch (std::exception e) {
        j["cat_increased_pressure"] = "";
    }
    int catTotal = 0;
    for (auto it:catmap) {
        if (it.second.category.get() > catTotal) {
            catTotal = it.second.category.get();
        }
    }
    j["cat_total"] = (catTotal >= SAW_job_category::ctg_1 && catTotal <= SAW_job_category::ctg_4) ? catarr[catTotal - 1] : "";
    j["cat_risky"] = catTotal >= SAW_job_category::ctg_2R;
    bool first = true;
    std::ostringstream se;
    for( auto it: catmap) {
        if (it.second.duration.get().empty()) {
            continue;
        }
        if (first) {
            first = false;
        } else {
            se << "<br/>";
        }
        if (it.second.factor.get() >= SAW_job_categorization_factors::risk_factor_dust && it.second.factor.get() <= SAW_job_categorization_factors::risk_factor_increased_pressure) {
            se << s_categorizationFactors[it.second.factor.get()] << ": " << it.second.duration.get();
        }
    }
    j["cat_durations"] = se.str();

    COccupationalHazardsMap ochmap;
    ochmap.loadCOccupationalHazards(db, W, prof.id.get());
    std::ostringstream s;
    first = true;
    for(auto it:ochmap) {
        if (it.second.riskName.get().isNull()) {
            continue;
        }
        if (first) {
            first = false;
        } else {
            s << "<br/>";
        }
        s << it.second.riskName.get().get();
        if ((it.second.riskEnum == SAW_occupational_hazards::R110 || it.second.riskEnum == SAW_occupational_hazards::R111) && !it.second.risk11.get().empty()) {
            s << " - " << it.second.risk11.get();
        }
    }
    j["occupational_hazards"] = s.str();

    CRiskFactorsWorkingConditionsMap rfwcmap;
    rfwcmap.loadCRiskFactorsWorkingConditions(db, W, prof.id.get());
    std::ostringstream s2;
    first = true;
    for(auto it:rfwcmap) {
        if (it.second.rFWorkingCondName.get().isNull()) {
            continue;
        }
        if (first) {
            first = false;
        } else {
            s2 << "<br/>";
        }
        s2 << it.second.rFWorkingCondName.get().get();
    }
    j["risk_factors_working_conds"] = s2.str();

    CLegalRequirementsForMedicalFitnessMap lrmap;
    lrmap.loadCLegalRequirementsForMedicalFitness(db, W, prof.id.get());
    std::ostringstream s3;
    first = true;
    for(auto it:lrmap) {
        if (it.second.reqName.get().isNull()) {
            continue;
        }
        if (first) {
            first = false;
        } else {
            s3 << "<br/>";
        }
        std::string rs = it.second.reqName.get().get();
        std::replace( rs.begin(), rs.end(), '\n', ' ');
        s3 << rs;
    }
    j["legal_req"] = s3.str();

    CProvidersWMS wms(mc.provider.get());
    if (!wms.load(db, W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "creaMedicalCheck invalid wms id");
        return AMSvr::AMSvrStatus::RECORD_NOT_FOUND;
    }
    j["wms_name"] = (std::string)wms.name.get();
    j["wms_address"] = (std::string)wms.address.get();
    j["wms_ico"] = TextUtils::formatICO(wms.ico.get());

    CPeopleData perdata(mc.person.get());
    if (perdata.load(db, W)) {
        j["perdata_address"] = (std::string)perdata.permanentAddress.get();
        j["perdata_birthdate"] = AMDialogs::AMUISynthesizeDatetime<std::string>(AMDialogs::dateFormatText, &perdata.dateBirth.get());
        j["perdata_in"] = (std::string)perdata.identityNumber.get();
    } else {
        j["perdata_address"] = "";
        j["perdata_birthdate"] = "";
        j["perdata_in"] = "";
    }

    CEmployerOsvcs osvc(emp.id.get());
    if (osvc.load(db, W) && osvc.isOsvc.get()) {
        j["osvc_address"] = (std::string)osvc.addressOsvc.get();
        j["osvc_address_hl"] = "Bydliště:";
        j["osvc_birthdate"] = AMDialogs::AMUISynthesizeDatetime<std::string>(AMDialogs::dateFormatText, &osvc.birthOsvc.get());
        j["osvc_birthdate_hl"] = "Narozen(a):";
    } else {
        j["osvc_address"] = "";
        j["osvc_address_hl"] = "&nbsp;";
        j["osvc_birthdate"] = "";
        j["osvc_birthdate_hl"] = "&nbsp;";
    }


    CProfessionEducationAreas edu(prof.id.get());
    int schoolId = -1;
    if (edu.load(db, W)) {
        j["school_codename"] = (std::string)edu.codeName.get();
        if (!edu.school.get().isNull()) {
            schoolId = edu.school.get().get();
        }
    } else {
        j["school_codename"] = "";
    }
    CEmployers school(schoolId);
    if (schoolId > 0 && school.load(db, W)) {
        j["school_name"] = (std::string)school.name.get();
        j["school_ico"] = TextUtils::formatICO(school.ico.get());
        j["school_address"] = (std::string)school.address.get();
        j["school_exists"] = true;
    } else {
        j["school_name"] = "";
        j["school_ico"] = "";
        j["school_address"] = "";
        j["school_exists"] = false;
    }

    CExaminationsMap em;
    COccupationalHazardsEnumMap ohMap;
    CRiskFactorsWorkingConditionsEnumMap rfMap;
    CLegalRequirementsForMedicalFitnessEnumMap lrMap;
    int ndx = 1;
    std::set<SAWEnum> exaTypesProcessed;
    CExaminationTypesMap exaMap;
    if (em.loadCExaminations(db, W, document)) {
        j["examinations"][0]["name"] = "Základní vyšetření";
        j["examinations"][0]["reasons"] = em.reasonsForExamination(SAW_examination_types::exa_zakladni, db, W, ohMap, rfMap, lrMap);
        exaTypesProcessed.insert(SAW_examination_types::exa_zakladni);
        for (auto itEm:em) {
            if (exaTypesProcessed.find(itEm.second.exaType.get()) != exaTypesProcessed.end()) {
                continue;
            }
            if (!exaMap.loaded) {
                exaMap.loadCExaminationTypes(db, W);
            }
            auto itExaMap = exaMap.find(itEm.second.exaType.get());
            if (itExaMap == exaMap.end()) {
                j["examinations"][ndx]["name"] = "Neznámé vyšetření " + std::to_string(itEm.second.exaType.get());
                j["examinations"][ndx]["reasons"] = std::vector<std::string>();
            } else {
                std::string name = itExaMap->second.title.get();
                if (!itExaMap->second.tooltip.get().empty()) {
                     name += " (" + itExaMap->second.tooltip.get() + ")";
                }
                j["examinations"][ndx]["name"] = name;
                j["examinations"][ndx]["reasons"] = em.reasonsForExamination(itEm.second.exaType.get(), db, W, ohMap, rfMap, lrMap);
            }
            exaTypesProcessed.insert(itEm.second.exaType.get());
            ndx++;
        }
    } else {
        j["examinations"] = std::vector<std::string>();
    }*/

    return AMSvr::AMSvrStatus::OK;
}
