/* automatically generated file, do not edit. */

#ifndef CRISKFACTORSWORKINGCONDITIONS_H
#define CRISKFACTORSWORKINGCONDITIONS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CRiskFactorsWorkingConditions: public AMSvr::AMSvrDbObject<int>
{
public:
    CRiskFactorsWorkingConditions();
    CRiskFactorsWorkingConditions(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> profession_;
    AMSvr::AMSvrProp<SAWEnum, int> rFWorkingCond;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> rFWorkingCondName;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CRiskFactorsWorkingConditionsMap: public std::map<SAWEnum, CRiskFactorsWorkingConditions>
{
public:
    bool loadCRiskFactorsWorkingConditions(AMSvr::AMSvrDB &db, pqxx::work &W, int profession);
};

#endif //CRISKFACTORSWORKINGCONDITIONS_H
