/* automatically generated file, do not edit. */


#ifndef CWORKINGMODES_H
#define CWORKINGMODES_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CWorkingModes: public AMSvr::AMSvrDbObject<int>
{
public:
    CWorkingModes();
    CWorkingModes(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> title;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CWORKINGMODES_H
