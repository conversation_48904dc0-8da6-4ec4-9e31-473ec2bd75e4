/* automatically generated file, do not edit. */

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CWMSSpecifics2: public AMSvr::AMSvrDbObject<int>
{
public:
    CWMSSpecifics2();
    CWMSSpecifics2(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<bool, int> occupationalDisease;
    AMSvr::AMSvrProp<bool, int> approvedDisability;
    AMSvr::AMSvrProp<bool, int> newValuation;
    AMSvr::AMSvrProp<bool, int> hearingLoss;
    AMSvr::AMSvrProp<bool, int> mcReqEmployer;
    AMSvr::AMSvrProp<bool, int> mcReqEmployee;
    AMSvr::AMSvrProp<bool, int> mcReqFollowing;
    //AMSvr::AMSvrProp<SAWEnum, int> degreeBefore;
    //AMSvr::AMSvrProp<SAWString, int> name;
    //AMSvr::AMSvrProp<SAWString, int> surname;
    //AMSvr::AMSvrProp<SAWEnum, int> degreeAfter;
    //AMSvr::AMSvrProp<AMCore::AMNameBase < SAWEnum, SAWString >, int> fullName;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};
