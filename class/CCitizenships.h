/* automatically generated file, do not edit. */

#ifndef CCITIZENSHIPS_H
#define CCITIZENSHIPS_H

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CCitizenships: public AMSvr::AMSvrDbObject<int>
{
public:
    CCitizenships();
    CCitizenships(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;

    AMSvr::AMSvrProp<SAWString, int> title;
    AMSvr::AMSvrProp<SAWString, int> code_A2;

    bool loadByCodeA2(SAWString code_A2, AMSvr::AMSvrDB &db, pqxx::work &W);
protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CCITIZENSHIPS_H