/* automatically generated file, do not edit. */

#include "CPeople.h"

CPeople::CPeople(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      degreeBefore(),
      degreeBefore_(),
      name(),
      surname(),
      degreeAfter(),
      degreeAfter_(),
      //cname(),
      sex(),
      email(),
      phone(),
      employer(),
      employerName(),
      //employer_(),
      centre(),
      centre_(),
      centrecode_(),
      eRelationship(),
      profession(),
      professionName(),
      //profession_(),
      state(),
      leader(),
      leaderName(),
      //leader_(),
      isLeader(),
      lastCheck(),
      //hiearchy(),
      note(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };
    
CPeople::CPeople()
    : AMSvr::AMSvrDbObject<int>(),
      degreeBefore(),
      degreeBefore_(),
      name(),
      surname(),
      degreeAfter(),
      degreeAfter_(),
      //cname(),
      sex(),
      email(),
      phone(),
      employer(),
      employerName(),
      //employer_(),
      centre(),
      centre_(),
      centrecode_(),
      eRelationship(),
      profession(),
      professionName(),
      //profession_(),
      state(),
      leader(),
      leaderName(),
      //leader_(),
      isLeader(),
      lastCheck(),
      //hiearchy(),
      note(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CPeople::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CPeople>(
    "dbo.people",
    {
        {&CPeople::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeople::degreeBefore, "degree_before", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::degreeBefore_, "degree_before_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.user_degrees udb1 ON udb1.id = t.degree_before"}, "udb1.degree AS degree_before_"},
        {&CPeople::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CPeople::surname, "surname", AMCore::AMDataType_string, {}, nullptr},
        {&CPeople::degreeAfter, "degree_after", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::degreeAfter_, "degree_after_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.user_degrees uda1 ON uda1.id = t.degree_after"}, "uda1.degree AS degree_after_"},
        //{&CPeople::cname, "cname", AMCore::AMDataType_name, {}, nullptr},
        {&CPeople::sex, "sex", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::email, "email", AMCore::AMDataType_string, {}, nullptr},
        {&CPeople::phone, "phone", AMCore::AMDataType_string, {}, nullptr},
        {&CPeople::employer, "employer", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeople::employerName, "employer_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.employers em ON em.id = t.employer"}, "em.name AS employer_name"},
        //{&CPeople::employer_, "employer_", AMCore::AMDataType_big_enum, {}, nullptr},
        {&CPeople::centre, "centre", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::centre_, "centre_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.centres cen ON cen.id = t.centre"}, "cen.name AS centre_"},
        {&CPeople::centrecode_, "centrecode_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.centres cen ON cen.id = t.centre"}, "cen.code || ' - ' || cen.name AS centrecode_"},
        {&CPeople::eRelationship, "e_relationship", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeople::professionName, "profession_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_name"},
        //{&CPeople::profession_, "profession_", AMCore::AMDataType_big_enum, {}, nullptr},
        {&CPeople::state, "state", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeople::leader, "leader", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeople::leaderName, "leader_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.people l ON l.id = t.leader", "LEFT JOIN dbo.user_degrees udb ON udb.id = l.degree_before", "LEFT JOIN dbo.user_degrees uda ON uda.id = l.degree_after"}, "CASE WHEN udb.degree <> '' THEN udb.degree || ' ' ELSE '' END || l.name || ' ' || l.surname || CASE WHEN uda.degree <> '' THEN ' ' || uda.degree ELSE '' END AS leader_name"},
        //{&CPeople::leader_, "leader_", AMCore::AMDataType_name_enum, {}, nullptr},
        {&CPeople::isLeader, "is_leader", AMCore::AMDataType_bool, {"LEFT JOIN dbo.users ur ON ur.id = t.id"}, "EXISTS(SELECT id FROM dbo.people WHERE leader = t.id) OR EXISTS(SELECT id FROM dbo.centres WHERE leader = t.id) OR EXISTS(SELECT id FROM dbo.workplaces WHERE leader = t.id) OR COALESCE(2 = ANY(ur.roles), false) AS is_leader"},
        {&CPeople::lastCheck, "last_check", AMCore::AMDataType_date_nullable, {""}, "(SELECT MAX(con.conclusion_date) AS lm_last_check FROM dbo.medical_checks m JOIN dbo.med_conclusions con ON con.id = m.id WHERE m.person = t.id AND con.conclusion_date IS NOT NULL) AS last_check"},
        //{&CPeople::hiearchy, "hiearchy", AMCore::AMDataType_integer_vector, {"LEFT JOIN dbv.people_hiearchy h ON h.person = t.id"}, "h.hiearchy AS hiearchy"},
        {&CPeople::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CPeople::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeople::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CPeople::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CPeople::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CPeople::context()
{
    return *g_context;
}

SAWString CPeople::cname()
{

    SAWString s = !degreeBefore_.get().isNull() && !degreeBefore_.get().get().empty() ? degreeBefore_.get().get() + " " + name.get() : name.get();
    s += " " + surname.get();
    if (!degreeAfter_.get().isNull() && !degreeAfter_.get().get().empty()) {
        s += " " + degreeAfter_.get().get();
    }
    return s;
}
