/* automatically generated file, do not edit. */

#ifndef COCCUPATION<PERSON><PERSON><PERSON><PERSON><PERSON>ENUM_H
#define COCCUPATION<PERSON>HA<PERSON>ARDSENUM_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class COccupationalHazardsEnum: public AMSvr::AMSvrDbObject<int>
{
public:
    COccupationalHazardsEnum();
    COccupationalHazardsEnum(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> title;
    //AMSvr::AMSvrProp<SAWString, int> color;
    AMSvr::AMSvrProp<SAWString, int> description;
    //AMSvr::AMSvrProp<SAWString, int> titledesc;
    //AMSvr::AMSvrProp<int, int> ord;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};


class COccupationalHazardsEnumMap: public std::map<int, COccupationalHazardsEnum>
{
public:
    COccupationalHazardsEnumMap();
    bool loadCOccupationalHazardsEnum(AMSvr::AMSvrDB &db, pqxx::work &W);
    bool loaded;
};

#endif //COCCUPATIONALHAZARDSENUM_H
