/* automatically generated file, do not edit. */

#include "CWMSSpecifics2.h"  

CWMSSpecifics2::CWMSSpecifics2(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      occupationalDisease(),
      approvedDisability(),
      newValuation(),
      hearingLoss(),
      mcReqEmployer(),
      mcReqEmployee(),
      mcReqFollowing(),
      /*degreeBefore(),
      name(),
      surname(),
      degreeAfter(),
      fullName(),*/
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CWMSSpecifics2::CWMSSpecifics2()
    : AMSvr::AMSvrDbObject<int>(),
      occupationalDisease(),
      approvedDisability(),
      newValuation(),
      hearingLoss(),
      mcReqEmployer(),
      mcReqEmployee(),
      mcReqFollowing(),
      /*degreeBefore(),
      name(),
      surname(),
      degreeAfter(),
      fullName(),*/
      note(),
      modified<PERSON>y(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CWMSSpecifics2::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CWMSSpecifics2>(
    "dbo.wms_specifics_2",
    {  
        {&CWMSSpecifics2::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CWMSSpecifics2::occupationalDisease, "occupational_disease", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::approvedDisability, "approved_disability", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::newValuation, "new_valuation", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::hearingLoss, "hearing_loss", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::mcReqEmployer, "mc_req_employer", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::mcReqEmployee, "mc_req_employee", AMCore::AMDataType_bool, {}, nullptr},
        {&CWMSSpecifics2::mcReqFollowing, "mc_req_following", AMCore::AMDataType_bool, {}, nullptr},
        /*{&CWMSSpecifics2::degreeBefore, "degree_before", AMCore::AMDataType_enum, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.degree_before AS degree_before"},
        {&CWMSSpecifics2::name, "name", AMCore::AMDataType_string, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.name AS name"},
        {&CWMSSpecifics2::surname, "surname", AMCore::AMDataType_string, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.surname AS surname"},
        {&CWMSSpecifics2::degreeAfter, "degree_after", AMCore::AMDataType_enum, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.degree_after AS degree_after"},
        {&CWMSSpecifics2::fullName, "full_name", AMCore::AMDataType_name, {}, nullptr},*/
        {&CWMSSpecifics2::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CWMSSpecifics2::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CWMSSpecifics2::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CWMSSpecifics2::context()
{
    return *g_context;
}
