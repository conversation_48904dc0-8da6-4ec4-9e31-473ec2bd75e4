/* automatically generated file, do not edit. */


#ifndef CPROVIDERSWMS_H
#define CPROVIDERSWMS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CProvidersWMS: public AMSvr::AMSvrDbObject<int>
{
public:
    CProvidersWMS();
    CProvidersWMS(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<unsigned int, int> ico;
    AMSvr::AMSvrProp<SAWString, int> address;
    AMSvr::AMSvrProp<bool, int> contractSign;
    AMSvr::AMSvrProp<SAWString, int> contractLink;
    AMSvr::AMSvrProp<bool, int> inspections;
    AMSvr::AMSvrProp<SAWString, int> email;
    AMSvr::AMSvrProp<SAWString, int> phone;
    AMSvr::AMSvrProp<SAWString, int> addressOffice;
    AMSvr::AMSvrProp<SAWString, int> web;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPROVIDERSWMS_H
