/* automatically generated file, do not edit. */

#include "CProfessionEducationAreas.h"  

CProfessionEducationAreas::CProfessionEducationAreas(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      profession(),
      profession_(),
      school(),
      //schoolName(),
      //school_(),
      codeName(),
      conditions(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CProfessionEducationAreas::CProfessionEducationAreas()
    : AMSvr::AMSvrDbObject<int>(),
      profession(),
      profession_(),
      school(),
      //schoolName(),
      //school_(),
      codeName(),
      conditions(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CProfessionEducationAreas::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CProfessionEducationAreas>(
    "dbo.profession_education_areas",
    {  
        {&CProfessionEducationAreas::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessionEducationAreas::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessionEducationAreas::profession_, "profession_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_"},
        {&CProfessionEducationAreas::school, "school", AMCore::AMDataType_integer_nullable, {}, nullptr},
        //{&CProfessionEducationAreas::schoolName, "school_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.employers em ON em.id = t.school"}, "em.name AS school_name"},
        //{&CProfessionEducationAreas::school_, "school_", AMCore::AMDataType_big_enum_nullable, {}, nullptr},
        {&CProfessionEducationAreas::codeName, "code_name", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessionEducationAreas::conditions, "conditions", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessionEducationAreas::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessionEducationAreas::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessionEducationAreas::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CProfessionEducationAreas::context()
{
    return *g_context;
}
