/* automatically generated file, do not edit. */

#include "CMedicalChecks.h"
#include "generated/medical_check_types.h"

CMedicalChecks::CMedicalChecks(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      person(),
      //personName(),
      //person_(),
      provider(),
      //provider_(),
      //employer(),
      checkType(),
      concurence(),
      reason(),
      purpose(),
      orderDate(),
      //performDate(),
      privilegedPerson(),
      //privilegedPersonName(),
      //privilegedPerson_(),
      appliedPerson(),
      //appliedPersonName(),
      //appliedPerson_(),
      medicalInfo(),
      note(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };
    
CMedicalChecks::CMedicalChecks()
    : AMSvr::AMSvrDbObject<int>(),
      person(),
      //personName(),
      //person_(),
      provider(),
      //provider_(),
      //employer(),
      checkType(),
      concurence(),
      reason(),
      purpose(),
      orderDate(),
      //performDate(),
      privilegedPerson(),
      //privilegedPersonName(),
      //privilegedPerson_(),
      appliedPerson(),
      //appliedPersonName(),
      //appliedPerson_(),
      medicalInfo(),
      note(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CMedicalChecks::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CMedicalChecks>(
    "dbo.medical_checks",
    {
        {&CMedicalChecks::id, "id", AMCore::AMDataType_integer, {}, nullptr},

        {&CMedicalChecks::person, "person", AMCore::AMDataType_integer, {}, nullptr},
        //{&CMedicalChecks::personName, "person_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.people p ON p.id = t.person", "LEFT JOIN dbo.user_degrees udb ON udb.id = p.degree_before", "LEFT JOIN dbo.user_degrees uda ON uda.id = p.degree_after"}, "CASE WHEN udb.degree <> '' THEN udb.degree || ' ' ELSE '' END || p.name || ' ' || p.surname || CASE WHEN uda.degree <> '' THEN ' ' || uda.degree ELSE '' END AS person_name"},
        //{&CMedicalChecks::person_, "person_", AMCore::AMDataType_name_enum, {}, nullptr},
        {&CMedicalChecks::provider, "provider", AMCore::AMDataType_enum, {}, nullptr},
        //{&CMedicalChecks::provider_, "provider_", AMCore::AMDataType_string, {"LEFT JOIN dbo.employers pr ON pr.id = t.provider"}, "pr.name AS provider_"},
        //{&CMedicalChecks::employer, "employer", AMCore::AMDataType_string, {"LEFT JOIN dbo.people p ON p.id = t.person", "LEFT JOIN dbo.employers em ON em.id = p.employer"}, "em.name AS employer"},
        {&CMedicalChecks::checkType, "check_type", AMCore::AMDataType_enum, {}, nullptr},
        {&CMedicalChecks::concurence, "concurence", AMCore::AMDataType_bool, {}, nullptr},
        {&CMedicalChecks::reason, "reason", AMCore::AMDataType_string, {}, nullptr},
        {&CMedicalChecks::purpose, "purpose", AMCore::AMDataType_string, {}, nullptr},
        {&CMedicalChecks::orderDate, "order_date", AMCore::AMDataType_date, {}, nullptr},
        //{&CMedicalChecks::performDate, "perform_date", AMCore::AMDataType_date_nullable, {}, nullptr},
        {&CMedicalChecks::privilegedPerson, "privileged_person", AMCore::AMDataType_integer_nullable, {}, nullptr},
        //{&CMedicalChecks::privilegedPersonName, "privileged_person_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.people pp ON pp.id = t.privileged_person", "LEFT JOIN dbo.user_degrees ppudb ON ppudb.id = pp.degree_before", "LEFT JOIN dbo.user_degrees ppuda ON ppuda.id = pp.degree_after"}, "CASE WHEN ppudb.degree <> '' THEN ppudb.degree || ' ' ELSE '' END || pp.name || ' ' || pp.surname || CASE WHEN ppuda.degree <> '' THEN ' ' || ppuda.degree ELSE '' END AS privileged_person_name"},
        //{&CMedicalChecks::privilegedPerson_, "privileged_person_", AMCore::AMDataType_name_enum_nullable, {}, nullptr},
        {&CMedicalChecks::appliedPerson, "applied_person", AMCore::AMDataType_integer, {}, nullptr},
        //{&CMedicalChecks::appliedPersonName, "applied_person_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.people ap ON ap.id = t.applied_person", "LEFT JOIN dbo.user_degrees apudb ON apudb.id = ap.degree_before", "LEFT JOIN dbo.user_degrees apuda ON apuda.id = ap.degree_after"}, "CASE WHEN apudb.degree <> '' THEN apudb.degree || ' ' ELSE '' END || ap.name || ' ' || ap.surname || CASE WHEN apuda.degree <> '' THEN ' ' || apuda.degree ELSE '' END AS applied_person_name"},
        //{&CMedicalChecks::appliedPerson_, "applied_person_", AMCore::AMDataType_name_enum, {}, nullptr},
        {&CMedicalChecks::medicalInfo, "medical_info", AMCore::AMDataType_string, {}, nullptr},
        {&CMedicalChecks::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CMedicalChecks::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CMedicalChecks::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CMedicalChecks::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CMedicalChecks::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CMedicalChecks::context()
{
    return *g_context;
}

bool CMedicalChecks::loadLastEnterOrPeriodical(AMSvr::AMSvrDB &db, pqxx::work &W, int person)
{
    std::ostringstream sql;
    loadSelect(sql, "dbo.medical_checks");
    sql << " JOIN dbo.med_conclusions con ON con.id = t.id WHERE person = $1 AND (check_type = " << SAW_medical_check_types::mct_enter << " OR check_type = " << SAW_medical_check_types::mct_periodical;
    sql << ") AND con.conclusion_date IS NOT NULL ORDER BY con.conclusion_date DESC LIMIT 1";
    try {
        pqxx::result R(db.query(sql.str(), W, person));
        if (R.begin() == R.end()) {
            return false;
        }
        loadFromData(R.begin());
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}

bool CMedicalChecks::loadLastEnter(AMSvr::AMSvrDB &db, pqxx::work &W, int person)
{
    std::ostringstream sql;
    loadSelect(sql, "dbo.medical_checks");
    sql << " JOIN dbo.med_conclusions con ON con.id = t.id WHERE person = $1 AND check_type = " << SAW_medical_check_types::mct_enter;
    sql << " AND con.conclusion_date IS NOT NULL ORDER BY con.conclusion_date DESC LIMIT 1";
    try {
        pqxx::result R(db.query(sql.str(), W, person));
        if (R.begin() == R.end()) {
            return false;
        }
        loadFromData(R.begin());
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}