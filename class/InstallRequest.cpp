//
// Created by <PERSON><PERSON><PERSON> on 29.10.23.
//

#include "InstallRequest.h"

InstallRequest::InstallRequest()
    :   AMSvr::AMSvrDbObject<int>(),
        ourSubdomain(true),
        domain(),
        eName(),
        eIco(),
        ePlace(),
        eStreet(),
        eDescriptiveNum(-9999),
        eOrientationNum("-9999"),
        eCity(),
        eZip(),
        eCountry(),
        eAccidentInsurance(),
        uDegreeBefore(),
        uName(),
        uSurname(),
        uDegreeAfter(),
        uSex(),
        uEmployerRelationship(),
        uUsername(),
        uEmail(),
        uPasshash(),
        cAckNewsletters(),
        cEmail(),
        cAckTesting(),
        cMainIndustry(),
        cEmployees(),
        cUseApp(),
        cWhichApp(),
        cTryAs(),
        c<PERSON><PERSON>s(),
        dSize(1),
        cWishes(),
        created()
{

}

AMSvr::AMSvrDbObjectContext<int> *InstallRequest::g_context = AMSvr::AMSvrDbObjectContext<int>::create<InstallRequest>(
    "dbo.install_requests",
    {
        {&InstallRequest::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&InstallRequest::ourSubdomain, "our_subdomain", AMCore::AMDataType_bool, {}, nullptr},
        {&InstallRequest::domain, "domain", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eName, "e_name", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eIco, "e_ico", AMCore::AMDataType_integer, {}, nullptr},
        {&InstallRequest::ePlace, "e_place", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eStreet, "e_street", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eDescriptiveNum, "e_descriptive_num", AMCore::AMDataType_integer, {}, nullptr},
        {&InstallRequest::eOrientationNum, "e_orientation_num", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eCity, "e_city", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eZip, "e_zip", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::eCountry, "e_country", AMCore::AMDataType_enum, {}, nullptr},
        {&InstallRequest::eAccidentInsurance, "e_accident_insurance", AMCore::AMDataType_enum, {}, nullptr},
        {&InstallRequest::uDegreeBefore, "u_degree_before", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uName, "u_name", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uSurname, "u_surname", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uDegreeAfter, "u_degree_after", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uSex, "u_sex", AMCore::AMDataType_enum, {}, nullptr},
        {&InstallRequest::uEmployerRelationship, "u_e_relationship", AMCore::AMDataType_enum, {}, nullptr},
        {&InstallRequest::uUsername, "u_username", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uEmail, "u_email", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::uPasshash, "u_passhash", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::cAckNewsletters, "c_ack_newsletters", AMCore::AMDataType_bool, {}, nullptr},
        {&InstallRequest::cEmail, "c_email", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::cAckTesting, "c_ack_testing", AMCore::AMDataType_bool, {}, nullptr},
        {&InstallRequest::cMainIndustry, "c_main_industry", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::cEmployees, "c_employees", AMCore::AMDataType_integer, {}, nullptr},
        {&InstallRequest::cUseApp, "c_use_app", AMCore::AMDataType_bool, {}, nullptr},
        {&InstallRequest::cWhichApp, "c_which_app", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::cTryAs, "c_try_as", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::cAreas, "c_areas", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::dSize, "d_size", AMCore::AMDataType_integer, {}, nullptr},
        {&InstallRequest::cWishes, "c_wishes", AMCore::AMDataType_string, {}, nullptr},
        {&InstallRequest::created, "created", AMCore::AMDataType_datetime, {}, nullptr},
    }
                                                            );
AMSvr::AMSvrDbObjectContext<int> &InstallRequest::context()
{
    return *g_context;
}
