//
// Created by <PERSON><PERSON><PERSON> on 29.10.23.
//

#ifndef SAW_INSTALLREQUEST_H
#define SAW_INSTALLREQUEST_H

#include "svr/AMSvrObject.h"
#include "SAWSettings.h"
class InstallRequest: public AMSvr::AMSvrDbObject<int>
{
public:
    InstallRequest();
    static AMSvr::AMSvrDbObjectContext<int> *g_context;
    AMSvr::AMSvrDbObjectContext<int> &context() override;

    AMSvr::AMSvrProp<bool, int> ourSubdomain;
    AMSvr::AMSvrProp<std::string, int> domain;
    AMSvr::AMSvrProp<std::string, int> eName;
    AMSvr::AMSvrProp<int, int> eIco;
    AMSvr::AMSvrProp<std::string, int> ePlace;
    AMSvr::AMSvrProp<std::string, int> eStreet;
    AMSvr::AMSvrProp<int, int> eDescriptiveNum;
    AMSvr::AMSvrProp<std::string, int> eOrientationNum;
    AMSvr::AMSvrProp<std::string, int> eCity;
    AMSvr::AMSvrProp<std::string, int> eZip;
    AMSvr::AMSvrProp<SAWEnum, int> eCountry;
    AMSvr::AMSvrProp<SAWEnum, int> eAccidentInsurance;
    AMSvr::AMSvrProp<std::string, int> uDegreeBefore;
    AMSvr::AMSvrProp<std::string, int> uName;
    AMSvr::AMSvrProp<std::string, int> uSurname;
    AMSvr::AMSvrProp<std::string, int> uDegreeAfter;
    AMSvr::AMSvrProp<SAWEnum, int> uSex;
    AMSvr::AMSvrProp<SAWEnum, int> uEmployerRelationship;
    AMSvr::AMSvrProp<std::string, int> uUsername;
    AMSvr::AMSvrProp<std::string, int> uEmail;
    AMSvr::AMSvrProp<std::string, int> uPasshash;
    AMSvr::AMSvrProp<bool, int> cAckNewsletters;
    AMSvr::AMSvrProp<std::string, int> cEmail;
    AMSvr::AMSvrProp<bool, int> cAckTesting;
    AMSvr::AMSvrProp<std::string, int> cMainIndustry;
    AMSvr::AMSvrProp<int, int> cEmployees;
    AMSvr::AMSvrProp<bool, int> cUseApp;
    AMSvr::AMSvrProp<std::string, int> cWhichApp;
    AMSvr::AMSvrProp<std::string, int> cTryAs;
    AMSvr::AMSvrProp<std::string, int> cAreas;
    AMSvr::AMSvrProp<int, int> dSize;
    AMSvr::AMSvrProp<std::string, int> cWishes;
    AMSvr::AMSvrProp<std::tm, int> created;

};


#endif //SAW_INSTALLREQUEST_H
