/* automatically generated file, do not edit. */

#ifndef CCATEGORIZATIONS_H
#define CCATEGORIZATIONS_H


#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CCategorizations: public AMSvr::AMSvrDbObject<int>
{
public:
    CCategorizations();
    CCategorizations(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> profession_;
    AMSvr::AMSvrProp<SAWEnum, int> factor;
    AMSvr::AMSvrProp<SAWEnum, int> category;
    AMSvr::AMSvrProp<SAWString, int> details;
    AMSvr::AMSvrProp<SAWString, int> duration;
    AMSvr::AMSvrProp<SAWString, int> measurement;
    AMSvr::AMSvrProp<SAWString, int> protocols;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CCategorizationsMap: public std::map<SAWEnum, CCategorizations>
{
public:
    bool loadCategorizations(AMSvr::AMSvrDB &db, pqxx::work &W, int profession);
};

#endif //CCATEGORIZATIONS_H