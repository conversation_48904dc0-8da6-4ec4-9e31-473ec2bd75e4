//
// Created by <PERSON><PERSON><PERSON> on 31.3.24.
//

#include "ConnectedAccountsPerformers.h"
#include "CConnectedAccounts.h"
#include <time.h>

int ConnectedAccountsPerformers::numberOfEmployees(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    int rv = -1;
    try {
        pqxx::result R(db.query("SELECT COUNT(*) FROM dbo.people t WHERE t.e_relationship = 1 OR t.e_relationship = 2 OR t.e_relationship = 3;", W));
        rv = R.begin().at(0).template as<int>();
    } catch (std::exception e) {

    }
    return rv;
}

void ConnectedAccountsPerformers::updatePrepayedPeriod(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, int currentEmployees, int newEmployees)
{
    CConnectedAccounts cc(sess.connectedAccountId);
    if (currentEmployees == newEmployees || currentEmployees < 0 || newEmployees < 0) {
        return;
    }
    if (cc.load(db, W)) {
        if (cc.validFrom.get() < cc.validUntil.get()) {
            std::time_t tFrom = mktime(&cc.validFrom.get());
            std::time_t tUntil = mktime(&cc.validUntil.get());
            //price of one sec
            long double dFrom = tFrom;
            long double dUntil = tUntil;
            long double dCurrentEmp = currentEmployees;
            long double dNewEmp = newEmployees;
            long double dAmount = (dUntil - dFrom) * dCurrentEmp;
            long double dNewUntil= dFrom + dAmount / dNewEmp;
            std::time_t newUntil = dNewUntil;
            cc.validUntil = *gmtime(&newUntil);
            cc.save(db, W);
        }
    }
}
