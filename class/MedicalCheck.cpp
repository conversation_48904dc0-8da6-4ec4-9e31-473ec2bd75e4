//
// Created by <PERSON><PERSON><PERSON> on 9.3.24.
//

#include "MedicalCheck.h"
#include "generated/medical_check_types.h"
#include "generated/examination_types.h"
#include "svr/AMSvrStatus.h"
#include "CPeople.h"
#include "CUsers.h"
#include "CPeopleData.h"
#include "COccupationalHazards.h"
#include "CRiskFactorsWorkingConditions.h"
#include "CLegalRequirementsForMedicalFitness.h"
#include "CExaminationsBigTable.h"
#include "CExaminations.h"
#include "CPersonalRisks.h"
#include "CMedicalChecks.h"
#include "CCategorizations.h"
#include "CWMSSpecifics2.h"
#include "generated/employer_relationships.h"
#include "generated/job_category.h"
#include "generated/med_concl_completeness.h"
#include "generated/med_check_states.h"
#include "amdialogs/AMUIParsers.h"
#include "svr/AMSvrHelpers.hpp"


#define LOG_ALGORITHM

#ifdef LOG_ALGORITHM
#define LOGA loga
#else
#define LOGA if (0) loga
#endif

#ifndef UNIT_TEST
#define GMTIME gmtime
#else
    extern struct tm *GMTIME(const time_t *);
#endif

inline std::string sdt(std::tm &t) {
    return std::to_string(t.tm_mday) + "." + std::to_string(t.tm_mon + 1) + "." +std::to_string(t.tm_year+1900);
}

void MedicalCheck::fillRecord(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response &res)
{
#ifdef LOG_ALGORITHM
    std::ostringstream loga;
#endif

    if (req.matches[1] != "medicalchecks") {
        sendError(res, AMSvr::CLIENT_ERROR, "Bad table for medicalChecks");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord bad table for medicalChecks");
        return;
    }
    int mcId = -1;
    std::string message = "";
    std::string sid;
    if (!req.has_param("medical_check")) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord noparam med_check");
        return;
    }
    sid = req.get_param_value("medical_check");
    try {
        mcId = std::stoi(sid);
    } catch (std::exception e) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord med_check is not number");
        return;
    }
    int perId = -1;
    if (!req.has_param("person")) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord noparam person");
        return;
    }
    sid = req.get_param_value("person");
    try {
        perId = std::stoi(sid);
    } catch (std::exception e) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord pasont is not number");
        return;
    }
    SAW_medical_check_types checkType;
    if (!req.has_param("check_type")) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord noparam chck_type");
        return;
    }
    sid = req.get_param_value("check_type");
    try {
        checkType = (SAW_medical_check_types)std::stoi(sid);
    } catch (std::exception e) {
        sendError(res, AMSvr::CLIENT_ERROR, "");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord checktype is not number");
        return;
    }
    CPeople per(perId);
    if (!per.load(db, W)) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "Person");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load person");
        return;
    }
    CPeopleData perData(perId);
    if (!perData.load(db, W)) {
        sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Personálie musí být vyplněny.");
        return;
    }
    std::string reason;
    std::string purpose;
    switch (checkType) {
        case mct_enter: reason = "§ 32 a § 103 odst. 1 písm. a) zákona č. 262/2006 Sb., Zákoníku práce, ve znění pozdějších předpisů; § 10, vyhlášky č. 79/2013 Sb., ve znění pozdějších předpisů";
                        purpose = "Zajištění, aby k výkonu práce v podmínkách s předpokládanou zdravotní náročností nebyla zařazena osoba ucházející se o zaměstnání, jejíž zdravotní způsobilost neodpovídá zařazení k předpokládané práci.";
                        break;
        case mct_output: reason = "§ 103 odst. 1 písm. a) zákona č. 262/2006 Sb., Zákoníku práce, ve znění pozdějších předpisů; § 13, vyhlášky č. 79/2013 Sb., ve znění pozdějších předpisů";
                        purpose = "Zjištění zdravotního stavu zaměstnance v době ukončení výkonu práce, a to s důrazem na zjištění takových změn zdravotního stavu, u kterých lze předpokládat souvislost se zdravotní náročností vykonávané práce.";
                        break;
        case mct_periodical: reason = "§ 103 odst. 1 písm. a) zákona č. 262/2006 Sb., Zákoníku práce, ve znění pozdějších předpisů; § 11, vyhlášky č. 79/2013 Sb., ve znění pozdějších předpisů";
                        purpose = "Zjištění včasné změny zdravotního stavu vzniklé v souvislosti se zdravotní náročností vykonávané práce nebo stárnutím organizmu, kdy další výkon práce by mohl vést k poškození zdraví posuzovaného zaměstnance, nebo k poškození zdraví jiných osob.";
                        break;
        case mct_following: reason = "§ 103 odst. 1 písm. a) zákona č. 262/2006 Sb., Zákoníku práce, ve znění pozdějších předpisů; § 14, vyhlášky č. 79/2013 Sb., ve znění pozdějších předpisů";
                        purpose = "Včasné zjištění změn zdravotního stavu vzniklých v souvislosti s prací za takových pracovních podmínek, jejichž důsledky se mohou projevit i po ukončení práce, a to za účelem včasného zajištění potřebných zdravotních služeb, popřípadě odškodnění.";
                        break;
        case mct_extraordinary: reason = "§ 103 odst. 1 písm. a) zákona č. 262/2006 Sb., Zákoníku práce, ve znění pozdějších předpisů; § 12, vyhlášky č. 79/2013 Sb., ve znění pozdějších předpisů";
                        purpose = "Zjištění zdravotního stavu posuzovaného zaměstnance v případě důvodného předpokladu, že došlo ke ztrátě nebo změně zdravotní způsobilosti k práci nebo pokud dojde ke zvýšení míry rizika již dříve zohledněného rizikového faktoru pracovních podmínek.";
                        break;
        default:
                        sendError(res, AMSvr::CLIENT_ERROR, "");
                        return;
    }
    CPeople usr(sess.userId);
    if (!usr.load(db, W)) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "User");
        return;
    }
    std::time_t now = time(0);
    struct AMCore::AMDatetimeus  actualDate;
    char modified_at[80];
    char items_datetime[80];
    actualDate = *GMTIME(&now);
    actualDate.usec = 0;
    strftime(modified_at, sizeof(modified_at), "%Y-%m-%d %H:%M:%S.000000", &actualDate);
    strftime(items_datetime, sizeof(items_datetime), "%Y-%m-%d %H:%M:%S", &actualDate);
    std::tm checkDate = actualDate;
    checkDate.tm_year = 137;
    mktime(&checkDate);
    //int additionMonths = 200 * 12;
    CUsers usrl(sess.userId);
    if (!usrl.load(db, W) || !usrl.valid.get()) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "User in a session");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load user");
        return;
    }
    COccupationalHazardsMap  occupHazards;
    if (!occupHazards.loadCOccupationalHazards(db, W, per.profession.get())) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "Occupational hazards");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load occupational hazards");
        return;
    }
    CRiskFactorsWorkingConditionsMap  rFWorkingConds;
    if (!rFWorkingConds.loadCRiskFactorsWorkingConditions(db, W, per.profession.get())) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "Working conds");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load fisk factor working conds");
        return;
    }
    CLegalRequirementsForMedicalFitnessMap  legalReqs;
    if (!legalReqs.loadCLegalRequirementsForMedicalFitness(db, W, per.profession.get())) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "Legal requirements");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load legar requirements");
        return;
    }
    CExaminationsBigTableMap bt;
    if (!bt.loadCExaminationsBigTable(db, W, occupHazards,rFWorkingConds,legalReqs, checkType)) {
        sendError(res, AMSvr::RECORD_NOT_FOUND, "Examinations big table");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load big table");
        return;
    }
    std::map<int, CExaminations> exs;
    std::string perName = per.cname();
    CExaminations exBase(-1);
    exBase.automatic = true;
    exBase.pass = false;
    exBase.person__ = per.id.get();
    exBase.personName.get() = perName;
    exBase.exaDate = AMCore::AMNullable<std::tm>();
    exBase.exaType = SAW_examination_types::exa_zakladni;
    exBase.modifiedBy = usrl.id.get();
    exBase.modifiedAt = actualDate;
    exBase.modifiedByName = usrl.name.get();
    exBase.medCheck = mcId;
    exs.insert({-1,exBase});

    LOGA << "Vytvářím Základní vyšetření\nPřidávám vyšetření z velké tabulky\n";
    int exId = -2;
    if (checkType == SAW_medical_check_types::mct_extraordinary) {
        CExaminations exBase(exId);
        exBase.automatic = true;
        exBase.pass = false;
        exBase.person__ = per.id.get();
        exBase.personName.get() = perName;
        exBase.exaDate = AMCore::AMNullable<std::tm>();
        exBase.exaType = SAW_examination_types::exa_indict_doctor;
        exBase.modifiedBy = usrl.id.get();
        exBase.modifiedAt = actualDate;
        exBase.modifiedByName = usrl.name.get();
        exBase.medCheck = mcId;
        exs.insert({exId,exBase});
        exId --;
    }
    for (auto it:bt) {
        CExaminations ex(exId);

        ex.automatic = true;
        ex.pass = false;
        ex.person__ = per.id.get();
        ex.personName.get() = perName;
        ex.occupHazard = it.second.occupHazard.get();
        ex.rFWorkingCond = it.second.rFWorkingCond.get();
        ex.legalReq = it.second.legalReq.get();
        ex.exaDate = AMCore::AMNullable<std::tm>();
        ex.exaType = it.second.examination;
        ex.modifiedBy = usrl.id.get();
        ex.modifiedAt = actualDate;
        ex.modifiedByName = usrl.name.get();
        ex.medCheck = mcId;
        ex.bigTableId = it.second.id.get();
        exs.insert({exId, ex});
        exId--;
    }
    CWMSSpecifics2 WMSSpecifics2(perId);
    bool WMSSpecifics2Loaded = WMSSpecifics2.load(db, W);
    std::map<int, std::tm> lastExaminations;
    std::map<int, std::tm> lastValidity;
    std::map<int, std::tm> lastExtraordinary;
    std::map<int, int> lastCheckType;
    std::map<int, int> examinationNotNeeded;
    std::vector<int> periodicChecksToRemove;
    if (checkType != SAW_medical_check_types::mct_enter && checkType != SAW_medical_check_types::mct_extraordinary) {
        LOGA << "Hledám poslední platnosti posudku a poslední platnosti v nich\n";
        std::string sql;
        if (checkType == SAW_medical_check_types::mct_following) {
            sql = "SELECT lmcs.exa_type, con.conclusion_date, con.conclusion_validity, mc2.check_type FROM "
                  "(SELECT exa_type ,MAX(mc.id) AS last_mc FROM dbo.examinations e JOIN dbo.medical_checks mc ON mc.id = e.med_check AND mc.person = $1 AND mc.check_type = 5 GROUP BY exa_type) lmcs "
                  "JOIN dbo.med_conclusions con ON con.id = lmcs.last_mc "
                  "JOIN dbo.medical_checks mc2 ON mc2.id = lmcs.last_mc";
        } else {
            sql = "SELECT lmcs.exa_type, con.conclusion_date, con.conclusion_validity, mc2.check_type FROM "
                    "(SELECT exa_type ,MAX(mc.id) AS last_mc FROM dbo.examinations e JOIN dbo.medical_checks mc ON mc.id = e.med_check AND mc.person = $1 AND (mc.check_type = 1 OR mc.check_type = 2) GROUP BY exa_type) lmcs "
                    "JOIN dbo.med_conclusions con ON con.id = lmcs.last_mc "
                    "JOIN dbo.medical_checks mc2 ON mc2.id = lmcs.last_mc";
        }
        try {
            pqxx::result R(db.query(sql, W, perId));
            for (auto it: R) {
                std::string ts = it.at(1).template as<std::string>();
                std::tm stm = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0LL, nullptr};
                strptime(ts.c_str(), "%Y-%m-%d", &stm);
                lastExaminations.insert({it.at(0).template as<int>(), stm});
                if (!it.at(2).is_null()) {
                    std::string ts = it.at(2).template as<std::string>();
                    std::tm stm = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0LL, nullptr};
                    strptime(ts.c_str(), "%Y-%m-%d", &stm);
                    lastValidity.insert({it.at(0).template as<int>(), stm});
                }
                lastCheckType.insert({it.at(0).template as<int>(), it.at(3).template as<int>()});
            }
        } catch (std::exception e) {
            res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable read conclusions validity");
            return;
        }
        if (checkType != SAW_medical_check_types::mct_following) {
            LOGA << "hledám poslední platnosti posudku mimořádné prohlídky s uplným rozsahem\n";
            sql = "SELECT t.exa_type, MAX(con.conclusion_date) FROM dbo.examinations t JOIN dbo.medical_checks mc ON mc.id = t.med_check LEFT JOIN dbo.med_conclusions con ON con.id = t.med_check WHERE con.conclusion_date IS NOT NULL AND mc.person = $1 "
                  " AND mc.check_type = 3 AND con.extraordinary_mc_range = true GROUP BY t.exa_type;";
            try {
                pqxx::result R(db.query(sql, W, perId));
                for (auto it: R) {
                    std::string ts = it.at(1).template as<std::string>();
                    std::tm stm = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0LL, nullptr};
                    strptime(ts.c_str(), "%Y-%m-%d", &stm);
                    lastExtraordinary.insert({it.at(0).template as<int>(), stm});
                }
            } catch (std::exception e) {
                res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
                AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable read conclusions validity 2");
                return;
            }
        }
        if (checkType == SAW_medical_check_types::mct_periodical) {
            sql = "SELECT t.id, t.examination  FROM dbe.examinations t "
                  "JOIN dbe.examinations u ON (u.occup_hazard = t.occup_hazard OR (u.occup_hazard IS NULL AND t.occup_hazard IS NULL)) AND (u.r_f_working_cond = t.r_f_working_cond  OR (u.r_f_working_cond IS NULL AND t.r_f_working_cond IS NULL)) AND (u.legal_req = t.legal_req  OR (u.legal_req IS NULL AND t.legal_req IS NULL)) "
                  " AND t.examination = u.examination AND t.check_type = 2 AND u.check_type = 1 "
                  "WHERE ";
        } else if (checkType == SAW_medical_check_types::mct_following) {
            sql = "SELECT t.id, t.examination  FROM dbe.examinations t "
                  "JOIN dbe.examinations u ON (u.occup_hazard = t.occup_hazard OR (u.occup_hazard IS NULL AND t.occup_hazard IS NULL)) AND (u.r_f_working_cond = t.r_f_working_cond  OR (u.r_f_working_cond IS NULL AND t.r_f_working_cond IS NULL)) AND (u.legal_req = t.legal_req  OR (u.legal_req IS NULL AND t.legal_req IS NULL)) "
                  " AND t.examination = u.examination AND t.check_type = 2 AND u.check_type = 4 "
                  "WHERE ";
        }
        if (checkType == SAW_medical_check_types::mct_following || checkType == SAW_medical_check_types::mct_periodical) {
            if (!occupHazards.empty() || !rFWorkingConds.empty() || !legalReqs.empty()) {
                std::ostringstream sqls;
                sqls << sql;
                bool first = true;
                for(auto &it:occupHazards) {
                    if (first) {
                        first = false;
                    } else {
                        sqls << " OR ";
                    }
                    sqls << "t.occup_hazard = " << it.first;
                }
                for(auto &it:rFWorkingConds) {
                    if (first) {
                        first = false;
                    } else {
                        sqls << " OR ";
                    }
                    sqls << "t.r_f_working_cond = " << it.first;
                }
                for(auto &it:legalReqs) {
                    if (first) {
                        first = false;
                    } else {
                        sqls << " OR ";
                    }
                    sqls << "t.legal_req = " << it.first;
                }
                try {
                    pqxx::result R(db.query(sqls.str(), W));
                    for (auto it = R.begin(); it != R.end(); it++) {
                        examinationNotNeeded.insert({it.at(0).template as<int>(), it.at(1).template as<int>()});
                    }
                } catch (const std::exception &e) {
                    res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
                    AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable read conclusions validity 5");
                    return;
                }
            }
        }
    } else {
        LOGA << "pokládám datum prohlídky na aktuální datum\n";
        checkDate = actualDate;
        std::string sql = "SELECT t.id FROM dbo.medical_checks t LEFT JOIN dbo.med_conclusions con ON con.id = t.id WHERE con.conclusion_date IS NULL AND t.person = $1 AND t.check_type = 2;";
        try {
            pqxx::result R(db.query(sql, W, perId));
            for (auto it: R) {
                periodicChecksToRemove.push_back(it.at(0).template as<int>());
            }
        } catch (std::exception e) {
            res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable read unclosed checks");
            return;
        }
    }
    CPersonalRisksMap perRisks;
    if (!perRisks.loadCPersonalRisks(db, W, perId)) {
        res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable read personal risks");
        return;
    }
    CCategorizationsMap catmap;
    catmap.loadCategorizations(db, W, per.profession.get());
    int catTotal = 0;
    for (auto it:catmap) {
        if (it.second.category.get() > catTotal) {
            catTotal = it.second.category.get();
        }
    }
    LOGA  << "Celková kategorie práce je " << catTotal << std::endl;

    //TODO testy
    if (per.eRelationship.get() == SAW_employer_relationships::emp_rls_student_HS || per.eRelationship.get() == SAW_employer_relationships::emp_rls_student_U) {
        if (checkType == SAW_medical_check_types::mct_periodical && catTotal <= SAW_job_category::ctg_2) {
            sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Periodickou prohlídku u žáka / studenta,\nkde je celková kategorie práce max 2\nse neprovádí");
            return;
        }
        if (checkType == SAW_medical_check_types::mct_output || checkType == SAW_medical_check_types::mct_following) {
            sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Tento typ prohlídky se u žáka / studenta\nneprovádí.");
            return;
        }
    }
    if (checkType == SAW_medical_check_types::mct_following) {
        if (!WMSSpecifics2Loaded || !WMSSpecifics2.mcReqFollowing.get()) {
            sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Následné prohlídky se neprovádí, pokud tak\nnestanoví orgán ochrany veřejného zdraví\n(krajská hygienická stanice).\n\n(Zaškrtávací políčko v záložce Specifika PLP II\nv agendě osoby)");
            return;
        }
    }
    int todayAge = yearsTo(perData.dateBirth.get(), actualDate);
    if (checkType == SAW_medical_check_types::mct_periodical) {
        if (! ( !WMSSpecifics2Loaded || (WMSSpecifics2Loaded && !WMSSpecifics2.mcReqEmployee.get() && !WMSSpecifics2.mcReqEmployer.get())
                && todayAge >= 18 && occupHazards.size() == 0 && rFWorkingConds.size() == 0 && legalReqs.size() == 0 && catTotal <= SAW_job_category::ctg_2)
            ) {
            if (!message.empty()) {
                message  += "\n\n";
            }
            message += "Periodická pracovnělékařská prohlídka\nnení pro tohoto zaměstnance povinná.\n\nVytvořenou prohlídku nemusíte uložit.";
            LOGA << "Nastavuji poznamku ze prohlidka není potřebná\n";
        }
    }
    if (per.eRelationship.get() != SAW_employer_relationships::emp_rls_employee
        &&  per.eRelationship.get() != SAW_employer_relationships::emp_rls_student_HS
        &&  per.eRelationship.get() != SAW_employer_relationships::emp_rls_student_U
        &&  per.eRelationship.get() != SAW_employer_relationships::emp_rls_temporary_worker_type_a
        &&  per.eRelationship.get() != SAW_employer_relationships::emp_rls_temporary_worker_type_b
        ) {
        sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Prohlídku pro tuto osobu neděláme.\n\n");
        return;
    }
    //bool mctEnterNeededExaExists = false;
    bool mctEnterNeededNewRisks = false;
    if (checkType == SAW_medical_check_types::mct_output) {
        CMedicalChecks enter;
        if (!enter.loadLastEnter(db, W, perId)) {
            sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Výstupní prohlídka není potřeba.\n\nNeexistuje prohlídka vstupní");
            return;
        }
    }
    if (checkType == SAW_medical_check_types::mct_periodical) {
        CMedicalChecks enter;
        if (!enter.loadLastEnter(db, W, perId)) {
            sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Periodickou prohlídku nelze udělat.\n\nNeexistuje prohlídka vstupní");
            return;
        }
    }
    AMCore::AMDatetimeus lastExtraordinaryDate;
    for (auto it:lastExtraordinary) {
        if (lastExtraordinaryDate < it.second) {
            lastExtraordinaryDate = it.second;
        }
    }
    LOGA << "Hlavní cyklus dnes je " << sdt(checkDate) << "   poslední mimořádná " << sdt(lastExtraordinaryDate) << std::endl;
    bool msgPeriodicBeforeExtraordinary = false;
    for (auto mcit = exs.begin(); mcit != exs.end();) {
        std::tm d = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};
        LOGA << "velkatabid=" << mcit->second.bigTableId
            << " exa_type=" << mcit->second.exaType.get()
            << " oh=" << (mcit->second.occupHazard.get().isNull() ? -1 : mcit->second.occupHazard.get().get())
            << " rf=" << (mcit->second.rFWorkingCond.get().isNull() ? -1 : mcit->second.rFWorkingCond.get().get())
            << " lr=" << (mcit->second.legalReq.get().isNull() ? -1 : mcit->second.legalReq.get().get())
            << " id=" << mcit->second.id.get() << ": ";
        auto leit = lastExaminations.find(mcit->second.exaType.get());
        bool zeroAddMon = false;
        bool max12Mon = false;
        int delay = 0;
        auto prit = perRisks.findByRiskActive(mcit->second.occupHazard.get(), mcit->second.rFWorkingCond.get(), mcit->second.legalReq.get());
        if (leit == lastExaminations.end()) {
            if (checkType == SAW_medical_check_types::mct_following) {
                auto pritin = perRisks.findByRiskInactive(mcit->second.occupHazard.get(), mcit->second.rFWorkingCond.get(), mcit->second.legalReq.get());
                if (pritin == perRisks.end()) {
                    mcit = exs.erase(mcit);
                    LOGA << "mažu, protože je následná a riziko ještě trvá\n";
                    if (mcit == exs.end()) {
                        break;
                    }
                    continue;
                }
            } else {

                if (prit == perRisks.end()) {
                    if (mcit->second.bigTableId == AMUI::DEFAULT_INT_ID) {
                        if (perData.onboardDate.get().isNull()) {
                            d = actualDate;
                            LOGA << "beru aktuální datum(" << sdt(d) << "), ";
                        } else {
                            d = perData.onboardDate.get().get();
                            LOGA << "beru nástupní datum(" << sdt(d) << "), ";
                        }
                    } else {
                        mcit = exs.erase(mcit);
                        LOGA << "mažu protože je nemá riziko v specifikách plp \n";
                        if (mcit == exs.end()) {
                            break;
                        }
                        continue;
                    }
                } else {
                    d = prit->second.appear;
                    LOGA << "beru datum začátku rizika(" << sdt(d) << "), ";
                }
                if ((per.eRelationship.get() == SAW_employer_relationships::emp_rls_student_HS || per.eRelationship.get() == SAW_employer_relationships::emp_rls_student_U)
                    && checkType == SAW_medical_check_types::mct_periodical && catTotal >= SAW_job_category::ctg_2R) {
                     max12Mon = true;
                    LOGA << "Žák/Student bude mít max periodu 12m,";
                }

            }
            auto itPreviousMCType = lastCheckType.find(mcit->second.exaType.get());
            auto itExaminationNotNeeded = examinationNotNeeded.find(mcit->second.bigTableId);
            int prevMCType = -1;
            if (itPreviousMCType == lastCheckType.end()) {
                prevMCType = SAW_medical_check_types::mct_enter;
                for (auto it:lastCheckType) {
                    if (it.second != SAW_medical_check_types::mct_enter) {
                        prevMCType = -1;
                        break;
                    }
                }
            } else {
                prevMCType = itPreviousMCType->second;
            }
            if (
                checkType == SAW_medical_check_types::mct_periodical
                && prevMCType == SAW_medical_check_types::mct_enter
                && itExaminationNotNeeded == examinationNotNeeded.end()
                ) {
                LOGA << "Bude perioda prestoze vystreni nebylo protoze vysetreni neni ve vstupni prohlidce\n";
            } else {
                zeroAddMon = true;
                LOGA << "Nebude perioda (na vyšetření ještě nebyl)\n";
            }
        } else {
            d = leit->second;
            LOGA << "beru datum posledního vyšetření (" << sdt(d) << ")\n";
        }


        //auto extraordinaryit = lastExtraordinary.find(mcit->second.exaType.get());
        //if (extraordinaryit != lastExtraordinary.end()) {
            if (d < lastExtraordinaryDate) {
                d = lastExtraordinaryDate;
                LOGA << "nastaveno datum na mimořádnou (" << sdt(d) << "), ";
            }
        //}
        for (auto it: perRisks) {
            if (!(it.second.appear.get() < d)) {
                mctEnterNeededNewRisks = true;
                LOGA << "umožním vstupní protože přibylo řiziko, ";
                break;
            }
        }

        LOGA << "            ";
        int age = yearsTo(perData.dateBirth.get(), d);

        CExaminationsBigTableMap::iterator btit2 = bt.find(mcit->second.bigTableId);
        if (mcit->second.bigTableId != AMUI::DEFAULT_INT_ID) {
            AMAssert(btit2 != bt.end());
            CExaminationsBigTable &btit = btit2->second;
            delay = btit.examinationDelay.get();
            if (delay) {
                LOGA << "přidávám delay (" << delay << "), ";
            }

            //zpracování výjimek
            switch (btit2->second.exceptionFn.get()) {
                case 1: {
                    if (WMSSpecifics2Loaded && WMSSpecifics2.hearingLoss.get()) {
                        delay = 0;
                        LOGA << "nedělám delay protože ztratil sluch, ";
                    }
                    break;
                }
                case 2: {
                    if (age >= 45) {
                        mcit = exs.erase(mcit);
                        LOGA << "mažu protože je starší 45 let, \n";
                        if (mcit == exs.end()) {
                            break;
                        }
                    }
                    break;
                }
                case 3: {
                    if (age < 45) {
                        mcit = exs.erase(mcit);
                        LOGA << "mažu protože je mladší 45 let, \n";
                        if (mcit == exs.end()) {
                            break;
                        }
                    }
                    break;
                }

                default:break;
            }
        }

        int addMon = -1;
        if (mcit->second.bigTableId == AMUI::DEFAULT_INT_ID) {
            if (age < 18) {
                addMon = 12;
            } else if (age < 50) {
                switch (catTotal) {
                    case SAW_job_category::ctg_1:addMon = 6*12;break;
                    case SAW_job_category::ctg_2:addMon = 4*12;break;
                    case SAW_job_category::ctg_2R:addMon = 2*12;break;
                    case SAW_job_category::ctg_3:addMon = 2*12;break;
                    case SAW_job_category::ctg_4:addMon = 12;break;
                }
            } else {
                switch (catTotal) {
                    case SAW_job_category::ctg_1:addMon = 4*12;break;
                    case SAW_job_category::ctg_2:addMon = 2*12;break;
                    case SAW_job_category::ctg_2R:addMon = 2*12;break;
                    case SAW_job_category::ctg_3:addMon = 2*12;break;
                    case SAW_job_category::ctg_4:addMon = 12;break;
                }
            }
            LOGA <<"Zákl. vyšetření perioda " << addMon << " (" << sdt(d) << "), ";
        } else {
            AMAssert(btit2 != bt.end());
            CExaminationsBigTable &btit = btit2->second;
            if (age < 50) {
                if (btit.validOver50Only) {
                    mcit = exs.erase(mcit);
                    if (mcit == exs.end()) {
                        break;
                    }
                    LOGA << "mazu kvuli valid over 50 only\n";
                    continue;
                }
                if (btit.examinationPeriodUnder50.get() > 0) {
                    addMon = btit.examinationPeriodUnder50.get();
                    LOGA << "<50 obecná perioda " << addMon << ", ";
                } else {
                    switch (catTotal) {
                        case SAW_job_category::ctg_1:addMon = btit.periodUnder50Ktg1.get();break;
                        case SAW_job_category::ctg_2:addMon = btit.periodUnder50Ktg2.get();break;
                        case SAW_job_category::ctg_2R:addMon = btit.periodUnder50Ktg2R.get();break;
                        case SAW_job_category::ctg_3:addMon = btit.periodUnder50Ktg3.get();break;
                        case SAW_job_category::ctg_4:addMon = btit.periodUnder50Ktg4.get();break;
                    }
                    LOGA << "<50 kategorie " << catTotal << " perioda " << addMon << ", ";
                }

            } else {
                if (btit.examinationPeriodOver50.get() > 0) {
                    addMon = btit.examinationPeriodOver50.get();
                    LOGA << ">50 obecná perioda " << addMon << ", ";
                } else {
                    switch (catTotal) {
                        case SAW_job_category::ctg_1:addMon = btit.periodOver50Ktg1.get();break;
                        case SAW_job_category::ctg_2:addMon = btit.periodOver50Ktg2.get();break;
                        case SAW_job_category::ctg_2R:addMon = btit.periodOver50Ktg2R.get();break;
                        case SAW_job_category::ctg_3:addMon = btit.periodOver50Ktg3.get();break;
                        case SAW_job_category::ctg_4:addMon = btit.periodOver50Ktg4.get();break;
                    }
                    LOGA << ">50 kategorie " << catTotal << " perioda " << addMon << ", ";
                }
            }

            if (addMon < 0) {
                mcit = exs.erase(mcit);
                LOGA << "mazu kvuli periodě < 0\n";
                if (mcit == exs.end()) {
                    break;
                }
                continue;
            }
        }



        if (!zeroAddMon) {
            if (max12Mon && addMon > 12) {
                addMon = 12;
                LOGA << "zařezávám periodu na 12měs, ";
            }
            if (addMon > 0) {
                d.tm_mon += addMon;
                mktime(&d);
                LOGA << "aplikuji periodu " << addMon << " na (" << sdt(d) << "), ";
            } else {
                if (delay <= 0) {
                    d = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0LL, nullptr};
                    LOGA << "datum nulováno, ";
                }
            }
        }
        if (delay > 0 && prit != perRisks.end()) {
            std::tm t = prit->second.appear;
            t.tm_mon += delay;
            mktime(&t);

            std::tm t2 = prit->second.appear;
            t2.tm_mon += delay >= 3 ? delay - 3 : 0;
            mktime(&t2);

            LOGA << "aplikuji delay " << delay << " na (" << sdt(t) << "), ";
            /*
            if (d < t) {
                d = t;
                LOGA << "beru delayed, ";
            } else {
                LOGA << "nechavam spočtené, ";
            }*/
            if (leit != lastExaminations.end() && t2 < leit->second) {
                LOGA << "nechávám (po delay už bylo vyšetření), ";
            } else {

                //if (t < d) {
                    d = t;
                    LOGA << "beru delayed, ";
                //} else {
                //    LOGA << "nechavam spočtené, ";
                //}
            }
        }

        /*
        if (extraordinaryit != lastExtraordinary.end()) {
            if (d < extraordinaryit->second) {
                msgPeriodicBeforeExtraordinary = true;
                LOGA << "bude msg že je per.pr před mimořádnou, ";
            }
        }*/

        if (!isDateZero(d)) {
            auto validityit = lastValidity.find(mcit->second.exaType.get());
            if (validityit != lastValidity.end()) {
                d = d < validityit->second ? d : validityit->second;
                LOGA << "min. platnost posudku a data (" << sdt(d) << "), ";
            }
            d.tm_hour = 0;
            d.tm_min = 0;
            d.tm_sec = 0;
            d.tm_isdst = 0;
            d.tm_gmtoff = 0;
            mcit->second.plannedDate = d;
            LOGA << "naplánované datum vyšetření (" << sdt(d) << "), ";
            if (d < checkDate) {
                checkDate = d;
            }
            LOGA << "datum prohlídky (" << sdt(checkDate) << "), ";
            if (!(actualDate < checkDate)) {
                checkDate = actualDate;
                LOGA << "datum prohlidky nastaveno na dnešek (" << sdt(checkDate) << "), ";
            }
        } else {
            d.tm_hour = 0;
            d.tm_min = 0;
            d.tm_sec = 0;
            d.tm_isdst = 0;
            d.tm_gmtoff = 0;
            mcit->second.plannedDate = d;
            LOGA << "naplánované datum vyšetřeníX (" << sdt(d) << "), ";
        }
        LOGA << std::endl;
        mcit++;
    }

    LOGA << "\n\nZbylo " << exs.size() <<" vyšetření na (" << sdt(checkDate) << ")\n";

    if (checkType == SAW_medical_check_types::mct_extraordinary
        || checkType == SAW_medical_check_types::mct_enter
        || checkType == SAW_medical_check_types::mct_output) {
        checkDate = actualDate;
        LOGA << "datum prohlidky nastaveno na dnešek\n";

        if (checkType == SAW_medical_check_types::mct_enter) {
            CMedicalChecks enter;
            bool found = false;
            if (enter.loadLastEnter(db, W, perId) && !mctEnterNeededNewRisks) {
                sendError(res, AMSvr::ASSIGNMENT_PROBLEM, "Vstupní prohlídka není potřeba.\n\nVstupní prohlídku dáváme po nástupu\ndo zaměstnání nebo změně\nProfesních rizik, rizik\n pracovních podmínek nebo požadavků\nprávních předpisů");
                return;
            }
        }
    }
    /*
    if (msgPeriodicBeforeExtraordinary) {
        if (!message.empty()) {
            message += "\n\n";
        }
        message += "Před termínem provedení mimořádné prohlídky\n"
                  "je naplánovaná řádná periodická prohlídka.\n"
                  "Mimořádnou prohlídku tedy není nutné provádět.\n\n"
                  "Konzultujte prosím tuto skutečnost s\n"
                  "poskytovatelem pracovnělékařských služeb.";
    }*/

    if (periodicChecksToRemove.size() > 0) {
        if (!message.empty()) {
            message += "\n\n";
        }
        message += "V SAWAPP jsou naplánované periodické prohlídky,\nkteré je potřeba naplánovat znovu. Budete chtít\nty staré vymazat?";
    }

    std::set<int> setExaTypes;
    for (auto mcit = exs.begin(); mcit != exs.end(); mcit++) {
        if (setExaTypes.find(mcit->second.exaType) != setExaTypes.end()) {
            continue;
        }
        std::tm minDate = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};
        minDate.tm_year = 151;
        //int eeee = mcit->second.exaType;
        for (auto mcit2 = mcit; mcit2 != exs.end(); mcit2++) {
            if (mcit2->second.exaType != mcit->second.exaType) {
                continue;
            }
            if (mcit2->second.plannedDate < minDate) {
                //std::tm ttt = mcit2->second.plannedDate;
                minDate = mcit2->second.plannedDate;
            }
        }
        if (isDateZero(minDate)) {
            minDate = checkDate;
        }
        for (auto mcit2 = mcit; mcit2 != exs.end(); mcit2++) {
            if (mcit2->second.exaType != mcit->second.exaType) {
                continue;
            }
            mcit2->second.plannedDate = minDate;
        }
        setExaTypes.insert(mcit->second.exaType);
    }

    std::tm borderDate = checkDate;
    borderDate.tm_mon += 3;
    mktime(&borderDate);
    for (auto mcit = exs.begin(); mcit != exs.end(); ) {
        if (borderDate < mcit->second.plannedDate) {
            mcit = exs.erase(mcit);
            if (mcit == exs.end()) {
                break;
            }
        } else {
            mcit++;
        }

    }

    if (mcId > 0) {
        CExaminationsMap em;
        if (!em.loadCExaminationsManualOnly(db, W, mcId)) {
            res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "MedicalCheck::fillRecord unable load examinations dbos");
            return;
        }
        for (auto it: em) {
            exs.insert({it.first, it.second});
        }
    }

    std::string medicalInfo;
    std::vector<CExaminations> exs2;
    for(auto it:exs) {
        exs2.push_back(it.second);
        CExaminationsBigTableMap::iterator btit = bt.find(it.second.bigTableId);
        if (btit != bt.end()) {
            if (!btit->second.medicalInfo.get().empty()) {
                if (!medicalInfo.empty()) {
                    medicalInfo += "\n\n";
                }
                medicalInfo += btit->second.medicalInfo.get();
            }
        }
    }


    char checkDatetime[80];
    actualDate = *GMTIME(&now);
    strftime(checkDatetime, sizeof(checkDatetime), "%Y-%m-%d", &checkDate);

    nlohmann::ordered_json j;
    j["status"] = AMSvr::OK;
    j["dbMessage"] = message;
#ifdef LOG_ALGORITHM
    j["log"] = loga.str();
    //printf("=============================\n%s\n===============================\n", loga.str().c_str());
#else
    j["log"] = "";
#endif
    j["periodical_checks_to_remove"] = periodicChecksToRemove;
    j["record"]["id"] = mcId;
    j["record"]["person"] = perId;
    j["record"]["person_name"] = (std::string)per.cname();
    j["record"]["provider"] = 4;
    j["record"]["check_type"] = checkType;
    j["record"]["concurence"] = false;
    j["record"]["reason"] = reason;
    j["record"]["purpose"] = purpose;
    j["record"]["order_date"] = checkDatetime;
    j["record"]["perform_date"] = nullptr;
    j["record"]["privileged_person"] = perId;
    j["record"]["privileged_person_name"] =  (std::string)per.cname();
    j["record"]["applied_person"] = sess.userId;
    j["record"]["applied_person_name"] = (std::string)usr.cname();
    j["record"]["medical_info"] = medicalInfo;
    j["record"]["sent"] = false;
    j["record"]["status"] = 2;
    j["record"]["note"] = "";
    j["record"]["modified_at"] = modified_at;
    j["record"]["modified_by_name"] = (std::string )usrl.name.get();
    j["items"]["status"] = 0;
    j["items"]["datetime"] = items_datetime;
    j["items"]["total"] = exs2.size();
    j["items"]["fieldsCount"] = 15;
    j["items"]["fields"] = nlohmann::json::array({"id","person__","person_name","med_check","exa_type","occup_hazard","r_f_working_cond","legal_req","automatic","pass","exa_date","note","modified_by","modified_at","modified_by_name"});
    j["items"]["dataCount"] = exs2.size();
    j["items"]["data"] = exs2;
    res.set_content(j.dump(), "application/json; charset=utf-8");
    return;
}

void MedicalCheck::updateStatus(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response &res, std::string &body)
{
    int status = -1;
    int newStatus = -1;
    int id = -1;
    nlohmann::ordered_json j = nlohmann::ordered_json::parse(body.empty() ? res.body.c_str() : body.c_str(), body.empty() ? res.body.c_str() + res.body.size() : body.c_str() + body.size());
    //printf("-------------------------------------\n%s\n------------------------------\n", body.c_str());
    try {
        if (j["status"].get<int>() != 0) {
            return;
        }
        id = j["record"]["id"].get<int>();
        status = j["record"]["status"].get<int>();
        newStatus = -1;
        if (j["record"]["perform_date"].is_null()) {
            std::string sOrderedDate = j["record"]["order_date"].get<std::string>();
            std::time_t now = time(0);
            struct AMCore::AMDatetimeus  orderedDate;
            struct AMCore::AMDatetimeus  actualDate;
            actualDate = *GMTIME(&now);
            actualDate.tm_hour = 0;
            actualDate.tm_min = 0;
            actualDate.tm_sec = 0;
            actualDate.usec = 0;
            struct AMCore::AMDatetimeus  actualDate90 = actualDate;
            actualDate90.tm_mday += 90;
            mktime(&actualDate90);
            strptime(sOrderedDate.c_str(), "%Y-%m-%d", &orderedDate);
            orderedDate.tm_hour = 0;
            orderedDate.tm_min = 0;
            orderedDate.tm_sec = 0;
            orderedDate.usec = 0;

            if (actualDate90 < orderedDate) {
                newStatus = SAW_med_check_states::med_check_planned;
            } else if (orderedDate < actualDate) {
                newStatus = SAW_med_check_states::med_check_behind;
            } else {
                bool sent = j["record"]["sent"].get<bool>();
                if (sent) {
                    newStatus = SAW_med_check_states::med_check_sent;
                } else {
                    newStatus = SAW_med_check_states::med_check_near;
                }
            }
        } else {
            int completeness = j["record"]["completeness"].get<int>();
            switch (completeness) {
                case SAW_med_concl_completeness::med_conc_comp_complete: newStatus = SAW_med_check_states::med_check_complete;break;
                case SAW_med_concl_completeness::med_conc_comp_apply_review: newStatus = SAW_med_check_states::med_check_review;break;
                case SAW_med_concl_completeness::med_conc_comp_wt_results: newStatus = SAW_med_check_states::med_check_wait;break;
            }
        };
    } catch(std::exception e) {
        res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "MedicalCheck::updateStatus exception");
        return;
    }

    if (newStatus != status) {
        try {
            db.query("UPDATE dbo.medical_checks SET status = $1 WHERE id = $2", W, newStatus, id);
        } catch(std::exception e) {
            res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "MedicalCheck::updateStatus exception 2 ");
            return;
        }
        if (body.empty()) {
            j["record"]["status"] = newStatus;
            res.set_content(j.dump(), "application/json; charset=utf-8");
        }
    }
}
