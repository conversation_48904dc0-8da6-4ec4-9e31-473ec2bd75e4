/* automatically generated file, do not edit. */

#ifndef CLEGALREQUIREMENTSFORMEDICALFITNESSENUM_H
#define CLEGALREQUIREMENTSFORMEDICALFITNESSENUM_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CLegalRequirementsForMedicalFitnessEnum: public AMSvr::AMSvrDbObject<int>
{
public:
    CLegalRequirementsForMedicalFitnessEnum();
    CLegalRequirementsForMedicalFitnessEnum(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> title;
    //AMSvr::AMSvrProp<SAWString, int> color;
    AMSvr::AMSvrProp<SAWString, int> description;
    //AMSvr::AMSvrProp<SAWString, int> titledesc;
    //AMSvr::AMSvrProp<int, int> ord;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CLegalRequirementsForMedicalFitnessEnumMap: public std::map<int, CLegalRequirementsForMedicalFitnessEnum>
{
public:
    CLegalRequirementsForMedicalFitnessEnumMap();
    bool loadCLegalRequirementsForMedicalFitnessEnum(AMSvr::AMSvrDB &db, pqxx::work &W);
    bool loaded;
};
#endif //CLEGALREQUIREMENTSFORMEDICALFITNESSENUM_H