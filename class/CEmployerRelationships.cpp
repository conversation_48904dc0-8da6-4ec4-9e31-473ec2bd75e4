/* automatically generated file, do not edit. */

#include "CEmployerRelationships.h"  

CEmployerRelationships::CEmployerRelationships(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      full_name()
    {           
    };
    
CEmployerRelationships::CEmployerRelationships()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      full_name()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CEmployerRelationships::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CEmployerRelationships>(
    "dbe.employer_relationships",
    {  
        {&CEmployerRelationships::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CEmployerRelationships::title, "title", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployerRelationships::full_name, "full_name", AMCore::AMDataType_string, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CEmployerRelationships::context()
{
    return *g_context;
}

bool CEmployerRelationships::loadByTitle(SAWString title, AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    loadSelect(sql, context().tableName);
    sql << " WHERE title = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, (std::string)title));
        if (R.begin() == R.end()) {
            return false;
        }
        loadFromData(R.begin());
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}

bool CEmployerRelationships::loadByFullName(SAWString full_name, AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    loadSelect(sql, context().tableName);
    sql << " WHERE full_name = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, (std::string)full_name));
        if (R.begin() == R.end()) {
            return false;
        }
        loadFromData(R.begin());
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}