/* automatically generated file, do not edit. */

#include "CEmployers.h"  

CEmployers::CEmployers(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      ico(),
      name(),
      address(),
      accidentInsurance(),
      datovka(),
      email(),
      phone(),
      web(),
      registration(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };
    
CEmployers::CEmployers()
    : AMSvr::AMSvrDbObject<int>(),
      ico(),
      name(),
      address(),
      accidentInsurance(),
      datovka(),
      email(),
      phone(),
      web(),
      registration(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CEmployers::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CEmployers>(
    "dbo.employers",
    {  
        {&CEmployers::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CEmployers::ico, "ico", AMCore::AMDataType_natural, {}, nullptr},
        {&CEmployers::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::address, "address", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::accidentInsurance, "accident_insurance", AMCore::AMDataType_enum, {}, nullptr},
        {&CEmployers::datovka, "datovka", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::email, "email", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::phone, "phone", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::web, "web", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::registration, "registration", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployers::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CEmployers::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CEmployers::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CEmployers::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CEmployers::context()
{
    return *g_context;
}
