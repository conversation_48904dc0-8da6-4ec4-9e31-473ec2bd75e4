/* automatically generated file, do not edit. */

#include "COccupationalHazardsEnum.h"  

COccupationalHazardsEnum::COccupationalHazardsEnum(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      //color(),
      description()//,
      //titledesc(),
      //ord()
    {           
    };
    
COccupationalHazardsEnum::COccupationalHazardsEnum()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      //color(),
      description()//,
      //titledesc(),
      //ord()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *COccupationalHazardsEnum::g_context = AMSvr::AMSvrDbObjectContext<int>::create<COccupationalHazardsEnum>(
    "dbe.occupational_hazards",
    {  
        {&COccupationalHazardsEnum::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&COccupationalHazardsEnum::title, "title", AMCore::AMDataType_string, {}, nullptr},
        //{&COccupationalHazardsEnum::color, "color", AMCore::AMDataType_string, {}, nullptr},
        {&COccupationalHazardsEnum::description, "description", AMCore::AMDataType_string, {}, nullptr},
        //{&COccupationalHazardsEnum::titledesc, "titledesc", AMCore::AMDataType_string, {}, "t.title || ' ' || t.description AS titledesc"},
        //{&COccupationalHazardsEnum::ord, "ord", AMCore::AMDataType_integer, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &COccupationalHazardsEnum::context()
{
    return *g_context;
}

COccupationalHazardsEnumMap::COccupationalHazardsEnumMap()
    :std::map<int, COccupationalHazardsEnum>(),
    loaded(false)
{
}


bool COccupationalHazardsEnumMap::loadCOccupationalHazardsEnum(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    COccupationalHazardsEnum::loadSelect(sql, "dbe.occupational_hazards");
    try {
        pqxx::result R(db.query(sql.str(), W));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            COccupationalHazardsEnum oh;
            oh.loadFromData(it);
            insert({oh.id, oh});
        }
        loaded = true;
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}