/* automatically generated file, do not edit. */

#include "CCategorizations.h"  

CCategorizations::CCategorizations(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      profession(),
      profession_(),
      factor(),
      category(),
      details(),
      duration(),
      measurement(),
      protocols(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CCategorizations::CCategorizations()
    : AMSvr::AMSvrDbObject<int>(),
      profession(),
      profession_(),
      factor(),
      category(),
      details(),
      duration(),
      measurement(),
      protocols(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CCategorizations::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CCategorizations>(
    "dbo.categorizations",
    {  
        {&CCategorizations::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategorizations::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategorizations::profession_, "profession_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_"},
        {&CCategorizations::factor, "factor", AMCore::AMDataType_enum, {}, nullptr},
        {&CCategorizations::category, "category", AMCore::AMDataType_enum, {}, nullptr},
        {&CCategorizations::details, "details", AMCore::AMDataType_string, {}, nullptr},
        {&CCategorizations::duration, "duration", AMCore::AMDataType_string, {}, nullptr},
        {&CCategorizations::measurement, "measurement", AMCore::AMDataType_string, {}, nullptr},
        {&CCategorizations::protocols, "protocols", AMCore::AMDataType_string, {}, nullptr},
        {&CCategorizations::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CCategorizations::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategorizations::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CCategorizations::context()
{
    return *g_context;
}

bool CCategorizationsMap::loadCategorizations(AMSvr::AMSvrDB &db, pqxx::work &W, int profession)
{
    std::ostringstream sql;
    CCategorizations::loadSelect(sql, "dbo.categorizations");
    sql << " WHERE profession = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, profession));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CCategorizations cat;
            cat.loadFromData(it);
            insert({cat.factor, cat});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}