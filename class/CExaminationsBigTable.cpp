/* automatically generated file, do not edit. */

#include "CExaminationsBigTable.h"  

CExaminationsBigTable::CExaminationsBigTable(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      checkType(),
      examination(),
      periodUnder50Ktg1(),
      periodUnder50Ktg2(),
      periodUnder50Ktg2R(),
      periodUnder50Ktg3(),
      periodUnder50Ktg4(),
      periodOver50Ktg1(),
      periodOver50Ktg2(),
      periodOver50Ktg2R(),
      periodOver50Ktg3(),
      periodOver50Ktg4(),
      examinationDelay(),
      examinationPeriodUnder50(),
      examinationPeriodOver50(),
      validOver50Only(),
      exceptionFn(),
      medicalInfo()
    {           
    };
    
CExaminationsBigTable::CExaminationsBigTable()
    : AMSvr::AMSvrDbObject<int>(),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      checkType(),
      examination(),
      periodUnder50Ktg1(),
      periodUnder50Ktg2(),
      periodUnder50Ktg2R(),
      periodUnder50Ktg3(),
      periodUnder50Ktg4(),
      periodOver50Ktg1(),
      periodOver50Ktg2(),
      periodOver50Ktg2R(),
      periodOver50Ktg3(),
      periodOver50Ktg4(),
      examinationDelay(),
      examinationPeriodUnder50(),
      examinationPeriodOver50(),
      validOver50Only(),
      exceptionFn(),
      medicalInfo()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CExaminationsBigTable::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CExaminationsBigTable>(
    "dbe.examinations",
    {  
        {&CExaminationsBigTable::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::occupHazard, "occup_hazard", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminationsBigTable::rFWorkingCond, "r_f_working_cond", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminationsBigTable::legalReq, "legal_req", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminationsBigTable::checkType, "check_type", AMCore::AMDataType_enum, {}, nullptr},
        {&CExaminationsBigTable::examination, "examination", AMCore::AMDataType_enum, {}, nullptr},
        {&CExaminationsBigTable::periodUnder50Ktg1, "period_under_50_ktg1", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodUnder50Ktg2, "period_under_50_ktg2", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodUnder50Ktg2R, "period_under_50_ktg2R", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodUnder50Ktg3, "period_under_50_ktg3", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodUnder50Ktg4, "period_under_50_ktg4", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodOver50Ktg1, "period_over_50_ktg1", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodOver50Ktg2, "period_over_50_ktg2", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodOver50Ktg2R, "period_over_50_ktg2R", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodOver50Ktg3, "period_over_50_ktg3", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::periodOver50Ktg4, "period_over_50_ktg4", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::examinationDelay, "examination_delay", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::examinationPeriodUnder50, "examination_period_under50", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::examinationPeriodOver50, "examination_period_over50", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::validOver50Only, "valid_over_50_only", AMCore::AMDataType_bool, {}, nullptr},
        {&CExaminationsBigTable::exceptionFn, "exception_fn", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationsBigTable::medicalInfo, "medical_info", AMCore::AMDataType_string, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CExaminationsBigTable::context()
{
    return *g_context;
}

bool CExaminationsBigTableMap::loadCExaminationsBigTable(
    AMSvr::AMSvrDB &db,
    pqxx::work &W,
    COccupationalHazardsMap &occupHazards,
    CRiskFactorsWorkingConditionsMap &rFWorkingConds,
    CLegalRequirementsForMedicalFitnessMap &legalReqs,
    SAWEnum medicalCheckType
)
{
    clear();
    if (occupHazards.empty() && rFWorkingConds.empty() && legalReqs.empty()) {
        return true;
    }
    std::ostringstream sql;
    CLegalRequirementsForMedicalFitness::loadSelect(sql, "dbe.examinations");
    sql << " WHERE check_type = $1 AND (";
    bool first = true;
    for(auto &it:occupHazards) {
        if (first) {
            first = false;
        } else {
            sql << " OR ";
        }
        sql << "occup_hazard = " << it.first;
    }
    for(auto &it:rFWorkingConds) {
        if (first) {
            first = false;
        } else {
            sql << " OR ";
        }
        sql << "r_f_working_cond = " << it.first;
    }
    for(auto &it:legalReqs) {
        if (first) {
            first = false;
        } else {
            sql << " OR ";
        }
        sql << "legal_req = " << it.first;
    }
    sql << ");";
    try {
        pqxx::result R(db.query(sql.str(), W, medicalCheckType));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CExaminationsBigTable btr;
            btr.loadFromData(it);
            insert({btr.id, btr});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}