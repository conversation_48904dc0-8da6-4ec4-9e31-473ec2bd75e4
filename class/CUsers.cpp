/* automatically generated file, do not edit. */

#include "CUsers.h"  

CUsers::CUsers(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      name(),
      roles(),
      valid(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CUsers::CUsers()
    : AMSvr::AMSvrDbObject<int>(),
      name(),
      roles(),
      valid(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CUsers::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CUsers>(
    "dbo.users",
    {  
        {&CUsers::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CUsers::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CUsers::roles, "roles", AMCore::AMDataType_enum_map, {}, nullptr},
        {&CUsers::valid, "valid", AMCore::AMDataType_bool, {}, nullptr},
        {&CUsers::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CUsers::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CUsers::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CUsers::context()
{
    return *g_context;
}
