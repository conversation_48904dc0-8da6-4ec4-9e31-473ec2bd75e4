/* automatically generated file, do not edit. */

#ifndef CEXAMINATIONSBIGTABLE_H
#define CEXAMINATIONSBIGTABLE_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"
#include "COccupationalHazards.h"
#include "CRiskFactorsWorkingConditions.h"
#include "CLegalRequirementsForMedicalFitness.h"

class CExaminationsBigTable: public AMSvr::AMSvrDbObject<int>
{
public:
    CExaminationsBigTable();
    CExaminationsBigTable(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> occupHazard;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> rFWorkingCond;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> legalReq;
    AMSvr::AMSvrProp<SAWEnum, int> checkType;
    AMSvr::AMSvrProp<SAWEnum, int> examination;
    AMSvr::AMSvrProp<int, int> periodUnder50Ktg1;
    AMSvr::AMSvrProp<int, int> periodUnder50Ktg2;
    AMSvr::AMSvrProp<int, int> periodUnder50Ktg2R;
    AMSvr::AMSvrProp<int, int> periodUnder50Ktg3;
    AMSvr::AMSvrProp<int, int> periodUnder50Ktg4;
    AMSvr::AMSvrProp<int, int> periodOver50Ktg1;
    AMSvr::AMSvrProp<int, int> periodOver50Ktg2;
    AMSvr::AMSvrProp<int, int> periodOver50Ktg2R;
    AMSvr::AMSvrProp<int, int> periodOver50Ktg3;
    AMSvr::AMSvrProp<int, int> periodOver50Ktg4;
    AMSvr::AMSvrProp<int, int> examinationDelay;
    AMSvr::AMSvrProp<int, int> examinationPeriodUnder50;
    AMSvr::AMSvrProp<int, int> examinationPeriodOver50;
    AMSvr::AMSvrProp<bool, int> validOver50Only;
    AMSvr::AMSvrProp<int, int> exceptionFn;
    AMSvr::AMSvrProp<SAWString, int> medicalInfo;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CExaminationsBigTableMap: public std::map<SAWEnum, CExaminationsBigTable>
{
public:
    bool loadCExaminationsBigTable(
        AMSvr::AMSvrDB &db,
        pqxx::work &W,
        COccupationalHazardsMap &occupHazards,
        CRiskFactorsWorkingConditionsMap &rFWorkingConds,
        CLegalRequirementsForMedicalFitnessMap &legalReqs,
        SAWEnum medicalCheckType
        );
};

#endif //CEXAMINATIONSBIGTABLE_H
