/* automatically generated file, do not edit. */

#include "CProvidersWMS.h"  

CProvidersWMS::CProvidersWMS(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      name(),
      ico(),
      address(),
      contractSign(),
      contractLink(),
      inspections(),
      email(),
      phone(),
      addressOffice(),
      web(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CProvidersWMS::CProvidersWMS()
    : AMSvr::AMSvrDbObject<int>(),
      name(),
      ico(),
      address(),
      contractSign(),
      contractLink(),
      inspections(),
      email(),
      phone(),
      addressOffice(),
      web(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CProvidersWMS::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CProvidersWMS>(
    "dbo.providers_wms",
    {
        {&CProvidersWMS::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CProvidersWMS::name, "name", AMCore::AMDataType_string, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.name AS name"},
        {&CProvidersWMS::ico, "ico", AMCore::AMDataType_natural, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.ico AS ico"},
        {&CProvidersWMS::address, "address", AMCore::AMDataType_address, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.address AS address"},
        {&CProvidersWMS::contractSign, "contract_sign", AMCore::AMDataType_bool, {}, nullptr},
        {&CProvidersWMS::contractLink, "contract_link", AMCore::AMDataType_string, {}, nullptr},
        {&CProvidersWMS::inspections, "inspections", AMCore::AMDataType_bool, {}, nullptr},
        {&CProvidersWMS::email, "email", AMCore::AMDataType_string, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.email AS email"},
        {&CProvidersWMS::phone, "phone", AMCore::AMDataType_string, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.phone AS phone"},
        {&CProvidersWMS::addressOffice, "address_office", AMCore::AMDataType_address, {}, nullptr},
        {&CProvidersWMS::web, "web", AMCore::AMDataType_string, {"LEFT JOIN dbo.employers emp ON emp.id = t.id"}, "emp.web AS web"},
        {&CProvidersWMS::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CProvidersWMS::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CProvidersWMS::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CProvidersWMS::context()
{
    return *g_context;
}
