//
// Created by <PERSON><PERSON><PERSON> on 24.10.23.
//

#ifndef SAW_LOMAINTENANCE_H
#define SAW_LOMAINTENANCE_H

#include "class/LongOpItem.h"
#include "generated/tables.h"

class LOMaintenance: public LongOpItem {

public:
    LOMaintenance();
    void run() override;
    void runPriv(AMSvr::AMSvrDB &db, pqxx::work &W);

    static void initThread();
    static void *threadLoop(void*);
protected:
    static pthread_t g_timerTid;
    static std::tm g_lastTime;
};


#endif //SAW_LOMAINTENANCE_H
