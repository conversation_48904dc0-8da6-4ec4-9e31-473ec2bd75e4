/* automatically generated file, do not edit. */


#ifndef CEMPLOYERRELATIONSHIPS_H
#define CEMPLOYERRELATIONSHIPS_H

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CEmployerRelationships: public AMSvr::AMSvrDbObject<int>
{
public:
    CEmployerRelationships();
    CEmployerRelationships(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;

    bool loadByTitle(SAWString title, AMSvr::AMSvrDB &db, pqxx::work &W);
    bool loadByFullName(SAWString full_name, AMSvr::AMSvrDB &db, pqxx::work &W);
    AMSvr::AMSvrProp<SAWString, int> title;
    AMSvr::AMSvrProp<SAWString, int> full_name;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CEMPLOYERRELATIONSHIPS_H