/* automatically generated file, do not edit. */


#ifndef CPEOPLEDATA_H
#define CPEOPLEDATA_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CPeopleData: public AMSvr::AMSvrDbObject<int>
{
public:
    CPeopleData();
    CPeopleData(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWEnum, int> citizenship;
    AMSvr::AMSvrProp<SAWString, int> permanentAddress;
    AMSvr::AMSvrProp<SAWString, int> postAddress;
    AMSvr::AMSvrProp<std::tm, int> dateBirth;
    AMSvr::AMSvrProp<SAWString, int> identityNumber;
    AMSvr::AMSvrProp<SAWString, int> identityCard;
    AMSvr::AMSvrProp<SAWEnum, int> insurance;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> onboardDate;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> leaveDate;
    AMSvr::AMSvrProp<std::map<SAWEnum, int>, int> drivingLicences;
    AMSvr::AMSvrProp<SAWEnum, int> degreeBefore;
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<SAWString, int> surname;
    AMSvr::AMSvrProp<SAWEnum, int> degreeAfter;
    //AMSvr::AMSvrProp<AMCore::AMNameBase < SAWEnum, SAWString >, int> fullName;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPEOPLEDATA_H
