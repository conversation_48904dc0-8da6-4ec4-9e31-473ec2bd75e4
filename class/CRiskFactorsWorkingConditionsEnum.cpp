/* automatically generated file, do not edit. */

#include "CRiskFactorsWorkingConditionsEnum.h"  

CRiskFactorsWorkingConditionsEnum::CRiskFactorsWorkingConditionsEnum(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      isHeadline(),
      //color(),
      description()//,
      //ord()
    {           
    };
    
CRiskFactorsWorkingConditionsEnum::CRiskFactorsWorkingConditionsEnum()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      isHeadline(),
      //color(),
      description()//,
      //ord()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CRiskFactorsWorkingConditionsEnum::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CRiskFactorsWorkingConditionsEnum>(
    "dbe.risk_factors_working_conds",
    {  
        {&CRiskFactorsWorkingConditionsEnum::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CRiskFactorsWorkingConditionsEnum::title, "title", AMCore::AMDataType_string, {}, nullptr},
        {&CRiskFactorsWorkingConditionsEnum::isHeadline, "is_headline", AMCore::AMDataType_bool, {}, nullptr},
        //{&CRiskFactorsWorkingConditionsEnum::color, "color", AMCore::AMDataType_string, {}, nullptr},
        {&CRiskFactorsWorkingConditionsEnum::description, "description", AMCore::AMDataType_string, {}, nullptr},
        //{&CRiskFactorsWorkingConditionsEnum::ord, "ord", AMCore::AMDataType_integer, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CRiskFactorsWorkingConditionsEnum::context()
{
    return *g_context;
}

CRiskFactorsWorkingConditionsEnumMap::CRiskFactorsWorkingConditionsEnumMap()
    :std::map<int, CRiskFactorsWorkingConditionsEnum>(),
     loaded(false)
{
}


bool CRiskFactorsWorkingConditionsEnumMap::loadCRiskFactorsWorkingConditionsEnum(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    CRiskFactorsWorkingConditionsEnum::loadSelect(sql, "dbe.risk_factors_working_conds");
    try {
        pqxx::result R(db.query(sql.str(), W));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CRiskFactorsWorkingConditionsEnum wc;
            wc.loadFromData(it);
            insert({wc.id, wc});
        }
        loaded = true;
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}