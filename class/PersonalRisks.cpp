//
// Created by <PERSON><PERSON><PERSON> on 21.1.24.
//

#include "PersonalRisks.h"
#include "COccupationalHazards.h"
#include "CRiskFactorsWorkingConditions.h"
#include "CLegalRequirementsForMedicalFitness.h"
#include "generated/person_states.h"

void PersonalRisks::insertOccupationalHazard(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res)
{
    int id = -1;
    try {
        nlohmann::json j = nlohmann::json::parse(res.body.c_str(), res.body.c_str() + res.body.size());
        if (j["status"].get<int>() != 0) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertOccupationalHazard insert not successfull");
            return;
        }
        id = j["record"]["id"].get<int>();
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "PersonalRisks::insertOccupationalHazard invalid response");
        return;
    }
    COccupationalHazards oh(id);
    //pqxx::work W(db.conn());
    if (!oh.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertOccupationalHazard bad param 3 id");
        //W.abort();
        return;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Do profese %s bylo přidáno riziko \"%s\".\n", buftm, oh.profession_.get().isNull() ? "" : oh.profession_.get().get().c_str(), oh.riskName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $4 , note = note || $2 "
            " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND occup_hazard = $1",
            W, oh.riskEnum.get(), std::string(buff), oh.profession.get(), sess.userId
            );
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT t.id, now(), NULL, $1, NULL ,NULL, $2 , $4 "
                "FROM dbo.people t LEFT JOIN dbo.personal_risks r ON r.person = t.id AND r.occup_hazard = $1 "
                "WHERE t.profession = $3 AND r.id IS NULL)",
            W, oh.riskEnum.get(), std::string(buff), oh.profession.get(), sess.userId
            );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertOccupationalHazard bad sql 1");
        //W.abort();
        return;
    }
    //W.commit();
}

bool PersonalRisks::deleteOccupationalHazard(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string sid)
{
    int id = -1;
    std::size_t sidLen = -1;
    try {
        id = std::stoi(sid,&sidLen);
        if (sidLen != sid.length()) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteOccupationalHazard bad param 1");
            W.abort();
            return false;
        }
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteOccupationalHazard bad param 2");
        W.abort();
        return false;
    }
    COccupationalHazards oh(id);
    if (!oh.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteOccupationalHazard bad param 3 id");
        W.abort();
        return false;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Z profese %s bylo odebráno riziko \"%s\".\n", buftm, oh.profession_.get().isNull() ? "" : oh.profession_.get().get().c_str(), oh.riskName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $4 , note = note || $2 "
                " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND occup_hazard = $1",
            W, oh.riskEnum.get(), buff, oh.profession.get(), sess.userId
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteOccupationalHazard bad sql 1");
        //W.abort();
        return false;
    }
    //W.commit();
    return true;
}


void PersonalRisks::insertRFWorkingCond(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res)
{
    int id = -1;
    try {
        nlohmann::json j = nlohmann::json::parse(res.body.c_str(), res.body.c_str() + res.body.size());
        if (j["status"].get<int>() != 0) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertRFWorkingCond insert not successfull");
            return;
        }
        id = j["record"]["id"].get<int>();
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "PersonalRisks::insertRFWorkingCond invalid response");
        return;
    }
    CRiskFactorsWorkingConditions rfwc(id);
    //pqxx::work W(db.conn());
    if (!rfwc.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertRFWorkingCond bad param 3 id");
        //W.abort();
        return;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Do profese %s byla přidána riziková pracovní podmínka \"%s\".\n", buftm, rfwc.profession_.get().isNull() ? "" : rfwc.profession_.get().get().c_str(), rfwc.rFWorkingCondName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $4 , note = note || $2 "
                " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND r_f_working_cond = $1",
            W, rfwc.rFWorkingCond.get(), buff, rfwc.profession.get(), sess.userId
                );
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT t.id, now(), NULL, NULL, $1, NULL, $2 , $4 "
                "FROM dbo.people t LEFT JOIN dbo.personal_risks r ON r.person = t.id AND r.r_f_working_cond = $1 "
                "WHERE t.profession = $3 AND r.id IS NULL)",
            W, rfwc.rFWorkingCond.get(), buff, rfwc.profession.get(), sess.userId
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertRFWorkingCond bad sql 1");
        //W.abort();
        return;
    }
    //W.commit();
}

bool PersonalRisks::deleteRFWorkingCond(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string sid)
{
    int id = -1;
    std::size_t sidLen = -1;
    try {
        id = std::stoi(sid,&sidLen);
        if (sidLen != sid.length()) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteRFWorkingCond bad param 4");
            W.abort();
            return false;
        }
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteRFWorkingCond bad param 1");
        ///W.abort();
        return false;
    }
    CRiskFactorsWorkingConditions rfwc(id);
    if (!rfwc.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteRFWorkingCond bad param 2 id");
        W.abort();
        return false;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Z profese %s byla odebrána riziková pracovní podmínka \"%s\".\n", buftm, rfwc.profession_.get().isNull() ? "" : rfwc.profession_.get().get().c_str(), rfwc.rFWorkingCondName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $4 , note = note || $2 "
            " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND r_f_working_cond = $1",
            W, rfwc.rFWorkingCond.get(), buff, rfwc.profession.get(), sess.userId
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteRFWorkingCond bad sql 1");
        //W.abort();
        return false;
    }
    //W.commit();
    return true;
}


void PersonalRisks::insertLegalReq(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res)
{
    int id = -1;
    try {
        nlohmann::json j = nlohmann::json::parse(res.body.c_str(), res.body.c_str() + res.body.size());
        if (j["status"].get<int>() != 0) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertLegalReq insert not successfull");
            return;
        }
        id = j["record"]["id"].get<int>();
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "PersonalRisks::insertLegalReq invalid response");
        return;
    }
    CLegalRequirementsForMedicalFitness lrmf(id);
    //pqxx::work W(db.conn());
    if (!lrmf.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertLegalReq bad param 3 id");
        W.abort();
        return;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Do profese %s byl přidán požadavek právního předpisu \"%s\".\n", buftm, lrmf.profession_.get().isNull() ? "" : lrmf.profession_.get().get().c_str(), lrmf.reqName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $4 , note = note || $2 "
            " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND legal_req = $1",
            W, lrmf.legalReq.get(), buff, lrmf.profession.get(), sess.userId
                );
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT t.id, now(), NULL, NULL, NULL, $1, $2 , $4 "
            "FROM dbo.people t LEFT JOIN dbo.personal_risks r ON r.person = t.id AND r.legal_req = $1 "
            "WHERE t.profession = $3 AND r.id IS NULL)",
            W, lrmf.legalReq.get(), buff, lrmf.profession.get(), sess.userId
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertLegalReq bad sql 1");
        //W.abort();
        return;
    }
    //W.commit();
}

bool PersonalRisks::deleteLegalReq(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string sid)
{
    int id = -1;
    std::size_t sidLen = -1;
    try {
        id = std::stoi(sid,&sidLen);
        if (sidLen != sid.length()) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteLegalReq bad param 1");
            //W.abort();
            return false;
        }
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteLegalReq bad param 2");
        //W.abort();
        return false;
    }
    CLegalRequirementsForMedicalFitness lrmf(id);
    if (!lrmf.load(db,W)) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteLegalReq bad param 3 id");
        //W.abort();
        return false;
    }
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Z profese %s byl odstraněn požadavek právního předpisu \"%s\".\n", buftm, lrmf.profession_.get().isNull() ? "" : lrmf.profession_.get().get().c_str(), lrmf.reqName.get().get().c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $4 , note = note || $2 "
            " WHERE person IN (SELECT id FROM dbo.people WHERE profession = $3) AND legal_req = $1",
            W, lrmf.legalReq.get(), buff, lrmf.profession.get(), sess.userId
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::deleteLegalReq bad sql 1");
        //W.abort();
        return false;
    }
    //W.commit();
    return true;
}

void PersonalRisks::insertPerson(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res)
{
    int id = -1;
    int idProfession = -1;
    std::string nameProfession = "";
    SAW_person_states state = SAW_person_states::ps_active;
    try {
        nlohmann::json j = nlohmann::json::parse(res.body.c_str(), res.body.c_str() + res.body.size());
        if (j["status"].get<int>() != 0) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertPerson insert not successfull");
            return;
        }
        id = j["record"]["id"].get<int>();
        idProfession = j["record"]["profession"].get<int>();
        nameProfession = j["record"]["profession_name"].get<std::string>();
        state = (SAW_person_states)j["record"]["state"].get<int>();
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "PersonalRisks::insertPerson invalid response");
        return;
    }
    if (state != SAW_person_states::ps_active) {
        return;
    }
    //pqxx::work W(db.conn());
    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    char buff[512];
    snprintf(buff, sizeof(buff) / sizeof(char),"%s: Vytvořena osoba s profesí %s.\n", buftm, nameProfession.c_str());
    buff[sizeof(buff) / sizeof(char) - 1] = '\0';
    try {
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT $1, now(), NULL, t.risk_enum, NULL ,NULL, $2 , $3 "
            "FROM dbo.occupational_hazards t WHERE t.profession = $4)",
            W, id, (std::string) buff, sess.userId, idProfession
                );
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT $1, now(), NULL, NULL, t.r_f_working_cond, NULL, $2 , $3 "
            "FROM dbo.risk_factors_working_conds t WHERE t.profession = $4)",
            W, id, (std::string) buff, sess.userId, idProfession
                );
        db.query(
            "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
            "(SELECT $1, now(), NULL, NULL, NULL, t.legal_req, $2 , $3 "
            "FROM dbo.legal_req_for_med_fitness t WHERE t.profession = $4)",
            W, id, (std::string) buff, sess.userId, idProfession
                );
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertPerson bad sql 1");
        //W.abort();
        return;
    }
    //W.commit();
}

void PersonalRisks::updatePerson(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, CPeople &person, const httplib::Request &req, httplib::Response& res)
{
    int id = -1;
    int idProfession = -1;
    std::string nameProfession = "";
    SAW_person_states state = SAW_person_states::ps_active;
    try {
        nlohmann::json j = nlohmann::json::parse(res.body.c_str(), res.body.c_str() + res.body.size());
        if (j["status"].get<int>() != 0) {
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertPerson insert not successfull");
            return;
        }
        id = j["record"]["id"].get<int>();
        idProfession = j["record"]["profession"].get<int>();
        nameProfession = j["record"]["profession_name"].get<std::string>();
        state = (SAW_person_states)j["record"]["state"].get<int>();
    } catch (std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Error, "PersonalRisks::insertPerson invalid response");
        return;
    }

    if (state == person.state && idProfession == person.profession) {
        return;
    }

    time_t     now = time(0);
    struct tm  tstruct = *localtime(&now);
    char       buftm[80];
    strftime(buftm, sizeof(buftm) / sizeof(char), AMDialogs::datetimeFormatText, &tstruct);
    //pqxx::work W(db.conn());
    char buff[512];
    try {
        if (state != person.state) {
            if (state != SAW_person_states::ps_active) {
                snprintf(buff, sizeof(buff) / sizeof(char), "%s: Osoba vyřazena z pracovního procesu.\n", buftm);
                buff[sizeof(buff) / sizeof(char) - 1] = '\0';
                db.query(
                    "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $1 , note = note || $2 "
                    " WHERE person = $3", W, sess.userId, buff, id
                        );
            } else {
                snprintf(buff, sizeof(buff) / sizeof(char), "%s: Osoba zařazena do pracovního procesu.\n", buftm);
                buff[sizeof(buff) / sizeof(char) - 1] = '\0';
                db.query(
                    "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                    " WHERE person = $3 AND occup_hazard IN (SELECT t.risk_enum FROM dbo.occupational_hazards t WHERE t.profession = $4)", W, sess.userId, buff, id, idProfession
                        );
                db.query(
                    "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                    " WHERE person = $3 AND r_f_working_cond IN (SELECT t.r_f_working_cond FROM dbo.risk_factors_working_conds t WHERE t.profession = $4)", W, sess.userId, buff, id, idProfession
                        );
                db.query(
                    "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                    " WHERE person = $3 AND legal_req IN (SELECT t.legal_req FROM dbo.legal_req_for_med_fitness t WHERE t.profession = $4)", W, sess.userId, buff, id, idProfession
                        );
            }
        }

        if (idProfession != person.profession) {
            snprintf(
                buff, sizeof(buff) / sizeof(char), "%s: Profese byla změněna z %s na %s.\n", buftm, person.professionName.get().isNull() ? "" : person.professionName.get().get().c_str(), nameProfession.c_str()
                    );
            buff[sizeof(buff) / sizeof(char) - 1] = '\0';
            db.query(
                "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND occup_hazard IN"
                " (SELECT t.risk_enum FROM dbo.occupational_hazards t WHERE t.profession = $4 EXCEPT SELECT t2.risk_enum FROM dbo.occupational_hazards t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, idProfession, person.profession.get()
                    );
            db.query(
                "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
                "(SELECT $1, now(), NULL, t.risk_enum, NULL ,NULL, $2 , $3 FROM "
                    "(SELECT t1.risk_enum FROM dbo.occupational_hazards t1 "
                        "LEFT JOIN dbo.personal_risks r ON r.occup_hazard = t1.risk_enum AND r.person = $1 "
                        "WHERE t1.profession = $4 AND r.id IS NULL "
                    "EXCEPT SELECT t2.risk_enum FROM dbo.occupational_hazards t2 WHERE t2.profession = $5)"
                " t)",
                W, id, (std::string) buff, sess.userId, idProfession, person.profession.get()
                    );
            db.query(
                "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND r_f_working_cond IN"
                " (SELECT t.r_f_working_cond FROM dbo.risk_factors_working_conds t WHERE t.profession = $4 EXCEPT SELECT t2.r_f_working_cond FROM dbo.risk_factors_working_conds t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, idProfession, person.profession.get()
                    );
            db.query(
                "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
                "(SELECT $1, now(), NULL, NULL, t.r_f_working_cond, NULL, $2 , $3 FROM "
                    "(SELECT t1.r_f_working_cond FROM dbo.risk_factors_working_conds t1 "
                        "LEFT JOIN dbo.personal_risks r ON r.r_f_working_cond = t1.r_f_working_cond AND r.person = $1 "
                        "WHERE t1.profession = $4 AND r.id IS NULL "
                    "EXCEPT SELECT t2.r_f_working_cond FROM dbo.risk_factors_working_conds t2 WHERE t2.profession = $5)"
                " t)",
                W, id, (std::string) buff, sess.userId, idProfession, person.profession.get()
                    );
            db.query(
                "UPDATE dbo.personal_risks pr SET appear = LEAST(appear, now()), disappear = NULL, modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND legal_req IN"
                " (SELECT t.legal_req FROM dbo.legal_req_for_med_fitness t WHERE t.profession = $4 EXCEPT SELECT t2.legal_req FROM dbo.legal_req_for_med_fitness t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, idProfession, person.profession.get()
                    );
            db.query(
                "INSERT INTO dbo.personal_risks (person, appear, disappear, occup_hazard, r_f_working_cond, legal_req, note, modified_by)"
                "(SELECT $1, now(), NULL, NULL, NULL, t.legal_req, $2 , $3 FROM "
                    "(SELECT t1.legal_req FROM dbo.legal_req_for_med_fitness t1 "
                        "LEFT JOIN dbo.personal_risks r ON r.legal_req = t1.legal_req AND r.person = $1 "
                        "WHERE t1.profession = $4 AND r.id IS NULL "
                    "EXCEPT SELECT t2.legal_req FROM dbo.legal_req_for_med_fitness t2 WHERE t2.profession = $5)"
                " t)",
                W, id, (std::string) buff, sess.userId, idProfession, person.profession.get()
                    );

            db.query(
                "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND pr.occup_hazard  IN "
                "(SELECT t1.risk_enum FROM dbo.occupational_hazards t1 WHERE t1.profession = $4 "
                "EXCEPT "
                "SELECT t2.risk_enum FROM dbo.occupational_hazards t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, person.profession.get(), idProfession
                    );
            db.query(
                "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND pr.r_f_working_cond  IN "
                "(SELECT t1.r_f_working_cond FROM dbo.risk_factors_working_conds t1 WHERE t1.profession = $4 "
                "EXCEPT "
                "SELECT t2.r_f_working_cond FROM dbo.risk_factors_working_conds t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, person.profession.get(), idProfession
                    );
            db.query(
                "UPDATE dbo.personal_risks pr SET disappear = GREATEST(disappear, now()), modified_by = $1 , note = note || $2 "
                " WHERE person = $3 AND pr.legal_req  IN "
                "(SELECT t1.legal_req FROM dbo.legal_req_for_med_fitness t1 WHERE t1.profession = $4 "
                "EXCEPT "
                "SELECT t2.legal_req FROM dbo.legal_req_for_med_fitness t2 WHERE t2.profession = $5)",
                W, sess.userId, buff, id, person.profession.get(), idProfession
                    );
        }
    } catch(std::exception e) {
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Warn, "PersonalRisks::insertPerson bad sql 1");
        //W.abort();
        return;
    }
    //W.commit();
}