/* automatically generated file, do not edit. */

#include "CCitizenships.h"  

CCitizenships::CCitizenships(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      code_A2()
    {           
    };
    
CCitizenships::CCitizenships()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      code_A2()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CCitizenships::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CCitizenships>(
    "dbe.citizenships",
    {  
        {&CCitizenships::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CCitizenships::title, "title", AMCore::AMDataType_string, {}, nullptr},
        {&CCitizenships::code_A2, "country_code_a2", AMCore::AMDataType_string, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CCitizenships::context()
{
    return *g_context;
}

bool CCitizenships::loadByCodeA2(SAWString code_A2, AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    loadSelect(sql, context().tableName);
    sql << " WHERE country_code_A2 = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, (std::string)code_A2));
        if (R.begin() == R.end()) {
            return false;
        }
        loadFromData(R.begin());
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}