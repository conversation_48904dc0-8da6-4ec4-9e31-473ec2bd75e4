//
// Created by <PERSON><PERSON><PERSON><PERSON> on 24.11.2024.
//

#ifndef SAWMAIL_H
#define SAWMAIL_H
#include <string>
#include "poco/Net/include/Poco/Net/MailMessage.h"



class SAWMail {
protected:
    static void generalNewUser(
        std::string mail,
        std::string subject,
        std::string domain,
        std::string userName,
        std::string password,
        std::string pin
        );
public:
    static void sendMail(Poco::Net::MailMessage &msg, const std::string &customer, const std::string &xrealip);
    static void afterInstall(
        std::string mail,
        std::string domain,
        std::string userName,
        std::string password
        );
    static void afterResetPassword(
        std::string mail,
        std::string domain,
        std::string userName,
        std::string password
        );
    static void afterCreateUser(
        std::string mail,
        std::string domain,
        std::string userName,
        std::string password
        );
};



#endif //SAWMAIL_H
