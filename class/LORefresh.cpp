//
// Created by <PERSON><PERSON><PERSON> on 24.10.23.
//

#include <pqxx/pqxx>
#include "LORefresh.h"
#include "nlohmann/json.hpp"
#include "svr/AMSvrDB.h"

void LORefresh::run()
{
    nlohmann::json jres = nlohmann::json::parse(input.get().c_str(), input.get().c_str() + input.get().size());
    SAW_tables tbl = (SAW_tables)jres["table"].get<int>();


    std::string sql;
    switch (tbl) {
        case people: sql = "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_hiearchy;"
                           "UPDATE dbo.healthy_restrictions t SET person__ = mc.person FROM dbo.medical_checks mc WHERE mc.id = t.med_check AND t.person__ <> mc.person;"
                           "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_restrictions;"
                           "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_check_status;";
                           break;
        case healthy_restrictions: sql = "UPDATE dbo.healthy_restrictions t SET person__ = mc.person FROM dbo.medical_checks mc WHERE mc.id = t.med_check AND t.person__ <> mc.person;"
                                         "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_restrictions;"
                                         "INSERT INTO dbh.heartbeat (id, operation, table_id, row, modified_at) VALUES (DEFAULT, 'U', " + std::to_string(people) + ", -**********, NOW()) ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';";break;
        case categorizations: sql = "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.job_categories;"
                                    "INSERT INTO dbh.heartbeat (id, operation, table_id, row, modified_at) VALUES (DEFAULT, 'U', " + std::to_string(people) + ", -**********, NOW()) ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';";break;

        case medical_checks:
        case med_conclusions:
        case wms_specifics_2:
        case personal_risks:
            sql = "REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_check_status;"
                  "INSERT INTO dbh.heartbeat (id, operation, table_id, row, modified_at) VALUES (DEFAULT, 'U', " + std::to_string(people) + ", -**********, NOW()) ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';";break;
        default:
            AMAssert(0);
    }
    sql += "INSERT INTO dbh.heartbeat (id, operation, table_id, row, modified_at) VALUES (DEFAULT, 'U', " + std::to_string(tbl) + ", -**********, NOW()) ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';";
    AMSvr::AMSvrDB db(AMSvr::AMSvrDB::domain2databaseName(customer.get()));
    pqxx::work W(db.conn());
    try {
        db.query(sql, W);
        W.commit();
    } catch (std::exception e) {
        W.abort();
        AMLog::AMLoggerSvr::Log("", "", AMLog::Warn, "LORefresh::run sql query failed");
        return;
    }

    AMSvr::AMSvrDB dbsaw("_saw");
    pqxx::work W2(dbsaw.conn());
    time_t t = time(nullptr);
    finished = *gmtime(&t);
    try {
        save(dbsaw, W2);
        W2.commit();
    } catch (std::exception e) {
        W2.abort();
        AMLog::AMLoggerSvr::Log("", "", AMLog::Warn, "LORefresh::run save query failed");
    }
}

LORefresh::LORefresh()
    : LongOpItem()
{
    task = loq_refresh;
}