/* automatically generated file, do not edit. */


#ifndef CPEOPLE_H
#define CPEOPLE_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CPeople: public AMSvr::AMSvrDbObject<int>
{
public:
    CPeople();
    CPeople(int id);

    SAWString cname();

    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWEnum, int> degreeBefore;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> degreeBefore_;
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<SAWString, int> surname;
    AMSvr::AMSvrProp<SAWEnum, int> degreeAfter;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> degreeAfter_;
    //AMSvr::AMSvrProp<AMCore::AMNameBase < SAWEnum, SAWString >, int> cname;
    AMSvr::AMSvrProp<SAWEnum, int> sex;
    AMSvr::AMSvrProp<SAWString, int> email;
    AMSvr::AMSvrProp<SAWString, int> phone;
    AMSvr::AMSvrProp<int, int> employer;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> employerName;
    //AMSvr::AMSvrProp<SAWEnum, int> employer_;
    AMSvr::AMSvrProp<SAWEnum, int> centre;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> centre_;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> centrecode_;
    AMSvr::AMSvrProp<SAWEnum, int> eRelationship;
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> professionName;
    //AMSvr::AMSvrProp<SAWEnum, int> profession_;
    AMSvr::AMSvrProp<SAWEnum, int> state;
    AMSvr::AMSvrProp<int, int> leader;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> leaderName;
    //AMSvr::AMSvrProp<SAWEnum, int> leader_;
    AMSvr::AMSvrProp<bool, int> isLeader;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> lastCheck;
    //AMSvr::AMSvrProp<std::vector<int>, int> hiearchy;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPEOPLE_H
