/* automatically generated file, do not edit. */


#ifndef COCCUPATION<PERSON><PERSON><PERSON>ARDS_H
#define COCCUPATION<PERSON><PERSON>ZARDS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class COccupationalHazards: public AMSvr::AMSvrDbObject<int>
{
public:
    COccupationalHazards();
    COccupationalHazards(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> profession_;
    AMSvr::AMSvrProp<SAWEnum, int> riskEnum;
    AMSvr::AMSvrProp<SAWString, int> risk11;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> riskName;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class COccupationalHazardsMap: public std::map<SAWEnum, COccupationalHazards>
{
public:
    bool loadCOccupationalHazards(AMSvr::AMSvrDB &db, pqxx::work &W, int profession);
};

#endif //COCCUPATIONALHAZARDS_H
