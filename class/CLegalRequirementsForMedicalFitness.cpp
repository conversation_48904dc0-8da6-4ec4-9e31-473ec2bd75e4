/* automatically generated file, do not edit. */

#include "CLegalRequirementsForMedicalFitness.h"  

CLegalRequirementsForMedicalFitness::CLegalRequirementsForMedicalFitness(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      profession(),
      profession_(),
      legalReq(),
      reqName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };
    
CLegalRequirementsForMedicalFitness::CLegalRequirementsForMedicalFitness()
    : AMSvr::AMSvrDbObject<int>(),
      profession(),
      profession_(),
      legalReq(),
      reqName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CLegalRequirementsForMedicalFitness::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CLegalRequirementsForMedicalFitness>(
    "dbo.legal_req_for_med_fitness",
    {
        {&CLegalRequirementsForMedicalFitness::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CLegalRequirementsForMedicalFitness::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&CLegalRequirementsForMedicalFitness::profession_, "profession_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_"},
        {&CLegalRequirementsForMedicalFitness::legalReq, "legal_req", AMCore::AMDataType_enum, {}, nullptr},
        {&CLegalRequirementsForMedicalFitness::reqName, "req_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbe.legal_req_for_med_fitness pr ON pr.id = t.legal_req"}, "pr.description AS req_name"},
        {&CLegalRequirementsForMedicalFitness::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CLegalRequirementsForMedicalFitness::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CLegalRequirementsForMedicalFitness::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CLegalRequirementsForMedicalFitness::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CLegalRequirementsForMedicalFitness::context()
{
    return *g_context;
}

bool CLegalRequirementsForMedicalFitnessMap::loadCLegalRequirementsForMedicalFitness(AMSvr::AMSvrDB &db, pqxx::work &W, int profession)
{
    std::ostringstream sql;
    CLegalRequirementsForMedicalFitness::loadSelect(sql, "dbo.legal_req_for_med_fitness");
    sql << " WHERE profession = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, profession));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CLegalRequirementsForMedicalFitness lr;
            lr.loadFromData(it);
            insert({lr.legalReq, lr});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}