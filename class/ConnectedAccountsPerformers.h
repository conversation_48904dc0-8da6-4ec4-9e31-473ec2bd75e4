//
// Created by <PERSON><PERSON><PERSON> on 31.3.24.
//

#ifndef SAW_CONNECTEDACCOUNTSPERFORMERS_H
#define SAW_CONNECTEDACCOUNTSPERFORMERS_H

#include "svr/AMSvrDB.h"
#include "svr/AMSvrSessionForTunnel.h"

class ConnectedAccountsPerformers {
public:
    static int numberOfEmployees(AMSvr::AMSvrDB &db, pqxx::work &W);
    static void updatePrepayedPeriod(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, int currentEmployees, int newEmployees);
};


#endif //SAW_CONNECTEDACCOUNTSPERFORMERS_H
