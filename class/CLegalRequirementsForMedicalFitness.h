/* automatically generated file, do not edit. */


#ifndef CLEGALREQUIREMENTSFORMEDICALFITNESS_H
#define CLEGALREQUIREMENTSFORMEDICALFITNESS_H

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CLegalRequirementsForMedicalFitness: public AMSvr::AMSvrDbObject<int>
{
public:
    CLegalRequirementsForMedicalFitness();
    CLegalRequirementsForMedicalFitness(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> profession_;
    AMSvr::AMSvrProp<SAWEnum, int> legalReq;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> reqName;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CLegalRequirementsForMedicalFitnessMap: public std::map<SAWEnum, CLegalRequirementsForMedicalFitness>
{
public:
    bool loadCLegalRequirementsForMedicalFitness(AMSvr::AMSvrDB &db, pqxx::work &W, int profession);
};

#endif //CLEGALREQUIREMENTSFORMEDICALFITNESS_H
