/* automatically generated file, do not edit. */

#ifndef CEMPLOYERS_H
#define CEMPLOYERS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CEmployers: public AMSvr::AMSvrDbObject<int>
{
public:
    CEmployers();
    CEmployers(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<unsigned int, int> ico;
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<SAWString, int> address;
    AMSvr::AMSvrProp<SAWEnum, int> accidentInsurance;
    AMSvr::AMSvrProp<SAWString, int> datovka;
    AMSvr::AMSvrProp<SAWString, int> email;
    AMSvr::AMSvrProp<SAWString, int> phone;
    AMSvr::AMSvrProp<SAWString, int> web;
    AMSvr::AMSvrProp<SAWString, int> registration;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CEMPLOYERS_H
