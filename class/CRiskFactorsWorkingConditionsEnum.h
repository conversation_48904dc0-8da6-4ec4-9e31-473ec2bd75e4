/* automatically generated file, do not edit. */


#ifndef CRISKFACTORSWORKINGCONDITIONSENUM_H
#define CRISKFACTORSWORKINGCONDITIONSENUM_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CRiskFactorsWorkingConditionsEnum: public AMSvr::AMSvrDbObject<int>
{
public:
    CRiskFactorsWorkingConditionsEnum();
    CRiskFactorsWorkingConditionsEnum(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> title;
    AMSvr::AMSvrProp<bool, int> isHeadline;
    //AMSvr::AMSvrProp<SAWString, int> color;
    AMSvr::AMSvrProp<SAWString, int> description;
    //AMSvr::AMSvrProp<int, int> ord;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CRiskFactorsWorkingConditionsEnumMap: public std::map<int, CRiskFactorsWorkingConditionsEnum>
{
public:
    CRiskFactorsWorkingConditionsEnumMap();
    bool loadCRiskFactorsWorkingConditionsEnum(AMSvr::AMSvrDB &db, pqxx::work &W);
    bool loaded;
};

#endif //CRISKFACTORSWORKINGCONDITIONSENUM_H
