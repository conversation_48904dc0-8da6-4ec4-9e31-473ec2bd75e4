/* automatically generated file, do not edit. */

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CExaminationTypes: public AMSvr::AMSvrDbObject<int>
{
public:
    CExaminationTypes();
    CExaminationTypes(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> title;
    AMSvr::AMSvrProp<SAWString, int> tooltip;
    //AMSvr::AMSvrProp<SAWString, int> titleWithTooltip;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CExaminationTypesMap: public std::map<int, CExaminationTypes>
{
public:
    CExaminationTypesMap();
    bool loadCExaminationTypes(AMSvr::AMSvrDB &db, pqxx::work &W);
    bool loaded;
};

