//
// Created by <PERSON><PERSON><PERSON> on 10.10.23.
//

#include "LongOpItem.h"
#include "LOInstall.h"
#include "LORefresh.h"
#include "LOMaintenance.h"

LongOpItem::LongOpItem(long long id)
    : AMSvr::AMSvrLongOpItem<long long>(),
      AMSvr::AMSvrDbObject<long long>(id),
        task(),
        input(),
        output(),
        customer(),
        step(),
        created(),
        finished()
{
}

LongOpItem::LongOpItem()
    : AMSvr::AMSvrLongOpItem<long long>(),
      AMSvr::AMSvrDbObject<long long>(),
      task(SAW_long_op_task::loq_install),
      input(),
      output(),
      customer(),
      step(),
      created(),
      finished()
{
}

AMSvr::AMSvrDbObjectContext<long long> *LongOpItem::g_context = AMSvr::AMSvrDbObjectContext<long long>::create<LongOpItem>(
    "dbo.long_op_queue",
    {
        {&LongOpItem::id, "id", AMCore::AMDataType_long, {}, nullptr},
        {(AMSvr::AMSvrProp<SAWEnum , long long> LongOpItem::*)&LongOpItem::task, "task", AMCore::AMDataType_enum, {}, nullptr},
        {&LongOpItem::input, "input", AMCore::AMDataType_string, {}, nullptr},
        {&LongOpItem::output, "output", AMCore::AMDataType_string, {}, nullptr},
        {&LongOpItem::customer, "customer", AMCore::AMDataType_string, {}, nullptr},
        {&LongOpItem::step, "step", AMCore::AMDataType_integer, {}, nullptr},
        {&LongOpItem::created, "created", AMCore::AMDataType_datetime, {}, nullptr},
        {&LongOpItem::finished, "finished", AMCore::AMDataType_datetime_nullable, {}, nullptr},
    }
    );

long long LongOpItem::getNumber()
{
    return (long long)id;
}

AMSvr::AMSvrDbObjectContext<long long> &LongOpItem::context()
{
    return *g_context;
}

bool LongOpItemVec::loadUnfinished(AMSvr::AMSvrDB &db, pqxx::work &W, std::string hostname, int hostPort)
{
    std::ostringstream sql;
    LongOpItem::loadSelect(sql, "dbo.long_op_queue");
    sql << " WHERE finished IS NULL";
    try {
        pqxx::result R(db.query(sql.str(), W));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        reserve(R.size());
        for (auto it: R) {
            LongOpItem *item = nullptr;
            SAW_long_op_task task = (SAW_long_op_task)it.at("task").as<long long>();
            switch (task) {
                case loq_install:item = new LOInstall(hostname, hostPort);break;
                case loq_refresh:item = new LORefresh();break;
                case loq_maintenance:item = new LOMaintenance();break;
                default:
                    AMAssert(0 && "Unknown task id");
            }
            item->loadFromData(it);
            push_back(item);
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}
