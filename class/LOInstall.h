//
// Created by <PERSON><PERSON><PERSON> on 24.10.23.
//

#ifndef SAW_LOINSTALL_H
#define SAW_LOINSTALL_H

#include <mutex>
#include "class/LongOpItem.h"
#include "class/InstallRequest.h"

class LOInstall: public LongOpItem {

public:
    LOInstall(std::string hostname, int hostPort);
    LOInstall(const LOInstall &right);
    //LOInstall(const LOInstall *right);
    void run() override;
    InstallRequest *parseRequest();
    bool createDatabase(InstallRequest &ir);
    bool createUser(AMSvr::AMSvrDB &db, InstallRequest &ir);
    int maxSteps();

    std::string decipherFile(int file_id);
protected:
    const LOInstall & lockCopyContruct(const LOInstall &right);
    //const LOInstall & lockCopyContruct(const LOInstall *right);
    static std::mutex g_mutex;
    int m_maxSteps;
    std::string m_hostname;
    int m_hostPort;
};


#endif //SAW_LOINSTALL_H
