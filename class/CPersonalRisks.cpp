/* automatically generated file, do not edit. */

#include "CPersonalRisks.h"  

CPersonalRisks::CPersonalRisks(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      person(),
      appear(),
      disappear(),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CPersonalRisks::CPersonalRisks()
    : AMSvr::AMSvrDbObject<int>(),
      person(),
      appear(),
      disappear(),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CPersonalRisks::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CPersonalRisks>(
    "dbo.personal_risks",
    {  
        {&CPersonalRisks::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CPersonalRisks::person, "person", AMCore::AMDataType_integer, {}, nullptr},
        {&CPersonalRisks::appear, "appear", AMCore::AMDataType_date, {}, nullptr},
        {&CPersonalRisks::disappear, "disappear", AMCore::AMDataType_date_nullable, {}, nullptr},
        {&CPersonalRisks::occupHazard, "occup_hazard", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CPersonalRisks::rFWorkingCond, "r_f_working_cond", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CPersonalRisks::legalReq, "legal_req", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CPersonalRisks::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CPersonalRisks::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CPersonalRisks::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CPersonalRisks::context()
{
    return *g_context;
}


bool CPersonalRisksMap::loadCPersonalRisks(AMSvr::AMSvrDB &db, pqxx::work &W, int person)
{
    std::ostringstream sql;
    CPersonalRisks::loadSelect(sql, "dbo.personal_risks");
    sql << " WHERE person = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, person));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CPersonalRisks pr;
            pr.loadFromData(it);
            insert({pr.id.get(), pr});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}

std::map<SAWEnum, CPersonalRisks>::iterator CPersonalRisksMap::findByRiskActive(AMCore::AMNullable<SAWEnum> occupHazard, AMCore::AMNullable<SAWEnum> rFWorkingCond, AMCore::AMNullable<SAWEnum> legalReq)
{
    for (std::map<SAWEnum, CPersonalRisks>::iterator it = begin(); it != end(); it++) {
        if (it->second.disappear.get().isNull() && it->second.occupHazard.get() == occupHazard && it->second.rFWorkingCond.get() == rFWorkingCond && it->second.legalReq.get() == legalReq) {
            return it;
        }
    }
    return end();
}

std::map<SAWEnum, CPersonalRisks>::iterator CPersonalRisksMap::findByRiskInactive(AMCore::AMNullable<SAWEnum> occupHazard, AMCore::AMNullable<SAWEnum> rFWorkingCond, AMCore::AMNullable<SAWEnum> legalReq)
{
    for (std::map<SAWEnum, CPersonalRisks>::iterator it = begin(); it != end(); it++) {
        if (!it->second.disappear.get().isNull() && it->second.occupHazard.get() == occupHazard && it->second.rFWorkingCond.get() == rFWorkingCond && it->second.legalReq.get() == legalReq) {
            return it;
        }
    }
    return end();
}