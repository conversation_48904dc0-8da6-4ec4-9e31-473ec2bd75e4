/* automatically generated file, do not edit. */

#include "CPeopleData.h"  

CPeopleData::CPeopleData(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      citizenship(),
      permanentAddress(),
      postAddress(),
      dateBirth(),
      identityNumber(),
      identityCard(),
      insurance(),
      onboardDate(),
      leaveDate(),
      drivingLicences(),
      degreeBefore(),
      name(),
      surname(),
      degreeAfter(),
      //fullName(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };
    
CPeopleData::CPeopleData()
    : AMSvr::AMSvrDbObject<int>(),
      citizenship(),
      permanentAddress(),
      postAddress(),
      dateBirth(),
      identityNumber(),
      identityCard(),
      insurance(),
      onboardDate(),
      leaveDate(),
      drivingLicences(),
      degreeBefore(),
      name(),
      surname(),
      degreeAfter(),
      //fullName(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CPeopleData::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CPeopleData>(
    "dbo.people_data",
    {  
        {&CPeopleData::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeopleData::citizenship, "citizenship", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeopleData::permanentAddress, "permanent_address", AMCore::AMDataType_string, {}, nullptr},
        {&CPeopleData::postAddress, "post_address", AMCore::AMDataType_string, {}, nullptr},
        {&CPeopleData::dateBirth, "date_birth", AMCore::AMDataType_date, {}, nullptr},
        {&CPeopleData::identityNumber, "identity_number", AMCore::AMDataType_string, {}, nullptr},
        {&CPeopleData::identityCard, "identity_card", AMCore::AMDataType_string, {}, nullptr},
        {&CPeopleData::insurance, "insurance", AMCore::AMDataType_enum, {}, nullptr},
        {&CPeopleData::onboardDate, "onboard_date", AMCore::AMDataType_date_nullable, {}, nullptr},
        {&CPeopleData::leaveDate, "leave_date", AMCore::AMDataType_date_nullable, {}, nullptr},
        {&CPeopleData::drivingLicences, "driving_licences", AMCore::AMDataType_enum_map, {}, nullptr},
        {&CPeopleData::degreeBefore, "degree_before", AMCore::AMDataType_enum, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.degree_before AS degree_before"},
        {&CPeopleData::name, "name", AMCore::AMDataType_string, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.name AS name"},
        {&CPeopleData::surname, "surname", AMCore::AMDataType_string, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.surname AS surname"},
        {&CPeopleData::degreeAfter, "degree_after", AMCore::AMDataType_enum, {"LEFT JOIN dbo.people p ON p.id = t.id"}, "p.degree_after AS degree_after"},
        //{&CPeopleData::fullName, "full_name", AMCore::AMDataType_name, {}, nullptr},
        {&CPeopleData::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CPeopleData::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CPeopleData::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CPeopleData::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CPeopleData::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CPeopleData::context()
{
    return *g_context;
}
