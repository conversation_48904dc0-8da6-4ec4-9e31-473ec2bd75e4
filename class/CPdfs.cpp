/* automatically generated file, do not edit. */

#include "CPdfs.h"  

CPdfs::CPdfs(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      classtype(0),
      type(0),
      document(0),
      data(),
      modified_by(0),
      modified_at()
    {           
    };
    
CPdfs::CPdfs()
    : AMSvr::AMSvrDbObject<int>(),
        classtype(0),
        type(0),
        document(0),
        data(),
        modified_by(0),
        modified_at()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CPdfs::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CPdfs>(
    "dbo.pdfs",
    {
        {&CPdfs::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CPdfs::classtype, "class__", AMCore::AMDataType_enum, {}, nullptr},
        {&CPdfs::type, "type", AMCore::AMDataType_enum, {}, nullptr},
        {&CPdfs::document, "document", AMCore::AMDataType_integer, {}, nullptr},
        {&CPdfs::data, "data", AMCore::AMDataType_string, {}, nullptr},
        {&CPdfs::modified_by, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CPdfs::modified_at, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CPdfs::context()
{
    return *g_context;
}
