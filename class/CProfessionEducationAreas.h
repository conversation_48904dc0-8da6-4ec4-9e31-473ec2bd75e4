/* automatically generated file, do not edit. */


#ifndef CPROFESSIONEDUCATIONAREAS_H
#define CPROFESSIONEDUCATIONAREAS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CProfessionEducationAreas: public AMSvr::AMSvrDbObject<int>
{
public:
    CProfessionEducationAreas();
    CProfessionEducationAreas(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> profession;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> profession_;
    AMSvr::AMSvrProp<AMCore::AMNullable<int>, int> school;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> schoolName;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> school_;
    AMSvr::AMSvrProp<SAWString, int> codeName;
    AMSvr::AMSvrProp<SAWString, int> conditions;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPROFESSIONEDUCATIONAREAS_H
