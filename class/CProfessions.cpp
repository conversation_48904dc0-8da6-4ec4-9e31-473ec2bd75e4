/* automatically generated file, do not edit. */

#include "CProfessions.h"  

CProfessions::CProfessions(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      name(),
      typeOfWork(),
      czIsco(),
      object(),
      objectAddress_(),
      workplace(),
      workplace_(),
      workingTime(),
      interruptMode(),
      workingMode(),
      workload(),
      addInfo(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CProfessions::CProfessions()
    : AMSvr::AMSvrDbObject<int>(),
      name(),
      typeOfWork(),
      czIsco(),
      object(),
      objectAddress_(),
      workplace(),
      workplace_(),
      workingTime(),
      interruptMode(),
      workingMode(),
      workload(),
      addInfo(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CProfessions::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CProfessions>(
    "dbo.professions",
    {  
        {&CProfessions::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessions::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::typeOfWork, "type_of_work", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::czIsco, "cz_isco", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessions::object, "object", AMCore::AMDataType_enum, {"LEFT JOIN dbo.workplaces wp ON wp.id = t.workplace"}, "wp.object AS object"},
        {&CProfessions::objectAddress_, "object_address_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.workplaces wp ON wp.id = t.workplace", "LEFT JOIN dbo.objects obj ON obj.id = wp.object"}, "obj.name || ' - ' || obj.address AS object_address_"},
        {&CProfessions::workplace, "workplace", AMCore::AMDataType_enum, {}, nullptr},
        {&CProfessions::workplace_, "workplace_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.workplaces wp ON wp.id = t.workplace"}, "wp.name AS workplace_"},
        {&CProfessions::workingTime, "working_time", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::interruptMode, "interrupt_mode", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::workingMode, "working_mode", AMCore::AMDataType_enum, {}, nullptr},
        {&CProfessions::workload, "workload", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::addInfo, "add_info", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CProfessions::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CProfessions::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CProfessions::context()
{
    return *g_context;
}
