//
// Created by <PERSON><PERSON><PERSON> on 10.10.23.
//

#ifndef SAW_LONGOPITEM_H
#define SAW_LONGOPITEM_H

#include <string>
#include <ctime>
#include "amcore/AMNullable.h"
#include "svr/AMSvrLongOpItem.h"
#include "svr/AMSvrObject.h"
#include "generated/long_op_task.h"
#include "svr/AMSvrLongOpManager.h"


namespace AMSvr {
    template<typename TIDType>
    class AMSvrProp<SAW_long_op_task, TIDType>: public AMSvrDbPropertyBase<TIDType>
    {
    public:
        AMSvrProp(SAW_long_op_task value): AMSvrDbPropertyBase<TIDType>(),m_value(value){}
        AMSvrProp(): AMSvrDbPropertyBase<TIDType>(),m_value(loq_install){}
        AMSvrProp &operator=(const SAW_long_op_task &v) {m_value = v;this->setModified();return *this;}
        explicit operator SAW_long_op_task () { return this->m_value; }
        void load(pqxx::field f) override{this->m_value = (SAW_long_op_task)f.as<int>(); this->clearModified();};
        void save(std::ostream &s) const override {s << (int)this->m_value;}
        SAW_long_op_task &get() {return m_value;}
    protected:
        SAW_long_op_task m_value;
    };
}

class LongOpItem: public AMSvr::AMSvrLongOpItem<long long>, public AMSvr::AMSvrDbObject<long long>
{
public:
    LongOpItem();
    LongOpItem(long long id);
    long long int getNumber() override;

    static AMSvr::AMSvrDbObjectContext<long long> *g_context;
    AMSvr::AMSvrDbObjectContext<long long> &context() override;

    AMSvr::AMSvrProp<SAW_long_op_task, long long> task;
    AMSvr::AMSvrProp<std::string, long long> input;
    AMSvr::AMSvrProp<std::string, long long> output;
    AMSvr::AMSvrProp<std::string, long long> customer;
    AMSvr::AMSvrProp<int, long long> step;
    AMSvr::AMSvrProp<std::tm, long long> created;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm >, long long > finished;

};


class LongOpItemVec: public std::vector<LongOpItem*>
{
public:
    bool loadUnfinished(AMSvr::AMSvrDB &db, pqxx::work &W, std::string hostname, int hostPort);
};

using LongOpManager = AMSvr::AMSvrLongOpManager<long long, LongOpItem>;

#endif //SAW_LONGOPITEM_H
