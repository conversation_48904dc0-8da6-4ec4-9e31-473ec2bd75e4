//
// Created by <PERSON><PERSON><PERSON> on 30.12.23.
//

#include "svr/Pdf.h"

#include <fstream>
#include <stdio.h>
#include <ctime>
#include <cstdio>
#include "amloglite/AMLoggerSvr.h"
#include "class/CPdfs.h"
#include "class/CPdfTypes.h"
#include "generated/pdf_types.h"
#include <spawn.h>
#include <wait.h>
#include <filesystem>


void Pdf::generate(AMSvr::AMSvrDB &db, pqxx::work &W, const httplib::Request &req, httplib::Response &res, std::string spdfDocId)
{
        static int tmpNo = 0;
        int pdfDocId = 0;
        std::size_t pdfDocIdLen = -1;
        try {
            pdfDocId = std::stoi(spdfDocId, &pdfDocIdLen);
        } catch (std::exception e) {
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid pdfDocId 1");
            W.abort();
            return;
        }
        if (pdfDocId == 0 || spdfDocId.size() != pdfDocIdLen) {
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid pdfDocId 2");
            W.abort();
            return;
        }
        CPdfs doc(pdfDocId);
        if (!doc.load(db, W)) {
            //res.set_content(std::string("{\"status\": 3}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Document not found");
            W.abort();
            return;
        }
        nlohmann::json j;
        try {
            j = nlohmann::json::parse(doc.data.get().begin(), doc.data.get().end());
        } catch (std::exception e) {
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid json ");
            W.abort();
            return;
        }
        char htmlFilename[64];
        char pdfFilename[64];
#ifdef DEBUG
        sprintf(htmlFilename, "/run/user/1000/temp%05i.html", tmpNo);
        sprintf(pdfFilename,  "/run/user/1000/temp%05i.pdf", tmpNo);
#else
        sprintf(htmlFilename, "/runt/temp%05i.html", tmpNo);
        sprintf(pdfFilename,  "/runt/temp%05i.pdf", tmpNo);
#endif
        tmpNo++;
        std::ofstream os(htmlFilename);
        if (!os.is_open()) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 25");
            W.abort();
            return;
        }
        switch(doc.type.get()) {
            case pdft_medical_inspection:viewMedicalCheck_10(os, j);break;
            case pdft_medical_inspection_color:viewMedicalCheck_10(os, j);break;
        }
        os.close();

        std::filesystem::copy(htmlFilename, "/var/log/saw/ggg.html", std::filesystem::copy_options::overwrite_existing);

        posix_spawn_file_actions_t child_fd_actions;
        int ret;
        if ((ret = posix_spawn_file_actions_init (&child_fd_actions)) !=0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 11");
            W.abort();
            return;
        }
        if ((ret = posix_spawn_file_actions_addopen (&child_fd_actions, 1, "/var/log/saw/foo-log.txt", O_WRONLY | O_CREAT | O_TRUNC, 0644)) != 0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 12");
            W.abort();
            return;
        }
        if ((ret = posix_spawn_file_actions_adddup2 (&child_fd_actions, 1, 2)) != 0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 13");
            W.abort();
            return;
        }

        pid_t processID;
        char executableFileName[] = "/usr/bin/wkhtmltopdf";
        char htmlFilenameP[64];
        sprintf(htmlFilenameP, "file://%s", htmlFilename);
        char param[] = "--enable-local-file-access";
        char *argV[] = {executableFileName, param, htmlFilenameP, pdfFilename, 0};
        int status = -1;
        status = posix_spawn(&processID,executableFileName,&child_fd_actions,NULL,argV,environ);
        if(status != 0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 1");
            W.abort();
            return;
        }
        pid_t returnedPID = -1;
        int statusw = -1;
        returnedPID = waitpid(processID, &statusw, 0);
        if (returnedPID <=0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 2");
            W.abort();
            return;
        }
        if (!WIFEXITED(statusw)) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 3");
            W.abort();
            return;
        }
        if (WEXITSTATUS(statusw) != 0) {
            std::remove(htmlFilename);
            //res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            res.status = 404;
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid file uanable transform 4 [%d][%i]", WEXITSTATUS(statusw), statusw);
            W.abort();
            return;
        }
        std::ifstream is(pdfFilename, std::ios::binary);
        std::vector<char> data = std::vector<char>(std::istreambuf_iterator<char>(is), std::istreambuf_iterator<char>());
        is.close();
        res.set_content(data.data(), data.size(), "application/pdf");
        std::remove(htmlFilename);
        std::remove(pdfFilename);
        W.commit();
}

bool Pdf::create(AMSvr::AMSvrDB &db, pqxx::work &W, const httplib::Request &req, httplib::Response &res, AMSvrSessionForTunnel &sess, std::string sdoctype, std::string sdocument)
{
    int docId = 0;
    std::size_t docIdLen = -1;
    int typeId = 0;
    std::size_t typeIdLen = -1;
    try {
        docId = std::stoi(sdocument, &docIdLen);
        typeId = std::stoi(sdoctype, &typeIdLen);
    } catch (std::exception e) {
        res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid create pdf data 1");
        W.abort();
        return false;
    }
    if (docId == 0 || sdocument.size() != docIdLen || typeId == 0 || sdoctype.size() != typeIdLen) {
        res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid create pdf data 2");
        W.abort();
        return false;
    }
    CPdfs doc;
    nlohmann::json j;
    AMSvr::AMSvrStatus status;
    switch(typeId) {
        case pdft_medical_inspection:status=creaMedicalCheck(db, W, req, docId, j);break;
        case pdft_medical_inspection_color:status = creaMedicalCheck(db, W, req, docId, j);break;
        default:
            res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
            AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid create pdf type 1");
            W.abort();
            return false;
    };
    if (status != AMSvr::AMSvrStatus::OK) {
        nlohmann::json j2;
        j2["status"] = status;
        j2["dbMessage"] = "";
        res.set_content(j2.dump(), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid create pdf invalid status from generator");
        W.abort();
        return false;
    }
    CPdfTypes ctype(typeId);
    if (!ctype.load(db, W)) {
        res.set_content(std::string("{\"status\": 1}"), "application/json; charset=utf-8");
        AMLog::AMLoggerSvr::Log(req.get_param_value("_customer"), req.get_header_value(X_REMOTE_IP), AMLog::Info, "Invalid create pdf type 2");
        W.abort();
        return false;
    }
    doc.document = docId;
    doc.type = typeId;
    doc.data = j.dump();
    doc.classtype = ctype.classtype.get();
    doc.modified_by = sess.userId;
    time_t t = time(nullptr);
    doc.modified_at = AMCore::AMDatetimeus(*gmtime(&t));
    doc.save(db, W);
    W.commit();
    return true;
}

