/* automatically generated file, do not edit. */

#include "CRiskFactorsWorkingConditions.h"  

CRiskFactorsWorkingConditions::CRiskFactorsWorkingConditions(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      profession(),
      profession_(),
      rFWorkingCond(),
      rFWorkingCondName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };
    
CRiskFactorsWorkingConditions::CRiskFactorsWorkingConditions()
    : AMSvr::AMSvrDbObject<int>(),
      profession(),
      profession_(),
      rFWorkingCond(),
      rFWorkingCondName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CRiskFactorsWorkingConditions::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CRiskFactorsWorkingConditions>(
    "dbo.risk_factors_working_conds",
    {
        {&CRiskFactorsWorkingConditions::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CRiskFactorsWorkingConditions::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&CRiskFactorsWorkingConditions::profession_, "profession_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_"},
        {&CRiskFactorsWorkingConditions::rFWorkingCond, "r_f_working_cond", AMCore::AMDataType_enum, {}, nullptr},
        {&CRiskFactorsWorkingConditions::rFWorkingCondName, "r_f_working_cond_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbe.risk_factors_working_conds rfwc ON rfwc.id = t.r_f_working_cond"}, "rfwc.title AS r_f_working_cond_name"},
        {&CRiskFactorsWorkingConditions::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CRiskFactorsWorkingConditions::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CRiskFactorsWorkingConditions::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CRiskFactorsWorkingConditions::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CRiskFactorsWorkingConditions::context()
{
    return *g_context;
}

bool CRiskFactorsWorkingConditionsMap::loadCRiskFactorsWorkingConditions(AMSvr::AMSvrDB &db, pqxx::work &W, int profession)
{
    std::ostringstream sql;
    CRiskFactorsWorkingConditions::loadSelect(sql, "dbo.risk_factors_working_conds");
    sql << " WHERE profession = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, profession));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CRiskFactorsWorkingConditions rfwc;
            rfwc.loadFromData(it);
            insert({rfwc.rFWorkingCond, rfwc});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}