/* automatically generated file, do not edit. */

#ifndef CEMPLOYEROSVCS_H
#define CEMPLOYEROSVCS_H

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CEmployerOsvcs: public AMSvr::AMSvrDbObject<int>
{
public:
    CEmployerOsvcs();
    CEmployerOsvcs(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<bool, int> isOsvc;
    AMSvr::AMSvrProp<SAWString, int> addressOsvc;
    AMSvr::AMSvrProp<std::tm, int> birthOsvc;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CEMPLOYEROSVCS_H