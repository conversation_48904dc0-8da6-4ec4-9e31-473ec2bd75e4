//
// Created by <PERSON><PERSON><PERSON> on 21.1.24.
//

#ifndef SAW_PERSONALRISKS_H
#define SAW_PERSONALRISKS_H

#include "svr/AMSvrDB.h"
#include "svr/AMSvrSessionForTunnel.h"
#include "CPeople.h"

class PersonalRisks{
public:
    static void insertOccupationalHazard(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res);
    static bool deleteOccupationalHazard(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string id);
    static void insertRFWorkingCond(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res);
    static bool deleteRFWorkingCond(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string id);
    static void insertLegalReq(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res);
    static bool deleteLegalReq(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, std::string id);

    static void insertPerson(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, const httplib::Request &req, httplib::Response& res);
    static void updatePerson(AMSvr::AMSvrDB &db, pqxx::work &W, AMSvrSessionForTunnel &sess, CPeople &person, const httplib::Request &req, httplib::Response& res);
};


#endif //SAW_PERSONALRISKS_H
