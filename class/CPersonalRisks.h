/* automatically generated file, do not edit. */

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CPersonalRisks: public AMSvr::AMSvrDbObject<int>
{
public:
    CPersonalRisks();
    CPersonalRisks(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> person;
    AMSvr::AMSvrProp<std::tm, int> appear;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> disappear;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> occupHazard;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> rFWorkingCond;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> legalReq;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

class CPersonalRisksMap: public std::map<SAWEnum, CPersonalRisks>
{
public:
    bool loadCPersonalRisks(AMSvr::AMSvrDB &db, pqxx::work &W, int person);
    std::map<SAWEnum, CPersonalRisks>::iterator findByRiskActive(AMCore::AMNullable<SAWEnum> occupHazard, AMCore::AMNullable<SAWEnum> rFWorkingCond, AMCore::AMNullable<SAWEnum> legalReq);
    std::map<SAWEnum, CPersonalRisks>::iterator findByRiskInactive(AMCore::AMNullable<SAWEnum> occupHazard, AMCore::AMNullable<SAWEnum> rFWorkingCond, AMCore::AMNullable<SAWEnum> legalReq);
};
