/* automatically generated file, do not edit. */

#include "CWorkingModes.h"  

CWorkingModes::CWorkingModes(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title()
    {           
    };
    
CWorkingModes::CWorkingModes()
    : AMSvr::AMSvrDbObject<int>(),
      title()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CWorkingModes::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CWorkingModes>(
    "dbe.working_modes",
    {  
        {&CWorkingModes::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CWorkingModes::title, "title", AMCore::AMDataType_string, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CWorkingModes::context()
{
    return *g_context;
}
