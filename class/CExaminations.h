/* automatically generated file, do not edit. */

#ifndef CEXAMINATIONS_H
#define CEXAMINATIONS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"
#include "nlohmann/json.hpp"
#include "COccupationalHazardsEnum.h"
#include "CRiskFactorsWorkingConditionsEnum.h"
#include "CLegalRequirementsForMedicalFitnessEnum.h"

class CExaminations: public AMSvr::AMSvrDbObject<int>
{
public:
    CExaminations();
    CExaminations(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> person__;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> personName;
    //AMSvr::AMSvrProp<SAWEnum, int> person_;
    AMSvr::AMSvrProp<int, int> medCheck;
    AMSvr::AMSvrProp<SAWEnum, int> exaType;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> occupHazard;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> rFWorkingCond;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> legalReq;
    AMSvr::AMSvrProp<bool, int> automatic;
    AMSvr::AMSvrProp<bool, int> pass;
    AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> exaDate;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> modifiedByName;
    //std::string modifiedByName;
    int bigTableId;
    std::tm plannedDate;
protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;
};

inline void to_json(nlohmann::ordered_json &j, const CExaminations &s)
{
    //{"id","person__","person_name","med_check","exa_type","occup_hazard","r_f_working_cond","legal_req","automatic","pass","exa_date","note","modified_by","modified_at","modified_by_name"}
    j = nlohmann::json::array( {
        s.id.get(),
        s.person__.get()
        });
    if (s.personName.get().isNull() ) {j.push_back(nullptr);} else {j.push_back((std::string)s.personName.get().get());};
    j.push_back(s.medCheck.get());
    j.push_back(s.exaType.get());
    if (s.occupHazard.get().isNull() ) {j.push_back(nullptr);} else {j.push_back(s.occupHazard.get().get());};
    if (s.rFWorkingCond.get().isNull() ) {j.push_back(nullptr);} else {j.push_back(s.rFWorkingCond.get().get());};
    if (s.legalReq.get().isNull() ) {j.push_back(nullptr);} else {j.push_back(s.legalReq.get().get());};
    j.push_back(s.automatic.get());
    j.push_back(s.pass.get());
    if (s.exaDate.get().isNull() ) {j.push_back(nullptr);} else {j.push_back(s.exaDate.toString());};
    j.push_back((std::string)s.note.get());
    j.push_back(s.modifiedBy.get());
    j.push_back(s.modifiedAt.toString());
    j.push_back(s.modifiedByName.get());
}

class CExaminationsMap: public std::map<int, CExaminations>
{
public:
    bool loadCExaminations(AMSvr::AMSvrDB &db, pqxx::work &W, int medicalCheck);
    bool loadCExaminationsManualOnly(AMSvr::AMSvrDB &db, pqxx::work &W, int medicalCheck);
    std::list<std::string> reasonsForExamination(SAWEnum exaType, AMSvr::AMSvrDB &db, pqxx::work &W ,COccupationalHazardsEnumMap &ohMap, CRiskFactorsWorkingConditionsEnumMap &rfMap, CLegalRequirementsForMedicalFitnessEnumMap &lrMap);
};

#endif //CEXAMINATIONS_H
