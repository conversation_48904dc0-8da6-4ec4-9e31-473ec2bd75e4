//
// Created by <PERSON><PERSON><PERSON><PERSON> on 24.11.2024.
//

#include "SAWMail.h"
#include <strstream>
#include "poco/Net/include/Poco/Net/StringPartSource.h"
#include "poco/Net/include/Poco/Net/FilePartSource.h"
#include "poco/Net/include/Poco/Net/MailMessage.h"
#include "poco/Net/include/Poco/Net/MediaType.h"
#include "poco/NetSSL_OpenSSL/include/Poco/Net/SecureSMTPClientSession.h"
#include "poco/NetSSL_OpenSSL/include/Poco/Net/SSLManager.h"
#include "poco/NetSSL_OpenSSL/include/Poco/Net/AcceptCertificateHandler.h"
#include "amloglite/AMLoggerSvr.h"

void SAWMail::sendMail(Poco::Net::MailMessage &msg, const std::string &customer, const std::string &xrealip)
{
    Poco::SharedPtr<Poco::Net::InvalidCertific<PERSON>> pCert = new Poco::Net::AcceptCertificateHandler(false);
    Poco::Net::Context::Ptr pContext = new Poco::Net::Context( Poco::Net::Context::TLSV1_2_CLIENT_USE, "", "", "",  Poco::Net::Context::VERIFY_NONE, 9, false, "ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH");
    Poco::Net::SSLManager::instance().initializeClient(0, pCert, pContext);

    try {
        Poco::Net::SecureSMTPClientSession smtp ("smtp.seznam.cz", 587);
        smtp.login();
        if (smtp.startTLS(pContext)) {
            smtp.login(Poco::Net::SecureSMTPClientSession::LoginMethod::AUTH_PLAIN, "<EMAIL>", "mXf92Kr+tfYMq_t");
            smtp.sendMessage(msg);
        } else {
            AMLog::AMLoggerSvr::Log(customer, xrealip, AMLog::Error, "WAR Email wan not send...TLS error");
        }
        smtp.close();
    } catch (Poco::Exception &e) {
        AMLog::AMLoggerSvr::Log(customer, xrealip, AMLog::Error, "WAR Email Exception %s-%s [%s]", e.name(), e.message().c_str(), e.what());
    }
}

void SAWMail::generalNewUser(
    std::string mail,
    std::string subject,
    std::string domain,
    std::string userName,
    std::string password,
    std::string pin
) {
    std::string viewDomain = domain;
    std::size_t pos = viewDomain.find('.');
    while (pos != std::string::npos) {
        viewDomain = viewDomain.replace(pos, 1, ".<wbr/>");
        pos = viewDomain.find(pos+7, '.');
    }

    Poco::Net::MailMessage msg;
    msg.addRecipient(Poco::Net::MailRecipient(Poco::Net::MailRecipient::PRIMARY_RECIPIENT, mail));
    msg.setSender("Agent SAW<<EMAIL>>");
    msg.setSubject(Poco::Net::MailMessage::encodeWord(subject));

    Poco::Net::MediaType mediaType("multipart", "related");
    mediaType.setParameter("type", "text/html");
    msg.setContentType(mediaType);

    std::ostringstream html;
    html << R"SS(<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <title>)SS" << subject << R"SS(</title>
</head>
<body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f0f0f0; color: #333;">

<div style="width: 100%; max-width: 980px; height: auto; overflow: hidden; margin: 40px auto 0 auto;">
    <img src="cid:safelinepng" alt="Bezpečnostní šrafování" style="width: 100%; height: auto; display: block;">
</div>

<div style="max-width: 980px; margin: 0px auto; background: white; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
    <div style="background-color:#86BDFF !important; color:#000049; padding:10px; text-align:center;">
        <img src="cid:sawapplogopng" alt="SAWAPP.cloud Logo" style="max-width: 180px; margin-bottom: 0px;">
        <h1 style="font-size: min(9vw, 44px); text-transform: uppercase;">Vítejte v SAWAPP.cloud!</h1>
        <h2 style="font-size: min(6.5vw, 32px); text-transform: uppercase;">Aplikaci ke správě BOZP</h2>
    </div>

    <div style="width: 100%; max-width: 980px; height: auto; overflow: hidden; margin: 0 auto;">
        <img src="cid:safelinepng" alt="Bezpečnostní šrafování" style="width: 100%; height: auto; display: block;">
    </div>

    <div style="padding: 20px; background-color: white; line-height: 1.6;">
        <div style="padding: 20px; background-color: #fffacd; line-height: 1.6;">
            <h3 style="font-size: min(5.5vw, 24px); text-transform: uppercase; margin-bottom: 30px;">Vaše přihlašovací údaje jsou:</h3>
            <div style="padding-left: 20px;">
                <p>Webová adresa vaší SAWAPP: <span style="font-family: 'Source Code Pro', monospace; color: black; font-weight: bold;">)SS" << viewDomain << R"SS(</span></p>
                <p>Uživatelské jméno: <span style="font-family: 'Source Code Pro', monospace; color: black; font-weight: bold;">)SS" << userName << R"SS(</span></p>
                <p>Heslo: <span style="font-family: 'Source Code Pro', monospace; color: red; font-weight: bold;">)SS" << password << R"SS(</span></p>
                <p>Jednorázový PIN: <span style="font-family: 'Source Code Pro', monospace; color: green; font-weight: bold;">)SS" << pin << R"SS(</span></p>
            </div>
        </div>
    </div>
    <div style="padding: 0px 40px 40px 40px; background-color: white; line-height: 1.4; font-size: 14px;">
        <h3 style="font-size: 20px; text-transform: uppercase; margin-bottom: 30px;">Pokyny pro první přihlášení do aplikace</h3>
        <p>Při prvním přihlášení se přihlásíte pomocí svého <b>uživatelského jména</b> a <b>hesla</b> spojeného s <b>PIN kódem</b>. Zadávejte bez mezer, ve formátu <b>"<span style="font-family: 'Source Code Pro', monospace; color: red; font-weight: bold;">heslo</span><span style="font-family: 'Source Code Pro', monospace; color: green; font-weight: bold;">PIN</span>"</b>.<br><br>
            <i>Příklad: Pokud je vaše vygenerované heslo "<span style="font-family: 'Source Code Pro', monospace; color: red; font-weight: bold;">VyGeneRov4n3Heslo</span>" a váš PIN kód je "<span style="font-family: 'Source Code Pro', monospace; color: green; font-weight: bold;">1234</span>", zadáte: "<span style="font-family: 'Source Code Pro', monospace; color: red; font-weight: bold;">VyGeneRov4n3Heslo</span><span style="font-family: 'Source Code Pro', monospace; color: green; font-weight: bold;">1234</span>".</i><br><br>
            Po prvním přihlášení budete vyzváni ke změně hesla.<br>
            Dokud si nenastavíte vlastní heslo, vždy používejte formát hesloPIN.</p>
        <a href="https://)SS" << domain << R"SS(" style="background-color: #4CAF50; color: white; margin-top: 10px; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; font-weight: bold; display: inline-block;">Přihlásit se</a>
    </div>
    <div style="text-align: center; margin: 20px 0;">
        <p><b>Děkujeme, že jste se rozhodli využít aplikaci SAWAPP.cloud.</b></p>
        <p>Můžete nás sledovat na sociálních sítích nebo nás kontaktovat kdykoliv potřebujete pomoc:</p>
        <a href="https://www.facebook.com/sawapp.cloud" style="margin-right: 10px; display: inline-block;"><img src="cid:fbicpng" alt="Facebook" style="width: 32px; height: 32px; vertical-align: middle;" class="social-icon"></a>
        <a href="https://www.linkedin.com/company/*********/" style="margin-right: 10px; display: inline-block;"><img src="cid:inicpng" alt="LinkedIn" style="width: 32px; height: 32px; vertical-align: middle;" class="social-icon"></a>
        <a href="https://www.youtube.com/@SAWAPP-cloud" style="margin-right: 10px; display: inline-block;"><img src="cid:yticpng" alt="YouTube" style="width: 32px; height: 32px; vertical-align: middle;" class="social-icon"></a>
        <p style="margin: 10px 0; font-size: 14px;">Email: <a href="mailto:<EMAIL>"><EMAIL></a> | Tel: <a href="tel:+420730732751">+*********** 751</a></p>
    </div>
    <div style="text-align: center; padding: 12px; background-color: #b8fbb8; font-size: 12px; color: #333;">
        <p>&copy; 2025 SAWAPP.cloud, Všechna práva vyhrazena.<br>
            Více informací na webu <a href="https://cz.sawapp.cloud" style="color: #333;">SAWAPP.cloud</a></p>
    </div>
</div>
</body>
</html>

)SS";
    msg.addPart("", new Poco::Net::StringPartSource(html.str(), "text/html"), Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_QUOTED_PRINTABLE);

    Poco::Net::FilePartSource *imagelg = new Poco::Net::FilePartSource("data/SAWAPP-logo.png", "image/png");
    imagelg->headers().add("Content-ID", "<sawapplogopng>");
    msg.addPart("", imagelg, Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_BASE64);

    Poco::Net::FilePartSource *imageln = new Poco::Net::FilePartSource("data/safeline.png", "image/png");
    imageln->headers().add("Content-ID", "<safelinepng>");
    msg.addPart("", imageln, Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_BASE64);

    Poco::Net::FilePartSource *imageyt = new Poco::Net::FilePartSource("data/yt-ic.png", "image/png");
    imageyt->headers().add("Content-ID", "<yticpng>");
    msg.addPart("", imageyt, Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_BASE64);

    Poco::Net::FilePartSource *imagein = new Poco::Net::FilePartSource("data/in-ic.png", "image/png");
    imagein->headers().add("Content-ID", "<inicpng>");
    msg.addPart("", imagein, Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_BASE64);

    Poco::Net::FilePartSource *imagefb = new Poco::Net::FilePartSource("data/fb-ic.png", "image/png");
    imagefb->headers().add("Content-ID", "<fbicpng>");
    msg.addPart("", imagefb, Poco::Net::MailMessage::CONTENT_INLINE, Poco::Net::MailMessage::ENCODING_BASE64);

    sendMail(msg, domain , "127.0.0.1");
}

void SAWMail::afterInstall(
    std::string mail,
    std::string domain,
    std::string userName,
    std::string password
    )
{
      generalNewUser(
          mail,
          "Začněte s aplikací SAWAPP.cloud",
          domain,
          userName,
          password,
          "PIN kód jste zadali při registraci."
      );
}

void SAWMail::afterResetPassword(
    std::string mail,
    std::string domain,
    std::string userName,
    std::string password
    )
{
    generalNewUser(
        mail,
        "Reset hesla v SAWAPP.cloud",
        domain,
        userName,
        password,
        "PIN Vám dá Váš administrátor aplikace SAWAPP."
    );
}

void SAWMail::afterCreateUser(
    std::string mail,
    std::string domain,
    std::string userName,
    std::string password
    )
{
    generalNewUser(
        mail,
        "Byl Vám vytvořen účet v SAWAPP.cloud",
        domain,
        userName,
        password,
        "PIN Vám dá Váš administrátor aplikace SAWAPP."
    );
}