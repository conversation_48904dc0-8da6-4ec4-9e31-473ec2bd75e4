/* automatically generated file, do not edit. */

#include "CExaminations.h"  

CExaminations::CExaminations(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      person__(),
      personName(),
      //person_(),
      medCheck(),
      exaType(),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      automatic(),
      pass(),
      exaDate(),
      note(),
      modifiedBy(),
      modifiedAt(),
      modifiedByName(),
      bigTableId(AMUI::DEFAULT_INT_ID),
      plannedDate{0,0,0,0,0,0,0, 0, 0,0LL, nullptr}
    {           
    };
    
CExaminations::CExaminations()
    : AMSvr::AMSvrDbObject<int>(),
      person__(),
      personName(),
      //person_(),
      medCheck(),
      exaType(),
      occupHazard(),
      rFWorkingCond(),
      legalReq(),
      automatic(),
      pass(),
      exaDate(),
      note(),
      modifiedBy(),
      modifiedAt(),
      modifiedByName(),
      bigTableId(AMUI::DEFAULT_INT_ID),
      plannedDate{0,0,0,0,0,0,0, 0, 0,0LL, nullptr}
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CExaminations::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CExaminations>(
    "dbo.examinations",
    {  
        {&CExaminations::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminations::person__, "person__", AMCore::AMDataType_integer, {"LEFT JOIN dbo.medical_checks mc ON mc.id = t.med_check"}, "mc.person AS person__"},
        {&CExaminations::personName, "person_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.medical_checks mc ON mc.id = t.med_check","LEFT JOIN dbo.people p ON p.id = mc.person", "LEFT JOIN dbo.user_degrees udb ON udb.id = p.degree_before", "LEFT JOIN dbo.user_degrees uda ON uda.id = p.degree_after"}, "CASE WHEN udb.degree <> '' THEN udb.degree || ' ' ELSE '' END || p.name || ' ' || p.surname || CASE WHEN uda.degree <> '' THEN ' ' || uda.degree ELSE '' END AS person_name"},
        //{&CExaminations::person_, "person_", AMCore::AMDataType_name_enum, {}, nullptr},
        {&CExaminations::medCheck, "med_check", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminations::exaType, "exa_type", AMCore::AMDataType_enum, {}, nullptr},
        {&CExaminations::occupHazard, "occup_hazard", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminations::rFWorkingCond, "r_f_working_cond", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminations::legalReq, "legal_req", AMCore::AMDataType_enum_nullable, {}, nullptr},
        {&CExaminations::automatic, "automatic", AMCore::AMDataType_bool, {}, nullptr},
        {&CExaminations::pass, "pass", AMCore::AMDataType_bool, {}, nullptr},
        {&CExaminations::exaDate, "exa_date", AMCore::AMDataType_date_nullable, {}, nullptr},
        {&CExaminations::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CExaminations::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminations::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        {&CExaminations::modifiedByName, "modified_by_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},

    }
    );

AMSvr::AMSvrDbObjectContext<int> &CExaminations::context()
{
    return *g_context;
}

bool CExaminationsMap::loadCExaminations(AMSvr::AMSvrDB &db, pqxx::work &W, int medicalCheck)
{
    std::ostringstream sql;
    CExaminations::loadSelect(sql, "dbo.examinations");
    sql << " WHERE med_check = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, medicalCheck));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CExaminations er;
            er.loadFromData(it);
            insert({er.id, er});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}

bool CExaminationsMap::loadCExaminationsManualOnly(AMSvr::AMSvrDB &db, pqxx::work &W, int medicalCheck)
{
    std::ostringstream sql;
    CExaminations::loadSelect(sql, "dbo.examinations");
    sql << " WHERE med_check = $1 AND automatic = false";
    try {
        pqxx::result R(db.query(sql.str(), W, medicalCheck));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CExaminations er;
            er.loadFromData(it);
            insert({er.id, er});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}

std::list<std::string> CExaminationsMap::reasonsForExamination(SAWEnum exaType, AMSvr::AMSvrDB &db, pqxx::work &W ,COccupationalHazardsEnumMap &ohMap, CRiskFactorsWorkingConditionsEnumMap &rfMap, CLegalRequirementsForMedicalFitnessEnumMap &lrMap)
{
    std::list<std::string> result;
    for (auto it: *this) {
        if (it.second.exaType.get() == exaType) {
            if (!it.second.occupHazard.get().isNull()) {
                if (!ohMap.loaded) {
                    ohMap.loadCOccupationalHazardsEnum(db, W);
                }
                auto itOh = ohMap.find(it.second.occupHazard.get().get());
                if (itOh == ohMap.end()) {
                    result.push_back("Neznámý důvod");
                } else {
                    result.push_back(itOh->second.title.get() + " " + itOh->second.description.get());
                }
            } else if (!it.second.rFWorkingCond.get().isNull()) {
                if (!rfMap.loaded) {
                    rfMap.loadCRiskFactorsWorkingConditionsEnum(db, W);
                }
                auto itRf = rfMap.find(it.second.rFWorkingCond.get().get());
                if (itRf == rfMap.end()) {
                    result.push_back("Neznámý důvod");
                } else {
                    result.push_back(itRf->second.title.get() + " " + itRf->second.description.get());
                }
            } else if (!it.second.legalReq.get().isNull()) {
                if (!lrMap.loaded) {
                    lrMap.loadCLegalRequirementsForMedicalFitnessEnum(db, W);
                }
                auto itLr = lrMap.find(it.second.legalReq.get().get());
                if (itLr == lrMap.end()) {
                    result.push_back("Neznámý důvod");
                } else {
                    result.push_back(itLr->second.title.get() + " " + itLr->second.description.get());
                }
            } else {
                result.insert(result.begin(), "Základní vyšetření");
            }
        }
    }
    return result;
}
