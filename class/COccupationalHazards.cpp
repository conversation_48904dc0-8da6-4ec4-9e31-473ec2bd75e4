/* automatically generated file, do not edit. */

#include "COccupationalHazards.h"  

COccupationalHazards::COccupationalHazards(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      profession(),
      profession_(),
      riskEnum(),
      risk11(),
      riskName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };
    
COccupationalHazards::COccupationalHazards()
    : AMSvr::AMSvrDbObject<int>(),
      profession(),
      profession_(),
      riskEnum(),
      risk11(),
      riskName(),
      modifiedBy(),
      modifiedAt()
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *COccupationalHazards::g_context = AMSvr::AMSvrDbObjectContext<int>::create<COccupationalHazards>(
    "dbo.occupational_hazards",
    {
        {&COccupationalHazards::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&COccupationalHazards::profession, "profession", AMCore::AMDataType_integer, {}, nullptr},
        {&COccupationalHazards::profession_, "profession_", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbo.professions prof ON prof.id = t.profession"}, "prof.name AS profession_"},
        {&COccupationalHazards::riskEnum, "risk_enum", AMCore::AMDataType_enum, {}, nullptr},
        {&COccupationalHazards::risk11, "risk11", AMCore::AMDataType_string, {}, nullptr},
        {&COccupationalHazards::riskName, "risk_name", AMCore::AMDataType_string_nullable, {"LEFT JOIN dbe.occupational_hazards pri ON pri.id = t.risk_enum"}, "pri.description AS risk_name"},
        {&COccupationalHazards::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&COccupationalHazards::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&COccupationalHazards::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&COccupationalHazards::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &COccupationalHazards::context()
{
    return *g_context;
}

bool COccupationalHazardsMap::loadCOccupationalHazards(AMSvr::AMSvrDB &db, pqxx::work &W, int profession)
{
    std::ostringstream sql;
    COccupationalHazards::loadSelect(sql, "dbo.occupational_hazards");
    sql << " WHERE profession = $1";
    try {
        pqxx::result R(db.query(sql.str(), W, profession));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            COccupationalHazards oh;
            oh.loadFromData(it);
            insert({oh.riskEnum, oh});
        }
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}