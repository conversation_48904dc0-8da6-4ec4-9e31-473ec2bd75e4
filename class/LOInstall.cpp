//
// Created by <PERSON><PERSON><PERSON> on 24.10.23.
//

#include "LOInstall.h"
#include "nlohmann/json.hpp"
#include "generated/accident_insurance_corps.h"
#include "generated/sex.h"
#include "class/CEmployerRelationships.h"
#include "class/CCitizenships.h"
#include "svr/AMSvrUser.h"
#include <sstream>
#include <fstream>
#include "SAWMail.h"
#include "svr/AMSvrConfig.h"


std::mutex LOInstall::g_mutex;

#define UNCHECKTEXT(text) "<div class=\"ct-div-block oxel_iconlist__row\"><div class=\"ct-fancy-icon oxel_iconlist__row__icon\"><svg><use xlink:href=\"#FontAwesomeicon-square-o\"></use></svg></div><div class=\"ct-text-block oxel_iconlist__row__label\">" text "</div></div>"
#define CHECKTEXT(text) "<div class=\"ct-div-block oxel_iconlist__row\"><div class=\"ct-fancy-icon oxel_iconlist__row__icon\"><svg><use xlink:href=\"#FontAwesomeicon-check-square-o\"></use></svg></div><div class=\"ct-text-block oxel_iconlist__row__label\">" text "</div></div>"

void LOInstall::run()
{
    std::size_t outputTextSize = 0;
    char buff[512];
    {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Zpracovávám formulář...");
    }
    InstallRequest *ir = parseRequest();
    {
        std::lock_guard<std::mutex> g(g_mutex);
        if (ir) {
            output = CHECKTEXT("Formulář zpracován");
            outputTextSize = output.get().length();
            output.get() += UNCHECKTEXT("Vytvářím databázi...");
            step = 1;
            m_maxSteps = 1/*form*/+ 1/*db creat*/ + 630/10+1/*tables*/ + (6280 - 6000)/10 + 1/*enumerations*/ + (ir->dSize != 1 ? ((8030 - 8010)/10 + 1) : 0) /*data*/ +1 /*data*/;
        } else {
            output = UNCHECKTEXT("Formulář obsahuje chyby!");
            step = -1;
            return;
        }
    }
    bool rv = createDatabase(*ir);
    if (rv) {
        std::lock_guard<std::mutex> g(g_mutex);
        output = output.get().substr(0,outputTextSize) + CHECKTEXT("Databáze vytvořena");
        outputTextSize = output.get().length();
        output.get() += UNCHECKTEXT("Vytvářím tabulky...");
        step = 2;
    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Nepodařilo se vytvořit databázi. Už existuje nebo je chyba ve jméně domény.");
        step = -1;
        delete ir;
        return;
    }
    AMSvr::AMSvrDB db(AMSvr::AMSvrDB::domain2databaseName(ir->domain.get()));
    pqxx::work W(db.conn());
    rv = true;
    for(int i = 0;i <= 630; i+=10) {
        std::string sql = decipherFile(i);
        try {
            db.query(sql, W);
        } catch (std::exception e) {
            rv = false;
            break;
        }
        {
            std::lock_guard<std::mutex> g(g_mutex);
            step = step.get() + 1;
        }
    }
    if (rv) {
        std::lock_guard<std::mutex> g(g_mutex);
        output = output.get().substr(0,outputTextSize) + CHECKTEXT("Tabulky vytvořeny");
        outputTextSize = output.get().length();
        output.get() += UNCHECKTEXT("Plním tabulky...");
    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Nepodařilo se vytvořit tabulky. Kontaktuje technickou podporu.");
        step = -1;
        delete ir;
        W.abort();
        return;
    }
    rv = true;
    for(int i = 6000;i <= 6280; i+=10) {
        std::string sql = decipherFile(i);
        try {
            db.query(sql, W);
        } catch (std::exception e) {
            rv = false;
            break;
        }
        {
            std::lock_guard<std::mutex> g(g_mutex);
            step = step.get() + 1;
        }
    }
    if (rv) {
        std::lock_guard<std::mutex> g(g_mutex);
        output = output.get().substr(0,outputTextSize) + CHECKTEXT("Tabulky naplněny");
        outputTextSize = output.get().length();
        output.get() += UNCHECKTEXT("Zakládám prvního uživatele...");
        step = step.get() + 1;
    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Nepodařilo se naplnit tabulky. Kontaktuje technickou podporu.");
        step = -1;
        delete ir;
        W.abort();
        return;
    }
    W.commit();
    std::string password = AMSvr::AMSvrUser::generatePassword();
    std::string pin = ir->uPasshash.get();
    ir->uPasshash = AMSvr::AMSvrUser::computePasswordHash(password + pin);
    rv = createUser(db, *ir);
    if (rv) {
        std::lock_guard<std::mutex> g(g_mutex);
        output = output.get().substr(0,outputTextSize) + CHECKTEXT("První uživatel vytvořen");
        outputTextSize = output.get().length();
        output.get() += UNCHECKTEXT("Dokončuji tabulky...");
        step = step.get() + 1;

    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Nepodařilo se vytvořit prvního uživatele. Kontaktuje technickou podporu.");
        step = -1;
        delete ir;
        return;
    }
    rv = true;
    pqxx::work W4(db.conn());
    for(int i = 8000;i <= 8000; i+=10) {
        std::string sql = decipherFile(i);
        try {
            db.query(sql, W4);
        } catch (std::exception e) {
            rv = false;
            break;
        }
        {
            std::lock_guard<std::mutex> g(g_mutex);
            step = step.get() + 1;
        }
    }
    if (rv) {
        std::lock_guard<std::mutex> g(g_mutex);
        output = output.get().substr(0,outputTextSize) + CHECKTEXT("Tabulky dokončeny");
        if (ir->dSize != 1) {
            outputTextSize = output.get().length();
            output.get() += UNCHECKTEXT("Dokončuji tabulky...");
        }
        step = step.get() + 1;
    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        output = UNCHECKTEXT("Nepodařilo se dokončit tabulky. Kontaktuje technickou podporu.");
        step = -1;
        delete ir;
        return;
    }
    if (ir->dSize != 1) {
        rv = true;
        for(int i = 8010;i <= 8030; i+=10) {
            if (i == 8020 && ir->dSize == 100) {
                continue;
            }
            std::string sql = decipherFile(i);
            try {
                db.query(sql, W4);
            } catch (std::exception e) {
                rv = false;
                break;
            }
            {
                std::lock_guard<std::mutex> g(g_mutex);
                step = step.get() + 1;
            }
        }
        if (rv) {
            std::lock_guard<std::mutex> g(g_mutex);
            output = output.get().substr(0,outputTextSize) + CHECKTEXT("Testovací data vytvořena");
            std::time_t tt = time(nullptr);
            finished = *gmtime(&tt);
            //step = step.get() + 1;
        } else {
            std::lock_guard<std::mutex> g(g_mutex);
            output = UNCHECKTEXT("Nepodařilo se vytvořit testovací data. Kontaktuje technickou podporu.");
            step = -1;
            delete ir;
            return;
        }
    } else {
        std::lock_guard<std::mutex> g(g_mutex);
        std::time_t tt = time(nullptr);
        finished = *gmtime(&tt);
    }

    W4.commit();
    AMSvr::AMSvrDB dbsaw("_saw");
    pqxx::work W2(dbsaw.conn());
    try {
        save(dbsaw, W2);
        W2.commit();
    } catch (std::exception e) {

    }

    SAWMail::afterInstall(
        ir->uEmail,
        ir->domain.get(),
        ir->uUsername.get(),
        password);
    delete ir;
}

LOInstall::LOInstall(std::string hostname, int hostPort)
    : LongOpItem(),
    m_maxSteps(100)
{
    task = loq_install;
    m_hostname = hostname;
    m_hostPort = hostPort;
}

LOInstall::LOInstall(const LOInstall &right)
    : LongOpItem(lockCopyContruct(right)),
    m_maxSteps(right.m_maxSteps),
    m_hostname(right.m_hostname),
    m_hostPort(right.m_hostPort)
{
    g_mutex.unlock();
}

const LOInstall &LOInstall::lockCopyContruct(const LOInstall &right)
{
    g_mutex.lock();
    return right;
}

int LOInstall::maxSteps()
{
    std::lock_guard<std::mutex> g(g_mutex);
    return m_maxSteps;
}

InstallRequest *LOInstall::parseRequest()
{
    InstallRequest *ir = new InstallRequest();
    try {
        size_t index = 0;
        while (true) {
            /* Locate the substring to replace. */
            index = input.get().find("\\\\", index);
            if (index == std::string::npos) break;
            /* Make the replacement. */
            input.get().replace(index, 2, "\\");
            /* Advance index forward so the next iteration doesn't pick it up as well. */
            index += 1;
        }
        nlohmann::json jres = nlohmann::json::parse(input.get().c_str(), input.get().c_str() + input.get().size());
        std::string customer;
        if (jres["Chcete aplikaci na vlastní doméně?"]["label"].get<std::string>() == "ANO") {
            ir->domain = jres["Vaše doména"].get<std::string>();
            ir->ourSubdomain = false;
        } else if (jres["Chcete aplikaci na vlastní doméně?"]["label"].get<std::string>() == "NE") {
            //printf("HHHHHHYYYY subdom=[%s] hosport=[%s] dom=[%s]\n", jres["Vaše subdoména .cz.sawapp.cloud"].get<std::string>().c_str(), hostPort.c_str(), ir->domain.get().c_str());
//#ifdef DEBUG
//            ir->domain = jres["Vaše subdoména .cz.sawapp.cloud"].get<std::string>() + ".saw.zdenekskulinek.cz" + (hostPort.empty() || hostPort == "443" ? "" : ":"+hostPort);
//#else
//            ir->domain = jres["Vaše subdoména .cz.sawapp.cloud"].get<std::string>() + ".cz.sawapp.cloud";
//#endif
            ir->domain = jres["Vaše subdoména .cz.sawapp.cloud"].get<std::string>() + "." + m_hostname + (m_hostPort == 443 ? "" : ":" + std::to_string(m_hostPort));
            //printf("HHHHHHYYYY subdom=[%s] hosport=[%s] dom=[%s]\n", jres["Vaše subdoména .cz.sawapp.cloud"].get<std::string>().c_str(), hostPort.c_str(), ir->domain.get().c_str());

            ir->ourSubdomain = true;
        } else {
            delete ir;
            return nullptr;
        }
        ir->eName = jres["Název zaměstnavatele"].get<std::string>();
        ir->eStreet = jres["Ulice"].get<std::string>();
        std::string s = jres["Číslo popisné"].get<std::string>();
        if (s.empty()) {
            ir->eDescriptiveNum = 1;
        } else {
            ir->eDescriptiveNum = std::stoi(s);
        }
        ir->eOrientationNum = jres["Číslo orientační"].get<std::string>();
        ir->eCity = jres["Město / obec"].get<std::string>();
        ir->eZip = jres["PSČ"].get<std::string>();
        ir->eCountry = 43;
        AMSvr::AMSvrDB db("_saw");
        pqxx::work W(db.conn());
        if (jres["Země"]["label"].get<std::string>() == "Jiná") {
            CCitizenships cc;
            if (!cc.loadByCodeA2(jres["Země - jiná"]["country_code"].get<std::string>(), db, W)) {
                delete ir;
                return nullptr;
            }
            ir->eCountry = cc.id.get();
        }
        s = jres["IČO"].get<std::string>();
        if (s.empty()) {
            ir->eIco = 0;
        } else {
            ir->eIco = std::stoi(s);
        }
        ir->eAccidentInsurance = jres["Pojišťovna pro zákonné pojištění odpovědnosti zaměstnavatele"]["label"].get<std::string>() == "Kooperativa pojišťovna a.s."
            ? SAW_accident_insurance_corps::accident_insurance_kooperativa : SAW_accident_insurance_corps::accident_insurance_generali;
        ir->uDegreeBefore = jres["Titul před"].get<std::string>();
        ir->uName = jres["Křestní jméno"].get<std::string>();
        ir->uSurname = jres["Příjmení"].get<std::string>();
        ir->uDegreeAfter = jres["Titul za"].get<std::string>();
        ir->uSex = jres["Pohlaví"]["label"].get<std::string>() == "muž" ? SAW_sex::male : SAW_sex::female;
        std::string er = jres["Vztah uživatele vůči zaměstnavateli"][0].get<std::string>();
        CEmployerRelationships cer;
        if (!cer.loadByFullName(er, db, W)) {
            delete ir;
            return nullptr;
        }
        ir->uEmployerRelationship = cer.id.get();
        ir->uUsername = jres["Uživatelské jméno"].get<std::string>();
        ir->uEmail = jres["E-mail"].get<std::string>();
        ir->uPasshash = jres["Jednorázový PIN"].get<std::string>();
        std::string dbSize = jres["Vyberte první velikost databáze"]["label"].get<std::string>();
        if (dbSize == "1 záznam (prázdná databáze - vhodné pro ostrou verzi aplikace)") {
            ir->dSize = 1;
        } else if (dbSize == "100 záznamů (malá testovací firma)") {
            ir->dSize = 100;
        } else {
            ir->dSize = 10000;
        }
        ir->cAckNewsletters = jres["Souhlasím se zasíláním obchodních sdělení"]["label"].get<std::string>() == "ANO";
        ir->cEmail = jres["E-mail pro obchodní sdělení"].get<std::string>();
        ir->cAckTesting = jres["Chcete nám pomoci při rozvoji aplikace?"]["label"].get<std::string>() == "ANO, chci, aby byla SAWAPP co nejlepší";
        ir->cMainIndustry = jres["Vaše hlavní činnost je"][0].get<std::string>();
        if (ir->cMainIndustry.get() == "Jiné") {
            ir->cMainIndustry = jres["Uveďte"].get<std::string>();
        }
        std::string employesNum = jres["Pro kolik zaměstnanců chcete aplikaci využívat (odhadněte)?"].get<std::string>();
        ir->cEmployees = employesNum.empty() ? 0 : std::stoi(employesNum);
        ir->cUseApp = jres["Používáte již nějakou aplikaci ke správě agendy BOZP a PO? Jakou?"]["label"].get<std::string>() == "ANO";
        ir->cWhichApp = jres["Jakou?"].get<std::string>();
        ir->cTryAs = jres["Aplikaci SAWAPP chcete vyzkoušet jako"]["label"].get<std::string>();
        if (ir->cTryAs.get() == "Jiná osoba") {
            ir->cTryAs = jres["Vyplňte prosím vaši profesi"].get<std::string>();
        }
        std::vector<std::string> v = jres["Které oblasti BOZP a PO byste pomocí SAWAPP nejraději řídili a spravovali?"]["label"].get<std::vector<std::string>>();
        std::ostringstream ss;
        bool first = true;
        for(auto s: v) {
            if (!first) {
                ss << ',';
            } else {
                first = false;
            }
            ss << s;
        }
        ir->cAreas = ss.str();
        ir->cWishes = jres["Popište nám prosím, pro jaké oblasti nebo úkoly byste SAWAPP rádi využívali"].get<std::string>();
        time_t t = time(nullptr);
        ir->created = *gmtime(&t);
        ir->save(db, W);
        W.commit();
    } catch(std::exception e) {
        printf("install ERROR=%s\n", e.what());
        delete ir;
        return nullptr;
    }
    return ir;
}

bool LOInstall::createDatabase(InstallRequest &ir)
{
    bool r = AMSvr::AMSvrUser::checkDomain(ir.domain.get());
    if (!r) {
        return false;
    }
    AMSvr::AMSvrDB db("postgres");
    try {
        db.nonTransactQuery(std::string("CREATE DATABASE ") + AMSvr::AMSvrDB::domain2databaseName(ir.domain.get()));
    } catch(std::exception e) {
        return false;
    }
    return true;
}

static unsigned char caesarDeCipher[] =
#include "../server_decode.txt"
;

std::string LOInstall::decipherFile(int file_id)
{
    char fname[256];
    snprintf(fname, sizeof(fname) / sizeof(char),"data/%04i", file_id);
    std::ifstream ifs(fname, std::ios::binary);
    std::string content = std::string(std::istreambuf_iterator<char>(ifs), std::istreambuf_iterator<char>());
    unsigned char *b = new unsigned char[content.length() +1 ];
    for(int j = 0; j < content.length() ; j++) {
        b[j] = caesarDeCipher[(unsigned char)content[j]];
    }
    b[content.length()] = '\0';
    std::string rv = (char*)b;
    //printf("FILE %s  [%i]:\n%s\n", fname, content.length(), b);
    delete[] b;
    return rv;
}

bool LOInstall::createUser(AMSvr::AMSvrDB &db, InstallRequest &ir)
{
    pqxx::work W2(db.conn());
    std::string country;
    try {
        pqxx::result R(db.query("SELECT title FROM dbe.citizenships WHERE id = $1", W2, ir.eCountry.get()));
        if (R.size() != 1) {
            return false;
        }
        country = R.begin()->at(0).template as<std::string>();
    } catch (std::exception e) {
        return false;
    }
    std::ostringstream address;
    if (!ir.ePlace.get().empty()) {
        address << ir.ePlace.get() << ": ";
    }
    address << ir.eStreet.get() << ' ' << ir.eDescriptiveNum.get();
    if (!ir.eOrientationNum.get().empty()) {
        address << '/' << ir.eOrientationNum.get();
    }
    address << ", " << ir.eCity.get() << ", " << ir.eZip.get() << ", " << country;
    try {
        db.query("INSERT INTO dbo.employers(id, ico, name, address, accident_insurance, datovka, email, phone, web, registration, note, modified_by) VALUES(DEFAULT, $1, $2, $3, $4, '', '', '', '', '', '', 0)",
            W2, ir.eIco.get(), ir.eName.get(), address.str(), ir.eAccidentInsurance.get() );
    } catch (std::exception e) {
        return false;
    }
    int dgBefore = -1;
    try {
        pqxx::result R(db.query("SELECT id FROM dbo.user_degrees WHERE degree = $1 AND after_name = false", W2, ir.uDegreeBefore.get()));
        if (R.size() == 1) {
            dgBefore = R.begin()->at(0).template as<int>();
        } else {
            pqxx::result R2(db.query("INSERT INTO dbo.user_degrees(degree, after_name, note, modified_by) VALUES($1, false, 'Z požadavku na instalaci.', 0) RETURNING id",W2, ir.uDegreeBefore.get() ));
            dgBefore = R2.begin()->at(0).template as<int>();
        }
    } catch (std::exception e) {
        return false;
    }
    int dgAfter = -1;
    try {
        pqxx::result R(db.query("SELECT id FROM dbo.user_degrees WHERE degree = $1 AND after_name = true", W2, ir.uDegreeAfter.get()));
        if (R.size() == 1) {
            dgAfter = R.begin()->at(0).template as<int>();
        } else {
            pqxx::result R2(db.query("INSERT INTO dbo.user_degrees(degree, after_name, note, modified_by) VALUES($1, true, 'Z požadavku na instalaci.', 0) RETURNING id",W2, ir.uDegreeAfter.get() ));
            dgAfter = R2.begin()->at(0).template as<int>();
        }
    } catch (std::exception e) {
        return false;
    }
    W2.commit();
    pqxx::work W3(db.conn());
    try {
        db.query("SET SESSION_REPLICATION_ROLE TO REPLICA;", W3);
        pqxx::result R3(db.query("INSERT INTO dbo.people(id, degree_before, name, surname, degree_after, sex, email, phone, note, employer, centre, e_relationship, profession, state, leader, modified_by) VALUES("
                "DEFAULT, $1, $2, $3, $4, $5, $6, '', '', 1, 1, $7, 1, 1, 1, 0) RETURNING id",
                W3, dgBefore, ir.uName.get(), ir.uSurname.get(), dgAfter, ir.uSex.get(), ir.uEmail.get(), ir.uEmployerRelationship.get()));
        int id = R3.begin()->at(0).template as<int>();
        db.query("INSERT INTO dbh.people(id, operation, record_id, degree_before, name, surname, degree_after, sex, email, phone, note, employer, centre, e_relationship, profession, state, leader, modified_by) VALUES("
                                 "DEFAULT, 'I', $8, $1, $2, $3, $4, $5, $6, '', '', 1, 1, $7, 1, 1, 1, 0)",
            W3, dgBefore, ir.uName.get(), ir.uSurname.get(), dgAfter, ir.uSex.get(), ir.uEmail.get(), ir.uEmployerRelationship.get(), id);
        db.query("SET SESSION_REPLICATION_ROLE TO DEFAULT;", W3);
        db.query("INSERT INTO dbs.login_attempts (id, attempts) VALUES ("+ std::to_string(id) +" , "+ std::to_string(AMSvr::loginAttempts) + ")", W3);
        db.query("INSERT INTO dbo.users(id, name, roles, passhash, need_update_password, valid, note, modified_by) VALUES(1, $1, '{1}', $2, true, true, '', 0)", W3, ir.uUsername.get(), ir.uPasshash.get());
        time_t t = time(nullptr);
        std::tm tt = *gmtime(&t);
        tt.tm_mon++;
        mktime(&tt);
        char buf[80];
        strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", &tt);
        std::string dmStripped = ir.domain.get();
        if (dmStripped.find(':') != std::string::npos) {
            dmStripped = dmStripped.substr(0, dmStripped.find(':'));
        }
        db.query("INSERT INTO dbo.connected_accounts(id, name, domain, valid_from, valid_until, note, modified_by) VALUES (DEFAULT, $1, $2, $3, $4, '', 0)", W3, ir.eName.get(), dmStripped, buf, buf);
    } catch (std::exception e) {
        return false;
    }
    W3.commit();
    pqxx::work W4(db.conn());
    try {
        db.query("REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_hiearchy;REFRESH MATERIALIZED VIEW CONCURRENTLY dbv.people_check_status;", W4);
    } catch (std::exception e) {
        return false;
    }
    W4.commit();
    return true;
}

