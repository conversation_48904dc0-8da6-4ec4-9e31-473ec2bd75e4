/* automatically generated file, do not edit. */

#include "CEmployerOsvcs.h"  

CEmployerOsvcs::CEmployerOsvcs(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      isOsvc(),
      addressOsvc(),
      birthOsvc(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };
    
CEmployerOsvcs::CEmployerOsvcs()
    : AMSvr::AMSvrDbObject<int>(),
      isOsvc(),
      addressOsvc(),
      birthOsvc(),
      note(),
      modifiedBy(),
      modifiedAt()//,
      //modifiedByName(),
      //modified()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CEmployerOsvcs::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CEmployerOsvcs>(
    "dbo.employer_osvc",
    {  
        {&CEmployerOsvcs::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CEmployerOsvcs::isOsvc, "is_osvc", AMCore::AMDataType_bool, {}, nullptr},
        {&CEmployerOsvcs::addressOsvc, "address_osvc", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployerOsvcs::birthOsvc, "birth_osvc", AMCore::AMDataType_date, {}, nullptr},
        {&CEmployerOsvcs::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CEmployerOsvcs::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CEmployerOsvcs::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        //{&CEmployerOsvcs::modifiedByName, "modified_by_name", AMCore::AMDataType_string, {"LEFT JOIN dbo.users u ON u.id = t.modified_by"}, "u.name AS modified_by_name"},
        //{&CEmployerOsvcs::modified, "modified", AMCore::AMDataType_author_tag, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CEmployerOsvcs::context()
{
    return *g_context;
}
