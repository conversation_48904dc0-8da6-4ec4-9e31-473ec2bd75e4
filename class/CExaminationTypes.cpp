/* automatically generated file, do not edit. */

#include "CExaminationTypes.h"  

CExaminationTypes::CExaminationTypes(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      title(),
      tooltip()//,
      //titleWithTooltip()
    {           
    };
    
CExaminationTypes::CExaminationTypes()
    : AMSvr::AMSvrDbObject<int>(),
      title(),
      tooltip()//,
      //titleWithTooltip()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CExaminationTypes::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CExaminationTypes>(
    "dbe.examination_types",
    {  
        {&CExaminationTypes::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CExaminationTypes::title, "title", AMCore::AMDataType_string, {}, nullptr},
        {&CExaminationTypes::tooltip, "tooltip", AMCore::AMDataType_string, {}, nullptr},
        //{&CExaminationTypes::titleWithTooltip, "title_with_tooltip", AMCore::AMDataType_string, {}, "CASE WHEN tooltip <> '' THEN title || '###' || tooltip ELSE title END AS title_with_tooltip"},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CExaminationTypes::context()
{
    return *g_context;
}

CExaminationTypesMap::CExaminationTypesMap()
    :std::map<int, CExaminationTypes>(),
     loaded(false)
{
}


bool CExaminationTypesMap::loadCExaminationTypes(AMSvr::AMSvrDB &db, pqxx::work &W)
{
    std::ostringstream sql;
    CExaminationTypes::loadSelect(sql, "dbe.examination_types");
    try {
        pqxx::result R(db.query(sql.str(), W));
        clear();
        if (R.begin() == R.end()) {
            return true;
        }
        for (auto it: R) {
            CExaminationTypes et;
            et.loadFromData(it);
            insert({et.id, et});
        }
        loaded = true;
    } catch (const std::exception &e) {
        return false;
    }
    return true;
}