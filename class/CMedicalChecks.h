/* automatically generated file, do not edit. */


#ifndef CMEDICALCHECKS_H
#define CMEDICALCHECKS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CMedicalChecks: public AMSvr::AMSvrDbObject<int>
{
public:
    CMedicalChecks();
    CMedicalChecks(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<int, int> person;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> personName;
    //AMSvr::AMSvrProp<SAWEnum, int> person_;
    AMSvr::AMSvrProp<SAWEnum, int> provider;
    //AMSvr::AMSvrProp<SAWString, int> provider_;
    //AMSvr::AMSvrProp<SAWString, int> employer;
    AMSvr::AMSvrProp<SAWEnum, int> checkType;
    AMSvr::AMSvrProp<bool, int> concurence;
    AMSvr::AMSvrProp<SAWString, int> reason;
    AMSvr::AMSvrProp<SAWString, int> purpose;
    AMSvr::AMSvrProp<std::tm, int> orderDate;
    //AMSvr::AMSvrProp<AMCore::AMNullable<std::tm>, int> performDate;
    AMSvr::AMSvrProp<AMCore::AMNullable<int>, int> privilegedPerson;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> privilegedPersonName;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWEnum>, int> privilegedPerson_;
    AMSvr::AMSvrProp<int, int> appliedPerson;
    //AMSvr::AMSvrProp<AMCore::AMNullable<SAWString>, int> appliedPersonName;
    //AMSvr::AMSvrProp<SAWEnum, int> appliedPerson_;
    AMSvr::AMSvrProp<SAWString, int> medicalInfo;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;
    //AMSvr::AMSvrProp<SAWString, int> modifiedByName;
    //AMSvr::AMSvrProp<AMCore::AMAuthorTag < SAWString >, int> modified;

    bool loadLastEnterOrPeriodical(AMSvr::AMSvrDB &db, pqxx::work &W, int person);
    bool loadLastEnter(AMSvr::AMSvrDB &db, pqxx::work &W, int person);
protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CMEDICALCHECKS_H
