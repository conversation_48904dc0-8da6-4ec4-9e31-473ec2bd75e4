/* automatically generated file, do not edit. */

#include "CPdfTypes.h"  

CPdfTypes::CPdfTypes(int id)
    : AMSvr::AMSvrDbObject<int>(id)
    {           
    };
    
CPdfTypes::CPdfTypes()
    : AMSvr::AMSvrDbObject<int>()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CPdfTypes::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CPdfTypes>(
    "dbe.pdf_types",
    {
        {&CPdfTypes::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CPdfTypes::title, "title", AMCore::AMDataType_string, {}, nullptr},
        {&CPdfTypes::classtype, "class", AMCore::AMDataType_enum, {}, nullptr},
        {&CPdfTypes::version, "version", AMCore::AMDataType_string, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CPdfTypes::context()
{
    return *g_context;
}
