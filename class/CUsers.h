/* automatically generated file, do not edit. */


#ifndef CUSERS_H
#define CUSERS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CUsers: public AMSvr::AMSvrDbObject<int>
{
public:
    CUsers();
    CUsers(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<std::map<SAWEnum, int>, int> roles;
    AMSvr::AMSvrProp<bool, int> valid;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modifiedAt;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CUSERS_H
