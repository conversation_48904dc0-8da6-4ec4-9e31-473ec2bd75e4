/* automatically generated file, do not edit. */


#ifndef CPDFS_H
#define CPDFS_H
#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"   
#include "amcore/AMAuthorTag.h"   
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"    
#include "SAWSettings.h"

class CPdfs: public AMSvr::AMSvrDbObject<int>
{
public:
    CPdfs();
    CPdfs(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;
    
    AMSvr::AMSvrProp<SAWEnum , int> classtype;
    AMSvr::AMSvrProp<SAWEnum , int> type;
    AMSvr::AMSvrProp<int, int> document;
    AMSvr::AMSvrProp<std::string, int> data;
    AMSvr::AMSvrProp<int, int> modified_by;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> modified_at;

protected:   
    static AMSvr::AMSvrDbObjectContext<int> *g_context;            
};

#endif //CPDFS_H

