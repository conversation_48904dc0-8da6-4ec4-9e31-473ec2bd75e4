/* automatically generated file, do not edit. */

#include "CConnectedAccounts.h"  

CConnectedAccounts::CConnectedAccounts(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      name(),
      domain(),
      validFrom(),
      validUntil(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };
    
CConnectedAccounts::CConnectedAccounts()
    : AMSvr::AMSvrDbObject<int>(),
      name(),
      domain(),
      validFrom(),
      validUntil(),
      note(),
      modifiedBy(),
      modifiedAt()
    {           
    };

AMSvr::AMSvrDbObjectContext<int> *CConnectedAccounts::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CConnectedAccounts>(
    "dbo.connected_accounts",
    {  
        {&CConnectedAccounts::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CConnectedAccounts::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CConnectedAccounts::domain, "domain", AMCore::AMDataType_string, {}, nullptr},
        {&CConnectedAccounts::validFrom, "valid_from", AMCore::AMDataType_datetime, {}, nullptr},
        {&CConnectedAccounts::validUntil, "valid_until", AMCore::AMDataType_datetime, {}, nullptr},
        {&CConnectedAccounts::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CConnectedAccounts::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CConnectedAccounts::modifiedAt, "modified_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CConnectedAccounts::context()
{
    return *g_context;
}
