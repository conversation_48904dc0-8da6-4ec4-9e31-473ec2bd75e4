
CREATE TABLE dbo.categories
(
    id SERIAL CONSTRAINT dbo_categories_pk PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    weight INT DEFAULT 0 NOT NULL,
    paid BOOL DEFAULT FALSE NOT NULL,
    supercategory INT NULL,
    note TEXT DEFAULT '' NOT NULL,
    deleted_at TIMESTAMP NULL,
    modified_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);


CREATE INDEX dbo_categories_name_index ON dbo.categories (name);
CREATE INDEX dbo_categories_weight_index ON dbo.categories (weight);
CREATE INDEX dbo_categories_paid_index ON dbo.categories (paid);
CREATE INDEX dbo_categories_supercategory_index ON dbo.categories (supercategory);
CREATE INDEX dbo_categories_deleted_at_index ON dbo.categories (deleted_at);
CREATE INDEX dbo_categories_created_at_index ON dbo.categories (created_at);
CREATE INDEX dbo_categories_updated_at_index ON dbo.categories (updated_at);


CREATE UNIQUE INDEX dbo_categories_name_unique_active 
ON dbo.categories (name) 
WHERE deleted_at IS NULL;


ALTER TABLE dbo.categories 
ADD CONSTRAINT fk_categories_supercategory 
FOREIGN KEY (supercategory) REFERENCES dbo.categories(id);


CREATE TRIGGER dbt_categories_update_timestamp 
BEFORE UPDATE ON dbo.categories 
FOR EACH ROW 
EXECUTE PROCEDURE trigger_update_modified_at_timestamp();


CREATE TABLE dbh.categories
(
    id SERIAL CONSTRAINT dbh_categories_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    record_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    weight INT DEFAULT 0 NOT NULL,
    paid BOOL DEFAULT FALSE NOT NULL,
    supercategory INT NULL,
    note TEXT DEFAULT '' NOT NULL,
    deleted_at TIMESTAMP NULL,
    modified_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_categories_record_id_index ON dbh.categories (record_id);
CREATE INDEX dbh_categories_updated_at_index ON dbh.categories (updated_at);
CREATE INDEX dbh_categories_operation_index ON dbh.categories (operation);


CREATE TRIGGER dbt_categories_delete
    AFTER DELETE
    ON dbo.categories FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_categories_insert
    AFTER INSERT
    ON dbo.categories FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_categories_update
    AFTER UPDATE
    ON dbo.categories FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();

CREATE OR REPLACE FUNCTION soft_delete_category(category_id INT, user_id INT)
RETURNS VOID
LANGUAGE PLPGSQL
AS $$
BEGIN
    UPDATE dbo.categories 
    SET deleted_at = NOW(), 
        updated_at = NOW(),
        modified_by = user_id
    WHERE id = category_id 
    AND deleted_at IS NULL;
END
$$;

CREATE OR REPLACE FUNCTION restore_category(category_id INT, user_id INT)
RETURNS VOID
LANGUAGE PLPGSQL
AS $$
BEGIN
    UPDATE dbo.categories 
    SET deleted_at = NULL, 
        updated_at = NOW(),
        modified_by = user_id
    WHERE id = category_id 
    AND deleted_at IS NOT NULL;
END
$$;
