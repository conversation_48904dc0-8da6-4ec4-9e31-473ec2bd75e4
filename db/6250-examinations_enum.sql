INSERT INTO dbe.examinations (id, occup_hazard, r_f_working_cond, legal_req, check_type, examination, period_under_50_ktg1, period_under_50_ktg2, period_under_50_ktg2r, period_under_50_ktg3, period_under_50_ktg4, period_over_50_ktg1, period_over_50_ktg2, period_over_50_ktg2r, period_over_50_ktg3, period_over_50_ktg4, examination_delay, examination_period_under50, examination_period_over50, valid_over_50_only, medical_info, exception_fn)
VALUES  (1, NULL, 2, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2, NULL, 2, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3, NULL, 2, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (4, NULL, 2, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (5, NULL, 2, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (6, NULL, 2, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (7, NULL, 2, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (8, NULL, 2, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (9, NULL, 2, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (10, NULL, 2, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (11, NULL, 2, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (12, NULL, 2, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (13, NULL, 2, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (14, NULL, 2, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (15, NULL, 2, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (16, NULL, 2, NULL, 4, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (17, NULL, 2, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (18, NULL, 2, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (19, NULL, 2, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (20, NULL, 2, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (21, NULL, 2, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (22, NULL, 3, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (23, NULL, 3, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (24, NULL, 3, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (25, NULL, 3, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (26, NULL, 3, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (27, NULL, 3, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (28, NULL, 3, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (29, NULL, 3, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (30, NULL, 3, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (31, NULL, 3, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (32, NULL, 3, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (33, NULL, 3, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (34, NULL, 3, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (35, NULL, 3, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (36, NULL, 3, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (37, NULL, 3, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (38, NULL, 3, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (39, NULL, 3, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (40, NULL, 3, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (41, NULL, 3, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (42, NULL, 3, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (43, NULL, 3, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (44, NULL, 4, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (45, NULL, 4, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (46, NULL, 4, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (47, NULL, 4, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (48, NULL, 4, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (49, NULL, 4, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (50, NULL, 4, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (51, NULL, 4, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (52, NULL, 4, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (53, NULL, 4, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (54, NULL, 4, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (55, NULL, 4, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (56, NULL, 4, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (57, NULL, 4, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (58, NULL, 4, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (59, NULL, 4, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (60, NULL, 4, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (61, NULL, 4, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (62, NULL, 4, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (63, NULL, 4, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (64, NULL, 4, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (65, NULL, 4, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 1.1.c Práce s chemickými látkami - látky s pozdním účinkem karcinogenním a mutagenním a u látek s fibrogenním účinkem, se následná prohlídka provádí na základě rozhodnutí orgánu ochrany veřejného zdraví.', 0),
        (66, NULL, 4, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (67, NULL, 4, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (68, NULL, 4, NULL, 5, 3, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (69, NULL, 4, NULL, 5, 41, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (70, NULL, 4, NULL, 5, 17, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (71, NULL, 5, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (72, NULL, 5, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (73, NULL, 5, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (74, NULL, 5, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (75, NULL, 5, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (76, NULL, 5, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (77, NULL, 5, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (78, NULL, 5, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (79, NULL, 5, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (80, NULL, 5, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (81, NULL, 5, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (82, NULL, 5, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (83, NULL, 5, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (84, NULL, 5, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (85, NULL, 5, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (86, NULL, 5, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (87, NULL, 5, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (88, NULL, 5, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (89, NULL, 5, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (90, NULL, 5, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (91, NULL, 5, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (92, NULL, 5, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (93, NULL, 5, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 1.2. Látky s pozdním účinkem karcinogenním a mutagenním - karcinogeny kategorie 1 a 2 nebo kategorie 1A a 1B a mutageny kategorie 1 a 2 nebo kategorie 1A a 1B, se následná prohlídka provádí na základě rozhodnutí orgánu ochrany veřejného zdraví.', 0),
        (94, NULL, 5, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (95, NULL, 5, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (96, NULL, 5, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (97, NULL, 5, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (98, NULL, 5, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (99, NULL, 5, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (100, NULL, 6, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (101, NULL, 6, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (102, NULL, 6, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (103, NULL, 6, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (104, NULL, 6, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (105, NULL, 8, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (106, NULL, 8, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (107, NULL, 8, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (108, NULL, 8, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (109, NULL, 8, NULL, 2, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (110, NULL, 8, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (111, NULL, 8, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (112, NULL, 8, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (113, NULL, 8, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (114, NULL, 8, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (115, NULL, 8, NULL, 3, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (116, NULL, 8, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (117, NULL, 8, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (118, NULL, 8, NULL, 3, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (119, NULL, 8, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (120, NULL, 8, NULL, 3, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (121, NULL, 8, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (122, NULL, 8, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (123, NULL, 8, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (124, NULL, 8, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (125, NULL, 8, NULL, 4, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (126, NULL, 8, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (127, NULL, 9, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (128, NULL, 9, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (129, NULL, 9, NULL, 2, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (130, NULL, 9, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (131, NULL, 9, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (132, NULL, 9, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (133, NULL, 9, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (134, NULL, 9, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (135, NULL, 9, NULL, 4, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (136, NULL, 9, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (137, NULL, 11, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (138, NULL, 11, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (139, NULL, 11, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (140, NULL, 11, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (141, NULL, 11, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (142, NULL, 11, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (143, NULL, 11, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (144, NULL, 11, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (145, NULL, 12, NULL, 2, 126, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (146, NULL, 12, NULL, 2, 25, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (147, NULL, 12, NULL, 2, 33, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (148, NULL, 12, NULL, 2, 19, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (149, NULL, 12, NULL, 2, 5, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (150, NULL, 12, NULL, 2, 26, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (151, NULL, 12, NULL, 2, 82, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 120, 24, 24, false, 'Pro rizikový faktor: 2.3. Arzén a jeho sloučeniny (se zvláštním zřetelem na C a R) se RTG hrudníku a ultrasonografie břicha provedou po 10 letech expozice arzénu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (152, NULL, 12, NULL, 2, 96, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 120, 24, 24, false, '', 0),
        (153, NULL, 12, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (154, NULL, 12, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (155, NULL, 12, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (156, NULL, 12, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (157, NULL, 12, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (158, NULL, 12, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (159, NULL, 12, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (160, NULL, 12, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (161, NULL, 12, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (162, NULL, 12, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (163, NULL, 12, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (164, NULL, 12, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (165, NULL, 12, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (166, NULL, 12, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (167, NULL, 12, NULL, 4, 96, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (168, NULL, 12, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (169, NULL, 12, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (170, NULL, 12, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (171, NULL, 12, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (172, NULL, 12, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (173, NULL, 12, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (174, NULL, 12, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (175, NULL, 12, NULL, 5, 96, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (176, NULL, 13, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (177, NULL, 13, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (178, NULL, 13, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (179, NULL, 13, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (180, NULL, 13, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (181, NULL, 13, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (182, NULL, 13, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (183, NULL, 13, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (184, NULL, 13, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (185, NULL, 13, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (186, NULL, 13, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (187, NULL, 13, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (188, NULL, 13, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (189, NULL, 13, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (190, NULL, 14, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (191, NULL, 14, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (192, NULL, 14, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (193, NULL, 14, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (194, NULL, 14, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (195, NULL, 14, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 72, 48, 48, false, 'RTG hrudníku se provede po 6 letech expozice hydridu antimonu SbH3 a dále se provádí 1 x 4 roky.', 0),
        (196, NULL, 14, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (197, NULL, 14, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (198, NULL, 14, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (199, NULL, 14, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (200, NULL, 14, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (201, NULL, 14, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (202, NULL, 14, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (203, NULL, 14, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (204, NULL, 14, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (205, NULL, 14, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (206, NULL, 14, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (207, NULL, 14, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (208, NULL, 15, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (209, NULL, 15, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (210, NULL, 15, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (211, NULL, 15, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (212, NULL, 15, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (213, NULL, 15, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (214, NULL, 15, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (215, NULL, 15, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (216, NULL, 15, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 72, 24, 24, false, 'RTG hrudníku se provede po 6 letech expozice berylliu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (217, NULL, 15, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (218, NULL, 15, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (219, NULL, 15, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (220, NULL, 15, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (221, NULL, 15, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (222, NULL, 15, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (223, NULL, 15, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (224, NULL, 15, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (225, NULL, 15, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (226, NULL, 15, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (227, NULL, 15, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (228, NULL, 15, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (229, NULL, 15, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (230, NULL, 15, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (231, NULL, 15, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (232, NULL, 15, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (233, NULL, 15, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (234, NULL, 15, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (235, NULL, 15, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (236, NULL, 15, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (237, NULL, 15, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (238, NULL, 15, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (239, NULL, 15, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (240, NULL, 15, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (241, NULL, 15, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (242, NULL, 15, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (243, NULL, 15, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (244, NULL, 15, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (245, NULL, 16, NULL, 2, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (246, NULL, 16, NULL, 2, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (247, NULL, 16, NULL, 2, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (248, NULL, 16, NULL, 2, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (249, NULL, 16, NULL, 2, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (250, NULL, 16, NULL, 2, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (251, NULL, 16, NULL, 2, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (252, NULL, 16, NULL, 2, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (253, NULL, 16, NULL, 2, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (254, NULL, 16, NULL, 2, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 120, 24, 24, false, 'RTG hrudníku a PSA u mužů se provede po 10 letech expozice kadminu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (255, NULL, 16, NULL, 2, 72, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 120, 24, 24, false, '', 0),
        (256, NULL, 16, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (257, NULL, 16, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (258, NULL, 16, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (259, NULL, 16, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (260, NULL, 16, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (261, NULL, 16, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (262, NULL, 16, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (263, NULL, 16, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (264, NULL, 16, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (265, NULL, 16, NULL, 1, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (266, NULL, 16, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (267, NULL, 16, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (268, NULL, 16, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (269, NULL, 16, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (270, NULL, 16, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (271, NULL, 16, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (272, NULL, 16, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (273, NULL, 16, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (274, NULL, 16, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (275, NULL, 16, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (276, NULL, 16, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (277, NULL, 16, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (278, NULL, 16, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (279, NULL, 16, NULL, 4, 72, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (280, NULL, 16, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (281, NULL, 16, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (282, NULL, 16, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (283, NULL, 16, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (284, NULL, 16, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (285, NULL, 16, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (286, NULL, 16, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (287, NULL, 16, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (288, NULL, 16, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (289, NULL, 16, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (290, NULL, 16, NULL, 5, 72, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (291, NULL, 17, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (292, NULL, 17, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (293, NULL, 17, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (294, NULL, 17, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (295, NULL, 17, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (296, NULL, 18, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (297, NULL, 18, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (298, NULL, 18, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (299, NULL, 18, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (300, NULL, 18, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (301, NULL, 18, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (302, NULL, 18, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (303, NULL, 18, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (304, NULL, 18, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (305, NULL, 18, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (306, NULL, 18, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (307, NULL, 18, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 24, 24, false, 'RTG hrudníku se provede po 10 letech expozice VI-mocným sloučeninám chrómu a dále se provádí 1 x 2 roky.', 0),
        (308, NULL, 18, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (309, NULL, 18, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (310, NULL, 18, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (311, NULL, 18, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (312, NULL, 18, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (313, NULL, 18, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (314, NULL, 18, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (315, NULL, 18, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (316, NULL, 18, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (317, NULL, 18, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (318, NULL, 18, NULL, 1, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (319, NULL, 18, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (320, NULL, 18, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (321, NULL, 18, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (322, NULL, 18, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (323, NULL, 18, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (324, NULL, 18, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (325, NULL, 18, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (326, NULL, 18, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (327, NULL, 18, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (328, NULL, 18, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (329, NULL, 18, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (330, NULL, 18, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (331, NULL, 18, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (332, NULL, 18, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (333, NULL, 18, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (334, NULL, 18, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (335, NULL, 18, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (336, NULL, 18, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (337, NULL, 18, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (338, NULL, 18, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (339, NULL, 18, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (340, NULL, 18, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (341, NULL, 18, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (342, NULL, 18, NULL, 5, 50, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (343, NULL, 18, NULL, 5, 77, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (344, NULL, 18, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (345, NULL, 19, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (346, NULL, 19, NULL, 2, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (347, NULL, 19, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (348, NULL, 19, NULL, 1, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (349, NULL, 19, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (350, NULL, 19, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (351, NULL, 19, NULL, 4, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (352, NULL, 19, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (353, NULL, 19, NULL, 5, 90, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (354, NULL, 19, NULL, 5, 129, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 48, 0, 0, false, '', 0),
        (355, NULL, 20, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (356, NULL, 20, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (357, NULL, 20, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (358, NULL, 20, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (359, NULL, 20, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (360, NULL, 20, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (361, NULL, 20, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (362, NULL, 20, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (363, NULL, 20, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (364, NULL, 21, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (365, NULL, 21, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (366, NULL, 21, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (367, NULL, 21, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (368, NULL, 21, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (369, NULL, 21, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (370, NULL, 21, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (371, NULL, 21, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (372, NULL, 21, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku, ORL vyšetření a rinoskopie se provedou po 10 letech expozice sloučeninám niklu s karcinogenním účinkem.', 0),
        (373, NULL, 21, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (374, NULL, 21, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (375, NULL, 21, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (376, NULL, 21, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (377, NULL, 21, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (378, NULL, 21, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (379, NULL, 21, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (380, NULL, 21, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (381, NULL, 21, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (382, NULL, 21, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (383, NULL, 21, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (384, NULL, 21, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (385, NULL, 21, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (386, NULL, 21, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (387, NULL, 21, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (388, NULL, 21, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (389, NULL, 21, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (390, NULL, 21, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (391, NULL, 21, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (392, NULL, 21, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (393, NULL, 21, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (394, NULL, 21, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (395, NULL, 21, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (396, NULL, 21, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (397, NULL, 21, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (398, NULL, 21, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (399, NULL, 21, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (400, NULL, 21, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (401, NULL, 21, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (402, NULL, 21, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (403, NULL, 21, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (404, NULL, 21, NULL, 5, 55, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (405, NULL, 21, NULL, 5, 77, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (406, NULL, 21, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (407, NULL, 23, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (408, NULL, 23, NULL, 2, 44, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (409, NULL, 23, NULL, 2, 25, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (410, NULL, 23, NULL, 2, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (411, NULL, 23, NULL, 2, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (412, NULL, 23, NULL, 2, 9, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (413, NULL, 23, NULL, 2, 5, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (414, NULL, 23, NULL, 2, 26, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (415, NULL, 23, NULL, 2, 38, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (416, NULL, 23, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (417, NULL, 23, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (418, NULL, 23, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (419, NULL, 23, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (420, NULL, 23, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (421, NULL, 23, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (422, NULL, 23, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (423, NULL, 23, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (424, NULL, 23, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (425, NULL, 23, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (426, NULL, 23, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (427, NULL, 23, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (428, NULL, 23, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (429, NULL, 23, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (430, NULL, 23, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (431, NULL, 23, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (432, NULL, 23, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (433, NULL, 23, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (434, NULL, 23, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (435, NULL, 23, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (436, NULL, 24, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (437, NULL, 24, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (438, NULL, 24, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (439, NULL, 24, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (440, NULL, 24, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (441, NULL, 24, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (442, NULL, 24, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (443, NULL, 24, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (444, NULL, 25, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (445, NULL, 25, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (446, NULL, 25, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (447, NULL, 25, NULL, 2, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 4, 4, false, 'S ohledem na rizikový faktor: 2.10.3. Organické sloučeniny fosforu (například organofosfáty - OF a trikrezylfosfát-TKP a další se zvláštním zřetelem na R, pokud některá sloučenina je takto klasifikována) se odborná vyšetření provádějí se zaměřením na periferní nervový systém (opožděný efekt) a stanovení aktivity cholinesterázy nebo acetylcholinesterázy v krvi provede podle závažnosti expozice 1x až 3x za rok, nejméně však 1x během práce.', 0),
        (448, NULL, 25, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (449, NULL, 25, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (450, NULL, 25, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (451, NULL, 25, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (452, NULL, 25, NULL, 1, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (453, NULL, 25, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (454, NULL, 25, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (455, NULL, 25, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (456, NULL, 25, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (457, NULL, 25, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (458, NULL, 25, NULL, 4, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (459, NULL, 25, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (460, NULL, 25, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (461, NULL, 26, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (462, NULL, 26, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (463, NULL, 26, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (464, NULL, 26, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (465, NULL, 26, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (466, NULL, 27, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (467, NULL, 27, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (468, NULL, 27, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (469, NULL, 27, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (470, NULL, 27, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (471, NULL, 27, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (472, NULL, 28, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (473, NULL, 28, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (474, NULL, 28, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (475, NULL, 28, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (476, NULL, 28, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (477, NULL, 28, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (478, NULL, 28, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (479, NULL, 28, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (480, NULL, 29, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (481, NULL, 29, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (482, NULL, 29, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (483, NULL, 29, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (484, NULL, 29, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (485, NULL, 30, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (486, NULL, 30, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (487, NULL, 30, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (488, NULL, 30, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (489, NULL, 30, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (490, NULL, 30, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (491, NULL, 31, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (492, NULL, 31, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (493, NULL, 31, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (494, NULL, 31, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (495, NULL, 31, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (496, NULL, 31, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (497, NULL, 31, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (498, NULL, 31, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (499, NULL, 32, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (500, NULL, 32, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (501, NULL, 32, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (502, NULL, 32, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (503, NULL, 32, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (504, NULL, 33, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (505, NULL, 33, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (506, NULL, 33, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (507, NULL, 33, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (508, NULL, 33, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (509, NULL, 33, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (510, NULL, 34, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (511, NULL, 34, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (512, NULL, 34, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (513, NULL, 34, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (514, NULL, 34, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (515, NULL, 34, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (516, NULL, 34, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (517, NULL, 34, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (518, NULL, 35, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (519, NULL, 35, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (520, NULL, 35, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (521, NULL, 35, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (522, NULL, 35, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (523, NULL, 35, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (524, NULL, 35, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (525, NULL, 35, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (526, NULL, 36, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (527, NULL, 36, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (528, NULL, 36, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (529, NULL, 36, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (530, NULL, 36, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (531, NULL, 36, NULL, 2, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (532, NULL, 36, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (533, NULL, 36, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (534, NULL, 36, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (535, NULL, 36, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (536, NULL, 36, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (537, NULL, 36, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (538, NULL, 36, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (539, NULL, 36, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (540, NULL, 36, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (541, NULL, 36, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (542, NULL, 36, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (543, NULL, 36, NULL, 4, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (544, NULL, 36, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (545, NULL, 37, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (546, NULL, 37, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (547, NULL, 37, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (548, NULL, 37, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (549, NULL, 37, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (550, NULL, 38, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (551, NULL, 38, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (552, NULL, 38, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (553, NULL, 38, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (554, NULL, 38, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (555, NULL, 38, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (556, NULL, 38, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (557, NULL, 38, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (558, NULL, 39, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (559, NULL, 39, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (560, NULL, 39, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (561, NULL, 39, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (562, NULL, 39, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (563, NULL, 39, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (564, NULL, 39, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (565, NULL, 39, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (566, NULL, 39, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (567, NULL, 39, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (568, NULL, 39, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (569, NULL, 39, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (570, NULL, 39, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (571, NULL, 39, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (572, NULL, 40, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (573, NULL, 40, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (574, NULL, 40, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (575, NULL, 40, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (576, NULL, 40, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (577, NULL, 40, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (578, NULL, 40, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (579, NULL, 40, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (580, NULL, 41, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (581, NULL, 41, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (582, NULL, 41, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (583, NULL, 41, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (584, NULL, 41, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (585, NULL, 41, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (586, NULL, 41, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (587, NULL, 41, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (588, NULL, 42, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (589, NULL, 42, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (590, NULL, 42, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (591, NULL, 42, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (592, NULL, 42, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (593, NULL, 43, NULL, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 3, 12, 12, false, '', 0),
        (594, NULL, 43, NULL, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 3, 12, 12, false, '', 0),
        (595, NULL, 43, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (596, NULL, 43, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (597, NULL, 43, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (598, NULL, 43, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (599, NULL, 43, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (600, NULL, 43, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (601, NULL, 44, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (602, NULL, 44, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (603, NULL, 44, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (604, NULL, 44, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (605, NULL, 44, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (606, NULL, 44, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (607, NULL, 44, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (608, NULL, 44, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (609, NULL, 45, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (610, NULL, 45, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (611, NULL, 45, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (612, NULL, 45, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (613, NULL, 45, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (614, NULL, 45, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (615, NULL, 45, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (616, NULL, 45, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (617, NULL, 45, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (618, NULL, 45, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (619, NULL, 45, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (620, NULL, 45, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (621, NULL, 45, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (622, NULL, 45, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (623, NULL, 46, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (624, NULL, 46, NULL, 2, 21, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (625, NULL, 46, NULL, 2, 50, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (626, NULL, 46, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (627, NULL, 46, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (628, NULL, 46, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (629, NULL, 46, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (630, NULL, 46, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (631, NULL, 46, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (632, NULL, 46, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (633, NULL, 46, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (634, NULL, 47, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (635, NULL, 47, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (636, NULL, 47, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (637, NULL, 47, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (638, NULL, 47, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (639, NULL, 47, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (640, NULL, 47, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (641, NULL, 47, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (642, NULL, 48, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (643, NULL, 48, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (644, NULL, 48, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (645, NULL, 48, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (646, NULL, 48, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (647, NULL, 48, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (648, NULL, 48, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (649, NULL, 48, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (650, NULL, 48, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (651, NULL, 49, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (652, NULL, 49, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (653, NULL, 49, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (654, NULL, 49, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (655, NULL, 49, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (656, NULL, 49, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (657, NULL, 49, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (658, NULL, 49, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (659, NULL, 49, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (660, NULL, 49, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (661, NULL, 49, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (662, NULL, 50, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (663, NULL, 50, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (664, NULL, 50, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (665, NULL, 50, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (666, NULL, 50, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (667, NULL, 50, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (668, NULL, 50, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (669, NULL, 50, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (670, NULL, 52, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (671, NULL, 52, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (672, NULL, 52, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (673, NULL, 52, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (674, NULL, 52, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (675, NULL, 52, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (676, NULL, 52, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (677, NULL, 52, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (678, NULL, 52, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (679, NULL, 52, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (680, NULL, 52, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (681, NULL, 52, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (682, NULL, 52, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (683, NULL, 52, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (684, NULL, 53, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (685, NULL, 53, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (686, NULL, 53, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (687, NULL, 53, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (688, NULL, 53, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (689, NULL, 53, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (690, NULL, 53, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (691, NULL, 53, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (692, NULL, 53, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (693, NULL, 53, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (694, NULL, 53, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (695, NULL, 53, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (696, NULL, 53, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (697, NULL, 53, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (698, NULL, 53, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (699, NULL, 53, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (700, NULL, 53, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (701, NULL, 54, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, 'První periodická prohlídka s ohledem na rizikový faktor: 2.27.2. Methylchlorid (= monochlormethan), se provede za 3 - 9 měsíců.', 0),
        (702, NULL, 54, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (703, NULL, 54, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (704, NULL, 54, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (705, NULL, 54, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (706, NULL, 54, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (707, NULL, 54, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (708, NULL, 54, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (709, NULL, 54, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (710, NULL, 54, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (711, NULL, 54, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (712, NULL, 54, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (713, NULL, 54, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (714, NULL, 54, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (715, NULL, 54, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (716, NULL, 54, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (717, NULL, 54, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (718, NULL, 54, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (719, NULL, 54, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (720, NULL, 54, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (721, NULL, 54, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (722, NULL, 54, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (723, NULL, 54, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (724, NULL, 55, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (725, NULL, 55, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (726, NULL, 55, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (727, NULL, 55, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (728, NULL, 55, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (729, NULL, 55, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (730, NULL, 55, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (731, NULL, 55, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (732, NULL, 55, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (733, NULL, 55, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (734, NULL, 55, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (735, NULL, 55, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (736, NULL, 55, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (737, NULL, 55, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (738, NULL, 55, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (739, NULL, 55, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (740, NULL, 55, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (741, NULL, 56, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (742, NULL, 56, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (743, NULL, 56, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (744, NULL, 56, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (745, NULL, 56, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (746, NULL, 56, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (747, NULL, 56, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (748, NULL, 56, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (749, NULL, 56, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (750, NULL, 56, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (751, NULL, 56, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (752, NULL, 56, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (753, NULL, 56, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (754, NULL, 56, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (755, NULL, 56, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (756, NULL, 56, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (757, NULL, 56, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (758, NULL, 56, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (759, NULL, 56, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (760, NULL, 56, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (761, NULL, 56, NULL, 5, 21, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (762, NULL, 57, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (763, NULL, 57, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (764, NULL, 57, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (765, NULL, 57, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (766, NULL, 57, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (767, NULL, 57, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (768, NULL, 57, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (769, NULL, 57, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (770, NULL, 57, NULL, 2, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Vodní chladový test a prstová pletyzmografie se provádějí po více než 10 leté práci v riziku vinylchloridu.', 0),
        (771, NULL, 57, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (772, NULL, 57, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (773, NULL, 57, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (774, NULL, 57, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (775, NULL, 57, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (776, NULL, 57, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (777, NULL, 57, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (778, NULL, 57, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (779, NULL, 57, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (780, NULL, 57, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (781, NULL, 57, NULL, 3, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'S ohledem na rizikový faktor: 2.27.4. Vinylchlorid (= chlorethen) (se zvláštním zřetelem na C), se vyšetření pro mimořádnou pracovnělékařskou prohlídku provádějí po vyšší expozici vinylchloridu.', 0),
        (782, NULL, 57, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (783, NULL, 57, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (784, NULL, 57, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (785, NULL, 57, NULL, 3, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (786, NULL, 57, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (787, NULL, 57, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (788, NULL, 57, NULL, 3, 11, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (789, NULL, 57, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (790, NULL, 57, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (791, NULL, 57, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (792, NULL, 57, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (793, NULL, 57, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (794, NULL, 57, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (795, NULL, 57, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (796, NULL, 57, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (797, NULL, 57, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'GMT se provede po více než 10 leté práci v riziku vinylchloridu.', 0),
        (798, NULL, 57, NULL, 4, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (799, NULL, 57, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (800, NULL, 57, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (801, NULL, 57, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (802, NULL, 57, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (803, NULL, 57, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (804, NULL, 57, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (805, NULL, 57, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (806, NULL, 57, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (807, NULL, 59, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (808, NULL, 59, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (809, NULL, 59, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (810, NULL, 59, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (811, NULL, 59, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (812, NULL, 60, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (813, NULL, 60, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (814, NULL, 60, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (815, NULL, 60, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (816, NULL, 60, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (817, NULL, 60, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (818, NULL, 60, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (819, NULL, 60, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (820, NULL, 60, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (821, NULL, 60, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (822, NULL, 60, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (823, NULL, 60, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (824, NULL, 60, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (825, NULL, 60, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (826, NULL, 60, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (827, NULL, 60, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (828, NULL, 60, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (829, NULL, 60, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (830, NULL, 60, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (831, NULL, 60, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (832, NULL, 60, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (833, NULL, 60, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (834, NULL, 60, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (835, NULL, 60, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (836, NULL, 60, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (837, NULL, 60, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (838, NULL, 60, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (839, NULL, 60, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (840, NULL, 60, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (841, NULL, 60, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (842, NULL, 60, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (843, NULL, 60, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (844, NULL, 60, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (845, NULL, 62, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (846, NULL, 62, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (847, NULL, 62, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (848, NULL, 62, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (849, NULL, 62, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (850, NULL, 63, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (851, NULL, 63, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (852, NULL, 63, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (853, NULL, 63, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (854, NULL, 63, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (855, NULL, 63, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (856, NULL, 63, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (857, NULL, 63, NULL, 1, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (858, NULL, 63, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (859, NULL, 63, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (860, NULL, 63, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (861, NULL, 63, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (862, NULL, 63, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (863, NULL, 63, NULL, 4, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (864, NULL, 63, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (865, NULL, 64, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (866, NULL, 64, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (867, NULL, 64, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (868, NULL, 64, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (869, NULL, 64, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (870, NULL, 64, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (871, NULL, 64, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (872, NULL, 64, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (873, NULL, 64, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (874, NULL, 64, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (875, NULL, 64, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (876, NULL, 64, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (877, NULL, 64, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (878, NULL, 64, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (879, NULL, 64, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (880, NULL, 64, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (881, NULL, 64, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (882, NULL, 66, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (883, NULL, 66, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (884, NULL, 66, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (885, NULL, 66, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (886, NULL, 66, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (887, NULL, 66, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (888, NULL, 66, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (889, NULL, 66, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (890, NULL, 66, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (891, NULL, 66, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (892, NULL, 66, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (893, NULL, 66, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (894, NULL, 66, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (895, NULL, 66, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (896, NULL, 67, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (897, NULL, 67, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (898, NULL, 67, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (899, NULL, 67, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (900, NULL, 67, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (901, NULL, 67, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (902, NULL, 67, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (903, NULL, 67, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (904, NULL, 67, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (905, NULL, 67, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (906, NULL, 67, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (907, NULL, 67, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (908, NULL, 67, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (909, NULL, 67, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (910, NULL, 69, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (911, NULL, 69, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Rinoskopie a ORL vyšetření se provádějí po nejméně desetileté expozici formaldehydu a jiným alifatickým aldehydům.', 0),
        (912, NULL, 69, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (913, NULL, 69, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (914, NULL, 69, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (915, NULL, 69, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (916, NULL, 69, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'ORL vyšetření se provádí po nejméně desetileté expozici formaldehydu a jiným alifatickým aldehydům.', 0),
        (917, NULL, 69, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (918, NULL, 69, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (919, NULL, 70, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (920, NULL, 70, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (921, NULL, 70, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (922, NULL, 70, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (923, NULL, 70, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (924, NULL, 70, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (925, NULL, 70, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (926, NULL, 70, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (927, NULL, 71, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (928, NULL, 71, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (929, NULL, 71, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (930, NULL, 71, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (931, NULL, 71, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (932, NULL, 71, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (933, NULL, 71, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (934, NULL, 71, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (935, NULL, 71, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (936, NULL, 71, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (937, NULL, 71, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (938, NULL, 71, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (939, NULL, 71, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (940, NULL, 71, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (941, NULL, 71, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (942, NULL, 71, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (943, NULL, 71, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (944, NULL, 72, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (945, NULL, 72, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (946, NULL, 72, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (947, NULL, 72, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (948, NULL, 72, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (949, NULL, 72, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (950, NULL, 72, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (951, NULL, 72, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (952, NULL, 72, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (953, NULL, 72, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (954, NULL, 72, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (955, NULL, 72, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (956, NULL, 72, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (957, NULL, 72, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (958, NULL, 72, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (959, NULL, 72, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (960, NULL, 72, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (961, NULL, 72, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (962, NULL, 72, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (963, NULL, 72, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (964, NULL, 72, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (965, NULL, 73, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (966, NULL, 73, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (967, NULL, 73, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (968, NULL, 73, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (969, NULL, 73, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (970, NULL, 73, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (971, NULL, 73, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (972, NULL, 73, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (973, NULL, 73, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (974, NULL, 73, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (975, NULL, 73, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (976, NULL, 73, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (977, NULL, 73, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (978, NULL, 73, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (979, NULL, 73, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (980, NULL, 73, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (981, NULL, 73, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (982, NULL, 73, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (983, NULL, 73, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (984, NULL, 73, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (985, NULL, 73, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (986, NULL, 73, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (987, NULL, 73, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (988, NULL, 73, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (989, NULL, 73, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (990, NULL, 73, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (991, NULL, 73, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (992, NULL, 73, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (993, NULL, 73, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (994, NULL, 73, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (995, NULL, 74, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (996, NULL, 74, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (997, NULL, 74, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (998, NULL, 74, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (999, NULL, 74, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1000, NULL, 74, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1001, NULL, 74, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1002, NULL, 74, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1003, NULL, 74, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1004, NULL, 74, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1005, NULL, 74, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1006, NULL, 74, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1007, NULL, 74, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1008, NULL, 74, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1009, NULL, 75, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1010, NULL, 75, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1011, NULL, 75, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1012, NULL, 75, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1013, NULL, 75, NULL, 2, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1014, NULL, 75, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1015, NULL, 75, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1016, NULL, 75, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1017, NULL, 75, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1018, NULL, 75, NULL, 1, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1019, NULL, 75, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1020, NULL, 75, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1021, NULL, 75, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1022, NULL, 75, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1023, NULL, 75, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1024, NULL, 75, NULL, 4, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1025, NULL, 75, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1026, NULL, 76, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1027, NULL, 76, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1028, NULL, 76, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1029, NULL, 76, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1030, NULL, 76, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1031, NULL, 76, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1032, NULL, 76, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1033, NULL, 76, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1034, NULL, 76, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1035, NULL, 76, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1036, NULL, 76, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1037, NULL, 76, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1038, NULL, 76, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1039, NULL, 76, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1040, NULL, 77, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1041, NULL, 77, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1042, NULL, 77, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1043, NULL, 77, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1044, NULL, 77, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1045, NULL, 77, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1046, NULL, 77, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1047, NULL, 77, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1048, NULL, 77, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1049, NULL, 77, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1050, NULL, 77, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1051, NULL, 77, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1052, NULL, 77, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1053, NULL, 77, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1054, NULL, 77, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1055, NULL, 77, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1056, NULL, 77, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1057, NULL, 78, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1058, NULL, 78, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1059, NULL, 78, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1060, NULL, 78, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1061, NULL, 78, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1062, NULL, 79, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1063, NULL, 79, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1064, NULL, 79, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1065, NULL, 79, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1066, NULL, 79, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1067, NULL, 79, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1068, NULL, 79, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1069, NULL, 79, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1070, NULL, 79, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1071, NULL, 79, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1072, NULL, 79, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1073, NULL, 79, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1074, NULL, 79, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1075, NULL, 79, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1076, NULL, 80, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1077, NULL, 80, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1078, NULL, 80, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1079, NULL, 80, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1080, NULL, 80, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1081, NULL, 81, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1082, NULL, 81, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1083, NULL, 81, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1084, NULL, 81, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1085, NULL, 81, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1086, NULL, 81, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1087, NULL, 81, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1088, NULL, 81, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1089, NULL, 82, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1090, NULL, 82, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1091, NULL, 82, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1092, NULL, 82, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1093, NULL, 82, NULL, 2, 89, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1094, NULL, 82, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1095, NULL, 82, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1096, NULL, 82, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1097, NULL, 82, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1098, NULL, 82, NULL, 1, 88, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1099, NULL, 82, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1100, NULL, 82, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1101, NULL, 82, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1102, NULL, 82, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1103, NULL, 82, NULL, 4, 89, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1104, NULL, 82, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1105, NULL, 83, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1106, NULL, 83, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1107, NULL, 83, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1108, NULL, 83, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1109, NULL, 83, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1110, NULL, 83, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1111, NULL, 83, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1112, NULL, 83, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1113, NULL, 83, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1114, NULL, 83, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1115, NULL, 83, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1116, NULL, 83, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1117, NULL, 83, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1118, NULL, 83, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1119, NULL, 83, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1120, NULL, 83, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1121, NULL, 83, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1122, NULL, 83, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1123, NULL, 83, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1124, NULL, 83, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1125, NULL, 84, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1126, NULL, 84, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1127, NULL, 84, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1128, NULL, 84, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1129, NULL, 84, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1130, NULL, 84, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1131, NULL, 84, NULL, 2, 50, -1, -1, 36, 36, 36, -1, -1, 36, 36, 36, 0, 36, 36, false, 'Oční vyšetření při práci s s trinitrotoluenem se provádí 1x 3 roky.', 0),
        (1132, NULL, 84, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1133, NULL, 84, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1134, NULL, 84, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1135, NULL, 84, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1136, NULL, 84, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1137, NULL, 84, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1138, NULL, 84, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1139, NULL, 84, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1140, NULL, 84, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1141, NULL, 84, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1142, NULL, 84, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1143, NULL, 84, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1144, NULL, 84, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1145, NULL, 84, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1146, NULL, 84, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1147, NULL, 84, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1148, NULL, 85, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1149, NULL, 85, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1150, NULL, 85, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1151, NULL, 85, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1152, NULL, 85, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1153, NULL, 85, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1154, NULL, 85, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1155, NULL, 85, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1156, NULL, 85, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1157, NULL, 85, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1158, NULL, 85, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1159, NULL, 85, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1160, NULL, 85, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1161, NULL, 85, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1162, NULL, 85, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1163, NULL, 85, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1164, NULL, 85, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1165, NULL, 85, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1166, NULL, 85, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1167, NULL, 85, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1168, NULL, 85, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1169, NULL, 85, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1170, NULL, 85, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1171, NULL, 86, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1172, NULL, 86, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1173, NULL, 86, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1174, NULL, 86, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1175, NULL, 86, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1176, NULL, 86, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1177, NULL, 86, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1178, NULL, 86, NULL, 2, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 6, 6, false, 'Při expozici aromatickým amino sloučeninám se provádí chemické vyšetření moče a močového sedimentu 2x za rok.', 0),
        (1179, NULL, 86, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1180, NULL, 86, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1181, NULL, 86, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1182, NULL, 86, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1183, NULL, 86, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1184, NULL, 86, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1185, NULL, 86, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1186, NULL, 86, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1187, NULL, 86, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1188, NULL, 86, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1189, NULL, 86, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1190, NULL, 86, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1191, NULL, 86, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1192, NULL, 86, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1193, NULL, 86, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1194, NULL, 86, NULL, 3, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1195, NULL, 86, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1196, NULL, 86, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1197, NULL, 86, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1198, NULL, 86, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1199, NULL, 86, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1200, NULL, 86, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1201, NULL, 86, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1202, NULL, 86, NULL, 4, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1203, NULL, 86, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1204, NULL, 86, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1205, NULL, 86, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1206, NULL, 86, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1207, NULL, 86, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1208, NULL, 86, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1209, NULL, 86, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1210, NULL, 86, NULL, 5, 29, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1211, NULL, 87, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1212, NULL, 87, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1213, NULL, 87, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1214, NULL, 87, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1215, NULL, 87, NULL, 2, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1216, NULL, 87, NULL, 2, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1217, NULL, 87, NULL, 2, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1218, NULL, 87, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1219, NULL, 87, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1220, NULL, 87, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1221, NULL, 87, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1222, NULL, 87, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1223, NULL, 87, NULL, 1, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1224, NULL, 87, NULL, 1, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1225, NULL, 87, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1226, NULL, 87, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1227, NULL, 87, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1228, NULL, 87, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1229, NULL, 87, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1230, NULL, 87, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1231, NULL, 87, NULL, 4, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1232, NULL, 87, NULL, 4, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1233, NULL, 87, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1234, NULL, 88, NULL, 2, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1235, NULL, 88, NULL, 2, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1236, NULL, 88, NULL, 2, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1237, NULL, 88, NULL, 2, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1238, NULL, 88, NULL, 2, 101, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1239, NULL, 88, NULL, 2, 32, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1240, NULL, 88, NULL, 2, 95, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1241, NULL, 88, NULL, 2, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1242, NULL, 88, NULL, 2, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1243, NULL, 88, NULL, 2, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1244, NULL, 88, NULL, 2, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1245, NULL, 88, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1246, NULL, 88, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1247, NULL, 88, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1248, NULL, 88, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1249, NULL, 88, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1250, NULL, 88, NULL, 1, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1251, NULL, 88, NULL, 1, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1252, NULL, 88, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1253, NULL, 88, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1254, NULL, 88, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1255, NULL, 88, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1256, NULL, 88, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1257, NULL, 88, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1258, NULL, 88, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1259, NULL, 88, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1260, NULL, 88, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1261, NULL, 88, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1262, NULL, 88, NULL, 4, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1263, NULL, 88, NULL, 4, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1264, NULL, 88, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1265, NULL, 88, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1266, NULL, 88, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1267, NULL, 88, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1268, NULL, 88, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1269, NULL, 88, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1270, NULL, 88, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1271, NULL, 88, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1272, NULL, 88, NULL, 5, 101, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1273, NULL, 88, NULL, 5, 32, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1274, NULL, 88, NULL, 5, 95, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1275, NULL, 88, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1276, NULL, 88, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1277, NULL, 88, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1278, NULL, 88, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1279, NULL, 89, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1280, NULL, 89, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1281, NULL, 89, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1282, NULL, 89, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1283, NULL, 89, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1284, NULL, 89, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1285, NULL, 89, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1286, NULL, 89, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1287, NULL, 89, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 24, 24, false, 'RTG hrudníku se provede po 10 letech expozice polycyklickým aromatickým uhlovodíkům a dále se provádí 1x 2 roky.', 0),
        (1288, NULL, 89, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1289, NULL, 89, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1290, NULL, 89, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1291, NULL, 89, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1292, NULL, 89, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1293, NULL, 89, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1294, NULL, 89, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1295, NULL, 89, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1296, NULL, 89, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1297, NULL, 89, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1298, NULL, 89, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1299, NULL, 89, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1300, NULL, 89, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1301, NULL, 89, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1302, NULL, 89, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1303, NULL, 89, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1304, NULL, 89, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1305, NULL, 89, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1306, NULL, 89, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1307, NULL, 89, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1308, NULL, 89, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1309, NULL, 89, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1310, NULL, 89, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1311, NULL, 89, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1312, NULL, 89, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1313, NULL, 89, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1314, NULL, 89, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1315, NULL, 89, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1316, NULL, 90, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1317, NULL, 90, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1318, NULL, 90, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1319, NULL, 90, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1320, NULL, 90, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1321, NULL, 91, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1322, NULL, 91, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1323, NULL, 91, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1324, NULL, 91, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1325, NULL, 91, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1326, NULL, 91, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1327, NULL, 91, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1328, NULL, 91, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1329, NULL, 91, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1330, NULL, 91, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1331, NULL, 91, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1332, NULL, 91, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1333, NULL, 91, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1334, NULL, 91, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1335, NULL, 91, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1336, NULL, 91, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1337, NULL, 91, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1338, NULL, 91, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1339, NULL, 91, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1340, NULL, 91, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1341, NULL, 92, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1342, NULL, 92, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1343, NULL, 92, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1344, NULL, 92, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1345, NULL, 92, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1346, NULL, 92, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1347, NULL, 92, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1348, NULL, 92, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1349, NULL, 92, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1350, NULL, 92, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1351, NULL, 92, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1352, NULL, 92, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1353, NULL, 92, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1354, NULL, 92, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1355, NULL, 92, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1356, NULL, 92, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1357, NULL, 92, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1358, NULL, 92, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1359, NULL, 92, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1360, NULL, 92, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1361, NULL, 92, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1362, NULL, 92, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1363, NULL, 92, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1364, NULL, 92, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1365, NULL, 92, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1366, NULL, 92, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1367, NULL, 93, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1368, NULL, 93, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1369, NULL, 93, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1370, NULL, 93, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1371, NULL, 93, NULL, 2, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1372, NULL, 93, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1373, NULL, 93, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1374, NULL, 93, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1375, NULL, 93, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1376, NULL, 93, NULL, 1, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1377, NULL, 93, NULL, 3, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'EMG v rozsahu stanovení distální motorické latence středových nervů se provádí pro rizikový faktor: 2.47. Karbamátové insekticidy (například inhibitory AChE - aldicarb, carbofuran, methomyl, bendiocarb, carbaryl, pirimicarb) (se zvláštním zřetelem na C, pokud některá z těchto sloučenin má tuto klasifikaci) před zahájením sezónní expozice, v průběhu expozice a po jejím ukončení.', 0),
        (1378, NULL, 93, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1379, NULL, 93, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1380, NULL, 93, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1381, NULL, 93, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1382, NULL, 93, NULL, 4, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1383, NULL, 93, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1384, NULL, 94, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1385, NULL, 94, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1386, NULL, 94, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1387, NULL, 94, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1388, NULL, 94, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1389, NULL, 94, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1390, NULL, 94, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1391, NULL, 94, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1392, NULL, 95, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1393, NULL, 95, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1394, NULL, 95, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1395, NULL, 95, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1396, NULL, 95, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1397, NULL, 95, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1398, NULL, 95, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1399, NULL, 95, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1400, NULL, 95, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1401, NULL, 95, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1402, NULL, 95, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1403, NULL, 95, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1404, NULL, 95, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1405, NULL, 95, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1406, NULL, 95, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1407, NULL, 95, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1408, NULL, 95, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1409, NULL, 96, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1410, NULL, 96, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1411, NULL, 96, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1412, NULL, 96, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1413, NULL, 96, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1414, NULL, 97, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1415, NULL, 97, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1416, NULL, 97, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1417, NULL, 97, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1418, NULL, 97, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1419, NULL, 98, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1420, NULL, 98, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1421, NULL, 98, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1422, NULL, 98, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1423, NULL, 98, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1424, NULL, 99, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1425, NULL, 99, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1426, NULL, 99, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1427, NULL, 99, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1428, NULL, 99, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1429, NULL, 99, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1430, NULL, 99, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1431, NULL, 99, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1432, NULL, 100, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1433, NULL, 100, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1434, NULL, 100, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1435, NULL, 100, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1436, NULL, 100, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1437, NULL, 101, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1438, NULL, 101, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1439, NULL, 101, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1440, NULL, 101, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1441, NULL, 101, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1442, NULL, 101, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1443, NULL, 101, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1444, NULL, 101, NULL, 1, 1, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1445, NULL, 101, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1446, NULL, 101, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1447, NULL, 101, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1448, NULL, 101, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1449, NULL, 101, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1450, NULL, 101, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1451, NULL, 102, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1452, NULL, 102, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1453, NULL, 102, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1454, NULL, 102, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1455, NULL, 102, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku se provede po 10 a více letech od začátku expozice uranu a jeho sloučeninám.', 0),
        (1456, NULL, 102, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1457, NULL, 102, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1458, NULL, 102, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1459, NULL, 102, NULL, 1, 1, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1460, NULL, 102, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1461, NULL, 102, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1462, NULL, 102, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1463, NULL, 102, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1464, NULL, 102, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1465, NULL, 102, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1466, NULL, 102, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1467, NULL, 102, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1468, NULL, 102, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1469, NULL, 102, NULL, 5, 3, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1470, NULL, 102, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 2.53.b Uran a jeho sloučeniny (nerozpustné sloučeniny uranu), individuálně zvážit RTG hrudníku.', 0),
        (1471, NULL, 103, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1472, NULL, 103, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1473, NULL, 103, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1474, NULL, 103, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1475, NULL, 103, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1476, NULL, 103, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1477, NULL, 103, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1478, NULL, 103, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1479, NULL, 103, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1480, NULL, 103, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1481, NULL, 103, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, true, '', 0),
        (1482, NULL, 104, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1483, NULL, 104, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1484, NULL, 104, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1485, NULL, 104, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1486, NULL, 104, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1487, NULL, 105, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1488, NULL, 105, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1489, NULL, 105, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1490, NULL, 105, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1491, NULL, 105, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1492, NULL, 105, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1493, NULL, 105, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1494, NULL, 105, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1495, NULL, 105, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1496, NULL, 105, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1497, NULL, 105, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1498, NULL, 105, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1499, NULL, 105, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1500, NULL, 105, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1501, NULL, 105, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1502, NULL, 105, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1503, NULL, 105, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1504, NULL, 105, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1505, NULL, 105, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1506, NULL, 105, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1507, NULL, 105, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1508, NULL, 105, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1509, NULL, 105, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1510, NULL, 105, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1511, NULL, 105, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1512, NULL, 105, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1513, NULL, 105, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1514, NULL, 105, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1515, NULL, 105, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1516, NULL, 105, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1517, NULL, 105, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1518, NULL, 105, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1519, NULL, 105, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1520, NULL, 106, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1521, NULL, 106, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1522, NULL, 106, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1523, NULL, 106, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1524, NULL, 106, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1525, NULL, 106, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1526, NULL, 106, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1527, NULL, 106, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1528, NULL, 106, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku se provede po 10 a více letech od začátku expozice halogenovým alkyletherům a aryletherům.', 0),
        (1529, NULL, 106, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1530, NULL, 106, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1531, NULL, 106, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1532, NULL, 106, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1533, NULL, 106, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1534, NULL, 106, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1535, NULL, 106, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1536, NULL, 106, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1537, NULL, 106, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1538, NULL, 106, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1539, NULL, 106, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1540, NULL, 106, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1541, NULL, 106, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1542, NULL, 106, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1543, NULL, 106, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1544, NULL, 106, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1545, NULL, 106, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1546, NULL, 106, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1547, NULL, 106, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1548, NULL, 106, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1549, NULL, 106, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1550, NULL, 106, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1551, NULL, 106, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1552, NULL, 106, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1553, NULL, 106, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1554, NULL, 106, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1555, NULL, 106, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1556, NULL, 106, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1557, NULL, 107, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1558, NULL, 107, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1559, NULL, 107, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1560, NULL, 107, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1561, NULL, 107, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1562, NULL, 107, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1563, NULL, 107, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1564, NULL, 107, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1565, NULL, 107, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1566, NULL, 107, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1567, NULL, 107, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1568, NULL, 107, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1569, NULL, 107, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1570, NULL, 107, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1571, NULL, 107, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1572, NULL, 107, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1573, NULL, 107, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1574, NULL, 107, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1575, NULL, 107, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1576, NULL, 107, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1577, NULL, 107, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1578, NULL, 107, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1579, NULL, 107, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1580, NULL, 107, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1581, NULL, 107, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1582, NULL, 107, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1583, NULL, 107, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1584, NULL, 107, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1585, NULL, 107, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1586, NULL, 107, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1587, NULL, 107, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1588, NULL, 107, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1589, NULL, 107, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1590, NULL, 107, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1591, NULL, 109, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1592, NULL, 109, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1593, NULL, 109, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1594, NULL, 109, NULL, 2, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1595, NULL, 109, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1596, NULL, 109, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1597, NULL, 109, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1598, NULL, 109, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1599, NULL, 109, NULL, 1, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1600, NULL, 109, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1601, NULL, 109, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1602, NULL, 109, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1603, NULL, 109, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1604, NULL, 109, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1605, NULL, 109, NULL, 4, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1606, NULL, 109, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1607, NULL, 109, NULL, 5, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, 'Pro rizikový faktor: 3.1. Ionizující záření, u pracovníků, u nichž byly zjištěny kožní změny nebo oční zákaly, nebo u nichž bylo během práce zjištěno významné překročení přípustných dávek na oční čočku nebo na kůži, popřípadě na kostní dřeň, provádět prohlídky zaměřené na možná poškození uvedených orgánů 1x za 1 - 2 roky od skončení expozice v rozsahu výstupní prohlídky.', 0),
        (1608, NULL, 109, NULL, 5, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1609, NULL, 109, NULL, 5, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1610, NULL, 109, NULL, 5, 76, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1611, NULL, 109, NULL, 5, 25, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1612, NULL, 110, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1613, NULL, 110, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1614, NULL, 110, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1615, NULL, 110, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1616, NULL, 110, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1617, NULL, 111, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1618, NULL, 111, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1619, NULL, 111, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1620, NULL, 111, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1621, NULL, 111, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1622, NULL, 111, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1623, NULL, 111, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1624, NULL, 111, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1625, NULL, 111, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1626, NULL, 111, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1627, NULL, 111, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1628, NULL, 111, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1629, NULL, 111, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1630, NULL, 111, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1631, NULL, 112, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1632, NULL, 112, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1633, NULL, 112, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1634, NULL, 112, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1635, NULL, 112, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1636, NULL, 112, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1637, NULL, 112, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1638, NULL, 112, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1639, NULL, 113, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1640, NULL, 113, NULL, 2, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1641, NULL, 113, NULL, 2, 84, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1642, NULL, 113, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Hodnocení ztráty sluchu podle Fowlera, ORL vyšetření a prahová tónová audiometrie se provedou při expozici hluku u rizikových prací po 10 letech od začátku expozice nebo při ztrátách sluchu vyšších jak 20 % podle Fowlera zjištěných screeningovou audiometrií nebo v případě významné progrese ztráty sluchu.', 1),
        (1643, NULL, 113, NULL, 2, 68, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 1),
        (1644, NULL, 113, NULL, 2, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 1),
        (1645, NULL, 113, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1646, NULL, 113, NULL, 1, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1647, NULL, 113, NULL, 1, 69, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1648, NULL, 113, NULL, 1, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1649, NULL, 113, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1650, NULL, 113, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1651, NULL, 113, NULL, 4, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1652, NULL, 113, NULL, 4, 69, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1653, NULL, 113, NULL, 4, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1654, NULL, 113, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1655, NULL, 114, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1656, NULL, 114, NULL, 2, 24, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1657, NULL, 114, NULL, 2, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1658, NULL, 114, NULL, 2, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1659, NULL, 114, NULL, 2, 5, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1660, NULL, 114, NULL, 2, 9, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1661, NULL, 114, NULL, 2, 26, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1662, NULL, 114, NULL, 2, 38, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1663, NULL, 114, NULL, 2, 101, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1664, NULL, 114, NULL, 2, 44, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1665, NULL, 114, NULL, 2, 60, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1666, NULL, 114, NULL, 2, 132, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1667, NULL, 114, NULL, 2, 87, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1668, NULL, 114, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1669, NULL, 114, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1670, NULL, 114, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1671, NULL, 114, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1672, NULL, 114, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1673, NULL, 114, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1674, NULL, 114, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1675, NULL, 114, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1676, NULL, 114, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1677, NULL, 114, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1678, NULL, 114, NULL, 1, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1679, NULL, 114, NULL, 1, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1680, NULL, 114, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1681, NULL, 114, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1682, NULL, 114, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1683, NULL, 114, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1684, NULL, 114, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1685, NULL, 114, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1686, NULL, 114, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1687, NULL, 114, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1688, NULL, 114, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1689, NULL, 114, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1690, NULL, 114, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1691, NULL, 114, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1692, NULL, 114, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1693, NULL, 114, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1694, NULL, 114, NULL, 4, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1695, NULL, 114, NULL, 4, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1696, NULL, 114, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1697, NULL, 114, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1698, NULL, 115, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1699, NULL, 115, NULL, 2, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1700, NULL, 115, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1701, NULL, 115, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1702, NULL, 115, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1703, NULL, 115, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1704, NULL, 115, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1705, NULL, 115, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1706, NULL, 115, NULL, 2, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1707, NULL, 115, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1708, NULL, 115, NULL, 2, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1709, NULL, 115, NULL, 2, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1710, NULL, 115, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1711, NULL, 115, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1712, NULL, 115, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1713, NULL, 115, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1714, NULL, 115, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1715, NULL, 115, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1716, NULL, 115, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1717, NULL, 115, NULL, 1, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1718, NULL, 115, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1719, NULL, 115, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1720, NULL, 115, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1721, NULL, 115, NULL, 1, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1722, NULL, 115, NULL, 1, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1723, NULL, 115, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1724, NULL, 115, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1725, NULL, 115, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1726, NULL, 115, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1727, NULL, 115, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1728, NULL, 115, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1729, NULL, 115, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1730, NULL, 115, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1731, NULL, 115, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1732, NULL, 115, NULL, 4, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1733, NULL, 115, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1734, NULL, 115, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1735, NULL, 115, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1736, NULL, 115, NULL, 4, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1737, NULL, 115, NULL, 4, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1738, NULL, 115, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1739, NULL, 115, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1740, NULL, 116, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1741, NULL, 116, NULL, 2, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1742, NULL, 116, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1743, NULL, 116, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1744, NULL, 116, NULL, 1, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1745, NULL, 116, NULL, 1, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1746, NULL, 116, NULL, 1, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1747, NULL, 116, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1748, NULL, 116, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1749, NULL, 116, NULL, 4, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1750, NULL, 116, NULL, 4, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1751, NULL, 116, NULL, 4, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1752, NULL, 116, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1753, NULL, 117, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1754, NULL, 117, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1755, NULL, 117, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1756, NULL, 117, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1757, NULL, 117, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1758, NULL, 118, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1759, NULL, 118, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1760, NULL, 118, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1761, NULL, 118, NULL, 2, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1762, NULL, 118, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1763, NULL, 118, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1764, NULL, 118, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1765, NULL, 118, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1766, NULL, 118, NULL, 1, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1767, NULL, 118, NULL, 1, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1768, NULL, 118, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1769, NULL, 118, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1770, NULL, 118, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1771, NULL, 118, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1772, NULL, 118, NULL, 4, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1773, NULL, 118, NULL, 4, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1774, NULL, 118, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1775, NULL, 120, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1776, NULL, 120, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1777, NULL, 120, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1778, NULL, 120, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1779, NULL, 120, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1780, NULL, 120, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1781, NULL, 120, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1782, NULL, 120, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1783, NULL, 120, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1784, NULL, 120, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1785, NULL, 120, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1786, NULL, 120, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1787, NULL, 120, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1788, NULL, 120, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1789, NULL, 121, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1790, NULL, 121, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1791, NULL, 121, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1792, NULL, 121, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1793, NULL, 121, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1794, NULL, 122, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1795, NULL, 122, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1796, NULL, 122, NULL, 1, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1797, NULL, 122, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1798, NULL, 122, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1799, NULL, 122, NULL, 4, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1800, NULL, 122, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1801, NULL, 124, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1802, NULL, 124, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1803, NULL, 124, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1804, NULL, 124, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1805, NULL, 124, NULL, 3, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1806, NULL, 124, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1807, NULL, 124, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1808, NULL, 124, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1809, NULL, 125, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1810, NULL, 125, NULL, 2, 52, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1811, NULL, 125, NULL, 2, 99, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1812, NULL, 125, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1813, NULL, 125, NULL, 1, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1814, NULL, 125, NULL, 1, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1815, NULL, 125, NULL, 3, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1816, NULL, 125, NULL, 3, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1817, NULL, 125, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1818, NULL, 125, NULL, 4, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1819, NULL, 125, NULL, 4, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1820, NULL, 125, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1821, NULL, 126, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1822, NULL, 126, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1823, NULL, 126, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1824, NULL, 126, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1825, NULL, 126, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1826, NULL, 128, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1827, NULL, 128, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1828, NULL, 128, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1829, NULL, 128, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1830, NULL, 128, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1831, NULL, 128, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1832, NULL, 128, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1833, NULL, 128, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1834, NULL, 128, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1835, NULL, 128, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1836, NULL, 129, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1837, NULL, 129, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1838, NULL, 129, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice prachům s fibrogenním účinkem nebo možným fibrogenním a karcinogenním účinkem a dále se provádí 1x 2 roky.', 0),
        (1839, NULL, 129, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1840, NULL, 129, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1841, NULL, 129, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice prachům s fibrogenním účinkem nebo možným fibrogenním a karcinogenním účinkem a dále se provádí 1x 2 roky.', 0),
        (1842, NULL, 129, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1843, NULL, 129, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1844, NULL, 129, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1845, NULL, 129, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1846, NULL, 129, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1847, NULL, 129, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1848, NULL, 129, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1849, NULL, 129, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1850, NULL, 129, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1851, NULL, 129, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1852, NULL, 130, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1853, NULL, 130, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1854, NULL, 130, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1855, NULL, 130, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1856, NULL, 130, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1857, NULL, 130, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1858, NULL, 130, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1859, NULL, 130, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1860, NULL, 131, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1861, NULL, 131, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1862, NULL, 131, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 48, 48, false, 'RTG hrudníku se provede poprvé po 4 letech expozice svářečských dýmů u svařování elektrickým obloukem a dále se provádí 1x 4 roky.', 0),
        (1863, NULL, 131, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1864, NULL, 131, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1865, NULL, 131, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1866, NULL, 131, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1867, NULL, 131, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1868, NULL, 131, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1869, NULL, 131, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1870, NULL, 131, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1871, NULL, 132, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1872, NULL, 132, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1873, NULL, 132, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1874, NULL, 132, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1875, NULL, 132, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1876, NULL, 132, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice krátkodobým produktům přeměny radonu a dále se provádí 1x 2 roky.', 0),
        (1877, NULL, 132, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1878, NULL, 132, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1879, NULL, 132, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1880, NULL, 132, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1881, NULL, 132, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1882, NULL, 132, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1883, NULL, 132, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1884, NULL, 132, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1885, NULL, 132, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1886, NULL, 132, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1887, NULL, 132, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1888, NULL, 132, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1889, NULL, 132, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1890, NULL, 132, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1891, NULL, 132, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1892, NULL, 132, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1893, NULL, 132, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1894, NULL, 132, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1895, NULL, 132, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1896, NULL, 133, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1897, NULL, 133, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1898, NULL, 133, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1899, NULL, 133, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1900, NULL, 133, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1901, NULL, 133, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1902, NULL, 133, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1903, NULL, 133, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1904, NULL, 133, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1905, NULL, 133, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 96, 0, 0, false, 'RTG hrudníku se provede poprvé po 8 letech expozice koksárenským plynům a zplyňování uhlí.', 0),
        (1906, NULL, 133, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1907, NULL, 133, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1908, NULL, 133, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1909, NULL, 133, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1910, NULL, 133, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1911, NULL, 133, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1912, NULL, 133, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1913, NULL, 133, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1914, NULL, 133, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1915, NULL, 133, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1916, NULL, 133, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1917, NULL, 133, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1918, NULL, 133, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1919, NULL, 133, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1920, NULL, 133, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1921, NULL, 133, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1922, NULL, 133, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1923, NULL, 133, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1924, NULL, 133, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1925, NULL, 133, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1926, NULL, 133, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1927, NULL, 133, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1928, NULL, 133, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1929, NULL, 133, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1930, NULL, 133, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1931, NULL, 133, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1932, NULL, 133, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1933, NULL, 133, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1934, NULL, 133, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1935, NULL, 133, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1936, NULL, 133, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1937, NULL, 134, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1938, NULL, 134, NULL, 2, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1939, NULL, 134, NULL, 2, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1940, NULL, 134, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'ORL vyšetření, rinoskopie rinoskopem a spirometrie se provedou po 10 letech od začátku expozice prachům tvrdých dřev.', 0),
        (1941, NULL, 134, NULL, 2, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (1942, NULL, 134, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (1943, NULL, 134, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1944, NULL, 134, NULL, 1, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1945, NULL, 134, NULL, 1, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zrcátkem provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie rinoskopem.', 2),
        (1946, NULL, 134, NULL, 1, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie rinoskopem provádí u zaměstnanců, kteří dovršili 45 let věku. Do 45 let věku se provádí rinoskopie zrcátkem.', 3),
        (1947, NULL, 134, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1948, NULL, 134, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1949, NULL, 134, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1950, NULL, 134, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se ORL vyšetření provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie zrcátkem.', 2),
        (1951, NULL, 134, NULL, 4, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zcátkem provádí po dovršení 45 let věku zaměstnance. Do 45 let se provádí ORL vyšetření.', 3),
        (1952, NULL, 134, NULL, 4, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1953, NULL, 134, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1954, NULL, 134, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1955, NULL, 134, NULL, 5, 55, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se ORL vyšetření se provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie zrcátkem.', 2),
        (1956, NULL, 134, NULL, 5, 79, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zrcátkem provádí po dovršení 45 let věku zaměstnance. Do 45 let se provádí ORL vyšetření.', 3),
        (1957, NULL, 134, NULL, 5, 78, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1958, NULL, 134, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1959, NULL, 135, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1960, NULL, 135, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1961, NULL, 135, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1962, NULL, 135, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1963, NULL, 135, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1964, NULL, 135, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1965, NULL, 135, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1966, NULL, 135, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1967, NULL, 136, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1968, NULL, 136, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1969, NULL, 136, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1970, NULL, 136, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1971, NULL, 136, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1972, NULL, 136, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1973, NULL, 136, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1974, NULL, 136, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1975, NULL, 138, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1976, NULL, 138, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1977, NULL, 138, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1978, NULL, 138, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1979, NULL, 138, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1980, NULL, 140, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1981, NULL, 140, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1982, NULL, 140, NULL, 1, 94, -1, -1, -1, -1, 0, -1, -1, -1, -1, 0, 0, 0, 0, false, '', 0),
        (1983, NULL, 140, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1984, NULL, 140, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1985, NULL, 140, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1986, NULL, 141, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1987, NULL, 141, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1988, NULL, 141, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1989, NULL, 141, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1990, NULL, 141, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1991, NULL, 143, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1992, NULL, 143, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1993, NULL, 143, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1994, NULL, 143, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1995, NULL, 143, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1996, NULL, 143, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1997, NULL, 143, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1998, NULL, 143, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1999, NULL, 143, NULL, 1, 75, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2000, NULL, 143, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2001, NULL, 143, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2002, NULL, 143, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2003, NULL, 143, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2004, NULL, 143, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2005, NULL, 143, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2006, NULL, 143, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2007, NULL, 143, NULL, 5, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 6, 0, 0, false, 'Pro rizikový faktor: 8.1. Tuberkulóza se za 6-12 měsíců po ukončení rizikové práce provede RTG hrudníku.', 0),
        (2008, NULL, 144, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2009, NULL, 144, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2010, NULL, 144, NULL, 2, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2011, NULL, 144, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2012, NULL, 144, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2013, NULL, 144, NULL, 1, 86, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2014, NULL, 144, NULL, 1, 7, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2015, NULL, 144, NULL, 1, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2016, NULL, 144, NULL, 1, 36, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2017, NULL, 144, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2018, NULL, 144, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2019, NULL, 144, NULL, 4, 7, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2020, NULL, 144, NULL, 4, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2021, NULL, 144, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2022, NULL, 145, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2023, NULL, 145, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2024, NULL, 145, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2025, NULL, 145, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2026, NULL, 145, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2027, NULL, 145, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2028, NULL, 145, NULL, 1, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2029, NULL, 145, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2030, NULL, 145, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2031, NULL, 145, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2032, NULL, 145, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2033, NULL, 145, NULL, 4, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2034, NULL, 145, NULL, 5, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, 'Pro rizikový faktor: 8.3. Syndrom získané imunodeficience (AIDS) je za 3 měsíce po ukončení rizikové práce, nutné individuálně zvážit provedení sérologie AIDS (HIV).', 0),
        (2035, NULL, 146, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2036, NULL, 146, NULL, 2, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2037, NULL, 146, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2038, NULL, 146, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2039, NULL, 146, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2040, NULL, 146, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2041, NULL, 146, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2042, NULL, 146, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2043, NULL, 146, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2044, NULL, 146, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2045, NULL, 146, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2046, NULL, 146, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2047, NULL, 146, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2048, NULL, 146, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2049, NULL, 146, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2050, NULL, 146, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2051, NULL, 146, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2052, NULL, 146, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2053, NULL, 146, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2054, NULL, 146, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2055, NULL, 147, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2056, NULL, 147, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2057, NULL, 147, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2058, NULL, 147, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2059, NULL, 147, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2060, NULL, 147, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2061, NULL, 147, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2062, NULL, 147, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2063, NULL, 147, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2064, NULL, 147, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2065, NULL, 147, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2066, NULL, 147, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2067, NULL, 147, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2068, NULL, 147, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2069, 20, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2070, 20, NULL, NULL, 2, 21, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 24, 0, 0, false, 'EKG po 2 letech v profesním riziku: Práce v hlubinných dolech', 0),
        (2071, 20, NULL, NULL, 2, 82, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech práce v profesním riziku: Práce v hlubinných dolech, následně vždy po 2 letech.', 0),
        (2072, 22, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2073, 22, NULL, NULL, 2, 22, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 24, 12, false, '', 0),
        (2074, 22, NULL, NULL, 2, 87, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2075, 1, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2076, 2, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2077, 3, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2078, 4, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2079, 5, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2080, 6, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2081, 7, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2082, 8, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2083, 9, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2084, 10, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2085, 11, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2086, 11, NULL, NULL, 2, 123, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2087, 12, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2088, 13, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2089, 14, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2090, 15, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2091, 16, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2092, 17, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2093, 18, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2094, 19, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2095, 21, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2096, 21, NULL, NULL, 2, 137, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2097, 21, NULL, NULL, 2, 80, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2098, 23, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2099, 24, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2100, 25, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2101, 26, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2102, 26, NULL, NULL, 2, 15, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2103, 27, NULL, NULL, 2, 126, -1, 48, 48, 48, 48, -1, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2104, 27, NULL, NULL, 2, 15, -1, 48, 48, 48, 48, -1, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2105, 20, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2106, 20, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2107, 20, NULL, NULL, 1, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2108, 22, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2109, 22, NULL, NULL, 1, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2110, 22, NULL, NULL, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2111, 1, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2112, 2, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2113, 3, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2114, 4, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2115, 5, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2116, 6, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2117, 7, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2118, 8, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2119, 9, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2120, 10, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2121, 11, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2122, 11, NULL, NULL, 1, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2123, 12, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2124, 13, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2125, 14, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2126, 15, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2127, 16, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2128, 17, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2129, 18, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2130, 19, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2131, 21, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2132, 21, NULL, NULL, 1, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2133, 21, NULL, NULL, 1, 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2134, 23, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2135, 23, NULL, NULL, 1, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2136, 23, NULL, NULL, 1, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2137, 23, NULL, NULL, 1, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2138, 23, NULL, NULL, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2139, 23, NULL, NULL, 1, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2140, 23, NULL, NULL, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2141, 23, NULL, NULL, 1, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2142, 23, NULL, NULL, 1, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2143, 23, NULL, NULL, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2144, 23, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2145, 24, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2146, 24, NULL, NULL, 1, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2147, 24, NULL, NULL, 1, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2148, 24, NULL, NULL, 1, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2149, 24, NULL, NULL, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2150, 24, NULL, NULL, 1, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2151, 24, NULL, NULL, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2152, 24, NULL, NULL, 1, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2153, 24, NULL, NULL, 1, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2154, 24, NULL, NULL, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2155, 24, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2156, 24, NULL, NULL, 1, 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2157, 25, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2158, 26, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2159, 26, NULL, NULL, 1, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2160, 27, NULL, NULL, 1, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2161, 27, NULL, NULL, 1, 15, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2162, 20, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2163, 22, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2164, 1, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2165, 2, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2166, 3, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2167, 4, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2168, 5, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2169, 6, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2170, 7, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2171, 8, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2172, 9, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2173, 10, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2174, 11, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2175, 12, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2176, 13, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2177, 14, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2178, 15, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2179, 16, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2180, 17, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2181, 18, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2182, 19, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2183, 21, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2184, 23, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2185, 24, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2186, 25, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2187, 26, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2188, 27, NULL, NULL, 3, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2189, 20, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2190, 20, NULL, NULL, 4, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2191, 20, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2192, 22, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2193, 22, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2194, 1, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2195, 1, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2196, 2, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2197, 2, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2198, 3, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2199, 3, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2200, 4, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2201, 4, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2202, 5, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2203, 5, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2204, 6, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2205, 6, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2206, 7, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2207, 7, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2208, 8, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2209, 8, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2210, 9, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2211, 9, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2212, 10, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2213, 10, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2214, 11, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2215, 11, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2216, 12, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2217, 12, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2218, 13, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2219, 13, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2220, 14, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2221, 14, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2222, 15, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2223, 15, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2224, 16, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2225, 16, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2226, 17, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2227, 17, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2228, 18, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2229, 18, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2230, 19, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2231, 19, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2232, 21, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2233, 21, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2234, 23, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2235, 23, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2236, 24, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2237, 24, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2238, 25, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2239, 25, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2240, 26, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2241, 26, NULL, NULL, 4, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2242, 26, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2243, 27, NULL, NULL, 4, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2244, 27, NULL, NULL, 4, 15, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2245, 27, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2246, NULL, NULL, 7, 1, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2247, NULL, NULL, 7, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2248, NULL, NULL, 7, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2249, NULL, NULL, 7, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2250, NULL, NULL, 7, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2251, NULL, NULL, 7, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2252, NULL, NULL, 7, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2253, NULL, NULL, 7, 1, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2254, NULL, NULL, 7, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2255, NULL, NULL, 7, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2256, NULL, NULL, 7, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2257, NULL, NULL, 7, 1, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2258, NULL, NULL, 7, 2, 35, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2259, NULL, NULL, 7, 2, 122, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2260, NULL, NULL, 7, 2, 115, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2261, NULL, NULL, 7, 2, 102, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2262, NULL, NULL, 7, 2, 108, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2263, NULL, NULL, 7, 2, 47, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2264, NULL, NULL, 7, 2, 30, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2265, NULL, NULL, 7, 2, 14, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2266, NULL, NULL, 7, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2267, NULL, NULL, 7, 4, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2268, NULL, NULL, 7, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2269, NULL, NULL, 7, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2270, NULL, NULL, 7, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2271, NULL, NULL, 7, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2272, NULL, NULL, 7, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2273, NULL, NULL, 7, 4, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2274, NULL, NULL, 7, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2275, NULL, NULL, 2, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2276, NULL, NULL, 2, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2277, NULL, NULL, 2, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2278, NULL, NULL, 2, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2279, NULL, NULL, 2, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2280, NULL, NULL, 2, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2281, NULL, NULL, 2, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2282, NULL, NULL, 2, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2283, NULL, NULL, 2, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2284, NULL, NULL, 2, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2285, NULL, NULL, 2, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2286, NULL, NULL, 2, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2287, NULL, NULL, 2, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2288, NULL, NULL, 2, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2289, NULL, NULL, 2, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2290, NULL, NULL, 2, 1, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2291, NULL, NULL, 2, 1, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2292, NULL, NULL, 2, 1, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2293, NULL, NULL, 2, 1, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2294, NULL, NULL, 2, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2295, NULL, NULL, 2, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2296, NULL, NULL, 2, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2297, NULL, NULL, 2, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2298, NULL, NULL, 2, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2299, NULL, NULL, 2, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2300, NULL, NULL, 2, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2301, NULL, NULL, 2, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2302, NULL, NULL, 2, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2303, NULL, NULL, 2, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2304, NULL, NULL, 2, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2305, NULL, NULL, 2, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2306, NULL, NULL, 2, 2, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2307, NULL, NULL, 2, 2, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2308, NULL, NULL, 2, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2309, NULL, NULL, 2, 2, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2310, NULL, NULL, 2, 2, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2311, NULL, NULL, 2, 2, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2312, NULL, NULL, 2, 2, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2313, NULL, NULL, 2, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2314, NULL, NULL, 2, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2315, NULL, NULL, 2, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2316, NULL, NULL, 2, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2317, NULL, NULL, 2, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2318, NULL, NULL, 2, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2319, NULL, NULL, 2, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2320, NULL, NULL, 2, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2321, NULL, NULL, 2, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2322, NULL, NULL, 2, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2323, NULL, NULL, 2, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2324, NULL, NULL, 2, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2325, NULL, NULL, 2, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2326, NULL, NULL, 2, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2327, NULL, NULL, 2, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2328, NULL, NULL, 2, 3, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2329, NULL, NULL, 2, 3, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2330, NULL, NULL, 2, 3, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2331, NULL, NULL, 2, 3, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2332, NULL, NULL, 2, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2333, NULL, NULL, 2, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2334, NULL, NULL, 3, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2335, NULL, NULL, 3, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2336, NULL, NULL, 3, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2337, NULL, NULL, 3, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2338, NULL, NULL, 3, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2339, NULL, NULL, 3, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2340, NULL, NULL, 3, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2341, NULL, NULL, 3, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2342, NULL, NULL, 3, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2343, NULL, NULL, 3, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2344, NULL, NULL, 3, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2345, NULL, NULL, 3, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2346, NULL, NULL, 3, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2347, NULL, NULL, 3, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2348, NULL, NULL, 3, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2349, NULL, NULL, 3, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2350, NULL, NULL, 3, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2351, NULL, NULL, 3, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2352, NULL, NULL, 3, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2353, NULL, NULL, 3, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2354, NULL, NULL, 3, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2355, NULL, NULL, 3, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2356, NULL, NULL, 3, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2357, NULL, NULL, 3, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2358, NULL, NULL, 3, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2359, NULL, NULL, 3, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2360, NULL, NULL, 3, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2361, NULL, NULL, 3, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2362, NULL, NULL, 3, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2363, NULL, NULL, 3, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2364, NULL, NULL, 3, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2365, NULL, NULL, 3, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2366, NULL, NULL, 3, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2367, NULL, NULL, 3, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2368, NULL, NULL, 3, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2369, NULL, NULL, 3, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2370, NULL, NULL, 3, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2371, NULL, NULL, 3, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2372, NULL, NULL, 4, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2373, NULL, NULL, 4, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2374, NULL, NULL, 4, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2375, NULL, NULL, 4, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2376, NULL, NULL, 4, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2377, NULL, NULL, 4, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2378, NULL, NULL, 4, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2379, NULL, NULL, 4, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2380, NULL, NULL, 4, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2381, NULL, NULL, 4, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2382, NULL, NULL, 4, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2383, NULL, NULL, 4, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2384, NULL, NULL, 4, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2385, NULL, NULL, 4, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2386, NULL, NULL, 4, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2387, NULL, NULL, 4, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2388, NULL, NULL, 4, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2389, NULL, NULL, 4, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2390, NULL, NULL, 4, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2391, NULL, NULL, 4, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2392, NULL, NULL, 4, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2393, NULL, NULL, 4, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2394, NULL, NULL, 4, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2395, NULL, NULL, 4, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2396, NULL, NULL, 4, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2397, NULL, NULL, 4, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2398, NULL, NULL, 4, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2399, NULL, NULL, 4, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2400, NULL, NULL, 4, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2401, NULL, NULL, 4, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2402, NULL, NULL, 4, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2403, NULL, NULL, 4, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2404, NULL, NULL, 4, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2405, NULL, NULL, 4, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2406, NULL, NULL, 4, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2407, NULL, NULL, 4, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2408, NULL, NULL, 4, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2409, NULL, NULL, 4, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2410, NULL, NULL, 5, 1, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2411, NULL, NULL, 5, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2412, NULL, NULL, 5, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2413, NULL, NULL, 5, 1, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2414, NULL, NULL, 5, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2415, NULL, NULL, 5, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2416, NULL, NULL, 5, 1, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2417, NULL, NULL, 5, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2418, NULL, NULL, 5, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2419, NULL, NULL, 5, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2420, NULL, NULL, 5, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2421, NULL, NULL, 5, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2422, NULL, NULL, 5, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2423, NULL, NULL, 5, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2424, NULL, NULL, 5, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2425, NULL, NULL, 5, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2426, NULL, NULL, 5, 1, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2427, NULL, NULL, 5, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2428, NULL, NULL, 5, 1, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2429, NULL, NULL, 5, 1, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2430, NULL, NULL, 5, 1, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2431, NULL, NULL, 5, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2432, NULL, NULL, 5, 1, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2433, NULL, NULL, 5, 1, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2434, NULL, NULL, 5, 1, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2435, NULL, NULL, 5, 2, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2436, NULL, NULL, 5, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2437, NULL, NULL, 5, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2438, NULL, NULL, 5, 2, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2439, NULL, NULL, 5, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2440, NULL, NULL, 5, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2441, NULL, NULL, 5, 2, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2442, NULL, NULL, 5, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2443, NULL, NULL, 5, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2444, NULL, NULL, 5, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2445, NULL, NULL, 5, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2446, NULL, NULL, 5, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2447, NULL, NULL, 5, 2, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2448, NULL, NULL, 5, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2449, NULL, NULL, 5, 2, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2450, NULL, NULL, 5, 2, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2451, NULL, NULL, 5, 2, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2452, NULL, NULL, 5, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2453, NULL, NULL, 5, 2, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2454, NULL, NULL, 5, 2, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2455, NULL, NULL, 5, 2, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2456, NULL, NULL, 5, 2, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2457, NULL, NULL, 5, 2, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2458, NULL, NULL, 5, 2, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2459, NULL, NULL, 5, 2, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2460, NULL, NULL, 5, 3, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2461, NULL, NULL, 5, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2462, NULL, NULL, 5, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2463, NULL, NULL, 5, 3, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2464, NULL, NULL, 5, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2465, NULL, NULL, 5, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2466, NULL, NULL, 5, 3, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2467, NULL, NULL, 5, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2468, NULL, NULL, 5, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2469, NULL, NULL, 5, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2470, NULL, NULL, 5, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2471, NULL, NULL, 5, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2472, NULL, NULL, 5, 3, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2473, NULL, NULL, 5, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2474, NULL, NULL, 5, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2475, NULL, NULL, 5, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2476, NULL, NULL, 5, 3, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2477, NULL, NULL, 5, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2478, NULL, NULL, 5, 3, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2479, NULL, NULL, 5, 3, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2480, NULL, NULL, 5, 3, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2481, NULL, NULL, 5, 3, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2482, NULL, NULL, 5, 3, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2483, NULL, NULL, 5, 3, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2484, NULL, NULL, 5, 3, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2485, NULL, NULL, 5, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2486, NULL, NULL, 5, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2487, NULL, NULL, 6, 1, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2488, NULL, NULL, 6, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2489, NULL, NULL, 6, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2490, NULL, NULL, 6, 1, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2491, NULL, NULL, 6, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2492, NULL, NULL, 6, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2493, NULL, NULL, 6, 1, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2494, NULL, NULL, 6, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2495, NULL, NULL, 6, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2496, NULL, NULL, 6, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2497, NULL, NULL, 6, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2498, NULL, NULL, 6, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2499, NULL, NULL, 6, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2500, NULL, NULL, 6, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2501, NULL, NULL, 6, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2502, NULL, NULL, 6, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2503, NULL, NULL, 6, 1, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2504, NULL, NULL, 6, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2505, NULL, NULL, 6, 1, 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2506, NULL, NULL, 6, 1, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2507, NULL, NULL, 6, 1, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2508, NULL, NULL, 6, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2509, NULL, NULL, 6, 1, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2510, NULL, NULL, 6, 1, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2511, NULL, NULL, 6, 1, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2512, NULL, NULL, 6, 2, 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2513, NULL, NULL, 6, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2514, NULL, NULL, 6, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2515, NULL, NULL, 6, 2, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2516, NULL, NULL, 6, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2517, NULL, NULL, 6, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2518, NULL, NULL, 6, 2, 114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2519, NULL, NULL, 6, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2520, NULL, NULL, 6, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2521, NULL, NULL, 6, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2522, NULL, NULL, 6, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2523, NULL, NULL, 6, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření elektrokardiografické se pro: Strojvedoucí - licence
(k odst. (5), § 7, vyhlášky č. 260/2023 Sb., úz) provede pouze u osob starších 40 let.', 7),
        (2524, NULL, NULL, 6, 3, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2525, NULL, NULL, 6, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2526, NULL, NULL, 6, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2527, NULL, NULL, 6, 3, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2528, NULL, NULL, 6, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2529, NULL, NULL, 6, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2530, NULL, NULL, 6, 3, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2531, NULL, NULL, 6, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2532, NULL, NULL, 6, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2533, NULL, NULL, 6, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2534, NULL, NULL, 6, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2535, NULL, NULL, 6, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2536, NULL, NULL, 6, 3, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2537, NULL, NULL, 6, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2538, NULL, NULL, 6, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2539, NULL, NULL, 6, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2540, NULL, NULL, 6, 3, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2541, NULL, NULL, 6, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2542, NULL, NULL, 6, 3, 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2543, NULL, NULL, 6, 3, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2544, NULL, NULL, 6, 3, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2545, NULL, NULL, 6, 3, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2546, NULL, NULL, 6, 3, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2547, NULL, NULL, 6, 3, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2548, NULL, NULL, 6, 3, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Jsou-li splněny podmínky pro provedení mimořádné lékařské prohlídky držitele licence strojvedoucího, a považuje-li to posuzující lékař za vhodné vzhledem k důvodu provádění mimořádné lékařské prohlídky nebo vzhledem ke skutečnostem zjištěným při mimořádné lékařské prohlídce, provede se u držitele licence strojvedoucího rovněž dopravně psychologické vyšetření podle § 4, vyhlášky č. 260/2023 Sb.', 0),
        (2549, NULL, NULL, 6, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2550, NULL, NULL, 6, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2551, NULL, NULL, 1, 1, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2552, NULL, NULL, 1, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2553, NULL, NULL, 1, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2554, NULL, NULL, 1, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2555, NULL, NULL, 1, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2556, NULL, NULL, 1, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2557, NULL, NULL, 1, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2558, NULL, NULL, 1, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2559, NULL, NULL, 1, 1, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Dopravně psychologickému vyšetření je povinen se podrobovat:
a) držitel řidičského oprávnění pro skupinu C1+E, C nebo C+E, pokud řídí nákladní automobil o největší povolené hmotnosti převyšující 7 500 kg nebo speciální automobil o největší povolené hmotnosti převyšující 7 500 kg nebo jízdní soupravu, která je složena z nákladního automobilu a přípojného vozidla nebo ze speciálního automobilu a přípojného vozidla a jejíž největší povolená hmotnost převyšuje 7 500 kg,
b) držitel řidičského oprávnění pro skupinu D1+E, D nebo D+E, pokud řídí motorové vozidlo zařazené do některé z těchto skupin vozidel.

Povinnost podrobit se dopravně psychologickému vyšetření před zahájením výkonu činnosti nevzniká, podrobil-li se držitel řidičského oprávnění uvedený v odstavci 1 dopravně psychologickému vyšetření před získáním tohoto řidičského oprávnění, a ode dne provedení vyšetření neuplynulo ke dni zahájení výkonu činnosti více než 6 měsíců.', 0),
        (2560, NULL, NULL, 1, 2, 131, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2561, NULL, NULL, 1, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2562, NULL, NULL, 1, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2563, NULL, NULL, 1, 2, 121, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2564, NULL, NULL, 1, 2, 99, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2565, NULL, NULL, 1, 2, 120, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2566, NULL, NULL, 1, 2, 110, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2567, NULL, NULL, 1, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2568, NULL, NULL, 1, 2, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, true, 'Dopravně psychologickému vyšetření je povinen se podrobovat:
a) držitel řidičského oprávnění pro skupinu C1+E, C nebo C+E, pokud řídí nákladní automobil o největší povolené hmotnosti převyšující 7 500 kg nebo speciální automobil o největší povolené hmotnosti převyšující 7 500 kg nebo jízdní soupravu, která je složena z nákladního automobilu a přípojného vozidla nebo ze speciálního automobilu a přípojného vozidla a jejíž největší povolená hmotnost převyšuje 7 500 kg,
b) držitel řidičského oprávnění pro skupinu D1+E, D nebo D+E, pokud řídí motorové vozidlo zařazené do některé z těchto skupin vozidel.

Kromě dopravně psychologickému vyšetření při zahájení činnosti řidiče, je nutné se podrobit i dalšímu dopravně psychologickému vyšetření nejdříve šest měsíců před dovršením 50 let a nejpozději v den dovršení 50 let a dále pak každých pět let. ', 0),
        (2569, NULL, NULL, 1, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2570, NULL, NULL, 1, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2571, NULL, NULL, 1, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2572, NULL, NULL, 8, 1, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2573, NULL, NULL, 8, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2574, NULL, NULL, 8, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2575, NULL, NULL, 8, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2576, NULL, NULL, 8, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2577, NULL, NULL, 8, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2578, NULL, NULL, 8, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2579, NULL, NULL, 8, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2580, NULL, NULL, 8, 1, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2581, NULL, NULL, 8, 1, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2582, NULL, NULL, 8, 1, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2583, NULL, NULL, 8, 2, 130, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2584, NULL, NULL, 8, 2, 34, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2585, NULL, NULL, 8, 2, 115, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2586, NULL, NULL, 8, 2, 121, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2587, NULL, NULL, 8, 2, 99, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2588, NULL, NULL, 8, 2, 120, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2589, NULL, NULL, 8, 2, 110, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2590, NULL, NULL, 8, 2, 47, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2591, NULL, NULL, 8, 2, 107, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2592, NULL, NULL, 8, 2, 18, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2593, NULL, NULL, 8, 2, 53, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2594, NULL, NULL, 8, 3, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2595, NULL, NULL, 8, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2596, NULL, NULL, 8, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2597, NULL, NULL, 8, 3, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2598, NULL, NULL, 8, 3, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2599, NULL, NULL, 8, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2600, NULL, NULL, 8, 3, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2601, NULL, NULL, 8, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2602, NULL, NULL, 8, 3, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2603, NULL, NULL, 8, 3, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2604, NULL, NULL, 8, 3, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2605, NULL, NULL, 8, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2606, NULL, NULL, 8, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2607, NULL, NULL, 9, 1, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2608, NULL, NULL, 9, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2609, NULL, NULL, 9, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2610, NULL, NULL, 9, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2611, NULL, NULL, 9, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2612, NULL, NULL, 9, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2613, NULL, NULL, 9, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2614, NULL, NULL, 9, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2615, NULL, NULL, 9, 1, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2616, NULL, NULL, 9, 1, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2617, NULL, NULL, 9, 1, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2618, NULL, NULL, 9, 2, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2619, NULL, NULL, 9, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2620, NULL, NULL, 9, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2621, NULL, NULL, 10, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2622, NULL, NULL, 10, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2623, NULL, NULL, 10, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2624, NULL, NULL, 10, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2625, NULL, NULL, 10, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2626, NULL, NULL, 11, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2627, NULL, NULL, 11, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2628, NULL, NULL, 11, 2, 126, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2629, NULL, NULL, 11, 2, 50, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2630, NULL, NULL, 11, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2631, NULL, NULL, 11, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2632, NULL, NULL, 11, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2633, NULL, NULL, 11, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2634, NULL, NULL, 12, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2635, NULL, NULL, 12, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2636, NULL, NULL, 12, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2637, NULL, NULL, 12, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2638, NULL, NULL, 12, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2639, NULL, NULL, 12, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2640, NULL, NULL, 12, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2641, NULL, NULL, 12, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2642, NULL, NULL, 12, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2643, NULL, NULL, 12, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2644, NULL, NULL, 12, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2645, NULL, NULL, 12, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2646, NULL, NULL, 12, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2647, NULL, NULL, 12, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2648, NULL, NULL, 12, 1, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2649, NULL, NULL, 12, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2650, NULL, NULL, 12, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2651, NULL, NULL, 12, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2652, NULL, NULL, 12, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2653, NULL, NULL, 12, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2654, NULL, NULL, 12, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2655, NULL, NULL, 12, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2656, NULL, NULL, 12, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2657, NULL, NULL, 12, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2658, NULL, NULL, 12, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2659, NULL, NULL, 12, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie I příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2660, NULL, NULL, 12, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2661, NULL, NULL, 12, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2662, NULL, NULL, 12, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2663, NULL, NULL, 12, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2664, NULL, NULL, 12, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2665, NULL, NULL, 12, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2666, NULL, NULL, 12, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2667, NULL, NULL, 13, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2668, NULL, NULL, 13, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2669, NULL, NULL, 13, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2670, NULL, NULL, 13, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2671, NULL, NULL, 13, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2672, NULL, NULL, 13, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2673, NULL, NULL, 13, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2674, NULL, NULL, 13, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2675, NULL, NULL, 13, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2676, NULL, NULL, 13, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2677, NULL, NULL, 13, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2678, NULL, NULL, 13, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2679, NULL, NULL, 13, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2680, NULL, NULL, 13, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2681, NULL, NULL, 13, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2682, NULL, NULL, 13, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2683, NULL, NULL, 13, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2684, NULL, NULL, 13, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2685, NULL, NULL, 13, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2686, NULL, NULL, 13, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2687, NULL, NULL, 13, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2688, NULL, NULL, 13, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2689, NULL, NULL, 13, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2690, NULL, NULL, 13, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie II příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2691, NULL, NULL, 13, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2692, NULL, NULL, 13, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2693, NULL, NULL, 13, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2694, NULL, NULL, 13, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2695, NULL, NULL, 13, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2696, NULL, NULL, 13, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2697, NULL, NULL, 13, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2698, NULL, NULL, 14, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2699, NULL, NULL, 14, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2700, NULL, NULL, 14, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2701, NULL, NULL, 14, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2702, NULL, NULL, 14, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2703, NULL, NULL, 14, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2704, NULL, NULL, 14, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2705, NULL, NULL, 14, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2706, NULL, NULL, 14, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2707, NULL, NULL, 14, 2, 34, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2708, NULL, NULL, 14, 2, 122, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2709, NULL, NULL, 14, 2, 115, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2710, NULL, NULL, 14, 2, 102, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2711, NULL, NULL, 14, 2, 108, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2712, NULL, NULL, 14, 2, 47, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2713, NULL, NULL, 14, 2, 30, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2714, NULL, NULL, 14, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie III příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2715, NULL, NULL, 14, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2716, NULL, NULL, 14, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2717, NULL, NULL, 14, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2718, NULL, NULL, 14, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2719, NULL, NULL, 14, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2720, NULL, NULL, 14, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2721, NULL, NULL, 14, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2722, NULL, NULL, 15, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2723, NULL, NULL, 15, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2724, NULL, NULL, 15, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2725, NULL, NULL, 15, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2726, NULL, NULL, 15, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2727, NULL, NULL, 15, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2728, NULL, NULL, 15, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2729, NULL, NULL, 15, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2730, NULL, NULL, 15, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2731, NULL, NULL, 15, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2732, NULL, NULL, 15, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2733, NULL, NULL, 15, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2734, NULL, NULL, 15, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2735, NULL, NULL, 15, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2736, NULL, NULL, 15, 1, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2737, NULL, NULL, 15, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2738, NULL, NULL, 15, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2739, NULL, NULL, 15, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2740, NULL, NULL, 15, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2741, NULL, NULL, 15, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2742, NULL, NULL, 15, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2743, NULL, NULL, 15, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2744, NULL, NULL, 15, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2745, NULL, NULL, 15, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2746, NULL, NULL, 15, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2747, NULL, NULL, 15, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie II a III - nositel dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2748, NULL, NULL, 15, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2749, NULL, NULL, 15, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2750, NULL, NULL, 15, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2751, NULL, NULL, 15, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2752, NULL, NULL, 15, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2753, NULL, NULL, 15, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2754, NULL, NULL, 15, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2755, NULL, NULL, 16, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2756, NULL, NULL, 16, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2757, NULL, NULL, 16, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2758, NULL, NULL, 16, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2759, NULL, NULL, 16, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2760, NULL, NULL, 16, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2761, NULL, NULL, 16, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2762, NULL, NULL, 16, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2763, NULL, NULL, 16, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2764, NULL, NULL, 16, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2765, NULL, NULL, 16, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2766, NULL, NULL, 16, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2767, NULL, NULL, 16, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2768, NULL, NULL, 16, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2769, NULL, NULL, 16, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2770, NULL, NULL, 16, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2771, NULL, NULL, 16, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2772, NULL, NULL, 16, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2773, NULL, NULL, 16, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2774, NULL, NULL, 16, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2775, NULL, NULL, 16, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2776, NULL, NULL, 16, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2777, NULL, NULL, 16, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2778, NULL, NULL, 16, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2779, NULL, NULL, 16, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2780, NULL, NULL, 16, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2781, NULL, NULL, 16, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2782, NULL, NULL, 16, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2783, NULL, NULL, 16, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena sboru dobrovolných hasičů obce (kategorie IV), příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2784, NULL, NULL, 16, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovlných hasičů obce (kategorie IV) jako své zaměstnání.', 0),
        (2785, NULL, NULL, 16, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2786, NULL, NULL, 16, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2787, NULL, NULL, 16, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2788, NULL, NULL, 16, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2789, NULL, NULL, 16, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2790, NULL, NULL, 16, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2791, NULL, NULL, 18, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2792, NULL, NULL, 18, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2793, NULL, NULL, 18, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2794, NULL, NULL, 18, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2795, NULL, NULL, 18, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2796, NULL, NULL, 18, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2797, NULL, NULL, 18, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2798, NULL, NULL, 18, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2799, NULL, NULL, 18, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2800, NULL, NULL, 18, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2801, NULL, NULL, 18, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2802, NULL, NULL, 18, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2803, NULL, NULL, 18, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2804, NULL, NULL, 18, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2805, NULL, NULL, 18, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2806, NULL, NULL, 18, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2807, NULL, NULL, 18, 2, 87, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2808, NULL, NULL, 18, 2, 100, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2809, NULL, NULL, 18, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena sboru dobrovolných hasičů obce (kategorie IV) - nositele dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2810, NULL, NULL, 18, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovolných hasičů obce (kategorie IV)  - nositele dýchací techniky, jako své zaměstnání.', 0),
        (2811, NULL, NULL, 18, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2812, NULL, NULL, 18, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2813, NULL, NULL, 18, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2814, NULL, NULL, 18, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2815, NULL, NULL, 18, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2816, NULL, NULL, 18, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2817, NULL, NULL, 19, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2818, NULL, NULL, 19, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2819, NULL, NULL, 19, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2820, NULL, NULL, 19, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2821, NULL, NULL, 19, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2822, NULL, NULL, 19, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2823, NULL, NULL, 19, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2824, NULL, NULL, 19, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2825, NULL, NULL, 19, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2826, NULL, NULL, 19, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2827, NULL, NULL, 19, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2828, NULL, NULL, 19, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2829, NULL, NULL, 19, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2830, NULL, NULL, 19, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2831, NULL, NULL, 19, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2832, NULL, NULL, 19, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2833, NULL, NULL, 19, 2, 87, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2834, NULL, NULL, 19, 2, 100, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2835, NULL, NULL, 19, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena jednotky sboru dobrovolných hasičů podniku (kategorie IV) - nositele dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2836, NULL, NULL, 19, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovlných hasičů podniku (kategorie IV) - nositel dýchací techniky, jako své zaměstnání.', 0),
        (2837, NULL, NULL, 19, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2838, NULL, NULL, 19, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2839, NULL, NULL, 19, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2840, NULL, NULL, 19, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2841, NULL, NULL, 19, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2842, NULL, NULL, 19, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2843, NULL, NULL, 20, 1, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2844, NULL, NULL, 20, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření u osob zacházejících s pyrotechnickými výrobky kategorie P2, T2 nebo F4, se provádějí s důrazem na zjištění nemocí, které vylučují nebo omezují zdravotní způsobilost k zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4.', 0),
        (2845, NULL, NULL, 20, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2846, NULL, NULL, 20, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2847, NULL, NULL, 20, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2848, NULL, NULL, 20, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2849, NULL, NULL, 20, 2, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2850, NULL, NULL, 20, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření u osob zacházejících s pyrotechnickými výrobky kategorie P2, T2 nebo F4, se provádějí s důrazem na zjištění nemocí, které vylučují nebo omezují zdravotní způsobilost k zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4.', 0),
        (2851, NULL, NULL, 20, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2852, NULL, NULL, 20, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2853, NULL, NULL, 20, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2854, NULL, NULL, 20, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2855, NULL, NULL, 20, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2856, NULL, NULL, 20, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2857, NULL, NULL, 20, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2858, NULL, NULL, 21, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2859, NULL, NULL, 21, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2860, NULL, NULL, 21, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci plavidel se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2861, NULL, NULL, 21, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2862, NULL, NULL, 21, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2863, NULL, NULL, 21, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2864, NULL, NULL, 21, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2865, NULL, NULL, 21, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2866, NULL, NULL, 21, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2867, NULL, NULL, 21, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2868, NULL, NULL, 21, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2869, NULL, NULL, 21, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2870, NULL, NULL, 21, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2871, NULL, NULL, 21, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2872, NULL, NULL, 21, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2873, NULL, NULL, 21, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2874, NULL, NULL, 21, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci kapitánů plavidel se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2875, NULL, NULL, 21, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2876, NULL, NULL, 21, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2877, NULL, NULL, 21, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2878, NULL, NULL, 21, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2879, NULL, NULL, 21, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2880, NULL, NULL, 21, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2881, NULL, NULL, 21, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2882, NULL, NULL, 21, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2883, NULL, NULL, 21, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2884, NULL, NULL, 21, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2885, NULL, NULL, 21, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2886, NULL, NULL, 22, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2887, NULL, NULL, 22, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2888, NULL, NULL, 22, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci palubních důstojníků se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2889, NULL, NULL, 22, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2890, NULL, NULL, 22, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2891, NULL, NULL, 22, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2892, NULL, NULL, 22, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2893, NULL, NULL, 22, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2894, NULL, NULL, 22, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2895, NULL, NULL, 22, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2896, NULL, NULL, 22, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2897, NULL, NULL, 22, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2898, NULL, NULL, 22, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2899, NULL, NULL, 22, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2900, NULL, NULL, 22, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2901, NULL, NULL, 22, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2902, NULL, NULL, 22, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci palubních důstojníků se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2903, NULL, NULL, 22, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2904, NULL, NULL, 22, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2905, NULL, NULL, 22, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2906, NULL, NULL, 22, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2907, NULL, NULL, 22, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2908, NULL, NULL, 22, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2909, NULL, NULL, 22, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2910, NULL, NULL, 22, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2911, NULL, NULL, 22, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2912, NULL, NULL, 22, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2913, NULL, NULL, 22, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2914, NULL, NULL, 23, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2915, NULL, NULL, 23, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2916, NULL, NULL, 23, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci palubní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2917, NULL, NULL, 23, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2918, NULL, NULL, 23, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2919, NULL, NULL, 23, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2920, NULL, NULL, 23, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2921, NULL, NULL, 23, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2922, NULL, NULL, 23, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2923, NULL, NULL, 23, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2924, NULL, NULL, 23, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2925, NULL, NULL, 23, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2926, NULL, NULL, 23, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2927, NULL, NULL, 23, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2928, NULL, NULL, 23, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2929, NULL, NULL, 23, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2930, NULL, NULL, 23, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci palubní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2931, NULL, NULL, 23, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2932, NULL, NULL, 23, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2933, NULL, NULL, 23, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2934, NULL, NULL, 23, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2935, NULL, NULL, 23, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2936, NULL, NULL, 23, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2937, NULL, NULL, 23, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2938, NULL, NULL, 23, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2939, NULL, NULL, 23, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2940, NULL, NULL, 23, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2941, NULL, NULL, 23, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2942, NULL, NULL, 24, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2943, NULL, NULL, 24, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2944, NULL, NULL, 24, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu navigační strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2945, NULL, NULL, 24, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2946, NULL, NULL, 24, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2947, NULL, NULL, 24, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2948, NULL, NULL, 24, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2949, NULL, NULL, 24, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2950, NULL, NULL, 24, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2951, NULL, NULL, 24, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2952, NULL, NULL, 24, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2953, NULL, NULL, 24, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2954, NULL, NULL, 24, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2955, NULL, NULL, 24, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2956, NULL, NULL, 24, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2957, NULL, NULL, 24, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2958, NULL, NULL, 24, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu navigační strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2959, NULL, NULL, 24, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2960, NULL, NULL, 24, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2961, NULL, NULL, 24, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2962, NULL, NULL, 24, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2963, NULL, NULL, 24, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2964, NULL, NULL, 24, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2965, NULL, NULL, 24, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2966, NULL, NULL, 24, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2967, NULL, NULL, 24, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2968, NULL, NULL, 24, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2969, NULL, NULL, 24, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2970, NULL, NULL, 25, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2971, NULL, NULL, 25, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2972, NULL, NULL, 25, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu hlídkové služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2973, NULL, NULL, 25, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2974, NULL, NULL, 25, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2975, NULL, NULL, 25, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2976, NULL, NULL, 25, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2977, NULL, NULL, 25, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2978, NULL, NULL, 25, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2979, NULL, NULL, 25, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2980, NULL, NULL, 25, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2981, NULL, NULL, 25, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2982, NULL, NULL, 25, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2983, NULL, NULL, 25, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2984, NULL, NULL, 25, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2985, NULL, NULL, 25, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2986, NULL, NULL, 25, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu hlídkové služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2987, NULL, NULL, 25, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2988, NULL, NULL, 25, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2989, NULL, NULL, 25, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2990, NULL, NULL, 25, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2991, NULL, NULL, 25, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2992, NULL, NULL, 25, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2993, NULL, NULL, 25, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2994, NULL, NULL, 25, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2995, NULL, NULL, 25, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2996, NULL, NULL, 25, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2997, NULL, NULL, 25, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2998, NULL, NULL, 26, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2999, NULL, NULL, 26, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3000, NULL, NULL, 26, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3001, NULL, NULL, 26, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3002, NULL, NULL, 26, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3003, NULL, NULL, 26, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3004, NULL, NULL, 26, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3005, NULL, NULL, 26, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3006, NULL, NULL, 26, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3007, NULL, NULL, 26, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní strážní služby.', 0),
        (3008, NULL, NULL, 26, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3009, NULL, NULL, 26, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3010, NULL, NULL, 26, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3011, NULL, NULL, 26, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3012, NULL, NULL, 26, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3013, NULL, NULL, 26, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3014, NULL, NULL, 26, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3015, NULL, NULL, 26, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3016, NULL, NULL, 26, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3017, NULL, NULL, 26, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3018, NULL, NULL, 26, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3019, NULL, NULL, 26, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3020, NULL, NULL, 26, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3021, NULL, NULL, 26, 2, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní strážní služby.', 0),
        (3022, NULL, NULL, 26, 2, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3023, NULL, NULL, 26, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3024, NULL, NULL, 26, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3025, NULL, NULL, 26, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3026, NULL, NULL, 27, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3027, NULL, NULL, 27, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3028, NULL, NULL, 27, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3029, NULL, NULL, 27, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3030, NULL, NULL, 27, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3031, NULL, NULL, 27, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3032, NULL, NULL, 27, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3033, NULL, NULL, 27, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3034, NULL, NULL, 27, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3035, NULL, NULL, 27, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní posádky.', 0),
        (3036, NULL, NULL, 27, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3037, NULL, NULL, 27, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3038, NULL, NULL, 27, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3039, NULL, NULL, 27, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3040, NULL, NULL, 27, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3041, NULL, NULL, 27, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3042, NULL, NULL, 27, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3043, NULL, NULL, 27, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3044, NULL, NULL, 27, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3045, NULL, NULL, 27, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3046, NULL, NULL, 27, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3047, NULL, NULL, 27, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3048, NULL, NULL, 27, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3049, NULL, NULL, 27, 2, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní posádky.', 0),
        (3050, NULL, NULL, 27, 2, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3051, NULL, NULL, 27, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3052, NULL, NULL, 27, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3053, NULL, NULL, 27, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3054, NULL, NULL, 1, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3055, NULL, NULL, 1, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3056, NULL, NULL, 2, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3057, NULL, NULL, 2, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (3058, NULL, NULL, 2, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3059, NULL, NULL, 3, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3060, NULL, NULL, 3, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (3061, NULL, NULL, 3, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3062, NULL, NULL, 4, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3063, NULL, NULL, 4, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (3064, NULL, NULL, 4, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3065, NULL, NULL, 5, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3066, NULL, NULL, 5, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (3067, NULL, NULL, 5, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3068, NULL, NULL, 6, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3069, NULL, NULL, 6, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (3070, NULL, NULL, 6, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3071, NULL, NULL, 7, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3072, NULL, NULL, 7, 2, 126, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (3073, NULL, NULL, 7, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3074, NULL, NULL, 8, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3075, NULL, NULL, 8, 2, 126, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (3076, NULL, NULL, 8, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3077, NULL, NULL, 9, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3078, NULL, NULL, 12, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3079, NULL, NULL, 12, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3080, NULL, NULL, 12, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3081, NULL, NULL, 12, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3082, NULL, NULL, 13, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3083, NULL, NULL, 13, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3084, NULL, NULL, 13, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3085, NULL, NULL, 13, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3086, NULL, NULL, 14, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3087, NULL, NULL, 14, 2, 126, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (3088, NULL, NULL, 14, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3089, NULL, NULL, 14, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3090, NULL, NULL, 15, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3091, NULL, NULL, 15, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3092, NULL, NULL, 15, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3093, NULL, NULL, 15, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3094, NULL, NULL, 16, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3095, NULL, NULL, 16, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3096, NULL, NULL, 16, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3097, NULL, NULL, 16, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3098, NULL, NULL, 17, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3099, NULL, NULL, 17, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3100, NULL, NULL, 17, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3101, NULL, NULL, 17, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3102, NULL, NULL, 17, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3103, NULL, NULL, 17, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3104, NULL, NULL, 17, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3105, NULL, NULL, 17, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3106, NULL, NULL, 17, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3107, NULL, NULL, 17, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3108, NULL, NULL, 17, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3109, NULL, NULL, 17, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3110, NULL, NULL, 17, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3111, NULL, NULL, 17, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3112, NULL, NULL, 17, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3113, NULL, NULL, 17, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3114, NULL, NULL, 17, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3115, NULL, NULL, 17, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3116, NULL, NULL, 17, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3117, NULL, NULL, 17, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3118, NULL, NULL, 17, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3119, NULL, NULL, 17, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3120, NULL, NULL, 17, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3121, NULL, NULL, 17, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3122, NULL, NULL, 17, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3123, NULL, NULL, 17, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3124, NULL, NULL, 17, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3125, NULL, NULL, 17, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3126, NULL, NULL, 17, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3127, NULL, NULL, 17, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3128, NULL, NULL, 17, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3129, NULL, NULL, 17, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3130, NULL, NULL, 17, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3131, NULL, NULL, 17, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3132, NULL, NULL, 17, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3133, NULL, NULL, 17, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3134, NULL, NULL, 17, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3135, NULL, NULL, 17, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3136, NULL, NULL, 18, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3137, NULL, NULL, 18, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3138, NULL, NULL, 18, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3139, NULL, NULL, 18, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3140, NULL, NULL, 19, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3141, NULL, NULL, 19, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3142, NULL, NULL, 19, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3143, NULL, NULL, 19, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3144, NULL, NULL, 20, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3145, NULL, NULL, 20, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3146, NULL, NULL, 20, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3147, NULL, NULL, 21, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3148, NULL, NULL, 21, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3149, NULL, NULL, 21, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3150, NULL, NULL, 22, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3151, NULL, NULL, 22, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3152, NULL, NULL, 22, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3153, NULL, NULL, 23, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3154, NULL, NULL, 23, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3155, NULL, NULL, 23, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3156, NULL, NULL, 24, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3157, NULL, NULL, 24, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3158, NULL, NULL, 24, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3159, NULL, NULL, 25, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3160, NULL, NULL, 25, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3161, NULL, NULL, 25, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3162, NULL, NULL, 26, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3163, NULL, NULL, 26, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3164, NULL, NULL, 26, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3165, NULL, NULL, 27, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3166, NULL, NULL, 27, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3167, NULL, NULL, 27, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0);