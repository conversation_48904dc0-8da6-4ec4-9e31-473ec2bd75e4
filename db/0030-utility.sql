CREATE FUNCTION trigger_update_modified_at_timestamp()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
BEGIN
    NEW.modified_at = now();
    RETURN NEW;
END;
$$;

CREATE FUNCTION trigger_audit_table_foreign_keys(tablename TEXT, newd RECORD)
    RETURNS TEXT
    LANGUAGE PLPGSQL
AS $$
DECLARE
    fkrow RECORD;
    fkid INT := -1;
    gsql TEXT;
    nkeys JSONB;
BEGIN
    FOR fkrow IN
        SELECT
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                     ON tc.constraint_name = kcu.constraint_name
                         AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                     ON ccu.constraint_name = tc.constraint_name
                         AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = tablename AND tc.table_schema = 'dbo' AND ccu.table_schema = 'dbo'
        LOOP
            EXECUTE 'SELECT MAX(id) FROM dbh.'|| fkrow.foreign_table_name ||' WHERE dbh.'|| fkrow.foreign_table_name ||'.record_id = $1.'|| fkrow.column_name USING newd INTO fkid;
            nkeys[fkrow.column_name] = fkid;
        END LOOP;
    gsql = (SELECT string_agg(CASE WHEN nkeys ? column_name THEN nkeys[column_name]::text WHEN column_name = 'modified_at' THEN format('''%s''', now()) ELSE format('$1.%s', column_name) END, ',') q
            FROM information_schema.columns
            WHERE table_schema = 'dbo'
              AND table_name = tablename);
    RETURN gsql;
END;
$$;

CREATE FUNCTION trigger_audit_table_delete()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''D'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, OLD) ||')' USING OLD;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('D', tbl_id, OLD.id)
    ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'D';
    RETURN OLD;
END;
$$;

CREATE FUNCTION trigger_audit_table_insert()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''I'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, NEW) ||')' USING NEW;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('I', tbl_id, NEW.id)
    ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'I';
    RETURN NEW;
END;
$$;

CREATE FUNCTION trigger_audit_table_update()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''U'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, NEW) ||')' USING NEW;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('I', tbl_id, NEW.id)
        ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';
    RETURN NEW;
END
$$;

