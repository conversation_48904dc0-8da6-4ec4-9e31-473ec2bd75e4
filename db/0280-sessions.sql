CREATE TABLE dbs.sessions
(
    id VARCHAR(64) CONSTRAINT dbs_sessions_pk PRIMARY KEY,
    remote_employer INT NOT NULL,
    remote_user INT NOT NULL,
    remote_is_leader BOOLEAN NOT NULL,
    roles INT[] NOT NULL,
    remote_domain VARCHAR(128) NOT NULL,
    local_user INT NOT NULL CONSTRAINT dbs_sessions_user_dbo_userd_id REFERENCES dbo.users (id),
    timezoneoffset INT NOT NULL,
    valid_until TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbs_sessions_user_index ON dbs.sessions (local_user);

CREATE TRIGGER dbt_sessions_roles_insert
    BEFORE INSERT
    ON dbs.sessions FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE TRIGGER dbt_session_roles_update
    BEFORE UPDATE
    ON dbs.sessions FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();