CREATE TABLE dbo.user_degrees
(
    id SERIAL CONSTRAINT dbo_user_degrees_pk PRIMARY KEY,
    degree VARCHAR(32) NOT NULL,
    after_name BOOL NOT NULL,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_user_degrees_after_name_index ON dbo.user_degrees (after_name);

CREATE UNIQUE INDEX dbo_user_degrees_degree_uindex ON dbo.user_degrees (degree, after_name);

CREATE TRIGGER dbt_user_degrees_changemodified BEFORE UPDATE
    ON dbo.user_degrees FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.user_degrees
(
    id SERIAL CONSTRAINT dbh_user_degrees_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    record_id INT NOT NULL,
    degree VARCHAR(32) NOT NULL,
    after_name BOOL NOT NULL,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_user_degrees_after_name_index ON dbh.user_degrees (after_name);

CREATE INDEX dbh_user_degrees_record_id_index ON dbh.user_degrees (record_id);

CREATE INDEX dbh_user_degrees_modified_at_index ON dbh.user_degrees (modified_at);

CREATE TRIGGER dbt_user_degrees_delete
    AFTER DELETE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_user_degrees_insert
    AFTER INSERT
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_user_degrees_update
    AFTER UPDATE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();

CREATE FUNCTION trigger_user_degrees_prevent_change_position()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
BEGIN
    IF (NEW.after_name <> OLD.after_name)
    THEN
        RAISE EXCEPTION 'Position of degree cannot be changed';
    END IF;
    RETURN NEW;
END
$$;

CREATE TRIGGER dbt_user_degrees_update_before
    BEFORE UPDATE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_user_degrees_prevent_change_position();