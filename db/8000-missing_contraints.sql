ALTER TABLE dbo.user_degrees ADD CONSTRAINT dbo_user_degrees_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.employers ADD CONSTRAINT dbo_employers_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.employer_statutories ADD CONSTRAINT dbo_employer_statutories_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.users ADD CONSTRAINT dbo_users_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.centres ADD CONSTRAINT dbo_centres_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.people ADD CONSTRAINT dbo_people_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.people_data ADD CONSTRAINT dbo_people_data_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.employer_osvc ADD CONSTRAINT dbo_employer_osvc_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.connected_accounts ADD CONSTRAINT dbo_connected_accounts_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.objects ADD CONSTRAINT dbo_objects_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.workplaces ADD CONSTRAINT dbo_workplaces_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.professions ADD CONSTRAINT dbo_professions_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.profession_timeshots ADD CONSTRAINT dbo_profession_timeshots_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.categorizations ADD CONSTRAINT dbo_categorizations_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.profession_precautions ADD CONSTRAINT dbo_profession_prec_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.occupational_hazards ADD CONSTRAINT dbo_occupational_hazards_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.risk_factors_working_conds ADD CONSTRAINT dbo_r_f_working_conds_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.profession_riskfactor_clarifies ADD CONSTRAINT dbo_profession_rf_c_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.legal_req_for_med_fitness ADD CONSTRAINT dbo_legal_req_f_m_f_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.profession_requirement_cs ADD CONSTRAINT dbo_profession_req_cs_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.profession_education_areas ADD CONSTRAINT dbo_professions_edu_a_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.providers_wms ADD CONSTRAINT dbo_providers_wms_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.medical_checks ADD CONSTRAINT dbo_medical_checks_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.personal_risks ADD CONSTRAINT dbo_personal_risks_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.healthy_restrictions ADD CONSTRAINT dbo_healthy_restrictions_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.examinations ADD CONSTRAINT dbo_examinations_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.med_conclusions ADD CONSTRAINT dbo_med_conclusions_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.pdfs ADD CONSTRAINT dbo_pdfs_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
ALTER TABLE dbo.wms_specifics_2 ADD CONSTRAINT dbo_wms_specifics_2_modified_by_dbo_users_id_fk FOREIGN KEY (modified_by) REFERENCES dbo.users(id);
