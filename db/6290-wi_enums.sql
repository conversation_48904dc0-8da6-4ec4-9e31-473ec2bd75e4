INSERT INTO dbe.injury_statuses (id, title, enum, order_) VALUES
    (1, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'wi_status_done', 100),
    (2, '<PERSON> ře<PERSON>en<PERSON>', 'wi_status_in_progress', 200),
    (3, '<PERSON><PERSON><PERSON><PERSON> st<PERSON> org<PERSON>', 'wi_status_investigating', 300),
    (4, '<PERSON><PERSON><PERSON><PERSON><PERSON> soud', 'wi_status_trial', 400),
    (5, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'wi_status_interrupted', 500),
    (6, 'Odlo<PERSON><PERSON>', 'wi_status_postponed', 600),
    (7, '<PERSON><PERSON><PERSON><PERSON>', 'wi_status_cancelled', 700);


INSERT INTO dbe.injury_type_stats (id, title, enum, order_) VALUES
    (1, 'Smrtelný pracovní úraz', 'wi_type_stat_death', 100),
    (2, 'Úraz s hospitalizací > 5 dnů', 'wi_type_stat_hospital', 200),
    (3, '<PERSON><PERSON> s pracovní ne<PERSON> > 3 kalend<PERSON><PERSON>ní dny', 'wi_type_stat_long', 300),
    (4, 'Ostatní', 'wi_type_stat_rest', 400);


INSERT INTO dbe.injury_types (id, code, title, enum, heading, order_) VALUES
    (1, 0,	'Neznámý nebo neurčený druh zranění', 'wi_unknown_or_unspecified_injury', true, 100),
    (2, 10, 'Rány a povrchová zranění', 'wi_cuts_and_surface_injuries', true, 200),
    (3, 11, 'Povrchové zranění', 'wi_surface_injuries', false, 300),
    (4, 12, 'Otevřené rány', 'wi_open_wounds', false, 400),
    (5, 19, 'Jiné typy ran a povrchových zranění', 'wi_other_cuts_and_surface_injuries', false, 500),
    (6, 20, 'Zlomeniny kostí', 'wi_bone_fractures', true, 600),
    (7, 21, 'Zavřené zlomeniny', 'wi_closed_fractures', false, 700),
    (8, 22, 'Otevřené zlomeniny', 'wi_open_fractures', false, 800),
    (9, 29, 'Jiné typy zlomenin kostí', 'wi_other_bone_fractures', false, 900),
    (10, 30, 'Vykloubení, vyvrtnutí, natažení', 'wi_dislocations_sprains_strains', true, 1000),
    (11, 31, 'Vykloubení nebo neúplné vykloubení', 'wi_dislocations_or_subluxations', false, 1100),
    (12, 32, 'Vyvrtnutí nebo natažení', 'wi_sprains_or_strains', false, 1200),
    (13, 39, 'Jiné typy vykloubení, vyvrtnutí, natažení', 'wi_other_dislocations_sprains_strains', false, 1300),
    (14, 40, 'Traumatická amputace (ztráta části těla)', 'wi_traumatic_amputation', true, 1400),
    (15, 50, 'Otřes mozku a vnitřní zranění', 'wi_concussions_and_internal_injuries', true, 1500),
    (16, 51, 'Otřes mozku a vnitrolebeční zranění', 'wi_concussions_and_intracranial_injuries', false, 1600),
    (17, 52, 'Vnitřní zranění', 'wi_internal_injuries', false, 1700),
    (18, 59, 'Jiné typy otřesů mozku a vnitřních zranění', 'wi_other_concussions_and_internal_injuries', false, 1800),
    (19, 60, 'Popáleniny, opařeniny a omrzliny', 'wi_burns_scalds_and_frostbites', true, 1900),
    (20, 61, 'Popáleniny a opařeniny (tepelné)', 'wi_burns_and_scalds_thermal', false, 2000),
    (21, 62, 'Chemické popáleniny (poleptání)', 'wi_chemical_burns', false, 2100),
    (22, 63, 'Omrzliny', 'wi_frostbites', false, 2200),
    (23, 69, 'Jiné typy popálenin, opařenin a omrzlin', 'wi_other_burns_scalds_and_frostbites', false, 2300),
    (24, 70, 'Otravy a infekce', 'wi_poisonings_and_infections', true, 2400),
    (25, 71, 'Akutní otravy', 'wi_acute_poisonings', false, 2500),
    (26, 72, 'Akutní infekce', 'wi_acute_infections', false, 2600),
    (27, 79, 'Jiné typy otrav a infekcí', 'wi_other_poisonings_and_infections', false, 2700),
    (28, 80, 'Tonutí a dušení', 'wi_drowning_and_asphyxiation', true, 2800),
    (29, 81, 'Dušení', 'wi_asphyxiation', false, 2900),
    (30, 82, 'Tonutí bez smrtelných následků', 'wi_non_fatal_drowning', false, 3000),
    (31, 89, 'Jiné typy tonutí a dušení', 'wi_other_drowning_and_asphyxiation', false, 3100),
    (32, 90, 'Účinky zvuku, vibrací a tlaku', 'wi_effects_of_sound_vibration_and_pressure', true, 3200),
    (33, 91, 'Akutní ztráta sluchu', 'wi_acute_hearing_loss', false, 3300),
    (34, 92, 'Působení tlaku (barotrauma)', 'wi_pressure_effects_barotrauma', false, 3400),
    (35, 99, 'Jiné účinky zvuku, vibrací a tlaku', 'wi_other_effects_of_sound_vibration_and_pressure', false, 3500),
    (36, 100, 'Účinky extrémních teplot, světla a ozáření', 'wi_effects_of_extreme_temperature_light_and_radiation', true, 3600),
    (37, 101, 'Úpal z tepla a slunečního záření', 'wi_heat_stroke_and_solar_radiation', false, 3700),
    (38, 102, 'Účinky ozáření (netepelné)', 'wi_radiation_effects_non_thermal', false, 3800),
    (39, 103, 'Účinky snížené teploty', 'wi_effects_of_cold', false, 3900),
    (40, 109, 'Jiné účinky extrémních teplot, světla a ozáření', 'wi_other_effects_of_extreme_temperature_light_radiation', false, 4000),
    (41, 110, 'Šok', 'wi_shock', true, 4100),
    (42, 111, 'Šoky po agresích a hrozbách', 'wi_shocks_from_assaults_and_threats', false, 4200),
    (43, 112, 'Traumatické šoky', 'wi_traumatic_shock', false, 4300),
    (44, 119, 'Jiné typy šoků', 'wi_other_shocks', false, 4400),
    (45, 120, 'Vícenásobné zranění', 'wi_multiple_injuries', true, 4500),
    (46, 999, 'Jiná specifická zranění nezahrnutá do jiných kategorií', 'wi_other_specific_injuries_not_elsewhere_classified', true, 4600);


INSERT INTO dbe.injury_parts (id, title, code, enum, heading, tooltip, valid, order_) VALUES
        (1, 'Zraněná část těla nespecifikována', 0, 'wi_unspecified_injury_area', true, '', true, 100),
        (2, 'Hlava bez podrobnějšího rozlišení, dále nespecifikována', 10, 'wi_head_unspecified', true, '', true, 200),
        (3, 'Hlava, mozek, lebeční nervy a cévy', 11, 'wi_head_brain_skull_nerves_vessels', false, '', true, 300),
        (4, 'Tvář', 12, 'wi_face', false, '', true, 400),
        (5, 'Oko', 13, 'wi_eye', false, '', true, 500),
        (6, 'Ucho', 14, 'wi_ear', false, '', true, 600),
        (7, 'Zuby', 15, 'wi_teeth', false, '', true, 700),
        (8, 'Hlava - více postižených oblastí', 18, 'wi_head_multiple_areas', false, '', true, 800),
        (9, 'Hlava - jiné části výše neuvedené', 19, 'wi_head_other', false, '', true, 900),
        (10, 'Krk včetně páteře a krčních obratlů', 20, 'wi_neck_including_spine_cervical_vertebrae_heading', true, '', true, 1000),
        (11, 'Krk včetně páteře a krčních obratlů', 21, 'wi_neck_including_spine_cervical_vertebrae', false, '', true, 1100),
        (12, 'Krk - jiné části dosud neuvedené', 29, 'wi_neck_other', false, '', true, 1200),
        (13, 'Záda včetně páteře a zádových obratlů', 30, 'wi_back_including_spine_back_vertebrae_heading', true, '', true, 1300),
        (14, 'Záda včetně páteře a zádových obratlů', 31, 'wi_back_including_spine_back_vertebrae', false, '', true, 1400),
        (15, 'Záda - jiné části výše neuvedené', 39, 'wi_back_other', false, '', true, 1500),
        (16, 'Trup a orgány bez podrobnějšího rozlišení', 40, 'wi_trunk_organs_unspecified', true, '', true, 1600),
        (17, 'Hrudní koš, žebra včetně kloubů a lopatek', 41, 'wi_chest_ribs_joints_scapula', false, '', true, 1700),
        (18, 'Oblast hrudníku včetně orgánů', 42, 'wi_chest_organs', false, '', true, 1800),
        (19, 'Pánevní a břišní oblast včetně orgánů', 43, 'wi_pelvic_abdominal_organs', false, '', true, 1900),
        (20, 'Trup - více postižených oblastí', 48, 'wi_trunk_multiple_areas', false, '', true, 2000),
        (21, 'Trup - jiné části výše neuvedené', 49, 'wi_trunk_other', false, '', true, 2100),
        (22, 'Horní končetiny bez podrobnějšího rozlišení', 50, 'wi_upper_limb_unspecified', true, '', true, 2200),
        (23, 'Rameno a ramenní klouby', 51, 'wi_shoulder_joints', false, '', true, 2300),
        (24, 'Ruka včetně lokte', 52, 'wi_arm_including_elbow', false, '', true, 2400),
        (25, 'Ruka od zápěstí dolů', 53, 'wi_hand_from_wrist_down', false, '', true, 2500),
        (26, 'Prst', 54, 'wi_finger', false, '', true, 2600),
        (27, 'Zápěstí', 55, 'wi_wrist', false, '', true, 2700),
        (28, 'Horní končetiny - více postižených oblastí', 58, 'wi_upper_limb_multiple_areas', false, '', true, 2800),
        (29, 'Horní končetiny - jiné části výše neuvedené', 59, 'wi_upper_limb_other', false, '', true, 2900),
        (30, 'Dolní končetiny bez podrobnějšího rozlišení', 60, 'wi_lower_limb_unspecified', true, '', true, 3000),
        (31, 'Bedra, bederní klouby', 61, 'wi_hip_hip_joints', false, '', true, 3100),
        (32, 'Noha včetně kolena', 62, 'wi_leg_including_knee', false, '', true, 3200),
        (33, 'Kotník', 63, 'wi_ankle', false, '', true, 3300),
        (34, 'Noha od kotníku dolů', 64, 'wi_foot_from_ankle_down', false, '', true, 3400),
        (35, 'Prst na noze', 65, 'wi_toe', false, '', true, 3500),
        (36, 'Dolní končetiny - více postižených oblastí', 68, 'wi_lower_limb_multiple_areas', false, '', true, 3600),
        (37, 'Dolní končetiny - jiné části výše neuvedené', 69, 'wi_lower_limb_other', false, '', true, 3700),
        (38, 'Celé tělo a více oblastí bez podrobnějšího rozlišení', 70, 'wi_whole_body_unspecified', true, '', true, 3800),
        (39, 'Celé tělo (systémové účinky)', 71, 'wi_whole_body_systemic_effects', false, '', true, 3900),
        (40, 'Tělo - více postižených oblastí', 72, 'wi_body_multiple_areas', false, '', true, 4000),
        (41, 'Tělo - jiná zraněná část výše neuvedená', 79, 'wi_body_other_unspecified_injury', false, '', true, 4100);

INSERT INTO dbe.injury_sources (id, title, enum, order_) VALUES
    (1, 'Dopravní prostředek', 'wi_transport_vehicle', 100),
    (2, 'Stroje a zařízení přenosná nebo mobilní', 'wi_portable_mobile_machinery', 200),
    (3, 'Materiál, břemena, předměty (pád, přiražení, odlétnutí, náraz, zavalení)', 'wi_materials_objects', 300),
    (4, 'Pád na rovině, z výšky, do hloubky, propadnutí', 'wi_fall_levels_heights_depths', 400),
    (5, 'Nástroj, přístroj, nářadí', 'wi_tools_instruments', 500),
    (6, 'Průmyslové škodliviny, chemické látky, biologické činitele', 'wi_industrial_hazards', 600),
    (7, 'Horké látky a předměty, oheň a výbušniny', 'wi_hot_materials_fire_explosives', 700),
    (8, 'Stroje a zařízení stabilní', 'wi_fixed_machinery', 800),
    (9, 'Lidé, zvířata nebo přírodní živly', 'wi_human_animal_natural_forces', 900),
    (10, 'Elektrická energie', 'wi_electrical_energy', 1000),
    (11, 'Jiný blíže nespecifikovaný zdroj', 'wi_other_unspecified_source', 1100);


INSERT INTO dbe.injury_causes (id, title, enum, order_) VALUES
    (1, 'Pro poruchu nebo vadný stav některého ze zdrojů úrazu', 'wi_source_fault_or_defect', 100),
    (2, 'Pro špatné nebo nedostatečné vyhodnocení rizika zaměstnavatelem', 'wi_employer_risk_evaluation_failure', 200),
    (3, 'Pro závady na pracovišti', 'wi_workplace_faults', 300),
    (4, 'Pro nedostatečné osobní zajištění zaměstnance včetně osobních ochranných pracovních prostředků', 'wi_employee_personal_security_lack', 400),
    (5, 'Pro porušení předpisů vztahujících se k práci nebo pokynů zaměstnavatele úrazem postiženého zaměstnance', 'wi_regulations_or_instructions_violation', 500),
    (6, 'Pro nepředvídatelné riziko práce nebo selhání lidského činitele', 'wi_unforeseeable_work_risk_or_human_error', 600),
    (7, 'Pro jiný, blíže nespecifikovaný důvod', 'wi_other_unspecified_reason', 700);


INSERT INTO dbe.injury_doc_types (id, title, enum) VALUES
    (1, 'Evideční list knihy úrazů', 'wi_registration_file'),
    (2, 'Záznam o úrazu', 'wi_injury_record'),
    (3, 'Záznam o úrazu - hlášení změn', 'wi_injury_record_change'),
    (4, 'Doklad o předání záznamu', 'wi_injury_record_handover');

INSERT INTO dbe.pdf_classes (id, title) VALUES (2, 'Záznam o úrazu');
INSERT INTO dbe.pdf_types (id, title, class ,version, commandline) VALUES (3, 'Evideční list knihy úrazů', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class, version, commandline) VALUES (4, 'Záznam o úrazu', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class ,version, commandline) VALUES (5, 'Záznam o úrazu - hlášení změn', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class, version, commandline) VALUES (6, 'Doklad o předání záznamu', 2, '1.0', '');