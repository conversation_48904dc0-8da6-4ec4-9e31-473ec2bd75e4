CREATE TABLE dbe.roles
(
    id    SERIAL CONSTRAINT dbe_roles_pk PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    enum  VARCHAR(32)  NOT NULL
);

INSERT INTO dbe.roles (id, title, enum) VALUES (1, 'Admin', 'r_admin');
INSERT INTO dbe.roles (id, title, enum) VALUES (2, '<PERSON>ed<PERSON><PERSON><PERSON>', 'r_leader');
INSERT INTO dbe.roles (id, title, enum) VALUES (3, '<PERSON>aměstnan<PERSON>', 'r_employee');
INSERT INTO dbe.roles (id, title, enum) VALUES (4, 'Odborn<PERSON> způsobilá osoba v BOZP', 'r_safetyofficer');
INSERT INTO dbe.roles (id, title, enum) VALUES (5, 'Odborn<PERSON> způsobilá osoba v PO', 'r_fireofficer');
INSERT INTO dbe.roles (id, title, enum) VALUES (6, 'Personalista', 'r_hr');
INSERT INTO dbe.roles (id, title, enum) VALUES (7, 'Poskytovatel pracovnělékařských služeb', 'r_doctor');
INSERT INTO dbe.roles (id, title, enum) VALUES (8, 'Vedoucí údržby', 'r_bossmaintenance');
INSERT INTO dbe.roles (id, title, enum) VALUES (9, 'Údržbář', 'r_maintenance');
INSERT INTO dbe.roles (id, title, enum) VALUES (10, 'Revizní technik elektrických zařízení', 'r_inspectionelectric');
INSERT INTO dbe.roles (id, title, enum) VALUES (11, 'Revizní technik tlakových zařízení', 'r_inspectionpressure');
INSERT INTO dbe.roles (id, title, enum) VALUES (12, 'Revizní technik plynových zařízení', 'r_inspectiongas');
INSERT INTO dbe.roles (id, title, enum) VALUES (13, 'Revizní technik zdvihacích zařízení', 'r_inspectioncrane');
INSERT INTO dbe.roles (id, title, enum) VALUES (14, 'Firemní ekolog', 'r_ecologist');
