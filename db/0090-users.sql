CREATE TABLE dbo.users
(
    id SERIAL CONSTRAINT dbo_users_pk PRIMARY KEY,
    degree_before INT NOT NULL CONSTRAINT dbo_people_degree_before_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    name <PERSON><PERSON><PERSON><PERSON>(64) NOT NULL,
    surname <PERSON><PERSON><PERSON><PERSON>(64) NOT NULL,
    degree_after INT NOT NULL CONSTRAINT dbo_people_degree_after_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    sex INT NOT NULL CONSTRAINT dbo_people_dbe_sex_id_fk REFERENCES dbe.sex(id),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(32) NOT NULL,
    roles INT[] NOT NULL,
    passhash VARCHAR(64) NOT NULL,
    need_update_password BOOLEAN NOT NULL,
    created_at DATE NOT NULL,
    deleted_at DATE,
    paid_until DATE,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_users_id_partial_index ON dbo.users (id) WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_email_partial_index ON dbo.users (email)  WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_name_partial_index ON dbo.users (name) WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_surname_partial_index ON dbo.users (surname)  WHERE deleted_at IS NULL;
/*ALTER TABLE dbo.users ADD CONSTRAINT users_name_uk UNIQUE (name);*/

CREATE TRIGGER dbt_users_changemodified BEFORE UPDATE
    ON dbo.users FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.users
(
    id SERIAL CONSTRAINT dbh_users_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    degree_before INT NOT NULL,
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL,
    sex INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    roles INT[] NOT NULL,
    passhash VARCHAR(64) NOT NULL,
    need_update_password BOOLEAN NOT NULL,
    created_at DATE NOT NULL,
    deleted_at DATE,
    paid_until DATE,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_users_record_id_index ON dbh.users (record_id);

CREATE INDEX dbh_users_name_index ON dbh.users (name);

CREATE INDEX dbh_users_modified_at_index ON dbh.users (modified_at);


CREATE TABLE dbs.login_attempts
(
    id INT CONSTRAINT dbs_login_attempts_pk PRIMARY KEY,
    attempts INT NOT NULL
);

CREATE TRIGGER dbt_users_delete
    AFTER DELETE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_users_insert
    AFTER INSERT
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_users_update
    AFTER UPDATE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();


CREATE OR REPLACE FUNCTION trigger_users_foreign_key_check_roles()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    roleid INT;
BEGIN
    FOREACH roleid IN ARRAY NEW.roles
        LOOP
            IF NOT EXISTS (SELECT FROM dbe.roles WHERE id = roleid) THEN
                RAISE EXCEPTION 'Users:roles:foreign key:fail';
            END IF;
        END LOOP;
    RETURN NEW;
END
$$;

CREATE TRIGGER dbt_users_roles_insert
    BEFORE INSERT
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE TRIGGER dbt_users_roles_update
    BEFORE UPDATE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE OR REPLACE FUNCTION trigger_roles_foreign_key_check_roles()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
BEGIN
    IF EXISTS (SELECT FROM dbo.users WHERE OLD.id = ANY (roles) ) THEN
        RAISE EXCEPTION 'Roles:users:foreign key:fail';
    END IF;
    RETURN NEW;
END
$$;


CREATE TRIGGER dbt_roles_roles_delete
    BEFORE DELETE
    ON dbe.roles FOR EACH ROW
EXECUTE FUNCTION trigger_roles_foreign_key_check_roles();

CREATE TRIGGER dbt_roles_roles_update
    BEFORE UPDATE
    ON dbe.roles FOR EACH ROW
EXECUTE FUNCTION trigger_roles_foreign_key_check_roles();
