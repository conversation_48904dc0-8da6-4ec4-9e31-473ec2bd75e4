CREATE TABLE dbo.people
(
    id SERIAL CONSTRAINT dbo_people_pk PRIMARY KEY,
    degree_before INT NOT NULL CONSTRAINT dbo_people_degree_before_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    name <PERSON><PERSON><PERSON><PERSON>(64) NOT NULL,
    surname <PERSON><PERSON><PERSON><PERSON>(64) NOT NULL,
    degree_after INT NOT NULL CONSTRAINT dbo_people_degree_after_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    sex INT NOT NULL CONSTRAINT dbo_people_dbe_sex_id_fk REFERENCES dbe.sex(id),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    note TEXT NOT NULL,

    employer INT NOT NULL CONSTRAINT dbo_people_employer_dbe_employers_id_fk REFERENCES dbo.employers(id),
    centre INT NOT NULL CONSTRAINT dbo_people_centre_dbo_centres_id_fk REFERENCES dbo.centres(id),
    e_relationship INT NOT NULL CONSTRAINT dbo_people_e_r_dbe_e_relationships_id_fk REFERENCES dbe.employer_relationships(id),
    profession INT NOT NULL,-- CONSTRAINT dbo_people_profession_dbo_professions_id_fk REFERENCES dbo.professions(id), at the end
    state INT NOT NULL DEFAULT 2 CONSTRAINT dbo_people_state_dbe_person_states_id_fk REFERENCES dbe.person_states(id),
    leader INT NOT NULL CONSTRAINT dbo_people_leader_dbo_people_id_fk REFERENCES dbo.people(id),

    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_people_id_partialindex ON dbo.people (id) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_name_partialindex ON dbo.people (name) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_surname_partialindex ON dbo.people (surname) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_e_relationship_partialindex ON dbo.people (e_relationship) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_leader_index ON dbo.people (id);

CREATE TRIGGER dbt_people_changemodified BEFORE UPDATE
    ON dbo.people FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.people
(
    id SERIAL CONSTRAINT dbh_people_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    record_id INT NOT NULL,
    degree_before INT NOT NULL,
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL,
    sex INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    note TEXT NOT NULL,

    employer INT NOT NULL,
    centre INT NOT NULL,
    e_relationship INT NOT NULL,
    profession INT NOT NULL,
    state INT NOT NULL DEFAULT 2,
    leader INT NOT NULL,

    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_people_record_id_index ON dbh.people (record_id);
CREATE INDEX dbh_people_name_index ON dbh.people (name);
CREATE INDEX dbh_people_surname_index ON dbh.people (surname);
CREATE INDEX dbh_people_e_relationship_index ON dbo.people (e_relationship);
CREATE INDEX dbh_people_modified_at_index ON dbh.people (modified_at);
CREATE INDEX dbh_people_leader_index ON dbh.people (id);

CREATE TRIGGER dbt_people_delete
    AFTER DELETE
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_people_insert
    AFTER INSERT
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_people_update
    AFTER UPDATE
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();