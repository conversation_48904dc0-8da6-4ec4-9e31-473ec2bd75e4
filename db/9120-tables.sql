CREATE TABLE dbe.tables
(
    id        SERIAL CONSTRAINT dbe_tables_pk PRIMARY KEY,
    title     VARCHAR(128) NOT NULL,
    enum      VARCHAR(32)  NOT NULL
);

INSERT INTO dbe.tables (id, title, enum) VALUES (1, 'Tituly osob', 'user_degrees');
INSERT INTO dbe.tables (id, title, enum) VALUES (2, 'Zaměstnavatelé', 'employers');
INSERT INTO dbe.tables (id, title, enum) VALUES (3, 'Statutáři', 'employer_statutories');
INSERT INTO dbe.tables (id, title, enum) VALUES (4, 'Uživatelé', 'users');
INSERT INTO dbe.tables (id, title, enum) VALUES (5, 'Osoby', 'people');
INSERT INTO dbe.tables (id, title, enum) VALUES (6, 'Personálie', 'people_data');
INSERT INTO dbe.tables (id, title, enum) VALUES (7, 'Střediska', 'centres');
INSERT INTO dbe.tables (id, title, enum) VALUES (8, 'Propojené <PERSON>', 'connected_accounts');
INSERT INTO dbe.tables (id, title, enum) VALUES (9, 'Profese', 'professions');
INSERT INTO dbe.tables (id, title, enum) VALUES (10, 'Zaměstnavatel OSVČ', 'employer_osvc');
INSERT INTO dbe.tables (id, title, enum) VALUES (11, 'Objekty', 'objects');
INSERT INTO dbe.tables (id, title, enum) VALUES (12, 'Pracoviště', 'workplaces');
INSERT INTO dbe.tables (id, title, enum) VALUES (13, 'Časový snímek profese', 'profession_timeshots');
INSERT INTO dbe.tables (id, title, enum) VALUES (14, 'Kategorizace prací', 'categorizations');
INSERT INTO dbe.tables (id, title, enum) VALUES (15, 'Opatření profese', 'profession_precautions');
INSERT INTO dbe.tables (id, title, enum) VALUES (16, 'Profesní rizika', 'occupational_hazards');
INSERT INTO dbe.tables (id, title, enum) VALUES (17, 'Rizikové faktory pracovních podmínek', 'risk_factors_working_conds');
INSERT INTO dbe.tables (id, title, enum) VALUES (18, 'Upřesnění rizikových faktorů', 'profession_riskfactor_clarifies');
INSERT INTO dbe.tables (id, title, enum) VALUES (19, 'Požadavky právních předpisů na zdravotní způsobilost', 'legal_req_for_med_fitness');
INSERT INTO dbe.tables (id, title, enum) VALUES (20, 'Upřesnění k dalším předpisům', 'profession_requirement_cs');
INSERT INTO dbe.tables (id, title, enum) VALUES (21, 'Oblasti vzdělávání', 'profession_education_areas');
INSERT INTO dbe.tables (id, title, enum) VALUES (22, 'Poskytovatelé PLS', 'providers_wms');
INSERT INTO dbe.tables (id, title, enum) VALUES (23, 'Soubory', 'files');
INSERT INTO dbe.tables (id, title, enum) VALUES (24, 'Pracovnělékařské prohlídky', 'medical_checks');
INSERT INTO dbe.tables (id, title, enum) VALUES (25, 'Pdf dokumenty', 'pdfs');
INSERT INTO dbe.tables (id, title, enum) VALUES (26, 'Osobní rizika', 'personal_risks');
INSERT INTO dbe.tables (id, title, enum) VALUES (27, 'Zdravotní omezení', 'healthy_restrictions');
INSERT INTO dbe.tables (id, title, enum) VALUES (28, 'Vyšetření', 'examinations');
INSERT INTO dbe.tables (id, title, enum) VALUES (29, 'Závěry lékaře', 'med_conclusions');
INSERT INTO dbe.tables (id, title, enum) VALUES (30, 'Specifika PLP II', 'wms_specifics_2');
INSERT INTO dbe.tables (id, title, enum) VALUES (31, 'Pracovní úrazy', 'working_injuries');
INSERT INTO dbe.tables (id, title, enum) VALUES (33, 'Záznamy o úrazu', 'working_injuries_records');