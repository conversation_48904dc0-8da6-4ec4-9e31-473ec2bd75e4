--SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.users (
    id,
    name,
    roles,
    passhash,
    need_update_password,
    valid,
    note,
    modified_by,
    modified_at
) VALUES (
    0,
    'agentsaw',
    '{1}',
    '',
    false,
    true,
    'Technický uživatel. Needitovat!',
    0,--sám se zapsal
    '2024-01-01 00:00:00.000000'
);
/*
INSERT INTO dbh.people (
    id,
    operation,
    record_id,
    degree_before,
    name,
    surname,
    degree_after,
    sex,
    e_relationship,
    leader,
    note,
    modified_by,
    modified_at,
    email,
    employer,
    centre,
    profession,
    state,
    phone
) VALUES (
    0,
    'I',
    0,
    1,--bez titulu
    'Agent',
    'SAW',
    2,--bez titulu
    1,--muž
    10,--ji<PERSON> osoba
    0,--sám sobě leaderem
    'Technický uživatel. Needitovat!',
    0,--sám se zapsal
    '2024-01-01 00:00:00.000000',
    '<EMAIL>',
    1,
    0,--st<PERSON><PERSON><PERSON><PERSON> <PERSON>a saw
    1,
    1,--aktivni
    ''
);

SET SESSION_REPLICATION_ROLE TO DEFAULT;*/