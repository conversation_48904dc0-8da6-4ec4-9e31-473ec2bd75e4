SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.centres (
    id,
    code,
    name,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    '9999',
    'SAWAPP',
    'Toto je vzorové středisko. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
);

INSERT INTO dbh.centres (
    id,
    operation,
    record_id,
    code,
    name,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
     DEFAULT,
     'I',
     1,
     '9999',
     'SAWAPP',
     'Toto je vzorové středisko. Upravte dle potřeby.',
     1,
     '',
     0,
     '2024-01-01 00:00:00.000000'
 );

SET SESSION_REPLICATION_ROLE TO DEFAULT;

INSERT INTO dbo.objects (
    id,
    name,
    address,
    description,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    'Budova SAWAPP',
    'Bezpeč<PERSON> 22, <PERSON><PERSON><PERSON><PERSON>, 77709',
    'Toto je vzorový objekt. Upravte dle potřeby.',
    '',
    0,
    '2024-01-01 00:00:00.000000'
    );

SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.workplaces (
    id,
    object,
    name,
    floorlevel,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    1,
    'Pracoviště SAWAPP',
    1,
    'Toto je vzorové pracovitě. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
    );

INSERT INTO dbh.workplaces (
    id,
    operation,
    record_id,
    object,
    name,
    floorlevel,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    'I',
    1,
    1,
    'Pracoviště SAWAPP',
    1,
    'Toto je vzorové pracovitě. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
);

SET SESSION_REPLICATION_ROLE TO DEFAULT;
