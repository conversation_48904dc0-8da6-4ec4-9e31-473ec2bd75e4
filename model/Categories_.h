//
// Created for categories table adaptation
//

#ifndef SAW_CATEGORIES__H
#define SAW_CATEGORIES__H

#include "SAWSettings.h"
#include "amuitable/AMUITableColumn.h"

const AMUITable::AMUITableColumn<SAWEnum, SAWString> Categories_[] = {
    {
        "id",
        "ID",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_integer<SAWEnum, SAWString>,
        AMCore::AMDataType_integer,
        0,
        "ID kategorie",
        "",
        "",
        AMUITable::AMUITableColumnMode::ID,
        AMUITable::AMUITableColumnMode::ID,
        "id",
        {},
        50.0f
    },
    {
        "name",
        "Název",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_string<SAWEnum, SAWString>,
        AMCore::AMDataType_string,
        255,
        "<PERSON><PERSON><PERSON><PERSON> kategorie",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::NORMAL,
        "name",
        {},
        200.0f
    },
    {
        "weight",
        "Váha",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_integer<SAWEnum, SAWString>,
        AMCore::AMDataType_integer,
        0,
        "Váha kategorie",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::NORMAL,
        "weight",
        {},
        80.0f
    },
    {
        "paid",
        "Placeno",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_bool<SAWEnum, SAWString>,
        AMCore::AMDataType_bool,
        0,
        "Je kategorie placená",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::NORMAL,
        "paid",
        {},
        80.0f
    },
    {
        "supercategory",
        "Nadkategorie",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_integer<SAWEnum, SAWString>,
        AMCore::AMDataType_enum,
        0,
        "Nadřazená kategorie",
        "@categories",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::NORMAL,
        "supercategory",
        {},
        150.0f
    },
    {
        "supercategory_name",
        "Název nadkategorie",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_string<SAWEnum, SAWString>,
        AMCore::AMDataType_string,
        255,
        "Název nadřazené kategorie",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "sc.name AS supercategory_name",
        {"LEFT JOIN dbo.categories sc ON sc.id = t.supercategory"},
        150.0f
    },
    {
        "note",
        "Poznámka",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_string<SAWEnum, SAWString>,
        AMCore::AMDataType_string,
        0,
        "Poznámka ke kategorii",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::NORMAL,
        "note",
        {},
        200.0f
    },
    {
        "deleted_at",
        "Smazáno",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_datetimeus<SAWEnum, SAWString>,
        AMCore::AMDataType_datetimeus,
        255,
        "Datum smazání",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "deleted_at",
        {},
        150.0f
    },
    {
        "created_at",
        "Vytvořeno kdy",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_datetimeus<SAWEnum, SAWString>,
        AMCore::AMDataType_datetimeus,
        255,
        "Datum vytvoření",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "created_at",
        {},
        150.0f
    },
    {
        "updated_at",
        "Aktualizováno kdy",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_datetimeus<SAWEnum, SAWString>,
        AMCore::AMDataType_datetimeus,
        255,
        "Datum aktualizace",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "updated_at",
        {},
        150.0f
    },
    {
        "modified_by",
        "_Změněno ID",
        AMUITable::AMUITableColumnFlags::NOHISTORY | AMUITable::AMUITableColumnFlags::NORECORD,
        AMUITable::AMUITableDefaultTransform_integer<SAWEnum, SAWString>,
        AMCore::AMDataType_integer,
        0,
        "_Změněno ID",
        "@users",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "modified_by",
        {},
        50.0f
    },
    {
        "modified_by_name",
        "_Změněno kým",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_string<SAWEnum, SAWString>,
        AMCore::AMDataType_string,
        255,
        "_Změněno kým",
        "",
        "",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "u.name AS modified_by_name",
        {"LEFT JOIN dbo.users u ON u.id = t.modified_by"},
        200.0f
    },
    {
        "modified",
        "Změněno",
        AMUITable::AMUITableColumnFlags::NONE,
        AMUITable::AMUITableDefaultTransform_author_tag<SAWEnum, SAWString>,
        AMCore::AMDataType_author_tag,
        64,
        "Změněno",
        "",
        "updated_at,modified_by_name",
        AMUITable::AMUITableColumnMode::NORMAL,
        AMUITable::AMUITableColumnMode::READONLY,
        "",
        {},
        260.0f
    }
};

#endif //SAW_CATEGORIES__H
