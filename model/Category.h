//
// Created for categories table adaptation
//

#ifndef SAW_ALL_CATEGORY_H
#define SAW_ALL_CATEGORY_H

#include "amuitable/AMUITableForm.h"
#include "amdialogs/AMUIFormElementString.h"
#include "amdialogs/AMUIFormElementInteger.h"
#include "amdialogs/AMUIFormElementBool.h"
#include "amdialogs/AMUIFormElementStringMultiline.h"
#include "amdialogs/AMUIFormElementAuthorTag.h"
#include "amuitable/AMUIFormElementEnumName.h"
#include "SAWSettings.h"

class Category: public AMUITable::AMUITableForm<SAWEnum, SAWString>
{
public:
    AMDialogs::AMUIFormElementInteger  id;
    AMDialogs::AMUIFormElementString   name;
    AMDialogs::AMUIFormElementInteger  weight;
    AMDialogs::AMUIFormElementBool<SAWString> paid;
    AMUITable::AMUIFormElementEnumName<SAWEnum, SAWString> supercategory;
    AMDialogs::AMUIFormElementStringMultiline note;
    AMDialogs::AMUIFormElementAuthorTag<SAWString> authorTag;

    Category(const std::string &strVoidParam);
    int sections() override;
    std::vector<AMDialogs::AMUIFormElement*>& elementsForSection(int n) override;
    std::string recordTitle() override;
    bool isDataAvail(void * mem) override;
    void init(AMUI::AMUIController* controller) override;

protected:
    std::vector<AMDialogs::AMUIFormElement*> m_sectionMain;
    std::vector<AMDialogs::AMUIFormElement*> m_sectionSecond;
};

#endif //SAW_ALL_CATEGORY_H
