//
// Created by <PERSON><PERSON><PERSON> on 14.5.23.
//
#include "TablesCS.h"
#include "model/UserRoles_.h"
#include "model/UserDegrees_.h"
#include "model/Categories_.h"
#include "model/Users_.h"
#include "model/Citizenships_.h"
#include "model/Sex_.h"
#include "model/People_.h"
#include "model/Pdfs_.h"
#include "amuitable/AMUIHeartbeatProvider_.h"
#include "generated/tables.h"
#include "amui/AMUIConfig.h"

const std::map<std::string, AMUITable::AMUITableDescriptor<SAWEnum, SAWString> > g_tables{
    {"userdegrees", {"dbo.user_degrees", "dbh.user_degrees", sizeof(UserDegrees_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), UserDegrees_, SAW_tables::user_degrees}}                                                                                                                     ,
    {"categories", {"dbo.categories", "dbh.categories", sizeof(Categories_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Categories_, SAW_tables::categories}}                                                                                                                               ,
    {"users", {"dbo.users", "dbh.users", sizeof(Users_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Users_, SAW_tables::users}}                                                                                                                                                            ,
    {"roles", {"dbe.roles", "", sizeof(UserRoles_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), UserRoles_, AMUI::DEFAULT_INT_ID}}                                                                                                                                                          ,
    {"citizenships", {"dbe.citizenships", "", sizeof(Citizenships_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Citizenships_, AMUI::DEFAULT_INT_ID}}                                                                                                                                      ,
    {"sex", {"dbe.sex", "", sizeof(Sex_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Sex_, AMUI::DEFAULT_INT_ID}}                                                                                                                                                                          ,
    {"people", {"dbo.people", "dbh.people", sizeof(People_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), People_, SAW_tables::people}}                                                                                                                                                      ,
    {"pdfs", {"dbo.pdfs", "dbh.pdfs", sizeof(Pdfs_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Pdfs_, SAW_tables::pdfs}},
    {"generatepdf", {"dbo.pdfs", "", sizeof(Pdfs_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), Pdfs_, SAW_tables::pdfs}},
    {"heartbeat", {"", "", sizeof(AMUIHeartbeatProvider_) / sizeof(AMUITable::AMUITableColumn<SAWEnum, SAWString>), AMUIHeartbeatProvider_, AMUI::DEFAULT_INT_ID}},
};