//
// Created for categories table adaptation
//

#include "Categories.h"
#include "Categories_.h"
#include "amuitable/AMUITableColumn.hpp"

int Categories::sSourcesCount()
{
    return sizeof (Categories_) / sizeof (AMUITable::AMUITableColumn<SAWEnum, SAWString>);
}

const AMUITable::AMUITableColumn<SAWEnum, SAWString> *Categories::sSources()
{
    return Categories_;
}

Categories::Categories()
    : TableProviderImpl("categories")
{

}
