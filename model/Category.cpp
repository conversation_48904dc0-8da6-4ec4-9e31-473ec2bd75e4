//
// Created for categories table adaptation
//

#include "Category.h"
#include "SAWSettings.h"
#include "ameliteui/AMEliteApp.h"
#include "amuitable/AMUITableConfig.h"
#include "amui/AMUIConfig.h"
#include "Categories.h"

bool Category::isDataAvail(void * mem)
{
    return AMEliteUI::AMEliteApp::instance().downloadHttpSyncAvail(mem);
}

void Category::init(AMUI::AMUIController* controller)
{
    AMUITable::AMUITableForm<SAWEnum, SAWString>::init(controller, Categories::sSourcesCount(), Categories::sSources(), std::string("categories"));

    id.init();
    name.init();
    weight.init();
    paid.init("Ne", "Ano");
    supercategory.init("categories", "name", {}, m_strVoidParam);
    note.init();
    authorTag.init();
}

std::string Category::recordTitle()
{
    return m_idElement->value() == AMUI::DEFAULT_INT_ID ? std::string() : std::string(name.buffer());
}

Category::Category(const std::string &strVoidParam)
    : AMUITable::AMUITableForm<SAWEnum, SAWString>(strVoidParam),
      id("id", true, false, true),
      name("name"),
      weight("weight"),
      paid("paid"),
      supercategory("supercategory"),
      note("note"),
      authorTag("modified"),
      m_sectionMain{&id, &name, &weight, &paid, &supercategory},
      m_sectionSecond{&note, &authorTag}
{

}

int Category::sections()
{
    return 2;
}

std::vector<AMDialogs::AMUIFormElement *>& Category::elementsForSection(int n)
{
    if (n == 0) {
        return m_sectionMain;
    } else {
        return m_sectionSecond;
    }
}
