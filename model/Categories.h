//
// Created for categories table adaptation
//

#ifndef SAW_ALL_CATEGORIES_H
#define SAW_ALL_CATEGORIES_H

#include <string>
#include "SAWSettings.h"
#include "TableProviderImpl.h"
#include "amuitable/AMUITableColumn.h"
#include "amcore/AMTextUtils.h"
#include "generated/tables.h"

class App;

class Categories: public TableProviderImpl {

public:
    Categories();
    static int sSourcesCount();
    static const AMUITable::AMUITableColumn<SAWEnum, SAWString>* sSources();
    static int tableId() {return SAW_tables::categories;}
};

#endif //SAW_ALL_CATEGORIES_H
