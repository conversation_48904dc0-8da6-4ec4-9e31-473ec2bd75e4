# renew_before_expiry = 30 days
version = 3.3.0
archive_dir = /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud
cert = /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/cert.pem
privkey = /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/privkey.pem
chain = /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/chain.pem
fullchain = /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/fullchain.pem

# Options used in the renewal process
[renewalparams]
account = b1747b4e4c9ff2f5413c3eadbb4d1958
pref_challs = dns-01,
config_dir = /Users/<USER>/dev/saw/certs/local
work_dir = /Users/<USER>/dev/saw/certs/local
logs_dir = /Users/<USER>/dev/saw/certs/local
server = https://acme-v02.api.letsencrypt.org/directory
authenticator = manual
key_type = ecdsa
