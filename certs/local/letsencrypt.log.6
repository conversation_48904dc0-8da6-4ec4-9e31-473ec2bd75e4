2024-11-07 14:21:13,445:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-07 14:21:13,446:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-07 14:21:13,446:DEBUG:certbot._internal.main:Arguments: ['-v', '--manual', '--key-type', 'rsa', '--email', '<EMAIL>', '-d', 'test.local.sawapp.cloud', '--agree-tos', '--preferred-challenges=dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2024-11-07 14:21:13,446:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-07 14:21:13,457:DEBUG:certbot._internal.log:Root logging level set at 20
2024-11-07 14:21:13,458:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 14:21:13,458:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x106a0e510>
Prep: True
2024-11-07 14:21:13,458:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x106a0e510> and installer None
2024-11-07 14:21:13,458:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-07 14:21:13,476:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-07 14:21:13,480:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-07 14:21:13,952:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-07 14:21:13,953:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:21:13 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "-Z6NxT0QIY0": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-07 14:21:13,953:DEBUG:acme.client:Requesting fresh nonce
2024-11-07 14:21:13,953:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-07 14:21:14,105:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-07 14:21:14,106:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:21:14 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQtygoaMDeHYxtyz9YZSs6ZMXyf2A-I_Ru31CqjIxZPjM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-07 14:21:14,107:DEBUG:acme.client:Storing nonce: dLyaEgFQtygoaMDeHYxtyz9YZSs6ZMXyf2A-I_Ru31CqjIxZPjM
2024-11-07 14:21:14,107:DEBUG:acme.client:JWS payload:
b'{\n  "contact": [\n    "mailto:<EMAIL>"\n  ],\n  "termsOfServiceAgreed": true\n}'
2024-11-07 14:21:14,113:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-acct:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAiandrIjogeyJuIjogInVIVmN4Y2JRS25UaVo2aVF5RGNPcFR0VVl4Q1hjXzlQVTNLdHJkWTExamJ0dHVoZkthY2lPRGlpMVMtczZPSE1oTXVlNFpXUll4WV9Hc3VSeVBQMjZ5bzVsNW0tZTV6OTF4WXZTRFk1QVExVWE0RTV3WDJYZnhFb0pBUkpiUGh6ODJkOG5hTkdSbUZDbk45V2tFRjJkdUI3T3BMdW1NTUJrd2FWczlEUVJrMnlJZzl2SGtwcHp1ZjloVl93anhqVzRGNktxTzQ2c3lZYmRXVWk0Z0IwbjJzN00xcHJNY2RhYy1KcVdGdS1EbDc2UE5iM2hqRzFSUTdUM1lYMGtYZzBjVkZpeUlycGhyTDl2bWh3SEVURjRITGYyd1R0RHIzVmthSmJlRG01TndUNzljZ0pSSjdZLW1uNzR0aTlXNzlrUlpqUTdUeGNGWVhQMjBvZnJNcTJZdyIsICJlIjogIkFRQUIiLCAia3R5IjogIlJTQSJ9LCAibm9uY2UiOiAiZEx5YUVnRlF0eWdvYU1EZUhZeHR5ejlZWlNzNlpNWHlmMkEtSV9SdTMxQ3FqSXhaUGpNIiwgInVybCI6ICJodHRwczovL2FjbWUtdjAyLmFwaS5sZXRzZW5jcnlwdC5vcmcvYWNtZS9uZXctYWNjdCJ9",
  "signature": "VKnlmLhZC22-Ho6-OOwaFuLKjCDyvZhl7BX6pBugzr5d7JfIQP71dvraAxpRilxvOIQmEruiev7IOx7zpR9eQoQcVhn1lRjccJoNHtNLe-GwMG--V0EmAYiUDSi3HFLt10NTDj2Zagfw-L71Tf1Bi9r74MzX5GfHvbII1KVxwP11YhywzJ6Lo-jgRBlN-eaygGO8uc4ZjyHqHOZ2jTQK1Z_CctFREiS88NWzpqNJPnBN7KEdmnNh7qSfRvwvZ0Pmkz0vbdEOLKYRg45mx3eEMV2SIxP5PjtdpH5PCgdycApP-Tm6o2v4LR56qetu-P4Pu_AsEOC8itTevASJiCIdNg",
  "payload": "******************************************************************************************************************************"
}
2024-11-07 14:21:14,290:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-acct HTTP/11" 201 569
2024-11-07 14:21:14,291:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 07 Nov 2024 13:21:14 GMT
Content-Type: application/json
Content-Length: 569
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf>;rel="terms-of-service"
Location: https://acme-v02.api.letsencrypt.org/acme/acct/**********
Replay-Nonce: guak6r7LEUmUDp1BaaROUTzTnrN9AE20KWUYM7rz36z73TBNLP0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "key": {
    "kty": "RSA",
    "n": "uHVcxcbQKnTiZ6iQyDcOpTtUYxCXc_9PU3KtrdY11jbttuhfKaciODii1S-s6OHMhMue4ZWRYxY_GsuRyPP26yo5l5m-e5z91xYvSDY5AQ1Ua4E5wX2XfxEoJARJbPhz82d8naNGRmFCnN9WkEF2duB7OpLumMMBkwaVs9DQRk2yIg9vHkppzuf9hV_wjxjW4F6KqO46syYbdWUi4gB0n2s7M1prMcdac-JqWFu-Dl76PNb3hjG1RQ7T3YX0kXg0cVFiyIrphrL9vmhwHETF4HLf2wTtDr3VkaJbeDm5NwT79cgJRJ7Y-mn74ti9W79kRZjQ7TxcFYXP20ofrMq2Yw",
    "e": "AQAB"
  },
  "contact": [
    "mailto:<EMAIL>"
  ],
  "initialIp": "*************",
  "createdAt": "2024-11-07T13:21:14.223321437Z",
  "status": "valid"
}
2024-11-07 14:21:14,292:DEBUG:acme.client:Storing nonce: guak6r7LEUmUDp1BaaROUTzTnrN9AE20KWUYM7rz36z73TBNLP0
2024-11-07 14:21:37,214:DEBUG:certbot._internal.display.obj:Notifying user: Account registered.
2024-11-07 14:21:37,214:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=JWKRSA(key=<ComparableRSAKey(<cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey object at 0x106fb9ad0>)>), contact=('mailto:<EMAIL>',), agreement=None, status='valid', terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service='https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf'), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-07 14:21:37,216:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for test.local.sawapp.cloud
2024-11-07 14:21:37,268:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "test.local.sawapp.cloud"\n    }\n  ]\n}'
2024-11-07 14:21:37,269:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "pXNFP7aRlaOqxiagwnX_H5ARWo15L7Kj8LXXezFtLJaOfJGXHneYZeAlb2Ra1yOdyHCV3Gp-FHciYepeT9snLexN3U1Vs2BBZghtTZLhbGnQd1eNJ0jBhjg1zCR6WeOy67A12tfyS4uXGkB5_ELJ8iN5EN3TicDB-16Ham8v8_4LbMany8-TujGTAlVVf3CDTnbYd52ZlyGIGhzGg6mOM-chk1Pl7dF3CEuGDKr0yHb7Xl3YJxDLMQloTROEhJMMWr0UjRnsRarVFW52WGq6aJ7GS8aeA-LdmFsgvyJzSMw8sqRq46bgUUBY-DrR6ne3KhTp1aX7hNdm_xbSVptu7w",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogInRlc3QubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfQogIF0KfQ"
}
2024-11-07 14:21:37,470:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 349
2024-11-07 14:21:37,471:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 07 Nov 2024 13:21:37 GMT
Content-Type: application/json
Content-Length: 349
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/320862227587
Replay-Nonce: dLyaEgFQRADGU-4nhQ9g660WQiBek_JoCc706gtcZ7P8HdQILms
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-14T13:21:37Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "test.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426841675327"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320862227587"
}
2024-11-07 14:21:37,471:DEBUG:acme.client:Storing nonce: dLyaEgFQRADGU-4nhQ9g660WQiBek_JoCc706gtcZ7P8HdQILms
2024-11-07 14:21:37,473:DEBUG:acme.client:JWS payload:
b''
2024-11-07 14:21:37,475:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426841675327:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUVJBREdVLTRuaFE5ZzY2MFdRaUJla19Kb0NjNzA2Z3RjWjdQOEhkUUlMbXMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0MTY3NTMyNyJ9",
  "signature": "BF8syUyLmEdV-HOIkOfpyJiDUVOtV7OSctrzT9KKlP2ZX1XXh_LnuEgnWqW5lhH0lxr36ezT71emf9m44wD2k5BmDuVkKMeVdMy0FhimtOGEcwD1mUL_qyiP60u6NmaIfd9Lu8Ftr0tw9-jyPSsZ4kSce6sYBZ5ge4jJnOyJfild-dnb_0I3b5LZQmER-ZFghkdk3f8vFkD2993iRQFqy8WShk6dUz9O9Gy-6rtTwAz5DgroPWYuaJQF20i22Fs8nCuATzg4vcBgNlE_f0AaZQIUhsdHtRV-uX6nbuZRzFfhhNyVufFKGFJ44FH-VkBUftlwxpG1pMaPWy0ClydZ6w",
  "payload": ""
}
2024-11-07 14:21:37,632:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426841675327 HTTP/11" 200 807
2024-11-07 14:21:37,633:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:21:37 GMT
Content-Type: application/json
Content-Length: 807
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQcZty2GgsbbevEEicdsZhy4ak1OCujylfGhgatlSs47s
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "test.local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T13:21:37Z",
  "challenges": [
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426841675327/SnBtBg",
      "status": "pending",
      "token": "NEpyQn_DRjedXKy5CoD4ID0Zccn-syokbO188sgeSo4"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426841675327/ihjrrA",
      "status": "pending",
      "token": "NEpyQn_DRjedXKy5CoD4ID0Zccn-syokbO188sgeSo4"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426841675327/nhLJ0Q",
      "status": "pending",
      "token": "NEpyQn_DRjedXKy5CoD4ID0Zccn-syokbO188sgeSo4"
    }
  ]
}
2024-11-07 14:21:37,633:DEBUG:acme.client:Storing nonce: dLyaEgFQcZty2GgsbbevEEicdsZhy4ak1OCujylfGhgatlSs47s
2024-11-07 14:21:37,634:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-07 14:21:37,634:INFO:certbot._internal.auth_handler:dns-01 challenge for test.local.sawapp.cloud
2024-11-07 14:21:37,637:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.test.local.sawapp.cloud.

with the following value:

rtTDilQWLdNViPOcrlLr7YgED5giVDsRVlx_l3Fq0bQ

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.test.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-07 14:36:02,253:DEBUG:certbot._internal.error_handler:Encountered signals: [1]
2024-11-07 14:36:02,254:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-07 14:36:02,255:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-07 14:36:02,256:DEBUG:certbot._internal.error_handler:Calling signal 1
