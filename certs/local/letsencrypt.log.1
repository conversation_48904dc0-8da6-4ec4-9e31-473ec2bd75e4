2025-05-09 11:49:14,564:DEBUG:certbot._internal.main:certbot version: 3.3.0
2025-05-09 11:49:14,564:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-05-09 11:49:14,564:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2025-05-09 11:49:14,564:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-05-09 11:49:14,579:DEBUG:certbot._internal.log:Root logging level set at 30
2025-05-09 11:49:14,580:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 11:49:14,580:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x10461c440>
Prep: True
2025-05-09 11:49:14,580:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x10461c440> and installer None
2025-05-09 11:49:14,580:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-05-09 11:49:14,616:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=datetime.timezone.utc), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-05-09 11:49:14,624:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-05-09 11:49:14,629:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-05-09 11:49:15,141:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/1.1" 200 1012
2025-05-09 11:49:15,142:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:49:15 GMT
Content-Type: application/json
Content-Length: 1012
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "https://letsencrypt.org/docs/profiles#classic",
      "shortlived": "https://letsencrypt.org/docs/profiles#shortlived (not yet generally available)",
      "tlsserver": "https://letsencrypt.org/docs/profiles#tlsserver"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.5-February-24-2025.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "qtYQJViUyaA": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-05-09 11:49:15,150:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-05-07 11:52:41 UTC.
2025-05-09 11:49:15,150:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-05-09 11:49:15,150:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.local.sawapp.cloud and local.sawapp.cloud
2025-05-09 11:49:15,152:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 11:49:15,153:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 11:49:15,317:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 11:49:15,317:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:49:15 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2ZvabbsoSjyb_9gfYcX9l1KFGROiNrQcBW-qM2cgvpkI
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 11:49:15,317:DEBUG:acme.client:Storing nonce: QmouDpB2ZvabbsoSjyb_9gfYcX9l1KFGROiNrQcBW-qM2cgvpkI
2025-05-09 11:49:15,318:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2025-05-09 11:49:15,322:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "M3ZZcdyIvV6uqF2GBTQaEfZhiGD_OtYhmVX51sOxXiCZ2lwx8qeh6qIl0z2M194UdUBh4A3KALqyyny1unzGVkO36jLKNUtswJ_eXMWKfw9wKbxwcluDUFtrHqLUiq43K1FdVSSA0kgfd8wEj3Q4IePGaiTD5AQztqUiu8ypn3rcahQAN41qQ2LNTVjvqOylI-mLaRwnmbqNO6kPB-TJm4cRh3n1JDK1WxWUZ3JvQQeC4A4nNTjcIaCa9mTLT-lbRjGyORnV3rWak3m-gUGdIneCXCILgXwJPCgPN3GTHxn_hGhSVjlUMl0MHole_AZqrfgvG-rooSStis32EpUraA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2025-05-09 11:49:15,531:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/1.1" 201 503
2025-05-09 11:49:15,532:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Fri, 09 May 2025 09:49:15 GMT
Content-Type: application/json
Content-Length: 503
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382333584497
Replay-Nonce: QmouDpB2h8WdBHzmkjBg192M2PxMVUDg5ca2RK_k9b6ugxOYGS0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-05-16T09:49:15Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595317",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382333584497"
}
2025-05-09 11:49:15,532:DEBUG:acme.client:Storing nonce: QmouDpB2h8WdBHzmkjBg192M2PxMVUDg5ca2RK_k9b6ugxOYGS0
2025-05-09 11:49:15,532:DEBUG:acme.client:JWS payload:
b''
2025-05-09 11:49:15,535:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595317:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJRbW91RHBCMmg4V2RCSHpta2pCZzE5Mk0yUHhNVlVEZzVjYTJSS19rOWI2dWd4T1lHUzAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzE3In0",
  "signature": "Od88Mv7CieYySmAlxgZtixMmUKXTPVZSqoB2f2Ey_sGs_qTLAxvmmbu1W2PlKTDfzknBZhfEIZxr7Yw4GyP_8W1TbtkOObYBSF9DBSZY2JHbKlxoPZO3-hla8EyKLwtSrbdvSKA48IuZ-_d7zEl9-_NQIUlROUnjn_oVD-WaO38x8sjRnZ1VieRF1eNMoqLlkh_-6OS5deWMA0J40C-cen6AW8ny1slCXiIm_8OZ63Fn6tI85K_Kn2kVW0-LKLPG_P_yVOMG8dzU_mjJ3tKHCLj3E-vMTa4bJMk9Fl7gERKdQ8c5CD-eFBLMnBX5xH1OcwfRI5FCWM2sPQDqcnBsXw",
  "payload": ""
}
2025-05-09 11:49:15,705:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595317 HTTP/1.1" 200 400
2025-05-09 11:49:15,706:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:49:15 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2A-H5GoDtizZQAdvb2PKt70wJ9hQ2lAlJQz1Gg6-5Uos
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:49:15Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg",
      "status": "pending",
      "token": "YD0h_VMCoFFtjRM75sDVY2GqzJlqRfZibORDbwA0lDY"
    }
  ],
  "wildcard": true
}
2025-05-09 11:49:15,706:DEBUG:acme.client:Storing nonce: QmouDpB2A-H5GoDtizZQAdvb2PKt70wJ9hQ2lAlJQz1Gg6-5Uos
2025-05-09 11:49:15,707:DEBUG:acme.client:JWS payload:
b''
2025-05-09 11:49:15,709:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJRbW91RHBCMkEtSDVHb0R0aXpaUUFkdmIyUEt0NzB3SjloUTJsQWxKUXoxR2c2LTVVb3MiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzM3In0",
  "signature": "ENpr8t80iG0Co7VxqxeTMVzgEQDmWcCmsHVrvsRy2CiCvyY4kgv_K228RnriLjlxg2N2zyN4SkUsUtAzKlBwJCgQhvEhSSGuEoNfTHPK4viAGLW-qe-BtIUXXow0RHm_SijnSlYERn1-Dv_GeuhfLi6PtlDOpRWyJPRadGIO2vP4Y5Li5AB1-V-hAAQ5DWc0vSyTT02lvgjdagWKNgtRLLsrPJilZfYkhR3_YvvzOkLPAW9PF9P9brYzpDmj1ArYyCL8OTP8Vm6rCisijBuJuNGWwm66IKB-6IXd1DobpZvHKZcAoVenFExiNEst-JbX0P095suJRd_nDVzZhPeAkw",
  "payload": ""
}
2025-05-09 11:49:15,879:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595337 HTTP/1.1" 200 826
2025-05-09 11:49:15,880:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:49:15 GMT
Content-Type: application/json
Content-Length: 826
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6Noiooos_1qWEB1pxSCVC-ljFnFBAxHENyMsnpRyyxCeg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:49:15Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/9qZ2Nw",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DjLcNQ",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    }
  ]
}
2025-05-09 11:49:15,881:DEBUG:acme.client:Storing nonce: ww5-sb6Noiooos_1qWEB1pxSCVC-ljFnFBAxHENyMsnpRyyxCeg
2025-05-09 11:49:15,882:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-05-09 11:49:15,882:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-05-09 11:49:15,882:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-05-09 11:49:15,884:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

3hcrZ4QwdWfBTj6tx4O9ZNPBzct5RdVq2si7OhSP6d0

2025-05-09 12:59:36,331:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

O6KKcGgyrIlCJ0VYUTMFJYjIMFWYJoUXqK38r6lN0Ws

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-05-09 13:51:47,879:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:47,885:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "q75qBP8U_tTjAtFKdcIO2oyhynksb935cHLbDJdBOIKBs3vn0LV9caoifDzHL4Z9s4RCgnBIoP4KWsWi8UAmVcJGH74msn9RjleEMstG2zwvcGICddK7wLl0QKR3Rm21_giHA5_EfpRmpDX3cG20JzAiDu5s7BV6r_iAeW1KJlXDl9SorDzmk-3xizCgm1X_o4C4kstiAiBc8SQBTlpe6sAjM3sXeuEBSHyIgwOhu8tItTdA5uNI4KEeqUr6bqL9qM0EaOq9Ni4bKWkFLQYKMw2TVtWVX4c3-lwG1bl0WDTaLdrTU1sXe0VY_bX-AlqlCasHRuaJgLDXKqhzukMZqg",
  "payload": "e30"
}
2025-05-09 13:51:47,888:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-05-09 13:51:48,385:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517752595317/KrnGXg HTTP/1.1" 400 203
2025-05-09 13:51:48,385:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Fri, 09 May 2025 11:51:48 GMT
Content-Type: application/problem+json
Content-Length: 203
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwkYB3vj6mMZKpuj4BpKYfUB7Bn_KDQ6Z6mihEG2lSfhw

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "Unable to validate JWS :: JWS has an invalid anti-replay nonce: \"ww5-sb6Noiooos_1qWEB1pxSCVC-ljFnFBAxHENyMsnpRyyxCeg\"",
  "status": 400
}
2025-05-09 13:51:48,386:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: Unable to validate JWS :: JWS has an invalid anti-replay nonce: "ww5-sb6Noiooos_1qWEB1pxSCVC-ljFnFBAxHENyMsnpRyyxCeg"
2025-05-09 13:51:48,386:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 13:51:48,386:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 13:51:48,550:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 13:51:48,551:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:48 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDDPkGtMKAhGGYQJLBMF7okNOXIM2YkqtDEuIH4zXfjbA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 13:51:48,552:DEBUG:acme.client:Storing nonce: yPpvzgaDDPkGtMKAhGGYQJLBMF7okNOXIM2YkqtDEuIH4zXfjbA
2025-05-09 13:51:48,552:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:48,555:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRERQa0d0TUtBaEdHWVFKTEJNRjdva05PWElNMllrcXRERXVJSDR6WGZqYkEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNTE3NzUyNTk1MzE3L0tybkdYZyJ9",
  "signature": "qYuCEhoQYCzXe8IQr5LfHNcG1Gt5IW43XTKQanSMm1J1jYjY66unU6Fz5PLRCYtCYCoR8Tk0nrwDR8U0AoF0i1Yap9fOIKUQZhtXADYQTUHHQq4-lSN-5QpPjfcnXySeERPNX2A3H16bfqh4VuHXdas95ouNPMObrYH2oUdGyChEAJRWCtXoc-6lAKNCEu2IIP4rTTVRZF3AglaYcKWmkI78LDGXSqCWT-7wUFaxdPNaP3gvcXyAZfcFbT1B4_t5s9Z_O_BlpJEOtD7IVvUUsqGsA2Z1JZMN0R7mBlWPiqeRhY1hG88r14BLcRvWmTJRU8qJJvZsuQtpWQNk5KkROA",
  "payload": "e30"
}
2025-05-09 13:51:48,754:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517752595317/KrnGXg HTTP/1.1" 200 194
2025-05-09 13:51:48,754:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:48 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595317>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg
Replay-Nonce: NUDoTsQwiKEWIksS_lqrf5LxQ4lOnvt9RsEqUmfdHXn-tMWXqyo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg",
  "status": "pending",
  "token": "YD0h_VMCoFFtjRM75sDVY2GqzJlqRfZibORDbwA0lDY"
}
2025-05-09 13:51:48,755:DEBUG:acme.client:Storing nonce: NUDoTsQwiKEWIksS_lqrf5LxQ4lOnvt9RsEqUmfdHXn-tMWXqyo
2025-05-09 13:51:48,755:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:48,758:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJOVURvVHNRd2lLRVdJa3NTX2xxcmY1THhRNGxPbnZ0OVJzRXFVbWZkSFhuLXRNV1hxeW8iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNTE3NzUyNTk1MzM3L0RJLWFyUSJ9",
  "signature": "Po3xi9tlTjiZymyy9DWCMCAMS1coO6pPH17YiZT9kGdyMVuAeUINPRTlrnx2f766z2TLpcLPEFeMCYgzR-dYZHEj-XpoP7I8-YPDLIBMtT-t5RJVUZp7VOUkHjtVtQxN9NMF5SEpGz959jXbMdQg4IRDcnm2cxL7sMAWljCrrjorxarJkR_GS1RYG4rh73Ot7Hjjutbd03SHNs1tWmgZn97yPtT21vsdp-iEWccABlVDoEIDljP9WjiiEGK8vLvbF4PVr1hnW1HpRsNEHa_Zzj5sDUrRrS7NWaTxKB7r_thYaWQ36BXiTIsFBt3Irdvi4LBmARZUlAIzW7Lb6jXaHA",
  "payload": "e30"
}
2025-05-09 13:51:48,931:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517752595337/DI-arQ HTTP/1.1" 200 194
2025-05-09 13:51:48,932:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:48 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ
Replay-Nonce: yPpvzgaD0Vm3nFuUum-qcYc2POslbR-4ULj2cQ6_UYlItwsCfcQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
  "status": "pending",
  "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
}
2025-05-09 13:51:48,932:DEBUG:acme.client:Storing nonce: yPpvzgaD0Vm3nFuUum-qcYc2POslbR-4ULj2cQ6_UYlItwsCfcQ
2025-05-09 13:51:48,933:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-05-09 13:51:49,938:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:49,941:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595317:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRDBWbTNuRnVVdW0tcWNZYzJQT3NsYlItNFVMajJjUTZfVVlsSXR3c0NmY1EiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzE3In0",
  "signature": "h_O2sL0dwmUbP2o9ufmn-qbkmtigrIr9D8mAaZn-p_L058iBdh0NPw50VU6lc6Smb62gwbM1Vx5DBQCwR80hJCEc_RQaSH8OvnPkL4nxzrLxApuS6o_0RbahqBJooA7yqgSVpfxMHKGbGkvl5oI21bOMWBcgP3ZD7cy8l-T2jojrDKkJ53v08gqMFrGymyNOSZKmKehuECq7pKxeRpAWVLkxkMVmrJvyeLSEsPkn4_Sw7MeHdjRzxwvfoBMDq7Z2fYWRHjxLKxrIg5G68cbHaNPAS0u9yYw-ge-lvYZgErXbI1mEkUWP-M6Ywi6FNJQbkNMe8MtKht2OXKsL3rh4tA",
  "payload": ""
}
2025-05-09 13:51:50,114:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595317 HTTP/1.1" 200 684
2025-05-09 13:51:50,115:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:50 GMT
Content-Type: application/json
Content-Length: 684
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwlaly1E5S_G25JgQ4rZwIWEt4um5NB_eDkkB-UOQQM9w
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2025-05-16T09:49:15Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595317/KrnGXg",
      "status": "invalid",
      "validated": "2025-05-09T11:51:48Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"O6KKcGgyrIlCJ0VYUTMFJYjIMFWYJoUXqK38r6lN0Ws\" found at _acme-challenge.local.sawapp.cloud",
        "status": 403
      },
      "token": "YD0h_VMCoFFtjRM75sDVY2GqzJlqRfZibORDbwA0lDY"
    }
  ],
  "wildcard": true
}
2025-05-09 13:51:50,115:DEBUG:acme.client:Storing nonce: NUDoTsQwlaly1E5S_G25JgQ4rZwIWEt4um5NB_eDkkB-UOQQM9w
2025-05-09 13:51:50,115:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:50,118:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJOVURvVHNRd2xhbHkxRTVTX0cyNUpnUTRyWndJV0V0NHVtNU5CX2VEa2tCLVVPUVFNOXciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzM3In0",
  "signature": "qnsbwyOJCxJlGBdCjIon6vi9RoBNVVRbExgKzRIJSI81XwR3Ot044hLOdOQ7OM0bjhz4htirPnPCnJiTj-HZfjagaikFk4FHVga5ycJVgQWRWQhr7dHuias_cPgDDtaJKV2HAPHZY7Rbqvy9JvCvjMIfI-GcVrYX8I6UMNtFhLsWLPuq-E92ti0NFR6-YtCrvlv_YiqaxDcMnROtdZGRVSxflh8n6cSlHKcB8Qb3JtFZlsiVj23dTzNWJDGyDfazPMoIfBnT-TPw_U1g79VJTgCF96TPX3wRB6fYm4Z0n6YPce4MZWrjo4gFuYrWqczf90wR_IqkXlMmeg_EVWxoAw",
  "payload": ""
}
2025-05-09 13:51:50,297:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595337 HTTP/1.1" 200 826
2025-05-09 13:51:50,298:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:50 GMT
Content-Type: application/json
Content-Length: 826
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwBF5NWF4y_KnUeuSu-6kTScbRjcJxnf0cBPvtwuek_t4
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:49:15Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DjLcNQ",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/9qZ2Nw",
      "status": "pending",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s"
    }
  ]
}
2025-05-09 13:51:50,298:DEBUG:acme.client:Storing nonce: NUDoTsQwBF5NWF4y_KnUeuSu-6kTScbRjcJxnf0cBPvtwuek_t4
2025-05-09 13:51:50,299:INFO:certbot._internal.auth_handler:Challenge failed for domain local.sawapp.cloud
2025-05-09 13:51:53,304:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:53,307:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJOVURvVHNRd0JGNU5XRjR5X0tuVWV1U3UtNmtUU2NiUmpjSnhuZjBjQlB2dHd1ZWtfdDQiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzM3In0",
  "signature": "BXbjPOsMB49nGazlQv8C7det8r9cyZO_24L54E2h0g1NBlIqrNwA7UABmmg_WPaoQc84Xtwd0f23aOTte7-UNWcbpMS8H9a91piNic34JR6ZNYzFi0WyxfStkdmPlc-vdgj_d8MkrSfc8FqbSJbvaF4EN17hyeJLS7vV9_DzQAm4m25v-fbTput0S_9qv5YEDgi1canrE365STN2KY6yPPl849WvY-5ybWzeUkgiuHTqKZWm7BCWwGQr110Vvj2DqI1ikQlUO1g0TtAyB_X7RcAExoCzuDVLQJqHSGIqdBb1T3pnE04X8OviMT0XjX1w9zm33KRGGTEmx9_wnQnmlw",
  "payload": ""
}
2025-05-09 13:51:53,473:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595337 HTTP/1.1" 200 519
2025-05-09 13:51:53,473:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:53 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwWXgD3AxCwwy6_oFS-bOZCX1R3R7Ic11cSdLtNETm6jM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:51Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
      "status": "valid",
      "validated": "2025-05-09T11:51:48Z",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 13:51:53,473:DEBUG:acme.client:Storing nonce: NUDoTsQwWXgD3AxCwwy6_oFS-bOZCX1R3R7Ic11cSdLtNETm6jM
2025-05-09 13:51:53,474:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-05-09 13:51:53,475:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: local.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "O6KKcGgyrIlCJ0VYUTMFJYjIMFWYJoUXqK38r6lN0Ws" found at _acme-challenge.local.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2025-05-09 13:51:53,478:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2025-05-09 13:51:53,478:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-05-09 13:51:53,479:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-05-09 13:51:53,479:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1871, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1577, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 130, in _get_and_save_cert
    renewal.renew_cert(config, domains, le_client, lineage)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/renewal.py", line 399, in renew_cert
    new_cert, new_chain, new_key, _ = le_client.obtain_certificate(domains, new_key)
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 423, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 492, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2025-05-09 13:51:53,485:ERROR:certbot._internal.log:Some challenges have failed.
