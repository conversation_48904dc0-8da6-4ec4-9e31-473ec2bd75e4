2025-02-06 13:11:20,110:DEBUG:certbot._internal.main:certbot version: 2.11.0
2025-02-06 13:11:20,110:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-02-06 13:11:20,110:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2025-02-06 13:11:20,110:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-02-06 13:11:20,124:DEBUG:certbot._internal.log:Root logging level set at 30
2025-02-06 13:11:20,125:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 13:11:20,125:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x1047da3c0>
Prep: True
2025-02-06 13:11:20,125:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x1047da3c0> and installer None
2025-02-06 13:11:20,125:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-02-06 13:11:20,161:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-02-06 13:11:20,169:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-02-06 13:11:20,175:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-02-06 13:11:20,650:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 828
2025-02-06 13:11:20,651:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:20 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "AKcQnR-0X2k": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "The same profile you're accustomed to"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-02-06 13:11:20,664:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-02-05 14:39:40 UTC.
2025-02-06 13:11:20,665:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-02-06 13:11:20,665:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.local.sawapp.cloud and local.sawapp.cloud
2025-02-06 13:11:20,667:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 13:11:20,667:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 13:11:20,825:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 13:11:20,826:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:20 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyymam1kgCejYHP0LElzQTd-xJxeQWwWeTuGbcrObhXEEc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 13:11:20,826:DEBUG:acme.client:Storing nonce: aTttXYyymam1kgCejYHP0LElzQTd-xJxeQWwWeTuGbcrObhXEEc
2025-02-06 13:11:20,826:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2025-02-06 13:11:20,831:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eW1hbTFrZ0NlallIUDBMRWx6UVRkLXhKeGVRV3dXZVR1R2Jjck9iaFhFRWMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL25ldy1vcmRlciJ9",
  "signature": "talP976gCBmHPdImEK1v8QC3_9fUBPikU_6kFBG0GkUgNcgVW2ceVbSF39_FBzklMSQweVNAPY1PxQto2V_1WKi-tZ0Fn1J9Xn4KxDjXrWhQ1jV5v2VWutA_AnxP1wZKjvu9qBDdQ5q4vq3ri9DRtb6AGXCl1piem--Yc85g2ABoF48h5LaOrtyfa0s16C4pTFLwB5bjA5tigzaT2Uzj3jwnYKHGrNTE6-eYoWJcUUvYrodlyKMqAD4GJsyug4SaVPhh-SJLUEUWzUBFBzgZCwzeu8lwXD93vbXY7IBvVH5piR5oi1c8aUABv2s_wO5S-XyBZndFNr4N7Ds-VRZlPw",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2025-02-06 13:11:21,036:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 503
2025-02-06 13:11:21,036:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 06 Feb 2025 12:11:20 GMT
Content-Type: application/json
Content-Length: 503
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351741742235
Replay-Nonce: hXNDuA8iaKJEYuDfg-708KD1cPpWk7qep9G6gkcsBiSPc-VinIE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-02-13T12:11:20Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351741742235"
}
2025-02-06 13:11:21,036:DEBUG:acme.client:Storing nonce: hXNDuA8iaKJEYuDfg-708KD1cPpWk7qep9G6gkcsBiSPc-VinIE
2025-02-06 13:11:21,037:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:11:21,039:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJoWE5EdUE4aWFLSkVZdURmZy03MDhLRDFjUHBXazdxZXA5RzZna2NzQmlTUGMtVmluSUUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODUzMzQ1MDk1In0",
  "signature": "AccBwjnRMmaomtY0Gr-vAJGyCEqYRHtcXSf23vVge-P9RcZ_WMmW8eAsr4UPuQZyOoEbee0BVz-2CEEI4bsWQH4CJNfu5a4JmK9DCHUYFa1DAX60Z7uPoRgZNC3euLbWD02LiZ6QVwDT1YLqh7fOWb9x6XzErzmySFo2eJwnQEhGmiGDwTOZO7HT_NLwW-SNP7OgF5l101L6sjZznl5gOjGUl6bo6mfx7gYdzj1G-4o_K-MF7YfpxynWPuv7l20L7C33jKy9f9aoH8UCwowEfTbNz3RAlbsMdj8oPyZvleSmoM1OmkoFXhWp0Ae3Tlj9-_DuQmubbDWA0zsxZ15FVg",
  "payload": ""
}
2025-02-06 13:11:21,226:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345095 HTTP/11" 200 519
2025-02-06 13:11:21,227:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:21 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyOikpyMEHY0X9Oe-QW_dZpMAvHLrPvOaB_oib3aF5x6I
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T12:11:07Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
      "status": "valid",
      "validated": "2025-02-06T12:11:06Z",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 13:11:21,227:DEBUG:acme.client:Storing nonce: aTttXYyyOikpyMEHY0X9Oe-QW_dZpMAvHLrPvOaB_oib3aF5x6I
2025-02-06 13:11:21,228:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:11:21,230:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eU9pa3B5TUVIWTBYOU9lLVFXX2RacE1BdkhMclB2T2FCX29pYjNhRjV4NkkiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODg2MTE4MTY1In0",
  "signature": "KB4hM8lG-sqm80lewSoSUSG3HiEVzD4Ni7ZaEwiJOENVkRYyLuvpwtx9t0tAAtnX06aFzB_S7h98WcsQEC_wwKwKWBygbKj1zxQ7PtHifoIgtrwQjXwzPQ9TjgwHZD264vk1mtzfRhYfrDVYZ1u9rV3EKPMYR6aqtkFIXH0ej0emIfJdYdJb_LZPvpxk_Lv2MsU9q-jRqOJG5HSSy48HT2ns9PW-ffZgvGtgt-AX_shW5S7kao98Ymw6dOeIzkyc1dBJMJ2b8MTDhCzvafZLbMw0jlbtpiljLUZeBKPcJSwP7VcJoilo99uwW6Zwgp8Pw1pth6e1LzE-Sg32BQezLQ",
  "payload": ""
}
2025-02-06 13:11:21,394:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471886118165 HTTP/11" 200 400
2025-02-06 13:11:21,395:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:21 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyXxQLKmjqsawVHdDLJcPb98lcfbLHVDorkXtQwxfgT2Y
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T12:11:20Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ",
      "status": "pending",
      "token": "eFCgO6_FV4y01oA_d28qzUcmH8-bwOfCOQ6WjFva58k"
    }
  ],
  "wildcard": true
}
2025-02-06 13:11:21,395:DEBUG:acme.client:Storing nonce: aTttXYyyXxQLKmjqsawVHdDLJcPb98lcfbLHVDorkXtQwxfgT2Y
2025-02-06 13:11:21,396:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-02-06 13:11:21,396:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-02-06 13:11:21,397:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

x2QYN7DefkMmCRPcyCWgsf0bof_62DnM50zS2dk7ZzU

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-02-06 13:51:06,657:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 13:51:06,664:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eVh4UUxLbWpxc2F3VkhkRExKY1BiOThsY2ZiTEhWRG9ya1h0UXd4ZmdUMlkiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNDcxODg2MTE4MTY1L1BoWV9KUSJ9",
  "signature": "nhAF0sHAqrL325ShHmtWPcguYpFYbhaICVbA68KnvkUkYmxBZjjYJomRXWEj6MqsMQSUjJP9xrVHCrV81_UP7S0srY3Sxxuv758wSCatPqaJVvtCAWXD6nnrW6YcM6CJ7sGTbIwXOAAp61v3XCMso29IGpBxb26qrufSgenC-P3rHixZvLz3Fr5GL9rT_Ls8y0gshOS00GD9STyizYujXWANkiG6JJNPwHiFAhPqyUqQnUlv8yLK_hR2uErIL_Cx5efAHgvkGjXcOWHTv6XsSfBACT0hXywhN2o-y652JD3nnJmDzhLYp9mWibEqbGlk7oMX1RKBjSLoFiuHSmZF6Q",
  "payload": "e30"
}
2025-02-06 13:51:06,667:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-02-06 13:51:07,169:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471886118165/PhY_JQ HTTP/11" 400 177
2025-02-06 13:51:07,170:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 06 Feb 2025 12:51:07 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: hXNDuA8ie6y21aykGUSN-uJ3_m8q0OkGAUjD_c2k15Q_uMNn50I

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"aTttXYyyXxQLKmjqsawVHdDLJcPb98lcfbLHVDorkXtQwxfgT2Y\"",
  "status": 400
}
2025-02-06 13:51:07,170:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "aTttXYyyXxQLKmjqsawVHdDLJcPb98lcfbLHVDorkXtQwxfgT2Y"
2025-02-06 13:51:07,170:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 13:51:07,170:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 13:51:07,327:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 13:51:07,327:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:07 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: hXNDuA8iPhsyXmPQcItY_7Zq5_d5Pz424jwVD9gnSzr9twyq_j8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 13:51:07,328:DEBUG:acme.client:Storing nonce: hXNDuA8iPhsyXmPQcItY_7Zq5_d5Pz424jwVD9gnSzr9twyq_j8
2025-02-06 13:51:07,328:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 13:51:07,330:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJoWE5EdUE4aVBoc3lYbVBRY0l0WV83WnE1X2Q1UHo0MjRqd1ZEOWduU3pyOXR3eXFfajgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNDcxODg2MTE4MTY1L1BoWV9KUSJ9",
  "signature": "Dkyjx_oeFD8adsZBW0ddpzhUgW4hSLmZ-wpmHPr7cE-DuXnNEW9Wia6q8EZ6CGdwwSD3tKJnbOGeKP0mDBuEQtjF9A8-9S4up14oFIMf0l7J-znzwYYbwJ8QERY9FT1cw1vsF-aKXqH2E1mOd7-xluHyy9BSZ9arrhFH67Lsgd5VqUqEZkDHvH64Nz453Q4r71ddxFcYs4gYP3Q0e9UDg7AVatYEGP7WDNg1CvUZ5pMy2sb8KL80gCgX3jDT4P39SeTl6TWvZ1zdKGrTbas1xD2_1sx7Qlw0jTXxd0-XdH2rn0coBJCBHCVVMbE6djlEUw3oLV3oMBsQ4GjenZbkJA",
  "payload": "e30"
}
2025-02-06 13:51:07,495:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471886118165/PhY_JQ HTTP/11" 200 194
2025-02-06 13:51:07,495:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:07 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ
Replay-Nonce: hXNDuA8iZgSyJrV292B7nhfmgY6JxbfqNlXSZ4Y-bJ1H53Rw2ng
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ",
  "status": "pending",
  "token": "eFCgO6_FV4y01oA_d28qzUcmH8-bwOfCOQ6WjFva58k"
}
2025-02-06 13:51:07,495:DEBUG:acme.client:Storing nonce: hXNDuA8iZgSyJrV292B7nhfmgY6JxbfqNlXSZ4Y-bJ1H53Rw2ng
2025-02-06 13:51:07,497:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-02-06 13:51:08,498:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:08,500:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJoWE5EdUE4aVpnU3lKclYyOTJCN25oZm1nWTZKeGJmcU5sWFNaNFktYkoxSDUzUncybmciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODUzMzQ1MDk1In0",
  "signature": "ZqPyYShDDE2A3XVYKpfLb0F8ozxar027Ck9vlT-b2vySDcQzQatGr3u3Pj8mFQCslBtOB8gJEy_gJVXZUlp1UkV_HjmPHjHHwtBHujkDQmhgdGKp63L31uuJHzDRVo7B2ESwl_eKqVc3kE1XaRnC3BufB2915YxF9Abs9NsBHJdszW51vkrpcjpGGDsNtYouQG9y8__vUbVBhWRsXjDJajint6uxBMqabaHe3cap0O6ZA_z3_b7jTmRdb0nMUrXYbMdK9lY9ymAyOI9Jlhw7JcUROve0UXKjoKALadKiMGJoQXQTLUFc0d41e7eZiTNlu9DjYgAC477egzbuHN3YJQ",
  "payload": ""
}
2025-02-06 13:51:08,661:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345095 HTTP/11" 200 519
2025-02-06 13:51:08,661:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:08 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: hXNDuA8iFzyL6YBUVQqT7JLTvMluJq4moyrgrfjW6adL17KiSfA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T12:11:07Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
      "status": "valid",
      "validated": "2025-02-06T12:11:06Z",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 13:51:08,662:DEBUG:acme.client:Storing nonce: hXNDuA8iFzyL6YBUVQqT7JLTvMluJq4moyrgrfjW6adL17KiSfA
2025-02-06 13:51:08,662:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:08,664:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJoWE5EdUE4aUZ6eUw2WUJVVlFxVDdKTFR2TWx1SnE0bW95cmdyZmpXNmFkTDE3S2lTZkEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODg2MTE4MTY1In0",
  "signature": "sgy_0N59HG8NycrUameNQhX-QmRQb1399vJ0xA-Q0wEkLFbOH5TtE0DIaQ7XxFIPPdgyZNe2V3bG6fFOzWek0bNm2rm4brIFXpFhQ4P_uevRsnRpTmyJgJu7LLya50X_glomrGD4nRhToYZspRiTMv3y_qfP4RD7H9cLnZitAyBKR6DLecuIQgBdmdZD8ytisJ8U5bY0kPiVhoYEcD8cu6a-s-vhz4e4GA-o8WcuLAgMDbAPNTf8o7Gs0hz1YYNuIS5dXMJzZxwQnmcBwCNXT8S_Is3ieYz8_ajnfcQV4ZB_FZwMHRT9h3ellC8VI6Q8pH9ASOvU__4pF26H9Ziv7Q",
  "payload": ""
}
2025-02-06 13:51:08,826:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471886118165 HTTP/11" 200 400
2025-02-06 13:51:08,826:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:08 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyXMMipI7inHUQHFDDgcG8zG-0shyNc_3Fd3kTn2l28U8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T12:11:20Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ",
      "status": "pending",
      "token": "eFCgO6_FV4y01oA_d28qzUcmH8-bwOfCOQ6WjFva58k"
    }
  ],
  "wildcard": true
}
2025-02-06 13:51:08,826:DEBUG:acme.client:Storing nonce: aTttXYyyXMMipI7inHUQHFDDgcG8zG-0shyNc_3Fd3kTn2l28U8
2025-02-06 13:51:11,827:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:11,829:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eVhNTWlwSTdpbkhVUUhGRERnY0c4ekctMHNoeU5jXzNGZDNrVG4ybDI4VTgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODg2MTE4MTY1In0",
  "signature": "b6ZgqwDYU6AH6h6GLXW7K-YBaDOF3G0XAmeUuXAuWLQmjdnD6WGQeZdDeEue1fsO65GAihj_Xug_lDP_8OJWrkkTsBvzBmsRjirAb1-9pQsCa0Mii8-oUk0EKwKW_KbRcEp6w0lzR9Q0daZFnTMHqWvqWAFaJIfnewuOrFX-98XwcsFjmPUmQdYMcY0js-yUFq6ErWg4mQtraSekTTmZK3tNIoxVuo7qBpKE9K-N-4R32fDy8y3sWKjUmbc2x5hzAk_S20lszYdefqufB_7zm8wOvcvbBS5UeETNgR4MnLM1-BqOJZ2L8Pyyn9e15C1Qg5b6aFGSWHML3DQ9XeO74g",
  "payload": ""
}
2025-02-06 13:51:11,994:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471886118165 HTTP/11" 200 539
2025-02-06 13:51:11,995:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:11 GMT
Content-Type: application/json
Content-Length: 539
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: hXNDuA8ib3piohYc4V8bMk7HVmWxCxaGgwU-Lk6L2Iq4TYD_haQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T12:51:09Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471886118165/PhY_JQ",
      "status": "valid",
      "validated": "2025-02-06T12:51:07Z",
      "token": "eFCgO6_FV4y01oA_d28qzUcmH8-bwOfCOQ6WjFva58k",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2025-02-06 13:51:11,995:DEBUG:acme.client:Storing nonce: hXNDuA8ib3piohYc4V8bMk7HVmWxCxaGgwU-Lk6L2Iq4TYD_haQ
2025-02-06 13:51:11,995:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-02-06 13:51:11,995:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-02-06 13:51:11,995:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkZ6TBMcsGQ/y\nig9eEnFJ/8YbpTTEQJrKO8lqMo+y4fO57qG8FKnBy2/kCrEc7wiBkWFiUAO1cIbz\ncq0ZKTt306BGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5z\nYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBF\nAiEAjZM8hbaw4KH8d1uPygfqXC2MnloP/AV9RPAYLmQAG6YCIEyqRfNAWh9VIM8h\nlc0I4LPcTBPe5H6+E/J0d3BCRssg\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2025-02-06 13:51:11,997:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2025-02-06 13:52:41.996998
2025-02-06 13:51:12,000:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkZ6TBMcsGQ_yig9eEnFJ_8YbpTTEQJrKO8lqMo-y4fO57qG8FKnBy2_kCrEc7wiBkWFiUAO1cIbzcq0ZKTt306BGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5zYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBFAiEAjZM8hbaw4KH8d1uPygfqXC2MnloP_AV9RPAYLmQAG6YCIEyqRfNAWh9VIM8hlc0I4LPcTBPe5H6-E_J0d3BCRssg"\n}'
2025-02-06 13:51:12,002:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351741742235:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJoWE5EdUE4aWIzcGlvaFljNFY4Yk1rN0hWbVd4Q3hhR2d3VS1MazZMMklxNFRZRF9oYVEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNDM2MDgyNjcvMzUxNzQxNzQyMjM1In0",
  "signature": "cLrsEYoCFfOJxXty5uKHldxzIRFyvyP7RjSWUAZzwxiWBFZnBlRRTfp0I3K4tZLgGYxE9nXC4ikQPS1wnIu4Cu8wGmYdArxex5GKwERpp9AImJN1_RBe35kaCOwQxoE8VlWX2WkqISc8e-9O0gTSoe0fmMAVsbQ-kRgXUARQUB3mSbrnTpsv7KSRXxbepa_vGiLKmjtgcn0h8D10I3uzv49dPYcukWwO_FgRdt9-_HmRYv38IA3qsA9YmhoR81Q-MkAqZKUoiirQbVUnX750KAtdFajMZXjV8zfzu305UNBt11jmE_ZE6vLOlYFXTsO29X3mqL1Hd8e2hhXcKduUVA",
  "payload": "ewogICJjc3IiOiAiTUlJQkFUQ0JxQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRWtaNlRCTWNzR1FfeWlnOWVFbkZKXzhZYnBUVEVRSnJLTzhscU1vLXk0Zk81N3FHOEZLbkJ5Ml9rQ3JFYzd3aUJrV0ZpVUFPMWNJYnpjcTBaS1R0MzA2QkdNRVFHQ1NxR1NJYjNEUUVKRGpFM01EVXdNd1lEVlIwUkJDd3dLb0lVS2k1c2IyTmhiQzV6WVhkaGNIQXVZMnh2ZFdTQ0VteHZZMkZzTG5OaGQyRndjQzVqYkc5MVpEQUtCZ2dxaGtqT1BRUURBZ05JQURCRkFpRUFqWk04aGJhdzRLSDhkMXVQeWdmcVhDMk1ubG9QX0FWOVJQQVlMbVFBRzZZQ0lFeXFSZk5BV2g5VklNOGhsYzBJNExQY1RCUGU1SDYtRV9KMGQzQkNSc3NnIgp9"
}
2025-02-06 13:51:13,349:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/351741742235 HTTP/11" ************-02-06 13:51:13,349:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:13 GMT
Content-Type: application/json
Content-Length: 605
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351741742235
Replay-Nonce: aTttXYyyXZvj0CZtElnD_Bhkn1vHZ9LVoPmhJ44xdkPLbfXiRRs
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-02-13T12:11:20Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351741742235",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/032e802da2ffed6807e1dcf8a2e072531a75"
}
2025-02-06 13:51:13,349:DEBUG:acme.client:Storing nonce: aTttXYyyXZvj0CZtElnD_Bhkn1vHZ9LVoPmhJ44xdkPLbfXiRRs
2025-02-06 13:51:14,352:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:14,354:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/351741742235:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eVhadmowQ1p0RWxuRF9CaGtuMXZIWjlMVm9QbWhKNDR4ZGtQTGJmWGlSUnMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNDM2MDgyNjcvMzUxNzQxNzQyMjM1In0",
  "signature": "USOjgMkfschYNxbxcRUig2F2JtZrPwLAQb-3TTZoQ2aQ40v1S-a-A3snipQa6n4sXKYYxs4bgsBTfq2sJqKRkT2Z9sZE5E4aH6xA7vG3Y8E5ADFqZOht18ZFBC-9CBKBRZJGmsMZu_3VuK9gGgbUggfMwpLVyRtBtSnpWTo4h523yaSwECdKTWIb5xNvd7Zy4DiIxgvP7G20PP0cSlQY5sn9vrUBwKbIoupt8l0Oifn1tLTltZZdC6KE3MPUt6TB-USkqz_rU-5GbMaibDp2NgSYLj7Ph7Ddb-baD_DVmbaUz-6iZs-1kh0LWI20ySwiZmmNzJBxqcb-tsBZizOpqA",
  "payload": ""
}
2025-02-06 13:51:14,532:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/351741742235 HTTP/11" ************-02-06 13:51:14,533:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:14 GMT
Content-Type: application/json
Content-Length: 605
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyc-4U4yZvmIgQwk70xg_sozoxDvbc9_wHcO8PHWcm-uk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-02-13T12:11:20Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471886118165"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351741742235",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/032e802da2ffed6807e1dcf8a2e072531a75"
}
2025-02-06 13:51:14,533:DEBUG:acme.client:Storing nonce: aTttXYyyc-4U4yZvmIgQwk70xg_sozoxDvbc9_wHcO8PHWcm-uk
2025-02-06 13:51:14,533:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:14,535:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/032e802da2ffed6807e1dcf8a2e072531a75:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJhVHR0WFl5eWMtNFU0eVp2bUlnUXdrNzB4Z19zb3pveER2YmM5X3dIY084UEhXY20tdWsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDMyZTgwMmRhMmZmZWQ2ODA3ZTFkY2Y4YTJlMDcyNTMxYTc1In0",
  "signature": "IAq3tdWrKzoG4sWY3olEFdikXROIvdNykqcxvCH5z-keBMcWclMi2Ps-0k7IugoAKoOMWuNsM0f0JR1YtiQl9juOrfWhckNRpxXK-1b5wJLr5dD3BbF8lHV1iqcWLXdXHwMIhJyMWgLUU77TnddLjID5wPBlW1nI8w20G4zxrbtFOPBs4UUSGKiCE2pzcEJlSUBcjYgAZxCmoUcYqmZPco_f4hwXXoXlVvbxLSVedtfSdWED6WCZpvQcXCfOSKMIJvWwEVlb1Cs8bTiFGl7vFyr06lXI4BlRaJYRvZdve_nPyxswHV6KUYpuO8_fQkeRJAlfiAftTEDl7w7PXyqnJg",
  "payload": ""
}
2025-02-06 13:51:14,703:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/032e802da2ffed6807e1dcf8a2e072531a75 HTTP/11" 200 2881
2025-02-06 13:51:14,703:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:14 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2881
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/032e802da2ffed6807e1dcf8a2e072531a75/1>;rel="alternate"
Replay-Nonce: aTttXYyyenUdWuo9l5f2WXAufufKHaBGhSu4RrG5b2Z4b6zLapk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDnDCCAyKgAwIBAgISAy6ALaL/7WgH4dz4ouByUxp1MAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NTAeFw0yNTAyMDYxMTUyNDJaFw0yNTA1MDcxMTUyNDFaMB8xHTAbBgNVBAMMFCou
bG9jYWwuc2F3YXBwLmNsb3VkMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkZ6T
BMcsGQ/yig9eEnFJ/8YbpTTEQJrKO8lqMo+y4fO57qG8FKnBy2/kCrEc7wiBkWFi
UAO1cIbzcq0ZKTt306OCAikwggIlMA4GA1UdDwEB/wQEAwIHgDAdBgNVHSUEFjAU
BggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUa2vn
AGs6iPYSfyXov5/D3T7KlOAwHwYDVR0jBBgwFoAUnytfzzwhT50Et+0rLMTGcIvS
1w0wVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vZTUuby5sZW5j
ci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9lNS5pLmxlbmNyLm9yZy8wMwYDVR0R
BCwwKoIUKi5sb2NhbC5zYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAT
BgNVHSAEDDAKMAgGBmeBDAECATCCAQMGCisGAQQB1nkCBAIEgfQEgfEA7wB1AMz7
D2qFcQll/pWbU87psnwi6YVcDZeNtql+VMD+TA2wAAABlNtQeuMAAAQDAEYwRAIg
HungzZu2veO8mFp07QKNDJxXy8ItUL+XprmZXPLEIVkCIAPZK0iTF8ZTip+7Q9e+
FKJ10OfuSUTvhl2IoY2pH90fAHYAE0rfGrWYQgl4DG/vTHqRpBa3I0nOWFdq367a
p8Kr4CIAAAGU21B8PwAABAMARzBFAiByU6QpG9Tn3ukx5dT5euAukzKNrrhuNUKv
j/Jbjp4sMAIhAIyC8MQtbBwaqMxxUezn+pl/i/EwPeTbpcVen5hbv+PAMAoGCCqG
SM49BAMDA2gAMGUCMB5eZ0wPGbZyftXb75XhnkqZM1BPk2PL9gIULuTfCCqTSoxU
dhc+shibLkjn2aietAIxAK3rt3qVnuqWPDowqzqwtOtGZ1aY1rTu22t1vHzr6vVK
IJ5MMUepEZJAhlup2m3TCg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRAIOPbGPOsTmMYgZigxXJ/d4wDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTUwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNCzqK
a2GOtu/cX1jnxkJFVKtj9mZhSAouWXW0gQI3ULc/FnncmOyhKJdyIBwsz9V8UiBO
VHhbhBRrwJCuhezAUUE8Wod/Bk3U/mDR+mwt4X2VEIiiCFQPmRpM5uoKrNijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSfK1/PPCFPnQS37SssxMZw
i9LXDTAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAH3KdNEVCQdqk0LKyuNImTKdRJY1C
2uw2SJajuhqkyGPY8C+zzsufZ+mgnhnq1A2KVQOSykOEnUbx1cy637rBAihx97r+
bcwbZM6sTDIaEriR/PLk6LKs9Be0uoVxgOKDcpG9svD33J+G9Lcfv1K9luDmSTgG
6XNFIN5vfI5gs/lMPyojEMdIzK9blcl2/1vKxO8WGCcjvsQ1nJ/Pwt8LQZBfOFyV
XP8ubAp/au3dc4EKWG9MO5zcx1qT9+NXRGdVWxGvmBFRAajciMfXME1ZuGmk3/GO
koAM7ZkjZmleyokP1LGzmfJcUd9s7eeu1/9/eg5XlXd/55GtYjAM+C4DG5i7eaNq
cm2F+yxYIPt6cbbtYVNJCGfHWqHEQ4FYStUyFnv8sjyqU8ypgZaNJ9aVcWSICLOI
E1/Qv/7oKsnZCWJ926wU6RqG1OYPGOi1zuABhLw61cuPVDT28nQS/e6z95cJXq0e
K1BcaJ6fJZsmbjRgD5p3mvEf5vdQM7MCEvU0tHbsx2I5mHHJoABHb8KVBgWp/lcX
GWiWaeOyB7RP+OfDtvi2OsapxXiV7vNVs7fMlrRjY1joKaqmmycnBvAq14AEbtyL
sVfOS66B8apkeFX2NY4XPEYV4ZSCe8VHPrdrERk2wILG3T/EGmSIkCYVUMSnjmJd
VQD9F6Na/+zmXCc=
-----END CERTIFICATE-----

2025-02-06 13:51:14,703:DEBUG:acme.client:Storing nonce: aTttXYyyenUdWuo9l5f2WXAufufKHaBGhSu4RrG5b2Z4b6zLapk
2025-02-06 13:51:14,711:DEBUG:certbot._internal.storage:Writing new private key to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/privkey2.pem.
2025-02-06 13:51:14,712:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/cert2.pem.
2025-02-06 13:51:14,712:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/chain2.pem.
2025-02-06 13:51:14,713:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/fullchain2.pem.
2025-02-06 13:51:14,722:DEBUG:certbot.configuration:Var account=b1747b4e4c9ff2f5413c3eadbb4d1958 (set by user).
2025-02-06 13:51:14,722:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2025-02-06 13:51:14,722:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-02-06 13:51:14,722:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-02-06 13:51:14,722:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-02-06 13:51:14,723:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2025-02-06 13:51:14,723:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 13:51:14,723:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 13:51:14,723:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/local/renewal/local.sawapp.cloud.conf.new.
2025-02-06 13:51:14,727:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/privkey.pem
This certificate expires on 2025-05-07.
These files will be updated when the certificate renews.
2025-02-06 13:51:14,727:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2025-02-06 13:51:14,727:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2025-02-06 13:51:14,727:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
