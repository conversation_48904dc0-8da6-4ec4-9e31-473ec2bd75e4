2024-11-07 14:42:33,839:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-07 14:42:33,839:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-07 14:42:33,839:DEBUG:certbot._internal.main:Arguments: ['--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2024-11-07 14:42:33,839:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-07 14:42:33,850:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-07 14:42:33,851:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 14:42:33,851:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x106ffe510>
Prep: True
2024-11-07 14:42:33,851:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x106ffe510> and installer None
2024-11-07 14:42:33,851:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-07 14:42:33,885:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-07 14:42:33,892:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-07 14:42:33,895:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-07 14:42:34,379:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-07 14:42:34,380:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:42:34 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "dhKZD30M-lU": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-07 14:42:34,381:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for *.local.sawapp.cloud and local.sawapp.cloud
2024-11-07 14:42:34,387:DEBUG:acme.client:Requesting fresh nonce
2024-11-07 14:42:34,388:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-07 14:42:34,534:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-07 14:42:34,535:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:42:34 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yiS4Q9ueA8I2r-oaCTCvwfEX35rRk9H5OIPEk-44WrREx8eMk6k
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-07 14:42:34,536:DEBUG:acme.client:Storing nonce: yiS4Q9ueA8I2r-oaCTCvwfEX35rRk9H5OIPEk-44WrREx8eMk6k
2024-11-07 14:42:34,536:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2024-11-07 14:42:34,542:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "okdEqO6LKlKk0-_KRRQd6DVu74FmBO0l9pHKzXaV7NY4PbIJsn8RJSTSte_DSq29yH00Q9NabavfA0CfI-r75mAWYrsBSc6ur2rHDG09ZwqzTpQdoRT9nFWjQGQq7GAhUerclzeomEXVlqg5m0XhCgp873-M1obDbiwOeJ2SYzD3CHCqAIIm1jMVHkhrECSYujSgmaM3kb_MBKWNn29hVAdH17D_2AJAlcpEbb1iUoDjwzY5GoYnnIeJZS8WEVmnGKWyccDFbMeLyYsVR67teC9oq359hi5qQ4CBGaZbcr1CiJObGuuzib6jISX-9fchPXBlp4oytdaLu0td5aF0Ow",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2024-11-07 14:42:34,842:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 487
2024-11-07 14:42:34,842:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 07 Nov 2024 13:42:34 GMT
Content-Type: application/json
Content-Length: 487
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/320866468607
Replay-Nonce: yiS4Q9ueYyJig33u4VFp7ulAF940K3BOs3RLIKnSp-jjx1978sI
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-14T13:42:34Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975427",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320866468607"
}
2024-11-07 14:42:34,842:DEBUG:acme.client:Storing nonce: yiS4Q9ueYyJig33u4VFp7ulAF940K3BOs3RLIKnSp-jjx1978sI
2024-11-07 14:42:34,843:DEBUG:acme.client:JWS payload:
b''
2024-11-07 14:42:34,845:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975427:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5aVM0UTl1ZVl5SmlnMzN1NFZGcDd1bEFGOTQwSzNCT3MzUkxJS25TcC1qangxOTc4c0kiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQyNyJ9",
  "signature": "ECUMAAOBXHaaFYmc3t5Cf5zcZyJfhqhozyeow3LaZxb10DSUdHPof6jT7BAGF4WJ6xQDfgICrQCUcROG8COhJ_Kvb3-Ij0TxVf0phSaGfKFfG-pa8WXg3LZgocFF-s0YXUwE3yYq2WCH3roMAIdu9DaUwx2TXaKQmLI4dX7W9IMQBtRhpiW223_18km5Md7uc3w2P4tZg1Oyc0B4zsCO9LLBEyu6p5o5r8LKJtGxFM3nnuoRGJBmkAbdkcH_pHLWkK67R4x13SDaxmxVtSjLfkxPswGniX82Gq77XppJH4dFB_i3p0ZHgfKSFKn6tR4qCr9PHqC78Qm7kAKgmmlDhA",
  "payload": ""
}
2024-11-07 14:42:35,009:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975427 HTTP/11" 200 392
2024-11-07 14:42:35,009:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:42:34 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yiS4Q9uec45kCqrn5WfyzEXAtazmMyjcdSrFqc211bk2wZaQ7Js
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T13:42:34Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ",
      "status": "pending",
      "token": "3_5l1r47y7w6MibXkGZnAtGQ0Wco0Usa3RhGtg-g54s"
    }
  ],
  "wildcard": true
}
2024-11-07 14:42:35,009:DEBUG:acme.client:Storing nonce: yiS4Q9uec45kCqrn5WfyzEXAtazmMyjcdSrFqc211bk2wZaQ7Js
2024-11-07 14:42:35,010:DEBUG:acme.client:JWS payload:
b''
2024-11-07 14:42:35,012:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5aVM0UTl1ZWM0NWtDcXJuNVdmeXpFWEF0YXptTXlqY2RTckZxYzIxMWJrMndaYVE3SnMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQzNyJ9",
  "signature": "D69xd-352iKGsm9dzWrA4n908zfD3jBW7jlbybfeGPfNxtwEvdDQd6nIn6wlgm5PQzsgaOk8-Czr65QiDnlsDTCUE1d_yJNw2H0C7Jvf0_QHKmO3r4V5ga7TgEgiE1Whpp8E4biXijjrLHBfUiNMee_nMBM9UN8dVstUPEj_HOA2xoIemz7lFj2dO7n-MHgNZvBT3EFCUKdxT-MpSW9UphWg_4R-P9tkeey7DS9uYYBZ8zvR_W4w1XlianwqJBLIUhChGQrCYlRk4EgIidf3mzV52PFkELq18nUEaE129FlochdY6UZ1pcHzK2gvlQz69KVsb4gpCv8XuumQvolvzQ",
  "payload": ""
}
2024-11-07 14:42:35,178:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975437 HTTP/11" 200 802
2024-11-07 14:42:35,178:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 13:42:35 GMT
Content-Type: application/json
Content-Length: 802
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6q3b_kIniXeb4WPKeYgVhhY_EhqBUyWcPCvbN7YdPWn6E
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T13:42:34Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/DWQSnA",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/cByfDA",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    }
  ]
}
2024-11-07 14:42:35,178:DEBUG:acme.client:Storing nonce: g1njj_6q3b_kIniXeb4WPKeYgVhhY_EhqBUyWcPCvbN7YdPWn6E
2024-11-07 14:42:35,179:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-07 14:42:35,179:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2024-11-07 14:42:35,179:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2024-11-07 14:42:35,181:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

WkvxSu_S6Jk_EGrcQZ77xVWRaJ-xoo1arGJ4S-xi6pQ

2024-11-07 15:17:58,107:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

N2WH3qF8RyFjetGGANd5t98C5MPMK3-B9RcOWzbHtdA

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-07 15:52:11,642:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-07 15:52:11,645:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ:
{
  "protected": "******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "tox_EUH7WqL22zf8-Q8hxQULwLJbPAPOrTNbjcMS7yaLVTnHgx14wN50t6VNYNXwmOdRG9uD6FY-WhVoTWHuhNu-p9mOwP66rwU_kKIFGb55c60j74BnIkdFwO2_1wYYZ1curJCPDq_g3R4n1wfcfe2DP6Uamm1MHdAgxpHGJGw-mpyE870ZiqEYwOe4p6n7aKteQaGUj44VLvKFDuqP0nZvpCGM79-7tDAZEWR2wRE-_lsI3d3CQe-6W6oluphpWsqadnokRbQnurgvYTLs-q3tfHntN3AsbnYKOrKjIvg0Gx2xIqYsKyuJ1-LuQRDB2Dh7FSr8Wja9NVXWLgNz_Q",
  "payload": "e30"
}
2024-11-07 15:52:11,648:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2024-11-07 15:52:12,106:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/426847975427/zlvyMQ HTTP/11" 400 177
2024-11-07 15:52:12,107:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 07 Nov 2024 14:52:12 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6qcTC0z-OEijxA8JnmfaB9oqtwfXNQOlBDHINqk8uGgWQ

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"g1njj_6q3b_kIniXeb4WPKeYgVhhY_EhqBUyWcPCvbN7YdPWn6E\"",
  "status": 400
}
2024-11-07 15:52:12,108:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "g1njj_6q3b_kIniXeb4WPKeYgVhhY_EhqBUyWcPCvbN7YdPWn6E"
2024-11-07 15:52:12,108:DEBUG:acme.client:Requesting fresh nonce
2024-11-07 15:52:12,108:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-07 15:52:12,251:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-07 15:52:12,252:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:12 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6qX_N7vEA7ilV9bJohZZyMrRJ62pFLTtc3hA0AUtdhQi0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-07 15:52:12,252:DEBUG:acme.client:Storing nonce: g1njj_6qX_N7vEA7ilV9bJohZZyMrRJ62pFLTtc3hA0AUtdhQi0
2024-11-07 15:52:12,252:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-07 15:52:12,257:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJnMW5qal82cVhfTjd2RUE3aWxWOWJKb2haWnlNclJKNjJwRkxUdGMzaEEwQVV0ZGhRaTAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQyNjg0Nzk3NTQyNy96bHZ5TVEifQ",
  "signature": "NT4gfnFY1OrBu88nmNAcy26ATIQju2PPF-hk3P4z7jCG2t2NOgB1C__c0r5eJA_wUt8zzbIy0EIegJ8P3P7y4XxUBDrCp-hIzNZP_1R9vx_xCpngoHq7_mMcGAB_AwbUdmFu-c4LaNF1CGJN-K3weToI2ji4_aiv6ZivFivheofw-FoFXDP1sh6kkM52PAuaNM-qOXP-fmNfcYHK5CyHC48m3FJo-c4Hv9GPTWCw6IjaU0p7aWde7mVYFYE68N6BH-cnGqF0Dx-fAzSHoPLXkCzYagCZoi7_F58Spq2AAKd8oE6Lv1LvXKpxFq3e_-skrt-YBHM1ZC-X6xKs3r3hUA",
  "payload": "e30"
}
2024-11-07 15:52:12,413:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/426847975427/zlvyMQ HTTP/11" 200 186
2024-11-07 15:52:12,414:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:12 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975427>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ
Replay-Nonce: g1njj_6qbyG5D17Y7HEm0mxX3KVdywJ4z52TqVtZO8S8EKT0Slw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ",
  "status": "pending",
  "token": "3_5l1r47y7w6MibXkGZnAtGQ0Wco0Usa3RhGtg-g54s"
}
2024-11-07 15:52:12,414:DEBUG:acme.client:Storing nonce: g1njj_6qbyG5D17Y7HEm0mxX3KVdywJ4z52TqVtZO8S8EKT0Slw
2024-11-07 15:52:12,415:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-07 15:52:12,420:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJnMW5qal82cWJ5RzVEMTdZN0hFbTBteFgzS1ZkeXdKNHo1MlRxVnRaTzhTOEVLVDBTbHciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQyNjg0Nzk3NTQzNy9abWV0R2cifQ",
  "signature": "f-C7PvZG0fmOLJmFALXhMiReGnKdeo7B78h1Gz0keLouwysyM9FWCIQqtwi3QPDCZ3t72VTmdUQih0u0emMQcLe20Y5JvqXQK6NT0Lpx5ePgj5w9L6BPOohzcQlRN7uoyb-v9EZTd77WTjK4ycS7axlFWKgAs3Zutp1qQzTQbL1EK9omRBed_FPvRFYnrxFUwoSAMI8l0YmBokEbzUWVs8D8yMtW2s3hpOeFIZccoqCDveX2EV2UMX1maBXQwa3HacQ2tjWwmhfn6yqMQmn46tMgM2HfMdcVUT-skX2PTdlp9QZHp7Ut0OUMbPsfzE7f0a0Wve2Zybh_usg3iuVrfQ",
  "payload": "e30"
}
2024-11-07 15:52:12,580:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/426847975437/ZmetGg HTTP/11" 200 186
2024-11-07 15:52:12,581:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:12 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg
Replay-Nonce: yiS4Q9ueRU-o_B8jQlXWrqn7K-2yHxMO_7O2BYL4KcIOt2kJSsw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
  "status": "pending",
  "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
}
2024-11-07 15:52:12,581:DEBUG:acme.client:Storing nonce: yiS4Q9ueRU-o_B8jQlXWrqn7K-2yHxMO_7O2BYL4KcIOt2kJSsw
2024-11-07 15:52:12,581:INFO:certbot._internal.auth_handler:Waiting for verification...
2024-11-07 15:52:13,587:DEBUG:acme.client:JWS payload:
b''
2024-11-07 15:52:13,592:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975427:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5aVM0UTl1ZVJVLW9fQjhqUWxYV3JxbjdLLTJ5SHhNT183TzJCWUw0S2NJT3Qya0pTc3ciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQyNyJ9",
  "signature": "VaIBsWDmBZtlVHzw6ohVJt74jIMppinmo60SD-8zDyw0oJFoupyqObWheyXBihM-ZWvDxXdzi2goyKT3yNvjm0plyM0ls_KfL7dFnOY4jfXevK0P2Ob0cpYqvQud3lcwRm3bB7N-tBzQ3Im6UDTzY0zJENrVy1mHcrYwJcxeVDacFdjG_cujLefpgTgabqpMi2E3w8sCA8U8xJhy1iW0nNY5ixvOZ0rF-v3muQNDWHCDKR9mm0DjAc-Rm3A7Ku-huhAV0Oqi7LetZ4Grgv0TzwwquZMbVQOk24-gBA2kAfjnBc3W8_4NHHN_YxoJ7tY4FAT1-X803W5bxMpb4h-cgQ",
  "payload": ""
}
2024-11-07 15:52:13,745:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975427 HTTP/11" 200 676
2024-11-07 15:52:13,746:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:13 GMT
Content-Type: application/json
Content-Length: 676
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6q8AEzo1rd-DQDsscMTKKpmHZ5te9gCQBXc3Nm9J950fw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2024-11-14T13:42:34Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975427/zlvyMQ",
      "status": "invalid",
      "validated": "2024-11-07T14:52:12Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"N2WH3qF8RyFjetGGANd5t98C5MPMK3-B9RcOWzbHtdA\" found at _acme-challenge.local.sawapp.cloud",
        "status": 403
      },
      "token": "3_5l1r47y7w6MibXkGZnAtGQ0Wco0Usa3RhGtg-g54s"
    }
  ],
  "wildcard": true
}
2024-11-07 15:52:13,746:DEBUG:acme.client:Storing nonce: g1njj_6q8AEzo1rd-DQDsscMTKKpmHZ5te9gCQBXc3Nm9J950fw
2024-11-07 15:52:13,747:DEBUG:acme.client:JWS payload:
b''
2024-11-07 15:52:13,750:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJnMW5qal82cThBRXpvMXJkLURRRHNzY01US0twbUhaNXRlOWdDUUJYYzNObTlKOTUwZnciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQzNyJ9",
  "signature": "p8cQiE4qWtS2LWBhiVkFryTpynPdrTTK12HpT4HN8ofLvyKRTHB4yC-14AaLTOcglJm0GMneVKguj9jrf95i-BdNk6frHoVVqI2vZ9vEYFLZfwoXUACZ0rvqORi-QdAgixM9T5Lx6NEl_HWBUVL1GJxiMNlv0iGx2ApVouOwEeB-W766J5BRmycJti8Cud-ACUPJMErz8P4vGufhsTvMZAhgn8TVSUy2XiVX_CiHnDfxMzgb5yj42pJopwievstlmeOo1bCFNf2tn8YZGsci6pcOcs4tYe3mVMTjMmAToWWzvW9qwiCBD6nq4kHAaqNUqTJXuadgpfLYDfwfigj55g",
  "payload": ""
}
2024-11-07 15:52:13,901:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975437 HTTP/11" 200 802
2024-11-07 15:52:13,901:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:13 GMT
Content-Type: application/json
Content-Length: 802
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6qGJxRAQP5n2dhc72Txx_i9w8ZsGV9hUAUK6ko7wJpjMc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T13:42:34Z",
  "challenges": [
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/DWQSnA",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/cByfDA",
      "status": "pending",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA"
    }
  ]
}
2024-11-07 15:52:13,901:DEBUG:acme.client:Storing nonce: g1njj_6qGJxRAQP5n2dhc72Txx_i9w8ZsGV9hUAUK6ko7wJpjMc
2024-11-07 15:52:13,902:INFO:certbot._internal.auth_handler:Challenge failed for domain local.sawapp.cloud
2024-11-07 15:52:16,907:DEBUG:acme.client:JWS payload:
b''
2024-11-07 15:52:16,913:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJnMW5qal82cUdKeFJBUVA1bjJkaGM3MlR4eF9pOXc4WnNHVjloVUFVSzZrbzd3SnBqTWMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQzNyJ9",
  "signature": "dLse3L7zXWC1kbYNdmyfbkv69aagREfu18RBqSK-Ef7Gp-EmJ-qbVdKeTwzIMlXs6rVNmPdjdh7y7mO0TRl-6uaveDizdGZBi-uS9BkcQ-7L1p5mEACvvoH6dAzVI_SZcDVoXTLFc18zaWyK5tq2IPgkzV2Lk0qg0vY5vl2gx9Sv844kzzvs4abmOFpS6vwyvAWj4K3zuGhHQ4AOxzqDs4YA3UgzCq0sHjOQ45-9t-Clwj6xH3hqXrsZeMbLe2dEFzvLLNQpxiQrFvnPKkjXNO8Z0LFFOB8h7weyPEzXJoz0eJ6kLX_7iLbrSzZRP3ZEuc9QqOGDdLWpkY_9tCATsA",
  "payload": ""
}
2024-11-07 15:52:17,066:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975437 HTTP/11" 200 511
2024-11-07 15:52:17,067:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:17 GMT
Content-Type: application/json
Content-Length: 511
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: g1njj_6q3d9gcmgbwON3t3vhrJdVcD81ep4YuuhU8AlMxToF0ro
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-07T14:52:14Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
      "status": "valid",
      "validated": "2024-11-07T14:52:12Z",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-07 15:52:17,067:DEBUG:acme.client:Storing nonce: g1njj_6q3d9gcmgbwON3t3vhrJdVcD81ep4YuuhU8AlMxToF0ro
2024-11-07 15:52:17,068:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2024-11-07 15:52:17,068:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: local.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "N2WH3qF8RyFjetGGANd5t98C5MPMK3-B9RcOWzbHtdA" found at _acme-challenge.local.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2024-11-07 15:52:17,073:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2024-11-07 15:52:17,073:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-07 15:52:17,073:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-07 15:52:17,074:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1600, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 143, in _get_and_save_cert
    lineage = le_client.obtain_and_enroll_certificate(domains, certname)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 517, in obtain_and_enroll_certificate
    cert, chain, key, _ = self.obtain_certificate(domains)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 428, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 496, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2024-11-07 15:52:17,079:ERROR:certbot._internal.log:Some challenges have failed.
