2024-11-07 14:18:52,244:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-07 14:18:52,244:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-07 14:18:52,244:DEBUG:certbot._internal.main:Arguments: ['-v', '--manual', '-d', 'test.local.sawapp.cloud', '--agree-tos', '--preferred-challenges=dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2024-11-07 14:18:52,244:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-07 14:18:52,256:DEBUG:certbot._internal.log:Root logging level set at 20
2024-11-07 14:18:52,256:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 14:18:52,256:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x105072510>
Prep: True
2024-11-07 14:18:52,256:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x105072510> and installer None
2024-11-07 14:18:52,256:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-07 14:20:54,153:ERROR:certbot.util:Invalid email address: [Ac.
2024-11-07 14:20:55,172:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1582, in certonly
    le_client = _init_le_client(config, auth, installer)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 833, in _init_le_client
    acc, acme = _determine_account(config)
                ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 739, in _determine_account
    config.email = display_ops.get_email()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/display/ops.py", line 63, in get_email
    raise errors.Error(
        "An e-mail address or "
        "--register-unsafely-without-email must be provided.")
certbot.errors.Error: An e-mail address or --register-unsafely-without-email must be provided.
2024-11-07 14:20:55,181:ERROR:certbot._internal.log:An e-mail address or --register-unsafely-without-email must be provided.
