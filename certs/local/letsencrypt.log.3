2025-02-06 11:37:11,929:DEBUG:certbot._internal.main:certbot version: 2.11.0
2025-02-06 11:37:11,929:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-02-06 11:37:11,929:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2025-02-06 11:37:11,929:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-02-06 11:37:11,942:DEBUG:certbot._internal.log:Root logging level set at 30
2025-02-06 11:37:11,942:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 11:37:11,942:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x102b563c0>
Prep: True
2025-02-06 11:37:11,943:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x102b563c0> and installer None
2025-02-06 11:37:11,943:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-02-06 11:37:11,977:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-02-06 11:37:11,983:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-02-06 11:37:11,987:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-02-06 11:37:12,453:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 828
2025-02-06 11:37:12,454:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 10:37:12 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "4V-n29LFi_g": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "The same profile you're accustomed to"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-02-06 11:37:12,468:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-02-05 14:39:40 UTC.
2025-02-06 11:37:12,468:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-02-06 11:37:12,468:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.local.sawapp.cloud and local.sawapp.cloud
2025-02-06 11:37:12,470:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 11:37:12,470:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 11:37:12,619:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 11:37:12,620:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 10:37:12 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XgH83rkAXL7AZ-zZLe-sKk33CPNEjZ4tEJMo0gAKOPlU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 11:37:12,620:DEBUG:acme.client:Storing nonce: TUC11O6XgH83rkAXL7AZ-zZLe-sKk33CPNEjZ4tEJMo0gAKOPlU
2025-02-06 11:37:12,620:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2025-02-06 11:37:12,625:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "On9U77T7ODsEZTTMW_LmlqfB1pgSQ93Fq_WwUFaSAy5jsLx3ikU6BrBcXfjuVPglyP78PyIIL4pcOVNM1wBWjNk0KTUZLSSUYpDoK649umt1fBEVNNLfn8eet7ssMf4nUfhtPTYKgPGtzIBjpufWJBy4Bfx1ql9yrlNtm3sBCEcGTAjQkFx4AEfdkKvKWbUN20buRjpzLixevK20SeIjib_mhQ8Gae5ZK9Z4tL8xiGaNs368u5bCvrgw1zYtBsLSiJojlWxixUExhuX1XSrA0iQzhlnTHlRpbXmAMM8DOl2tpRVWvc4qJz278RKKiphMjXHOznuTPisPD9TCssJM8Q",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2025-02-06 11:37:12,967:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 503
2025-02-06 11:37:12,968:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 06 Feb 2025 10:37:12 GMT
Content-Type: application/json
Content-Length: 503
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351720052755
Replay-Nonce: TUC11O6Xm3ci_U0reR8nYW3gKa-kR2t5YLBUGni6Tqo-IaQTGEM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-02-13T10:37:12Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345085",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351720052755"
}
2025-02-06 11:37:12,968:DEBUG:acme.client:Storing nonce: TUC11O6Xm3ci_U0reR8nYW3gKa-kR2t5YLBUGni6Tqo-IaQTGEM
2025-02-06 11:37:12,969:DEBUG:acme.client:JWS payload:
b''
2025-02-06 11:37:12,972:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345085:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJUVUMxMU82WG0zY2lfVTByZVI4bllXM2dLYS1rUjJ0NVlMQlVHbmk2VHFvLUlhUVRHRU0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODUzMzQ1MDg1In0",
  "signature": "GwwywJhlxfT4wPtb3gyzHgyHh4KBSxcdHbrePN-bXeBHLklcQH2w2ffSXBDcSoIdv5P2_sy6FgJmRptKGfKA3_jx1CXanMXzsQA0k-dQ_HNzHd6M_3POcfZ1UfxnUC-5vgWB6dXopP1e8i3LRQRfnr6uYa7_YXndFbNX1mS2gheKv00Y4KZ9H5mYF1oaSzSAB9zkj_qq-TxiOUrRPbF7e7MvE4tZRTvP6WqFuw3iavYa2Tb_mXTnYPESO8OBN38q6ALQpkIe7ovwNrYXY4rYoYtpM97v2tefV0OWobd8ZHoQlYWTbigwonBTpOHLQPfOJVxyev3wH-0GQDTXcaSZ8Q",
  "payload": ""
}
2025-02-06 11:37:13,124:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345085 HTTP/11" 200 400
2025-02-06 11:37:13,125:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 10:37:13 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XTTQ6NVnxCTWGybHjRP-Tgaop6KLgHDUZZ63dzYQmYW0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T10:37:12Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA",
      "status": "pending",
      "token": "leXQlAWnP67Ko0z-8I-gGRLudEszPa3Je4auKzchWy4"
    }
  ],
  "wildcard": true
}
2025-02-06 11:37:13,125:DEBUG:acme.client:Storing nonce: TUC11O6XTTQ6NVnxCTWGybHjRP-Tgaop6KLgHDUZZ63dzYQmYW0
2025-02-06 11:37:13,125:DEBUG:acme.client:JWS payload:
b''
2025-02-06 11:37:13,128:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJUVUMxMU82WFRUUTZOVm54Q1RXR3liSGpSUC1UZ2FvcDZLTGdIRFVaWjYzZHpZUW1ZVzAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODUzMzQ1MDk1In0",
  "signature": "ZwFQmEiXzhk56-JA_rnDcxtqd8MOreGn33QBUcLSoS9rBDBnYDy21kl-6iv49cOkka4IlIpQL1Bi5Kd3VKyB5BQK5jccrAXXoboCjNLnd3rmn-vByaSqW84IDxTBvr1LIIKxvZkxtByKGC-0vzNrDPdf6UaYfF4jG-pgwXE_6eZzlHTIc89Bg-i4lPOybqa1QaGv-PPuizb70WqSPkhCXFCQijscktuVjIxnFgilLrzKcHMjRkDwZTqGhYDNOQCBps1gifRlcYrGQJNKmX_B6q5FXCo_ZsGb3c5WdTtoq1Mpu2WHriUJ23duaky8E2DD4Ns76E4VuZ-DjWRJfcN2Qg",
  "payload": ""
}
2025-02-06 11:37:13,281:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345095 HTTP/11" ************-02-06 11:37:13,282:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 10:37:13 GMT
Content-Type: application/json
Content-Length: 826
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57PP92pLlC67_u3olWrZH3EaL5m2eFUc_68ibTpxyQfptY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T10:37:12Z",
  "challenges": [
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/aN3PIw",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/8AFSdg",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    }
  ]
}
2025-02-06 11:37:13,282:DEBUG:acme.client:Storing nonce: UvlUZ57PP92pLlC67_u3olWrZH3EaL5m2eFUc_68ibTpxyQfptY
2025-02-06 11:37:13,283:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-02-06 11:37:13,284:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-02-06 11:37:13,284:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-02-06 11:37:13,285:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

OUwOj9hPggr6h7CEwe0NRMm7x3C0wGc5LG7UTiShqGU

2025-02-06 12:57:36,821:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

43H9pj2YvEbhAKRHAz1lZ8fBLVMMbA-vvlNeGDuTdFE

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-02-06 13:11:05,053:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 13:11:05,059:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "LkuNCEhAK0ZImoa9LNrrK0Hu499j4eMKG10niAl64xVlE8e7ynUjejCQgId9AB_70bNhBiMGPdmIKJFWfZhD5Zwvs3-q8YSZ4zmhwhgu5uImXplMbezMw7oAYlqlyV3Dnx-Tg1bkt6ZwCyxFoozci6dbiht67Q5TX1WGomi_WCGqB64n4p8HAj0_0A27pgyfI1iypf6Z3N1Xt1pkxKsgUjRbG_n4YlEieUOSq-JpdhX7GqA1DLZtRRy8A-8OkLYghqi4hM6cVFJ62EUWbJJJ_EXLxs_DrGxmOrZp9a0CCUNjPyRXsDqT7RBtk15c8Xom1RgXowp1z06pMD1_rqDwOQ",
  "payload": "e30"
}
2025-02-06 13:11:05,068:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-02-06 13:11:05,633:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471853345085/tvL_kA HTTP/11" 400 177
2025-02-06 13:11:05,633:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 06 Feb 2025 12:11:05 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6Xyy9cRz79xkyW3foVcZTsl2W7FcGIW9KsdIt9Sv6C2mQ

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"UvlUZ57PP92pLlC67_u3olWrZH3EaL5m2eFUc_68ibTpxyQfptY\"",
  "status": 400
}
2025-02-06 13:11:05,634:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "UvlUZ57PP92pLlC67_u3olWrZH3EaL5m2eFUc_68ibTpxyQfptY"
2025-02-06 13:11:05,634:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 13:11:05,634:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 13:11:05,780:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 13:11:05,780:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:05 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57P6C54fHkZzDT8kHnZbMic2WQDpXzP6zJYlJoHiKyorYA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 13:11:05,781:DEBUG:acme.client:Storing nonce: UvlUZ57P6C54fHkZzDT8kHnZbMic2WQDpXzP6zJYlJoHiKyorYA
2025-02-06 13:11:05,781:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 13:11:05,782:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "Sw-HtW7_X_0ErzeTiQ3EcyavE1S5FARFz-E0lyXRwIfneYHLd6NtwAQARAPhvHn-AufOqXdGyFH5CzLTW_OYy01rhR_zmYcNj7GtQ_VX1SOiTcCeE-OUa4e9ur7owxrahQV4k2qsUqySTdPElmW_NlOaJA0ZLLKdTscnGi9hEOEnUksAUK5KSCYEJ2t9-axob0O4sERPGT1b3EeJk8WYfLbonKd3e-d_X8teTAN1EzmgTqyp4JHRvT0Utdd4aXo1k2L3AU8lCBf2spB8_2jJmJrQ9EJJl5P8rOS_cumY_LMpR0hzpQ_ZcC6dCX_LCwhx1c2G4NaJ1dsyT5bzxNGdBQ",
  "payload": "e30"
}
2025-02-06 13:11:05,950:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471853345085/tvL_kA HTTP/11" 200 194
2025-02-06 13:11:05,950:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:05 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345085>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA
Replay-Nonce: UvlUZ57PIVVCaX_ZzgETIPDCKmtny465VhQ0wJjuwjGOUDfQT8U
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA",
  "status": "pending",
  "token": "leXQlAWnP67Ko0z-8I-gGRLudEszPa3Je4auKzchWy4"
}
2025-02-06 13:11:05,951:DEBUG:acme.client:Storing nonce: UvlUZ57PIVVCaX_ZzgETIPDCKmtny465VhQ0wJjuwjGOUDfQT8U
2025-02-06 13:11:05,951:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 13:11:05,954:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJVdmxVWjU3UElWVkNhWF9aemdFVElQRENLbXRueTQ2NVZoUTB3Smp1d2pHT1VEZlFUOFUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNDcxODUzMzQ1MDk1L1ZuUE5SZyJ9",
  "signature": "lpLme5Ui2NssIG_lB966De-aGgImyr-H92-egi0x9nenk9A7G8Kc-ExCZhTaUd5Ew-vLCN-E-fuXNbTjumUXm7kzKzPEuM-4Y8Ub9vtVqLmCmE2Hd2l46olwQjPGHWkeuNzH_zf421MWoJzETQzUo-9_yPjfs8TE25a1HePsJucPCTae3o46_NZt-hvTXB6Rj4GBHBs0UJsYdGw9vx1O6kG4FqPEC0XepNHfecRxJu8C39i0r_Yvrb1fTzZACwd7yfvWk_UHpnkuRW4fOYUpa5KdUSCrLyGUNNdTQr0AwZ5ZmJdEaCNu5Q4vRGmr6_ZAbywCKJzLfqW_cAIT5PyT4Q",
  "payload": "e30"
}
2025-02-06 13:11:06,108:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471853345095/VnPNRg HTTP/11" 200 194
2025-02-06 13:11:06,109:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:06 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg
Replay-Nonce: UvlUZ57Pc_R1O6p5_GVULdpCw-ctlPt_n3lTVfNZidIWovumiZY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
  "status": "pending",
  "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
}
2025-02-06 13:11:06,109:DEBUG:acme.client:Storing nonce: UvlUZ57Pc_R1O6p5_GVULdpCw-ctlPt_n3lTVfNZidIWovumiZY
2025-02-06 13:11:06,109:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-02-06 13:11:07,115:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:11:07,119:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345085:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "UpycxlFDk_w_-KcE40yNVAAoYvLEdbtQvV1fqUqEqn6G5MGj2aEeryNTgTBaTvtlnU_3kZOkJjfK86iJaLw3NtTWhUwVwxGhj5PVNWGx1EqXVyIoKc3Pkug9_pxv_4b3utoYvI-exFPk8CMy-u_tGe0bFW4SZoIV3549vhPU4YllPZtrXOQ_ORnS6G9YYSYCbClMx2oa51CGCuofzbmuhKWk3SJnhGNLK-L845KfqC5VIIW_DRPpuWnH8zKt0m--S1u7PJIy3kcRWi1LbM7oXarAgt7PidGP4qSlf-JAwvvMhnol1xbp946DCAYeaPvqTk7zVfPV4TiFdw2QXYDsBw",
  "payload": ""
}
2025-02-06 13:11:07,273:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345085 HTTP/11" 200 684
2025-02-06 13:11:07,274:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:07 GMT
Content-Type: application/json
Content-Length: 684
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XkxulkKmA4WTpOB6_g25JFM5Zp9pjBhAtcuELWLCOgMA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2025-02-13T10:37:12Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345085/tvL_kA",
      "status": "invalid",
      "validated": "2025-02-06T12:11:05Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"43H9pj2YvEbhAKRHAz1lZ8fBLVMMbA-vvlNeGDuTdFE\" found at _acme-challenge.local.sawapp.cloud",
        "status": 403
      },
      "token": "leXQlAWnP67Ko0z-8I-gGRLudEszPa3Je4auKzchWy4"
    }
  ],
  "wildcard": true
}
2025-02-06 13:11:07,275:DEBUG:acme.client:Storing nonce: TUC11O6XkxulkKmA4WTpOB6_g25JFM5Zp9pjBhAtcuELWLCOgMA
2025-02-06 13:11:07,275:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:11:07,277:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "dAM7uI6zgEcke_vEjzvuq8ME9nkWYZvqccnYWVURu3mqeHrc0QNW2L6Nhs6ZM03eQxMpoLjS8PaXr2iHqYEdrVlr6GK4YTawrDCDNBYf13EPYSXoO7J-1gvuntzjkyxZeWm0lBMVBDlk_KfKWW5aHaEX4FSq8C-JaHRLD2ukUMK2-0iaLThC5O57jSPAyOzwVIVHk9eoSdKYub_e55t15rWcY6YxpOEjLeC9Ktu_aCQGUUgh9BQWluHLVHdJFVw1KDOtcHfDlTtYLklpYR3BQ9rMQjqVv0oX_giEaPiKXhrsl-FGqlIrr5CpxNrRd8DDtcHFx64aK3oxe5mJ9An3yg",
  "payload": ""
}
2025-02-06 13:11:07,429:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345095 HTTP/11" ************-02-06 13:11:07,430:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:07 GMT
Content-Type: application/json
Content-Length: 826
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57PxwlIsytL9foUzUOj55mtKGHT9P_0KR6bNQkfVL41LZY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T10:37:12Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/8AFSdg",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/aN3PIw",
      "status": "pending",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM"
    }
  ]
}
2025-02-06 13:11:07,430:DEBUG:acme.client:Storing nonce: UvlUZ57PxwlIsytL9foUzUOj55mtKGHT9P_0KR6bNQkfVL41LZY
2025-02-06 13:11:07,430:INFO:certbot._internal.auth_handler:Challenge failed for domain local.sawapp.cloud
2025-02-06 13:11:10,432:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:11:10,434:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471853345095:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJVdmxVWjU3UHh3bElzeXRMOWZvVXpVT2o1NW10S0dIVDlQXzBLUjZiTlFrZlZMNDFMWlkiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNDcxODUzMzQ1MDk1In0",
  "signature": "bUUE_SlwRmYUi6ml_s3BJBIz_XsxsEFM3INJ-TRBQ1u2g0sfCscEMoMyMaiebZPUh4nUShNhbCgXNCkJfpPwq9oWT5JyfR3_ls53FX300VzMwAzYTr8Ww1yOmlsYxikJTDsK8rZrWlgJXUVaa_9-pwAkVErYJOH3xgMmMSvNSApcm7_ZwWmpxnjD8H_lPXjUo_kLDJDgxgHCiFf0Wg7z_PsetWQlggO3KvTGO7__KXKjztvxq88O2JyJzk-o3HUzFUTIXo1BogFP90N_z4Jm_j70hBkjG3hfbLVsg1aKEUtyQ27QhMZ_lwT5hdsWEptM1t1v647I8sqmACK_VYGd3A",
  "payload": ""
}
2025-02-06 13:11:10,586:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471853345095 HTTP/11" 200 519
2025-02-06 13:11:10,587:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:11:10 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57PIqq_3uJ05h_DSnUaD92LLTVvuGxhfaqyW3jtlFq41UE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T12:11:07Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471853345095/VnPNRg",
      "status": "valid",
      "validated": "2025-02-06T12:11:06Z",
      "token": "6kJxUh0BjfV1BVkS-E0exZv4xURAGbloVMsVEdRJMNM",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 13:11:10,587:DEBUG:acme.client:Storing nonce: UvlUZ57PIqq_3uJ05h_DSnUaD92LLTVvuGxhfaqyW3jtlFq41UE
2025-02-06 13:11:10,587:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-02-06 13:11:10,589:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: local.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "43H9pj2YvEbhAKRHAz1lZ8fBLVMMbA-vvlNeGDuTdFE" found at _acme-challenge.local.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2025-02-06 13:11:10,596:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2025-02-06 13:11:10,596:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-02-06 13:11:10,596:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-02-06 13:11:10,596:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1600, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 131, in _get_and_save_cert
    renewal.renew_cert(config, domains, le_client, lineage)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/renewal.py", line 399, in renew_cert
    new_cert, new_chain, new_key, _ = le_client.obtain_certificate(domains, new_key)
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 428, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 496, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2025-02-06 13:11:10,602:ERROR:certbot._internal.log:Some challenges have failed.
