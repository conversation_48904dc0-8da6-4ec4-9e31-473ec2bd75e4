2025-05-09 13:51:56,282:DEBUG:certbot._internal.main:certbot version: 3.3.0
2025-05-09 13:51:56,282:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-05-09 13:51:56,282:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2025-05-09 13:51:56,282:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-05-09 13:51:56,296:DEBUG:certbot._internal.log:Root logging level set at 30
2025-05-09 13:51:56,297:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 13:51:56,297:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x106b99be0>
Prep: True
2025-05-09 13:51:56,297:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x106b99be0> and installer None
2025-05-09 13:51:56,297:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-05-09 13:51:56,333:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=datetime.timezone.utc), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-05-09 13:51:56,341:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-05-09 13:51:56,347:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-05-09 13:51:56,850:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/1.1" 200 1012
2025-05-09 13:51:56,851:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:56 GMT
Content-Type: application/json
Content-Length: 1012
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "https://letsencrypt.org/docs/profiles#classic",
      "shortlived": "https://letsencrypt.org/docs/profiles#shortlived (not yet generally available)",
      "tlsserver": "https://letsencrypt.org/docs/profiles#tlsserver"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.5-February-24-2025.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "pVAWPOisGAQ": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-05-09 13:51:56,861:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-05-07 11:52:41 UTC.
2025-05-09 13:51:56,861:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-05-09 13:51:56,861:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.local.sawapp.cloud and local.sawapp.cloud
2025-05-09 13:51:56,864:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 13:51:56,864:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 13:51:57,027:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 13:51:57,027:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:56 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB24sk6I6n4TwD5tUAmtOJfQPOjNazJZZOJQ3fYij6nJQ4
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 13:51:57,028:DEBUG:acme.client:Storing nonce: QmouDpB24sk6I6n4TwD5tUAmtOJfQPOjNazJZZOJQ3fYij6nJQ4
2025-05-09 13:51:57,028:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2025-05-09 13:51:57,031:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "d_4T03JRrTYhjbXn_VdlLo9UWXmCDcsh8Z7K3qqZ5AY0ulKf5gCxn2vSDycMDdKbDPa5wxODfI8Sr32AtNa1rQwnW2TDbaB7otM-wWGPlySTu_GLHOUvZphV4D16qIJ6OXAsqWJ3xFkbf2gDTCtdzCzpdP9e9sxiH3nUdnHOb58bpVEPRNM6AtqBt_qi79L3YRTrXhSBCQYkYMQB_swYBGv0tZpK05sKU04IsE0g-dWZfrcwF7LnayABZ9SQkOJMMNBxE_7h8hHjsw-apCavzCYxvSmzUqII_7Ve5OcWziQYFPtwvzlw0CRNatLETaMC6AB3fnWQCZgzrK5i5QGzxA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2025-05-09 13:51:57,236:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/1.1" 201 503
2025-05-09 13:51:57,237:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Fri, 09 May 2025 11:51:57 GMT
Content-Type: application/json
Content-Length: 503
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382358971837
Replay-Nonce: QmouDpB2ZOYbdHvCSiYBtMZ5694hacToZ0ZjDpF-hFPu8hMtuNk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-05-16T11:51:57Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382358971837"
}
2025-05-09 13:51:57,237:DEBUG:acme.client:Storing nonce: QmouDpB2ZOYbdHvCSiYBtMZ5694hacToZ0ZjDpF-hFPu8hMtuNk
2025-05-09 13:51:57,238:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:57,240:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "bahvyKewdz6vzkNHcmgpn2175TpOwGvm_YhrRuqQygPNPEP-8wpq31rnIFI8TghARd47mRtQ-udqfzifw4yvcUXkKm45pVsA2fjDv16kfLvjHnILDUUVkPo4GM7uupVadnd01humkz4NS1ctl8he_ZkAHIMt4vxdBuEmoROOWtIAyUGt5-7NgV_YjhDr-rqe4FLMwWrDITek5x3QcltzbWnGwfdmBeKqGaVXNU5xaY2mVY66kYs689572LYaenr0aWeZZmIOGRLPJbgqttXhOcl25ykLY0j2PFqizHFQhYxMGi5NHHhnCx95VS7E5IlrJtEcdocfBaz2HAsqd31ggw",
  "payload": ""
}
2025-05-09 13:51:57,412:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595337 HTTP/1.1" 200 519
2025-05-09 13:51:57,413:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:57 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6NkW1Xce_OmdaNqrzvAGXZmiLTW3DwXm7yYd3ABUhgYnY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:51Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
      "status": "valid",
      "validated": "2025-05-09T11:51:48Z",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 13:51:57,413:DEBUG:acme.client:Storing nonce: ww5-sb6NkW1Xce_OmdaNqrzvAGXZmiLTW3DwXm7yYd3ABUhgYnY
2025-05-09 13:51:57,414:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:57,416:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "SHTZFmgHyTcEoDTQbed0BHOeJC7k8t-77O4y4xq961VLolZerTg8vS5d9Wwo-5KeZX2QUQ-t8usGIoQG8p1Glplslxieg2w01cKi2-ElINd_CKnumBFbQginGYEE2cBkrO53f2qlGUcP03_vv6xVePNSM9tCEdXNCSnEq4DGrL0CkwTtkR1dVBJEtFyafURvrCc-uP8oL271HUuq6ZiPB5OkPGrCjydzAwMTM4zksOBvn7pSKDE0ij1Ftqo1KED_i7s8MSkKN8ZkKvf4_Wxf7uqSSxYsGeyz_rali1d5DgpcujV2m5Xzd7vUtgQHODyu7z3uY5119GKe0tD4s_ahnA",
  "payload": ""
}
2025-05-09 13:51:57,587:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791092417 HTTP/1.1" 200 400
2025-05-09 13:51:57,588:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:57 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6N23Wv5TIlJKbXKWo0ptpZtfDUua6mAA002z7zCaHpMtU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T11:51:57Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg",
      "status": "pending",
      "token": "WamTar_V_VdyTiPvZT6Cha95eFoaHsRMA7_uQDYPPzM"
    }
  ],
  "wildcard": true
}
2025-05-09 13:51:57,588:DEBUG:acme.client:Storing nonce: ww5-sb6N23Wv5TIlJKbXKWo0ptpZtfDUua6mAA002z7zCaHpMtU
2025-05-09 13:51:57,589:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-05-09 13:51:57,589:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2025-05-09 13:51:57,591:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

dw2Qr1iJWo5imPyyQ4TcH-UIBYrWejnTwgNHOvJS1Ag

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-05-09 14:45:52,273:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 14:45:52,279:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "J2LgOH_G7QlXOZ9nIe4_AXIoFlkjumYguPQjwfgl_1HOVtUHonzbKprREP-ezcKTm6BYP1ZIYBOEnuYUTN9LseCVQobu5kl2gAfQ5EEI8tNkwCLkynW2uHoA5PfN8WaSVTIgxXCPlXw2pBL5aHTyP0TWxpCk_AByCGFxDU5mvmVfVscpEN_PhDutXkNrezLMsKehiGOb7B3dEGa9oGZMt5IW75TXkcQNh19JzYNOLVK28OYMOKcJqFcKrK76Y0j3RGN__wE523ViK4U7x-FB0uW5PgyvTA2IcHSAYYo-2TiQYNfVjqumFciN8mAgpHRxpUaXUxXD3ZjVQ0ZHCbk9Pg",
  "payload": "e30"
}
2025-05-09 14:45:52,282:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-05-09 14:45:52,799:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517791092417/7Ilfvg HTTP/1.1" ************-05-09 14:45:52,799:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Fri, 09 May 2025 12:45:52 GMT
Content-Type: application/problem+json
Content-Length: 203
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwHpRCcUGcJMi_gTt9JXgaWy6xgnMVuUWUauhc7E19KZ4

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "Unable to validate JWS :: JWS has an invalid anti-replay nonce: \"ww5-sb6N23Wv5TIlJKbXKWo0ptpZtfDUua6mAA002z7zCaHpMtU\"",
  "status": 400
}
2025-05-09 14:45:52,800:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: Unable to validate JWS :: JWS has an invalid anti-replay nonce: "ww5-sb6N23Wv5TIlJKbXKWo0ptpZtfDUua6mAA002z7zCaHpMtU"
2025-05-09 14:45:52,800:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 14:45:52,800:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 14:45:52,955:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 14:45:52,956:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:52 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDJEoK8BtXO-mGEQ1NPJPsmAMmV-wvH8Ml3GK1jC8p_dQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 14:45:52,957:DEBUG:acme.client:Storing nonce: yPpvzgaDJEoK8BtXO-mGEQ1NPJPsmAMmV-wvH8Ml3GK1jC8p_dQ
2025-05-09 14:45:52,957:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 14:45:52,960:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhREpFb0s4QnRYTy1tR0VRMU5QSlBzbUFNbVYtd3ZIOE1sM0dLMWpDOHBfZFEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNDM2MDgyNjcvNTE3NzkxMDkyNDE3LzdJbGZ2ZyJ9",
  "signature": "WSQGs_J2j4T7SKKmX7fIOQmZuzCIyOkRRu1_XZm7hKE5dGnGe1lwOfJA_4WslQL5Fayxqd-5ArykSeugkoN-3i2tNbCUDOdk1wUJRapKsyIuLMBS15uNKerUz22BXjMYEUX8iBLWzl6lbv5nn0yA0T5AsW3v1UPgkZJRX4zxgb0su7cZm-GqK1ubMlesAfRQNKnP4zfgjiu-J7IRpOikWun63NF52dypvAWgfvscTtBkBTdr0wGfnKbh2iHXEGVARMI-SZUskP8czNVUF1uPICEy7sBW1RCTQJAJsoXF5WD3xKiPXuJ12kXr02kUV_KcLDA6f8E7V0SYsbjWt_Oz0w",
  "payload": "e30"
}
2025-05-09 14:45:53,130:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517791092417/7Ilfvg HTTP/1.1" 200 194
2025-05-09 14:45:53,131:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:53 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg
Replay-Nonce: yPpvzgaDfXpmBFJm_ZPqR5TOx8S9kbULRpGxfJtg9_sYM7p5IGQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg",
  "status": "pending",
  "token": "WamTar_V_VdyTiPvZT6Cha95eFoaHsRMA7_uQDYPPzM"
}
2025-05-09 14:45:53,131:DEBUG:acme.client:Storing nonce: yPpvzgaDfXpmBFJm_ZPqR5TOx8S9kbULRpGxfJtg9_sYM7p5IGQ
2025-05-09 14:45:53,132:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-05-09 14:45:54,137:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:54,140:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRGZYcG1CRkptX1pQcVI1VE94OFM5a2JVTFJwR3hmSnRnOV9zWU03cDVJR1EiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzUyNTk1MzM3In0",
  "signature": "DjgyZVoN57ddBv35Re8TKKvZUg34dnnOmc7ffvRmadqVogkzGV8355nfJf-PXQQbj8DeNrhzzunbsmWjfKuzAnzhpuq2iuyu7uKDgWXmlmIwulqVOStpDCemCOuFuPD-1ln2jXp32-6U7SZ_W2T-OAuEb8FZno4YVDvQbCA-SsUlQWJMRr7mBF88t8z6XzFzPP7I80u0jF-aZy5s3NTLzZX9XPizPQrKyWvMsfLdlHRpYZH2u-8CRMOuZTbWUrW0sg_cccU-12j97ojmGwrAY5bIUpCunNU6UNN8BGaakVKjmA5v2ysKBsrLqHWAXiP58JUpzOlkSNdP7Gt7OMmvZw",
  "payload": ""
}
2025-05-09 14:45:54,307:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517752595337 HTTP/1.1" 200 519
2025-05-09 14:45:54,309:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:54 GMT
Content-Type: application/json
Content-Length: 519
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaD9C00_3K-9CfXdMeJuzAQpZCFh89r731p7VbtmF2K1HQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:51Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517752595337/DI-arQ",
      "status": "valid",
      "validated": "2025-05-09T11:51:48Z",
      "token": "NGYd0ORGSLV4b2w9huQTRUAVKZ-FrjTbcf94x2RGZ-s",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 14:45:54,309:DEBUG:acme.client:Storing nonce: yPpvzgaD9C00_3K-9CfXdMeJuzAQpZCFh89r731p7VbtmF2K1HQ
2025-05-09 14:45:54,310:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:54,312:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRDlDMDBfM0stOUNmWGRNZUp1ekFRcFpDRmg4OXI3MzFwN1ZidG1GMksxSFEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzkxMDkyNDE3In0",
  "signature": "f9bJtVChlfgdewz9Ntbu8wEctonXcXJyLgcgbk6UQhWPuERrYVm4ToBU7KEU27aS5DiFjn3h9QXtdFQvoU7VH82YBKIbH2v-1IZ1zwGAIQ2wjDbcDMlQT8DVLMh1oHTT0fN29OdzQ6bERqvwygM_cqeRM2jTZiU68XrmCb29PFIVWg-i7IAXMTBWuUccnK9UyCYvAVcrGDPfe_UQe9_4F68jDwY59wM8L8gReYtAl3_prfgB1Q7bPOQfOUG-jTo5spIksAs1TD-psVzlRtuRLa2vqWEhe_Lffyz3e1Z7PLAsjhikLlGYoU1m9dJTgBaWJr0LCP0xc-vBPFKmQbSUlw",
  "payload": ""
}
2025-05-09 14:45:54,479:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791092417 HTTP/1.1" 200 400
2025-05-09 14:45:54,480:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:54 GMT
Content-Type: application/json
Content-Length: 400
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDBlMmY1ET5BXEwLUCexIqpsDpeDCQgRZ2jdzm_hiaxY0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T11:51:57Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg",
      "status": "pending",
      "token": "WamTar_V_VdyTiPvZT6Cha95eFoaHsRMA7_uQDYPPzM"
    }
  ],
  "wildcard": true
}
2025-05-09 14:45:54,481:DEBUG:acme.client:Storing nonce: yPpvzgaDBlMmY1ET5BXEwLUCexIqpsDpeDCQgRZ2jdzm_hiaxY0
2025-05-09 14:45:57,485:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:57,489:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhREJsTW1ZMUVUNUJYRXdMVUNleElxcHNEcGVEQ1FnUloyamR6bV9oaWF4WTAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNDM2MDgyNjcvNTE3NzkxMDkyNDE3In0",
  "signature": "Z9_DGBjRWYawsRX5KGuNelXS8RPu3gKbRppETkZD_9smAGwehDzeliaZD6TIPR1TTBg8iFWBJ3xb6bfpLBOPCWE4Wh-mqcgN3nNy-XSNYwj_K9bQtWrGW19c0FEgY_tf_JN9vPm9uRceFc5va5K8S7Kh0OnRXn4KuxluQONZmdl3auCwoiRA0qB2Sq2kbNMYq6f3dsgJqBdRXqVmAx_j6ta5IOaTH-FdvK3D0XM2QgGwZX_KW8WJrG8Nfr3jbDh5tELm64KoLE3FPtab0yhD9bIoVTXK3D-Q5GnwbxUsSjg_g6Od_Zde11ZVkhmngP34jSMtXThPxinfsIL2Nk3Bjw",
  "payload": ""
}
2025-05-09 14:45:57,655:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791092417 HTTP/1.1" ************-05-09 14:45:57,656:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:57 GMT
Content-Type: application/json
Content-Length: 539
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDzPL5X_AmtTphlkPiziw_MEr406TlQigoi3CWkaXBGcE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T12:45:55Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791092417/7Ilfvg",
      "status": "valid",
      "validated": "2025-05-09T12:45:53Z",
      "token": "WamTar_V_VdyTiPvZT6Cha95eFoaHsRMA7_uQDYPPzM",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2025-05-09 14:45:57,656:DEBUG:acme.client:Storing nonce: yPpvzgaDzPL5X_AmtTphlkPiziw_MEr406TlQigoi3CWkaXBGcE
2025-05-09 14:45:57,657:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-05-09 14:45:57,657:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-05-09 14:45:57,658:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzCLgPOmrXUUa\nw6P/Aj1drrPi85031dP7DwxKjzltm5/kB4NZJUhA1wcVHdB1Dvh2uNOc9V3bnXog\nieN6Y6bAzKBGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5z\nYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBF\nAiAz9eKLZuhIczKQOTpdKggu88rsmL/OMEG6tk1+BgUx6gIhANQhDXgCl6Euq1Sn\nkFzXLoRPi232GRNzGUuZX/cl8HPs\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2025-05-09 14:45:57,659:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2025-05-09 14:47:27.659082
2025-05-09 14:45:57,660:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzCLgPOmrXUUaw6P_Aj1drrPi85031dP7DwxKjzltm5_kB4NZJUhA1wcVHdB1Dvh2uNOc9V3bnXogieN6Y6bAzKBGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5zYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBFAiAz9eKLZuhIczKQOTpdKggu88rsmL_OMEG6tk1-BgUx6gIhANQhDXgCl6Euq1SnkFzXLoRPi232GRNzGUuZX_cl8HPs"\n}'
2025-05-09 14:45:57,662:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382358971837:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRHpQTDVYX0FtdFRwaGxrUGl6aXdfTUVyNDA2VGxRaWdvaTNDV2thWEJHY0UiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNDM2MDgyNjcvMzgyMzU4OTcxODM3In0",
  "signature": "CkYYVAdmyZWSqEmediTa7Dft7DwG4m2anRUIE9LDyZpwYdH-n2N40L14b-JNZFRw7Cxph7MHwo5ycMpTR18Z8-_Qdu904Lr0dkprFdS4m-1uuWZR6y6KO271heibtB13IsXOiEGdLbqJ1j0-rcfUolDjR-JJapB7P8JeIAqyAwLFOcwN51dXfbPQ_J1SyEpotT4dhEUIrpXizPGFjDaRNt2wkBG9qjX236kDG_VhhOkXvInkzVE3Xf_lAeCcf_GYm1sVmNOyfJR3BAR6fw-eeg0So_HPXGMAyC8ToJKyk-tNEYZGip5zpfRIJu5ICXTM0ZVQgfaas-Uhvg65KJ99eQ",
  "payload": "ewogICJjc3IiOiAiTUlJQkFUQ0JxQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRXpDTGdQT21yWFVVYXc2UF9BajFkcnJQaTg1MDMxZFA3RHd4S2p6bHRtNV9rQjROWkpVaEExd2NWSGRCMUR2aDJ1Tk9jOVYzYm5Yb2dpZU42WTZiQXpLQkdNRVFHQ1NxR1NJYjNEUUVKRGpFM01EVXdNd1lEVlIwUkJDd3dLb0lVS2k1c2IyTmhiQzV6WVhkaGNIQXVZMnh2ZFdTQ0VteHZZMkZzTG5OaGQyRndjQzVqYkc5MVpEQUtCZ2dxaGtqT1BRUURBZ05JQURCRkFpQXo5ZUtMWnVoSWN6S1FPVHBkS2dndTg4cnNtTF9PTUVHNnRrMS1CZ1V4NmdJaEFOUWhEWGdDbDZFdXExU25rRnpYTG9SUGkyMzJHUk56R1V1WlhfY2w4SFBzIgp9"
}
2025-05-09 14:46:00,554:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/382358971837 HTTP/1.1" 200 605
2025-05-09 14:46:00,556:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:46:00 GMT
Content-Type: application/json
Content-Length: 605
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382358971837
Replay-Nonce: yPpvzgaDf_YSPhCXvyFq3aO1G9BlnNfGrCayGUBA8BVWOvRaR8k
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-05-16T11:51:57Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382358971837",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/0618ada4f5f9c304965bf68d35968b93a7d0"
}
2025-05-09 14:46:00,556:DEBUG:acme.client:Storing nonce: yPpvzgaDf_YSPhCXvyFq3aO1G9BlnNfGrCayGUBA8BVWOvRaR8k
2025-05-09 14:46:01,559:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:46:01,563:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/382358971837:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJ5UHB2emdhRGZfWVNQaENYdnlGcTNhTzFHOUJsbk5mR3JDYXlHVUJBOEJWV092UmFSOGsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNDM2MDgyNjcvMzgyMzU4OTcxODM3In0",
  "signature": "q1P20xfDYXfv92snrgEM69kw80gx3t7DPim2RiWNiSNgyLVvYkHsxl7B9iEKmvvki85lZzz6DhyIwZANM5ZvA5U8wrDE9-wPHdX8MkfHzGOVdIJKGY_i5jEfwzi2kLreiPCOiaVkAd1LC3cASXQFhfdn8uQWKU11xoz10lOYETX2Li6wIx5PK_m6CMpwQ-MmewgNBwpp7xtcMG6jYGxOKe8Wul8DoSTMZUGMXtUz_9avgwTJOW7ehA9e_M-N1tNRhScHOR9Tuc2hf1T6BAC-R4hegpwOZc-MuKw3mbZfWD2KeDMDlLbaZPR4lbzBm3si2eHq1KV_rON1JqXuzj0x6Q",
  "payload": ""
}
2025-05-09 14:46:01,769:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/382358971837 HTTP/1.1" 200 605
2025-05-09 14:46:01,770:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:46:01 GMT
Content-Type: application/json
Content-Length: 605
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQw665gtUT1iyhcKwvlFdUZh7dJ_iyKQHm3go1iiy4zGuA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-05-16T11:51:57Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517752595337",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791092417"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382358971837",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/0618ada4f5f9c304965bf68d35968b93a7d0"
}
2025-05-09 14:46:01,771:DEBUG:acme.client:Storing nonce: NUDoTsQw665gtUT1iyhcKwvlFdUZh7dJ_iyKQHm3go1iiy4zGuA
2025-05-09 14:46:01,771:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:46:01,774:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/0618ada4f5f9c304965bf68d35968b93a7d0:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJOVURvVHNRdzY2NWd0VVQxaXloY0t3dmxGZFVaaDdkSl9peUtRSG0zZ28xaWl5NHpHdUEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDYxOGFkYTRmNWY5YzMwNDk2NWJmNjhkMzU5NjhiOTNhN2QwIn0",
  "signature": "swtB5EZnKAv2QNGCWs0b0H_-kGrNu9O_XPvsV8M0k3WrxxVkt_0iwsxGHdEFyMKRNwyrHlbEbuRNMsKFJR1Cl0RIEaDwt1jPyKVQs8VbYlEVPO_o5m4rimmwXnDw2X7AEWxY1NT2s5YHCPmkKpTO78_0CKDuWbH7yIJKiJTOQH-s3nR32YDRtmVxsqEebHuLJvaW-TvJo-E7gmnfypdG35GCIiUxc7qiNT5S5AXCxn7ndT7xMMiabuY12iQ1RLLHaPn2tMX6oDRZEd417QGJ8OPuIy-fILIRf422nFuLfp3pkPhfzUSUJIlwBnK3bY9kGdYxURVVKgCk9JygGdGU0Q",
  "payload": ""
}
2025-05-09 14:46:01,968:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/0618ada4f5f9c304965bf68d35968b93a7d0 HTTP/1.1" 200 2897
2025-05-09 14:46:01,969:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:46:01 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2897
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/0618ada4f5f9c304965bf68d35968b93a7d0/1>;rel="alternate"
Replay-Nonce: NUDoTsQwV7VYf_0g_gAP-Et3snSX_MykCJj_bJO5gYqR1r8Marw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDqDCCAy+gAwIBAgISBhitpPX5wwSWW/aNNZaLk6fQMAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NjAeFw0yNTA1MDkxMTQ3MjdaFw0yNTA4MDcxMTQ3MjZaMB8xHTAbBgNVBAMMFCou
bG9jYWwuc2F3YXBwLmNsb3VkMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzCLg
POmrXUUaw6P/Aj1drrPi85031dP7DwxKjzltm5/kB4NZJUhA1wcVHdB1Dvh2uNOc
9V3bnXogieN6Y6bAzKOCAjYwggIyMA4GA1UdDwEB/wQEAwIHgDAdBgNVHSUEFjAU
BggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQU/wJc
rkOyboQahQV0WoZHF54XnRUwHwYDVR0jBBgwFoAUkydGmAOpUWiOmNbEQkjbI79Y
lNIwMgYIKwYBBQUHAQEEJjAkMCIGCCsGAQUFBzAChhZodHRwOi8vZTYuaS5sZW5j
ci5vcmcvMDMGA1UdEQQsMCqCFCoubG9jYWwuc2F3YXBwLmNsb3VkghJsb2NhbC5z
YXdhcHAuY2xvdWQwEwYDVR0gBAwwCjAIBgZngQwBAgEwLQYDVR0fBCYwJDAioCCg
HoYcaHR0cDovL2U2LmMubGVuY3Iub3JnLzgxLmNybDCCAQQGCisGAQQB1nkCBAIE
gfUEgfIA8AB2AMz7D2qFcQll/pWbU87psnwi6YVcDZeNtql+VMD+TA2wAAABlrUU
wD0AAAQDAEcwRQIgGYqAK4nsm7C+wlGP/GazMxSCcLyZkletHOx0bxHGO3ECIQCy
Yerzh+UAGbEQxXbtQr0LKXfIsuLqGtwUFQwWk7yVXgB2AO08S9boBsKkogBX28sk
4jgB31Ev7cSGxXAPIN23Pj/gAAABlrUUx+0AAAQDAEcwRQIhAIsPdvyUU98D98/u
mLN1uB9C0foX8c0zSUjV9n8JRG+jAiAqeVos93JZfSiPKJMrsfJJRIM+aogbC1Lg
XMFOijo0kDAKBggqhkjOPQQDAwNnADBkAjBDGcOHSFlc8snqvxdA9BwtgVygngXU
smOCeionJiquP1YxpleqvIh3SbjlINpiBx8CMD8SkpFHDcEfgWo25TDfXP/LxOZD
ZhZEKjg10EdrW6u8XEZqvXWevsUf8l9Uu5U4kQ==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRALBXPpFzlydw27SHyzpFKzgwDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTYwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAATZ8Z5G
h/ghcWCoJuuj+rnq2h25EqfUJtlRFLFhfHWWvyILOR/VvtEKRqotPEoJhC6+QJVV
6RlAN2Z17TJOdwRJ+HB7wxjnzvdxEP6sdNgA1O1tHHMWMxCcOrLqbGL0vbijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSTJ0aYA6lRaI6Y1sRCSNsj
v1iU0jAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAfYt7SiA1sgWGCIpunk46r4AExIRc
MxkKgUhNlrrv1B21hOaXN/5miE+LOTbrcmU/M9yvC6MVY730GNFoL8IhJ8j8vrOL
pMY22OP6baS1k9YMrtDTlwJHoGby04ThTUeBDksS9RiuHvicZqBedQdIF65pZuhp
eDcGBcLiYasQr/EO5gxxtLyTmgsHSOVSBcFOn9lgv7LECPq9i7mfH3mpxgrRKSxH
pOoZ0KXMcB+hHuvlklHntvcI0mMMQ0mhYj6qtMFStkF1RpCG3IPdIwpVCQqu8GV7
s8ubknRzs+3C/Bm19RFOoiPpDkwvyNfvmQ14XkyqqKK5oZ8zhD32kFRQkxa8uZSu
h4aTImFxknu39waBxIRXE4jKxlAmQc4QjFZoq1KmQqQg0J/1JF8RlFvJas1VcjLv
YlvUB2t6npO6oQjB3l+PNf0DpQH7iUx3Wz5AjQCi6L25FjyE06q6BZ/QlmtYdl/8
ZYao4SRqPEs/6cAiF+Qf5zg2UkaWtDphl1LKMuTNLotvsX99HP69V2faNyegodQ0
LyTApr/vT01YPE46vNsDLgK+4cL6TrzC/a4WcmF5SRJ938zrv/duJHLXQIku5v0+
EwOy59Hdm0PT/Er/84dDV0CSjdR/2XuZM3kpysSKLgD1cKiDA+IRguODCxfO9cyY
Ig46v9mFmBvyH04=
-----END CERTIFICATE-----

2025-05-09 14:46:01,969:DEBUG:acme.client:Storing nonce: NUDoTsQwV7VYf_0g_gAP-Et3snSX_MykCJj_bJO5gYqR1r8Marw
2025-05-09 14:46:01,973:DEBUG:certbot._internal.storage:Writing new private key to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/privkey3.pem.
2025-05-09 14:46:01,974:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/cert3.pem.
2025-05-09 14:46:01,974:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/chain3.pem.
2025-05-09 14:46:01,974:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud/fullchain3.pem.
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var account=b1747b4e4c9ff2f5413c3eadbb4d1958 (set by user).
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2025-05-09 14:46:01,982:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2025-05-09 14:46:01,983:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 14:46:01,983:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 14:46:01,984:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/local/renewal/local.sawapp.cloud.conf.new.
2025-05-09 14:46:01,988:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/privkey.pem
This certificate expires on 2025-08-07.
These files will be updated when the certificate renews.
2025-05-09 14:46:01,988:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2025-05-09 14:46:01,989:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2025-05-09 14:46:01,989:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
