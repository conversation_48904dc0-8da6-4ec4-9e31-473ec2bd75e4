2024-11-07 15:52:39,054:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-07 15:52:39,055:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-07 15:52:39,055:DEBUG:certbot._internal.main:Arguments: ['--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.local.sawapp.cloud', '-d', 'local.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/local', '--work-dir', 'certs/local', '--logs-dir', 'certs/local']
2024-11-07 15:52:39,055:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-07 15:52:39,066:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-07 15:52:39,067:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 15:52:39,067:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x104e26510>
Prep: True
2024-11-07 15:52:39,067:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x104e26510> and installer None
2024-11-07 15:52:39,067:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-07 15:52:39,101:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), b1747b4e4c9ff2f5413c3eadbb4d1958, Meta(creation_dt=datetime.datetime(2024, 11, 7, 13, 21, 14, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-07 15:52:39,107:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-07 15:52:39,111:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-07 15:52:39,586:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-07 15:52:39,586:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:39 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "Rar2yQWqtII": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-07 15:52:39,587:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for *.local.sawapp.cloud and local.sawapp.cloud
2024-11-07 15:52:39,591:DEBUG:acme.client:Requesting fresh nonce
2024-11-07 15:52:39,591:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-07 15:52:39,747:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-07 15:52:39,748:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:39 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQVaumuf_z4Wav9wmsxcJXm2pzbEmN3LaKJS5J__wPjpg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-07 15:52:39,748:DEBUG:acme.client:Storing nonce: dLyaEgFQVaumuf_z4Wav9wmsxcJXm2pzbEmN3LaKJS5J__wPjpg
2024-11-07 15:52:39,749:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.local.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "local.sawapp.cloud"\n    }\n  ]\n}'
2024-11-07 15:52:39,753:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "XxN5r8DGdN2DUuablPlrGtDWlmMWavH-UMwdZoIhkxYt8fXreo7pRRLHzxtD8AoT9cSzeIp9ZP1Z8igisWFBsVM93-BHmlko4rnjdtWfBxoZ6DMSTGZ-QwV_-jr485b9EkasTAEcZibv4Vxh7YIgihtq3aSAPl5_1w5BtorEffey5rDo9E6rDDTEtIYkK6mBFyYJ_jOChRbHyRMGXF7frk34aQFygLIboj5RnQ1RG7vQHUa8Ukj45x5ucbWdLUsDSdS05TqFgs-Tz5ETX9FkjecMPPl2fTc4bV3k__jFGOyZ7b7TD35bC2WO63zKwfIQHdq-fTJXkhg75heTfdDLcA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIioubG9jYWwuc2F3YXBwLmNsb3VkIgogICAgfSwKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogImxvY2FsLnNhd2FwcC5jbG91ZCIKICAgIH0KICBdCn0"
}
2024-11-07 15:52:39,972:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 487
2024-11-07 15:52:39,973:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 07 Nov 2024 14:52:39 GMT
Content-Type: application/json
Content-Length: 487
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/320880825037
Replay-Nonce: guak6r7LeBw04GcrHJfsf5MPqK4N84CqMnBHCtV_W64uO-RWLYM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320880825037"
}
2024-11-07 15:52:39,973:DEBUG:acme.client:Storing nonce: guak6r7LeBw04GcrHJfsf5MPqK4N84CqMnBHCtV_W64uO-RWLYM
2024-11-07 15:52:39,973:DEBUG:acme.client:JWS payload:
b''
2024-11-07 15:52:39,978:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437:
{
  "protected": "********************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "s_Bac2dLTy2xbB94gRKUXpwObodhd0I7HWq4E1XUQpjPx_uFKZVaj7bKkde3dnqSIcwfJGaVNXE7VMZp6Z_ymDk7vKQwNvZc8mAh_xnScrQDL43nTBfxYKrwryXKrvXmR1nShuUA6zh9AQiN4_QNGVVn0-sqrPBopixNPAv6BwmLkPk5HmPSK5QHIxQdYB18D-61NwmHMeTWDT9tFP7d6IIO2OqakgGZsdWcXZk0tKLiZoFxCJnXuDNYdCUL2Jmu0zHqkP1pEy7A0bxSx059PnV-rmuFLtAmLju9EJb3qtjK23AUun_mSh_3hchxY1hbIHd4aF0v9EHhwkP2OlhF-A",
  "payload": ""
}
2024-11-07 15:52:40,140:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975437 HTTP/11" 200 511
2024-11-07 15:52:40,141:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:40 GMT
Content-Type: application/json
Content-Length: 511
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQXbWKIrJca9zo8RAJs8VYqGBatcDsbyy6wpGtp7cyfbU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-07T14:52:14Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
      "status": "valid",
      "validated": "2024-11-07T14:52:12Z",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-07 15:52:40,141:DEBUG:acme.client:Storing nonce: dLyaEgFQXbWKIrJca9zo8RAJs8VYqGBatcDsbyy6wpGtp7cyfbU
2024-11-07 15:52:40,141:DEBUG:acme.client:JWS payload:
b''
2024-11-07 15:52:40,145:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUVhiV0tJckpjYTl6bzhSQUpzOFZZcUdCYXRjRHNieXk2d3BHdHA3Y3lmYlUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "klAJkkkh6CqPqoGDw_ML9CjJHNMB7LVIg-XjDZIJKFgx-exhpu-3YwY_oW-h-ypzVprTnHkW8wW2jhPbDT3HiIiNphANcuBDU2tks9hLjTVUJzVVbTxNeYk9HgJ8CQ9k_L0rY5f3gCbQeA2aVhCGMTms7dJghu-2QU4uzugNKrjkwlt2s5lXZSm_AQM3lRpXpCt6vCwc2ItsLZAlGAVcRKzYg7KQC_aWY-1oL1UTiHJafOBabl3MfmMTNsi65WFtq1Guh7hd-erIQ3qu3a8FQJG2M4Vk4iPp9nB6op_bd3nAeZzLOhk4wGP9A4cxS-lqyOIUgjDTq40tTwqOKzSuQQ",
  "payload": ""
}
2024-11-07 15:52:40,307:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 392
2024-11-07 15:52:40,308:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 14:52:40 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQe8v1PctSWfKrrIlWagxS-QWeL4q39SUdKCT_M1PaC3g
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "pending",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
    }
  ],
  "wildcard": true
}
2024-11-07 15:52:40,308:DEBUG:acme.client:Storing nonce: dLyaEgFQe8v1PctSWfKrrIlWagxS-QWeL4q39SUdKCT_M1PaC3g
2024-11-07 15:52:40,309:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-07 15:52:40,309:INFO:certbot._internal.auth_handler:dns-01 challenge for local.sawapp.cloud
2024-11-07 15:52:40,311:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.local.sawapp.cloud.

with the following value:

48qTpajb0Hmj4lGWWuGOKnzviYpMLVaaMBeYPsEaAsw

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.local.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-07 16:37:56,360:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-07 16:37:56,364:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA:
{
  "protected": "******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "iU8V_fLi8SBp0DJLkcxMbLIWUMmFcy8Fv4PbalwhAqUm5Chp_GdvHCUJPf0z_3DevwEpc4z7c0LGXJJYR1U7y0Dk75aUBa4wfh--lX0H3NhJWD4wlqYJtJWfB8lXafxc0pzPUFmIylNIwygjX-f8ylbzwObmTIQ0fR5pjAiGZJDEhjbpnwPYRhKmGuGopjuj_I7Z-zZcC1a8JTwku9rgzzIrGVEnpOJqRViS80zj8RzaayKw6Y4jmV1O9aC8-2hJUhsG_oFtAJj0XzKeX3TvuZyUVuTRtGG7UcMVZ2xj73_E66QCx-iWNvChBfoDoAXNO2oIDgAzaCnUBf7kn4ESKg",
  "payload": "e30"
}
2024-11-07 16:37:56,367:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2024-11-07 16:37:56,918:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/426868889507/3KeBDA HTTP/11" 400 177
2024-11-07 16:37:56,919:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 07 Nov 2024 15:37:56 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: guak6r7L8tlkDUg4TrvLqBaegra2mw29Xx-xDK2F5pkmMHQktl0

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"dLyaEgFQe8v1PctSWfKrrIlWagxS-QWeL4q39SUdKCT_M1PaC3g\"",
  "status": 400
}
2024-11-07 16:37:56,920:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "dLyaEgFQe8v1PctSWfKrrIlWagxS-QWeL4q39SUdKCT_M1PaC3g"
2024-11-07 16:37:56,920:DEBUG:acme.client:Requesting fresh nonce
2024-11-07 16:37:56,920:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-07 16:37:57,151:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-07 16:37:57,152:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:37:57 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: guak6r7LWVBzINrxxfdJV-35maRWivIgi-l6Zgjhy6RSdxEs6fk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-07 16:37:57,153:DEBUG:acme.client:Storing nonce: guak6r7LWVBzINrxxfdJV-35maRWivIgi-l6Zgjhy6RSdxEs6fk
2024-11-07 16:37:57,153:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-07 16:37:57,158:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJndWFrNnI3TFdWQnpJTnJ4eGZkSlYtMzVtYVJXaXZJZ2ktbDZaZ2poeTZSU2R4RXM2ZmsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQyNjg2ODg4OTUwNy8zS2VCREEifQ",
  "signature": "k6Lr-YbbTxpkTPTRKG8-yBI6kVlCdliUHnaVUdvP5MTkjzedJUl7VA3vkPRjPFP4XXpZ_5sfjYEsClTtPqA818rl_vKud0zYKNLkA8BCeSAa7gQ9bgb7pn-y09Nku6B4Evm3zA6KN22tTTHmax2rICKBhm5ZWIe0pfGjLHeK0z6huS8ogZtswWF9TadsZaPYXvAN2qhfwGXgJydl8wPPoPqMPY-YYcDVvAZSbhwHM_zkewpeqchkNyg5gPNqrnn1hO4itDv4QSZXQk9VG-L-0wTEkDqgR2--hsCmVW8S6Od2nRmp8Hnbdyd-3TOzhE6Vctv80KHMRRaVzLLwLgQJ8g",
  "payload": "e30"
}
2024-11-07 16:37:57,327:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/426868889507/3KeBDA HTTP/11" 200 186
2024-11-07 16:37:57,328:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:37:57 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA
Replay-Nonce: dLyaEgFQd761OXw1yyRwhlgRWL6P3tizprLEePrs7wkXblvg65k
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
  "status": "pending",
  "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
}
2024-11-07 16:37:57,328:DEBUG:acme.client:Storing nonce: dLyaEgFQd761OXw1yyRwhlgRWL6P3tizprLEePrs7wkXblvg65k
2024-11-07 16:37:57,329:INFO:certbot._internal.auth_handler:Waiting for verification...
2024-11-07 16:37:58,334:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:37:58,340:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUWQ3NjFPWHcxeXlSd2hsZ1JXTDZQM3RpenByTEVlUHJzN3drWGJsdmc2NWsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg0Nzk3NTQzNyJ9",
  "signature": "azbZQZ4lav2W0roEJTzKxeQXUXezuZFeuEXuw9DjUBMUI-q5RWsabbMSY9_L-jLWGxFD4thlCMTC1yZ3VV4efkgU_RVdIqg9fMnoBvPAbYdwBrmvH_OIdjYwpGhxk4XDaXU6hirZ-oqr7ttseySixYJVgqSxOqHZYwdofIFmMewVv1-8zihe-qeIjh7K0zhJp_hq-iH1OWvaHeKOs9lANP7n4bwrfIsWNtNSXat_qLborWLkunB4rhyDPJF-r1Mo6SKapkmb3tJqUXKm4JXkrmwl2TCry75_M4SYjGGimQYYOwtcMxFT-lZ2-7qrXLFAqBkY0tQmaIBZAuZcdyVD3w",
  "payload": ""
}
2024-11-07 16:37:58,524:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426847975437 HTTP/11" 200 511
2024-11-07 16:37:58,525:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:37:58 GMT
Content-Type: application/json
Content-Length: 511
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQNpOwyNdBlWL8JXrNkP9Sn-4qRTfLGCCI0dquUI1ynWU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-07T14:52:14Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426847975437/ZmetGg",
      "status": "valid",
      "validated": "2024-11-07T14:52:12Z",
      "token": "acjTxE7ugAQ8EPKLoLWWOM_eteE6Gzta_T3G3GAcQIA",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-07 16:37:58,525:DEBUG:acme.client:Storing nonce: dLyaEgFQNpOwyNdBlWL8JXrNkP9Sn-4qRTfLGCCI0dquUI1ynWU
2024-11-07 16:37:58,526:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:37:58,529:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUU5wT3d5TmRCbFdMOEpYck5rUDlTbi00cVJUZkxHQ0NJMGRxdVVJMXluV1UiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "XTy3mKBHZ3uL9uaQlUCXpLbkGK1R46_imax6bpmMzAup_OPJFdIeUBCsKrqqmpXVqi_juNE3Ci0JFjXOq0dMvnPrOBbtj5ERCLcSSRCgrI5pRqD24DCDZb4-KdZvlx5bGIhUt42Bnyy1Kk1uZBKkmssxmYGFIrHCXH1_JhlljNc2Lv5PXWXOVU5A3F74SKlZqpub-bbMF80pvZW-fY-d6le7O0hXRqjwK6LRhYz16fm2njO7Qpw-PG8Rqk3yPHTk4JnB2pbcUgC876FUG0IkywBJ6AhpDrzvwn42yi41ruvqlTYN_gxioI-wAZX25eVbo720-dcaeziKnjS-CjpsyQ",
  "payload": ""
}
2024-11-07 16:37:58,697:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 392
2024-11-07 16:37:58,698:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:37:58 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: guak6r7LunrPe8d5dc1uhBkJQUr7RiLtKwxJyxAaQYxM5nUHESw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "pending",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
    }
  ],
  "wildcard": true
}
2024-11-07 16:37:58,698:DEBUG:acme.client:Storing nonce: guak6r7LunrPe8d5dc1uhBkJQUr7RiLtKwxJyxAaQYxM5nUHESw
2024-11-07 16:38:01,703:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:01,708:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJndWFrNnI3THVuclBlOGQ1ZGMxdWhCa0pRVXI3UmlMdEt3eEp5eEFhUVl4TTVuVUhFU3ciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "LNfXm9YNng40I98hi2T12JakUe8QA6Go8iOXxNbQZrQh7657QF8c7djg25fl57d_pnzMOr7qrNSG1cpD7kUwcVP8QIEI-JzMH-14XtkRSuQq_8YDOIhmw1cmOLl_bXUOBV44eBcSmCkHxVDVFaM4uScRAyM_6oNU2M0qpZER4XmpA66DAC-QL1homI0Dt0qkcxDoGehnfiprNG8If-RieGT9-YM0DA1iN4Y2203o7BrcYDrg-2T6G2_sUKRzRV-oV5w9IaP0S6hEM19xf3BatdfsPEEJhJiiEIPVwPHWhboF2C3Sr4JvlmfM-Pt6TIUMi8FwcujfWH7UU6XoO0V71Q",
  "payload": ""
}
2024-11-07 16:38:01,865:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 392
2024-11-07 16:38:01,866:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:01 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: guak6r7LWNOe67m12XRjJkUt7FhDiLlMUqHiGlSeAtWF31gF5Fg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "pending",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
    }
  ],
  "wildcard": true
}
2024-11-07 16:38:01,866:DEBUG:acme.client:Storing nonce: guak6r7LWNOe67m12XRjJkUt7FhDiLlMUqHiGlSeAtWF31gF5Fg
2024-11-07 16:38:04,872:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:04,876:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJndWFrNnI3TFdOT2U2N20xMlhSakprVXQ3RmhEaUxsTVVxSGlHbFNlQXRXRjMxZ0Y1RmciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "aCQgVbU6aGiHv3BRimFVVjuBe5RpdS3KG0cVm-w1VoOl6fm4C13B6A5mJOtj7eFp1aTc8zmySXOkQ-R17OAYamzIxZPcK8qT5JtHUXcODszX5u43cgXqT0MQ4UOjhsGjbhtDA3786EP0432HUB3xz2pxMDPPS9Bxx3H8CQaolJ0AsionmCrxUbiG99nj5_5Zcf6oNtEylBMtEQTflxEjZymVrR70p3y_9Mph81sWqzpEszc1F_dDEKHNVBX-wmrljf_zj6b_FAfsXS8Ah-q0TqulHkWJREZnsMJN0Hj0QZT4Z23w_hFUGO5L4H6cX2B7d8603pCtzQSP17HN1ytckw",
  "payload": ""
}
2024-11-07 16:38:05,037:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 392
2024-11-07 16:38:05,038:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:04 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQbMGvujW_4ySSYPvc9IWkJAwE3j4fPTfHIgfl2mXThdM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "pending",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
    }
  ],
  "wildcard": true
}
2024-11-07 16:38:05,038:DEBUG:acme.client:Storing nonce: dLyaEgFQbMGvujW_4ySSYPvc9IWkJAwE3j4fPTfHIgfl2mXThdM
2024-11-07 16:38:08,044:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:08,048:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUWJNR3Z1aldfNHlTU1lQdmM5SVdrSkF3RTNqNGZQVGZISWdmbDJtWFRoZE0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "MriOZFArAsctnoeDGGDFmZxv3NaqBLSgPBlI4-rrfLco5iSX-W9mnm9P_W3VCY7_lUD1JoN9D8vOEN0EwbEw3JBHEW08fbUiyuZE8lrjgNlfIS6PLKlk1VLW5HlyMErT6AwWrAecY-Ks8M1W5uI5wjaZZUZZs9V0Sh4wVE0ukY8AUrS6JR8Vuqf_bc8hfYUdgb9NJ2m3VNGt9FBEnLaMkb4C-yrEWDHLGQVBTbab59TM7FLdIJBA6HUyHG_8BvqxPEksPLEmN53g58y2WYu0ETM4NNbbszYc0B33vKv2FIgLwjc8ux8JHqaOK1SPNGeIPnLxUU3FOLAjPp_D47xyIw",
  "payload": ""
}
2024-11-07 16:38:08,209:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 392
2024-11-07 16:38:08,210:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:08 GMT
Content-Type: application/json
Content-Length: 392
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQdV2bkJYGDKx7a2G-ryw36teOe1jbNMRSwJRtqK5z2_M
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-14T14:52:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "pending",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE"
    }
  ],
  "wildcard": true
}
2024-11-07 16:38:08,211:DEBUG:acme.client:Storing nonce: dLyaEgFQdV2bkJYGDKx7a2G-ryw36teOe1jbNMRSwJRtqK5z2_M
2024-11-07 16:38:11,216:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:11,221:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUWRWMmJrSllHREt4N2EyRy1yeXczNnRlT2UxamJOTVJTd0pSdHFLNXoyX00iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyNjg2ODg4OTUwNyJ9",
  "signature": "oeF0_njiWivdPe6XfHp9gyHYj_lPX-pIPRli3KfGY0qo3jaL4Gj23905-Rs2oQbjnnEowxSNjNRiqoVRMZiPpZwcYS5UzFOmi-mgTrUpnStOukF6NlqE-XfwLvBxW2L3wimPpK99IWQPW5mNNCahxXKD8FKSI3RxYTOBs-c7DD7FrwMe5RGDiwrL4h-wPEaXgM6wjxSGgnxJc15CPK2p21bj00UptDiT892fjSkZ8KG7ftr60R41SalFApYuuLqDxQ07F9RLGBe01UIgjSiyr3wM_S6YuVmM_FCZ37B9WoIb7Jc79aDi19Vjqv0TPg8raMEp3349K_iJLIHVPd1xCQ",
  "payload": ""
}
2024-11-07 16:38:11,381:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/426868889507 HTTP/11" 200 531
2024-11-07 16:38:11,382:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:11 GMT
Content-Type: application/json
Content-Length: 531
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: guak6r7LlPTDmN8DcMYRCX_JLd4eQUFGRL7BuiAtrahpeLOB5xM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "local.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-07T15:38:08Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/426868889507/3KeBDA",
      "status": "valid",
      "validated": "2024-11-07T15:37:57Z",
      "token": "kHdf1ftAjXtoQmxeMciJ9HkK9IBjmD1GsdwjKIus0EE",
      "validationRecord": [
        {
          "hostname": "local.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2024-11-07 16:38:11,382:DEBUG:acme.client:Storing nonce: guak6r7LlPTDmN8DcMYRCX_JLd4eQUFGRL7BuiAtrahpeLOB5xM
2024-11-07 16:38:11,383:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-07 16:38:11,383:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-07 16:38:11,384:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU553rrbyYTae\nk+oOvBgdqC3ZvULD6rjAvbMd054cEoay4kO2+slCpZWenoS69nIIqXDY1jA/HtQK\nGFfTGNP2j6BGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5z\nYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBF\nAiB3EG/iXU/dhvMiwg+f/bwX3D30AJJfTC5hYqwbGO5PEgIhAJWKCbL3F5Jixeol\nA749RyR15OgIhzmT7vbX0ixlAk40\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2024-11-07 16:38:11,385:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2024-11-07 16:39:41.385329
2024-11-07 16:38:11,386:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBATCBqAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU553rrbyYTaek-oOvBgdqC3ZvULD6rjAvbMd054cEoay4kO2-slCpZWenoS69nIIqXDY1jA_HtQKGFfTGNP2j6BGMEQGCSqGSIb3DQEJDjE3MDUwMwYDVR0RBCwwKoIUKi5sb2NhbC5zYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAKBggqhkjOPQQDAgNIADBFAiB3EG_iXU_dhvMiwg-f_bwX3D30AJJfTC5hYqwbGO5PEgIhAJWKCbL3F5JixeolA749RyR15OgIhzmT7vbX0ixlAk40"\n}'
2024-11-07 16:38:11,388:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320880825037:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJndWFrNnI3TGxQVERtTjhEY01ZUkNYX0pMZDRlUVVGR1JMN0J1aUF0cmFocGVMT0I1eE0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNDM2MDgyNjcvMzIwODgwODI1MDM3In0",
  "signature": "APXBGOvh-zwLcJWs-v3AY6bUesyTaeRbzqSofeuexoSXnXX0r6VlXJFFmD-qDgNfrpx3yVVfpiqbTJFvHxeFfcjc58nlQfaARYbkIpGDPlVeq-7yvHoHaGluG5EjpjMdw0aL_Q9-GvotOBwwmNvKoH-lHDUQ-gnCTF1CI9E4eUfTQDdEFpt6-pnSibMYV7HAEwcHCr-8TH2YBGzaao7XVi0ncvo5w88fghOQ1rLQ1P2QihdTWsNYiLFNsG5kyKbgKrNwLDFzwrFFUTzwbNFbE2e2DqoOziYTIsca_omgyDayuiL8wLpFJJtqI7HfgGdzTM4JrCr_i5AzYEVboRUcVw",
  "payload": "ewogICJjc3IiOiAiTUlJQkFUQ0JxQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRVU1NTNycmJ5WVRhZWstb092QmdkcUMzWnZVTEQ2cmpBdmJNZDA1NGNFb2F5NGtPMi1zbENwWldlbm9TNjluSUlxWERZMWpBX0h0UUtHRmZUR05QMmo2QkdNRVFHQ1NxR1NJYjNEUUVKRGpFM01EVXdNd1lEVlIwUkJDd3dLb0lVS2k1c2IyTmhiQzV6WVhkaGNIQXVZMnh2ZFdTQ0VteHZZMkZzTG5OaGQyRndjQzVqYkc5MVpEQUtCZ2dxaGtqT1BRUURBZ05JQURCRkFpQjNFR19pWFVfZGh2TWl3Zy1mX2J3WDNEMzBBSkpmVEM1aFlxd2JHTzVQRWdJaEFKV0tDYkwzRjVKaXhlb2xBNzQ5UnlSMTVPZ0loem1UN3ZiWDBpeGxBazQwIgp9"
}
2024-11-07 16:38:12,035:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/320880825037 HTTP/11" 200 589
2024-11-07 16:38:12,036:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:11 GMT
Content-Type: application/json
Content-Length: 589
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/320880825037
Replay-Nonce: dLyaEgFQdW4EYrDqEDcwkPMa-7C4gyFtMEdbfMRgKdBJ_brZv8s
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2024-11-14T14:52:39Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320880825037",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/03c9bffb21f4b0b07c6910c43f322cc6d67f"
}
2024-11-07 16:38:12,036:DEBUG:acme.client:Storing nonce: dLyaEgFQdW4EYrDqEDcwkPMa-7C4gyFtMEdbfMRgKdBJ_brZv8s
2024-11-07 16:38:13,037:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:13,042:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/320880825037:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUWRXNEVZckRxRURjd2tQTWEtN0M0Z3lGdE1FZGJmTVJnS2RCSl9iclp2OHMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNDM2MDgyNjcvMzIwODgwODI1MDM3In0",
  "signature": "gYFjrAkfWjDUYsUsfl3pPtDj9uCOz7TYLEVykpKH3L-g9uWrzV51AmSHVEfVTSvbOqRC6vyZEigXRm1a2OWf1Xvgy0_-H72DPnrmMQLYj8ycXQ-uWoPwc9ZuFznruc_-702AizSB11bULn7shtA5_qTw5IubPDJw-94oKLuJTu_gsLQfynCKbyPDfQQWjYmvsMB61t_ArEE68Tx-6G80sJoEhaXPZ1eADC64snLbxknpSZW7D8hvO5y4fxkFP4SFod0Kv2KB0M7m28-TCuxhwA-L9xg0ET-OGTIoBq-wozkf8ZRglV4qdCjUnR92ohFArcQ9UMlbO2tdCl9uZmXGXQ",
  "payload": ""
}
2024-11-07 16:38:13,206:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/320880825037 HTTP/11" 200 589
2024-11-07 16:38:13,207:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:13 GMT
Content-Type: application/json
Content-Length: 589
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: dLyaEgFQmA1GtUm-A6ySDZyjae99UB4vzCn6keji2j4o8JEGF1w
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2024-11-14T14:52:39Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "local.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.local.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426847975437",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/426868889507"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/320880825037",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/03c9bffb21f4b0b07c6910c43f322cc6d67f"
}
2024-11-07 16:38:13,207:DEBUG:acme.client:Storing nonce: dLyaEgFQmA1GtUm-A6ySDZyjae99UB4vzCn6keji2j4o8JEGF1w
2024-11-07 16:38:13,208:DEBUG:acme.client:JWS payload:
b''
2024-11-07 16:38:13,211:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/03c9bffb21f4b0b07c6910c43f322cc6d67f:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA0MzYwODI2NyIsICJub25jZSI6ICJkTHlhRWdGUW1BMUd0VW0tQTZ5U0RaeWphZTk5VUI0dnpDbjZrZWppMmo0bzhKRUdGMXciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDNjOWJmZmIyMWY0YjBiMDdjNjkxMGM0M2YzMjJjYzZkNjdmIn0",
  "signature": "oVRl6c0OzYKYz_Qa4dMGj11E5fmr7Db5xOxATuAPFv7zUqpsskCVZZU4OpxuNoMe8uF7uRW-QjyOcLNA7BfFDAdSfzMgSy3qM4duegWXqYNAJv8QNH5ACf-8EJCTqfh44jWmP03pPIhFUo4-OLQxb7VfIJFWDMpD6RzTpA1AvnXLzFgOYb2H9K5FtYy3rRMyYs9NtWqvwpDgLz6FoW3-UGmZL3YdwajL93lRWFtQC7FJKZCsEn3JXd3a6iZC_N8a1tTQyEg1KTgHbwrZB0agMU4AyqVgtANvJoNZ1sP71EXCEKlp6lTAkjI3LbBR3xKXW1Xqroh5xCBKsgNa5Zlxvw",
  "payload": ""
}
2024-11-07 16:38:13,369:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/03c9bffb21f4b0b07c6910c43f322cc6d67f HTTP/11" 200 2881
2024-11-07 16:38:13,370:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 07 Nov 2024 15:38:13 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2881
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/03c9bffb21f4b0b07c6910c43f322cc6d67f/1>;rel="alternate"
Replay-Nonce: dLyaEgFQ4Jbb1L4yIeY-fsJQpozaIAJwGfutJ_eI1hJ7JZCauhk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDnjCCAySgAwIBAgISA8m/+yH0sLB8aRDEPzIsxtZ/MAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NjAeFw0yNDExMDcxNDM5NDFaFw0yNTAyMDUxNDM5NDBaMB8xHTAbBgNVBAMMFCou
bG9jYWwuc2F3YXBwLmNsb3VkMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU553
rrbyYTaek+oOvBgdqC3ZvULD6rjAvbMd054cEoay4kO2+slCpZWenoS69nIIqXDY
1jA/HtQKGFfTGNP2j6OCAiswggInMA4GA1UdDwEB/wQEAwIHgDAdBgNVHSUEFjAU
BggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUWIwC
0Mes3afsIfqK1XiZfPtLh3IwHwYDVR0jBBgwFoAUkydGmAOpUWiOmNbEQkjbI79Y
lNIwVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vZTYuby5sZW5j
ci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9lNi5pLmxlbmNyLm9yZy8wMwYDVR0R
BCwwKoIUKi5sb2NhbC5zYXdhcHAuY2xvdWSCEmxvY2FsLnNhd2FwcC5jbG91ZDAT
BgNVHSAEDDAKMAgGBmeBDAECATCCAQUGCisGAQQB1nkCBAIEgfYEgfMA8QB3AKLj
CuRF772tm3447Udnd1PXgluElNcrXhssxLlQpEfnAAABkwdGqOwAAAQDAEgwRgIh
AN7D/zjEz7/fqdiIkwGVbdBBXAHNs1Kc6QeCeLtXCMg2AiEAghhji0RVqI1gkgQ2
hxo8jy8S+L00twe6WrHoH0r3CSEAdgATSt8atZhCCXgMb+9MepGkFrcjSc5YV2rf
rtqnwqvgIgAAAZMHRqmrAAAEAwBHMEUCIQDDF9lNd4mbFc3Ct4GcCx2plRAT9/Vw
qNNpc4/9iezasAIgCPhsfwfENqDIEigoQ2v9chBG28x4rf8QSLnbEOTuiSUwCgYI
KoZIzj0EAwMDaAAwZQIwHwMTx2gaRb9rYFKWEMuFK/tIKk5SR0uuidp7rTEDt+7C
t5HTvsGllcpBqh7eTFNdAjEA7z19kjS27XBS2ugV3Y6Beo4AvC8xpJZQokLy6NeL
2uM7XIaytc6InzdIn2H+KeKI
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRALBXPpFzlydw27SHyzpFKzgwDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
****************************************************************
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTYwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAATZ8Z5G
h/ghcWCoJuuj+rnq2h25EqfUJtlRFLFhfHWWvyILOR/VvtEKRqotPEoJhC6+QJVV
6RlAN2Z17TJOdwRJ+HB7wxjnzvdxEP6sdNgA1O1tHHMWMxCcOrLqbGL0vbijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSTJ0aYA6lRaI6Y1sRCSNsj
v1iU0jAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAfYt7SiA1sgWGCIpunk46r4AExIRc
MxkKgUhNlrrv1B21hOaXN/5miE+LOTbrcmU/M9yvC6MVY730GNFoL8IhJ8j8vrOL
pMY22OP6baS1k9YMrtDTlwJHoGby04ThTUeBDksS9RiuHvicZqBedQdIF65pZuhp
eDcGBcLiYasQr/EO5gxxtLyTmgsHSOVSBcFOn9lgv7LECPq9i7mfH3mpxgrRKSxH
pOoZ0KXMcB+hHuvlklHntvcI0mMMQ0mhYj6qtMFStkF1RpCG3IPdIwpVCQqu8GV7
s8ubknRzs+3C/Bm19RFOoiPpDkwvyNfvmQ14XkyqqKK5oZ8zhD32kFRQkxa8uZSu
h4aTImFxknu39waBxIRXE4jKxlAmQc4QjFZoq1KmQqQg0J/1JF8RlFvJas1VcjLv
YlvUB2t6npO6oQjB3l+PNf0DpQH7iUx3Wz5AjQCi6L25FjyE06q6BZ/QlmtYdl/8
ZYao4SRqPEs/6cAiF+Qf5zg2UkaWtDphl1LKMuTNLotvsX99HP69V2faNyegodQ0
LyTApr/vT01YPE46vNsDLgK+4cL6TrzC/a4WcmF5SRJ938zrv/duJHLXQIku5v0+
EwOy59Hdm0PT/Er/84dDV0CSjdR/2XuZM3kpysSKLgD1cKiDA+IRguODCxfO9cyY
Ig46v9mFmBvyH04=
-----END CERTIFICATE-----

2024-11-07 16:38:13,370:DEBUG:acme.client:Storing nonce: dLyaEgFQ4Jbb1L4yIeY-fsJQpozaIAJwGfutJ_eI1hJ7JZCauhk
2024-11-07 16:38:13,372:INFO:certbot._internal.client:Non-standard path(s), might not work with crontab installed by your operating system package manager
2024-11-07 16:38:13,373:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/local/archive.
2024-11-07 16:38:13,373:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/local/live.
2024-11-07 16:38:13,374:DEBUG:certbot._internal.storage:Writing README to /Users/<USER>/dev/saw/certs/local/live/README.
2024-11-07 16:38:13,375:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/local/archive/local.sawapp.cloud.
2024-11-07 16:38:13,376:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud.
2024-11-07 16:38:13,376:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/cert.pem.
2024-11-07 16:38:13,377:DEBUG:certbot._internal.storage:Writing private key to /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/privkey.pem.
2024-11-07 16:38:13,377:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/chain.pem.
2024-11-07 16:38:13,377:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/fullchain.pem.
2024-11-07 16:38:13,377:DEBUG:certbot._internal.storage:Writing README to /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/README.
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var account=b1747b4e4c9ff2f5413c3eadbb4d1958 (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/local (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2024-11-07 16:38:13,385:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 16:38:13,385:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-07 16:38:13,385:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/local/renewal/local.sawapp.cloud.conf.
2024-11-07 16:38:13,387:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/local/live/local.sawapp.cloud/privkey.pem
This certificate expires on 2025-02-05.
These files will be updated when the certificate renews.
2024-11-07 16:38:13,387:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2024-11-07 16:38:13,388:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2024-11-07 16:38:13,388:INFO:certbot._internal.eff:Subscribe to the EFF mailing list (email: <EMAIL>).
2024-11-07 16:38:13,388:DEBUG:certbot._internal.eff:Sending POST request to https://supporters.eff.org/subscribe/certbot:
{'data_type': 'json', 'email': '<EMAIL>', 'form_id': 'eff_supporters_library_subscribe_form'}
2024-11-07 16:38:13,388:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): supporters.eff.org:443
2024-11-07 16:38:14,232:DEBUG:urllib3.connectionpool:https://supporters.eff.org:443 "POST /subscribe/certbot HTTP/11" 307 180
2024-11-07 16:38:14,414:DEBUG:urllib3.connectionpool:https://supporters.eff.org:443 "POST /donate/functions/certbot_subscribe HTTP/11" 200 None
2024-11-07 16:38:14,415:DEBUG:certbot._internal.eff:Received response:
b'{"status":true,"message":"Subscribed"}'
2024-11-07 16:38:14,418:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
