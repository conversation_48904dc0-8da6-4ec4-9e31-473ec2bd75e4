2025-05-09 11:53:38,813:DEBUG:certbot._internal.main:certbot version: 3.3.0
2025-05-09 11:53:38,814:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-05-09 11:53:38,814:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2025-05-09 11:53:38,814:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-05-09 11:53:38,827:DEBUG:certbot._internal.log:Root logging level set at 30
2025-05-09 11:53:38,827:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 11:53:38,827:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x104ab1be0>
Prep: True
2025-05-09 11:53:38,827:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x104ab1be0> and installer None
2025-05-09 11:53:38,828:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-05-09 11:53:38,862:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=datetime.timezone.utc), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-05-09 11:53:38,869:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-05-09 11:53:38,872:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-05-09 11:53:39,351:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/1.1" 200 1012
2025-05-09 11:53:39,353:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:53:39 GMT
Content-Type: application/json
Content-Length: 1012
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "VPnf6QqUDsg": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "https://letsencrypt.org/docs/profiles#classic",
      "shortlived": "https://letsencrypt.org/docs/profiles#shortlived (not yet generally available)",
      "tlsserver": "https://letsencrypt.org/docs/profiles#tlsserver"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.5-February-24-2025.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-05-09 11:53:39,363:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-05-07 15:12:35 UTC.
2025-05-09 11:53:39,363:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-05-09 11:53:39,363:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2025-05-09 11:53:39,364:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 11:53:39,364:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 11:53:39,521:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 11:53:39,522:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:53:39 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQw5r9BGOdK8PoiaVnQoo_9bGsKuZrt_Og4tq6VR9ZF6Mo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 11:53:39,523:DEBUG:acme.client:Storing nonce: NUDoTsQw5r9BGOdK8PoiaVnQoo_9bGsKuZrt_Og4tq6VR9ZF6Mo
2025-05-09 11:53:39,523:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2025-05-09 11:53:39,527:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "VEmU94d0Tfl4RcXQu0hvJH9jFL1wQK2lnPFlXA-de0HA5Mphr3yDYLmXu6cu5KUXWuztXck0qcHlGeVK-6zq5eIPG2augS9GI0xqKD3OKAaZCUoAQG1wJZfRtoiceWt1YjqLC7yoqDeAryujD6glUCdi6to0gwSUBn2EApJZI7J2z_wY_QeLH8xJaglfcUUBnp0UsD4zayY8BYVY1SZ3wycvbhrEN7pCMjyclHun4Swki_f3kpmF_URgwl-9W72M7kk7W2KAo12IxfMhEZIRpt9KNDXP8DsnbgmKg0CPQGHjMYn2CWPpmu787KPVvVqGcWVBxnbv4SZA3umg5fC3Cw",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2025-05-09 11:53:40,013:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/1.1" 201 507
2025-05-09 11:53:40,014:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Fri, 09 May 2025 09:53:39 GMT
Content-Type: application/json
Content-Length: 507
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382334455457
Replay-Nonce: yPpvzgaDoJpDZwArV_lZn1vUQ__LKkft6P80JDZsE6qDyVvX0SY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-05-16T09:53:39Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900127",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382334455457"
}
2025-05-09 11:53:40,014:DEBUG:acme.client:Storing nonce: yPpvzgaDoJpDZwArV_lZn1vUQ__LKkft6P80JDZsE6qDyVvX0SY
2025-05-09 11:53:40,014:DEBUG:acme.client:JWS payload:
b''
2025-05-09 11:53:40,017:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900127:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "aBraF4cvnzCLQGk-JilTX1Uu9MxoUG4XurvILU8FR9aQpg8vT9pJzLX___I6QJ542QOJbAsoMTTmWXFIgJLC3psZjfOpJDDDoC1Elu6jdYHWinJFR8D59FjNWAKbrjtiYbCLQOpZvqe624IbzYYyQAqtTWTQJPiM1nPIF7g4d6OAwYzVJuMDX2Krk4OVV_px3JYg3bVOz8MjymL2959rgFAflMFNlq7ZypKwB-0rKEYUZdabEwqrEZPFNVQkHDoseJnTws-dtUj-EisWUHBZX3LbCnrp-cL1EPvM1DBem1vxlteD9iBh10NYOQsr4_tg10AhlyilZQLqea9DsMotOg",
  "payload": ""
}
2025-05-09 11:53:40,187:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900127 HTTP/1.1" 200 402
2025-05-09 11:53:40,188:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:53:40 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDksIhgXjcV8nOEroSKDeMIPLHwNEKFl5uIrSAuz1MLdk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:53:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw",
      "status": "pending",
      "token": "tR86kal5h1ayZZW1JF_uB3kBkHcYM8viOcerJUa3IfA"
    }
  ],
  "wildcard": true
}
2025-05-09 11:53:40,188:DEBUG:acme.client:Storing nonce: yPpvzgaDksIhgXjcV8nOEroSKDeMIPLHwNEKFl5uIrSAuz1MLdk
2025-05-09 11:53:40,188:DEBUG:acme.client:JWS payload:
b''
2025-05-09 11:53:40,190:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJ5UHB2emdhRGtzSWhnWGpjVjhuT0Vyb1NLRGVNSVBMSHdORUtGbDV1SXJTQXV6MU1MZGsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3In0",
  "signature": "RahkYFSgidHU0i6jWxe90YwDzCSWgnzxB_dWEdCWuQsqjz579dJMEJNjQ4uIjAZbwrGGEVRHMszzt-J66QpDafmf6Km4uF-HRtOxmQ7O5BMvDlGtYGEoBR4KoZXe0XqTYGFYR6OjyM-RXk4U7A2fvYkLOB8MTCxV1YvwfsM-ZXWqWaOyHpCG9GOUNeu_F3SI0zMXsAkKYlylwpEacCw7Suqa2233JkQunklU-INaDLHICNOc6ODrBMWmpexk2AQw5PhM1V4CD944POWK8c14doLzGCdWQ40nh1UnTR01JsV9Hc8Dgt_vOlp17L_ln6C8sKT80UVr0VsjNBQUZhkaJw",
  "payload": ""
}
2025-05-09 11:53:40,394:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900217 HTTP/1.1" 200 828
2025-05-09 11:53:40,395:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 09:53:40 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwisgGmyuoZxjntEmZlXW2wuFq-nzGDJgpamrOxFqM69A
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:53:39Z",
  "challenges": [
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/d6GZQQ",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/ko8e1w",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    }
  ]
}
2025-05-09 11:53:40,395:DEBUG:acme.client:Storing nonce: NUDoTsQwisgGmyuoZxjntEmZlXW2wuFq-nzGDJgpamrOxFqM69A
2025-05-09 11:53:40,396:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-05-09 11:53:40,396:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-05-09 11:53:40,397:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-05-09 11:53:40,398:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

5S9X_qFdX78cjfxqvuu2zpk2hGJrFIdFRsIEeWVXWR4

2025-05-09 12:57:13,435:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

fXJfOlQOdPWwQ3x02YGzhrHarRyElerye4JAxKKDxa8

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-05-09 13:51:17,558:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:17,563:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "X6IOtpdFu2XG7uZyxApJmoTG7X9PncE4Xa_W-Fwk6iukB5d-TQgeeEd9S3rTYjcnyXD8fWzAv6Gh2FH7J-OE-VzksmJsActyy_0-3SFzIlOI89ofoycYxKE3ELrE3rdb7nzDD3zJBqEtf-gb4Wx8fJZZ_jJUszfoVMp4caXqqj-JJ7Dw9o9HKyDekrn-6jyr6xGetUC6V4pHAx3nnM4cO8EHemz20VjvnB01hy7VmM7_FOkTObh0HF5Gg9MBQ_M7w_l-u4ceFn7YZOFsHTOLEiv1GjdLDK40sFjN5_0pFZ3iEinHKhsuvB7WOr6IGAsb5zTfhcX98U0CRqKtz2jBSA",
  "payload": "e30"
}
2025-05-09 13:51:17,570:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-05-09 13:51:18,081:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517753900127/5lS8cw HTTP/1.1" 400 203
2025-05-09 13:51:18,082:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Fri, 09 May 2025 11:51:18 GMT
Content-Type: application/problem+json
Content-Length: 203
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwGittCBHhs6LZUk0vvc_EZjF-ZNw7ZF2qfstnrHSPQp8

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "Unable to validate JWS :: JWS has an invalid anti-replay nonce: \"NUDoTsQwisgGmyuoZxjntEmZlXW2wuFq-nzGDJgpamrOxFqM69A\"",
  "status": 400
}
2025-05-09 13:51:18,083:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: Unable to validate JWS :: JWS has an invalid anti-replay nonce: "NUDoTsQwisgGmyuoZxjntEmZlXW2wuFq-nzGDJgpamrOxFqM69A"
2025-05-09 13:51:18,083:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 13:51:18,083:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 13:51:18,236:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 13:51:18,237:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:18 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwGkFD9UTHrEHCb6z269c9GYScfCndudqamZLEbCtbyhg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 13:51:18,237:DEBUG:acme.client:Storing nonce: NUDoTsQwGkFD9UTHrEHCb6z269c9GYScfCndudqamZLEbCtbyhg
2025-05-09 13:51:18,237:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:18,239:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJOVURvVHNRd0drRkQ5VVRIckVIQ2I2ejI2OWM5R1lTY2ZDbmR1ZHFhbVpMRWJDdGJ5aGciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNTYyNDgyOTcvNTE3NzUzOTAwMTI3LzVsUzhjdyJ9",
  "signature": "bmC8RMKkFNwhG3UYf5W0qOWTF5IMuxPG0IanW3Fo0Zi9IwOOQUDbadATS5mIWPfFGyNFqCBdgR3fssSiCnUTYnK0cRfZY0S9V3vVPM8NTIdYYCs6qg7kINkVju0YQIp2cTV4XTtomnbxB2LVeuPQkIAGEZjuQ7AxJROtE9x2qNCL-Q-Z3xX3qywbAB1Z9Bd-sWlkhZC9WXc_crhoHvzi-jiHphzBtZI93zyFCACvfoXKhDTUhBlyc2ii2M82D6n1S-Ip9FVZMYo-MXzu8P7bTS7rj4Yeg8m7tlVIcCIWD7LjYTXF8ItaJVm70KQjMEF54kkvi_9ZpoFwChDfcKf3Rw",
  "payload": "e30"
}
2025-05-09 13:51:18,412:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517753900127/5lS8cw HTTP/1.1" 200 194
2025-05-09 13:51:18,413:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:18 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900127>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw
Replay-Nonce: NUDoTsQwWk81Om5doLxhcAMU7zhvuOojCA___ajUvRXQwiWT_JA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw",
  "status": "pending",
  "token": "tR86kal5h1ayZZW1JF_uB3kBkHcYM8viOcerJUa3IfA"
}
2025-05-09 13:51:18,413:DEBUG:acme.client:Storing nonce: NUDoTsQwWk81Om5doLxhcAMU7zhvuOojCA___ajUvRXQwiWT_JA
2025-05-09 13:51:18,414:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 13:51:18,417:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJOVURvVHNRd1drODFPbTVkb0x4aGNBTVU3emh2dU9vakNBX19fYWpVdlJYUXdpV1RfSkEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3L0lhUmpKQSJ9",
  "signature": "IPCW1BLnBKif3l3chVbVHg5YSKaVF1ufg7UsUFeyvxNTc5hi9uJfAGf7bZ_HzvbamaWz2-QSRYnkLfCcoGvTLly_CaVYNPWgNVMLpdQ0LZqmLVexDsvEADR6nKhQCtWaPpj57ULr7aP9pXt5L8jK25aD5l0-HqWgqM1vfgP2PNksz1g4d9USqBjpkyHL139cbvW37yQtlU99E7mYUhwmWXVs24QSdp6ayGNx1tJmJ-S7frN8OX5narUp7XyLwZvanUPcoT6B2Wxo8fwmYOGVe0x48cNrDAtzMeiqP5tjc_IXY8Pnn0PpSLlK6IQ9n_A67e3MgncS6h98oaidwbp4cg",
  "payload": "e30"
}
2025-05-09 13:51:18,604:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517753900217/IaRjJA HTTP/1.1" 200 194
2025-05-09 13:51:18,605:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:18 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA
Replay-Nonce: NUDoTsQweN4CWvMKFvC1s3Qht6VnNuRD4T4udhtdTvu_aQmz-J8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
  "status": "pending",
  "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
}
2025-05-09 13:51:18,605:DEBUG:acme.client:Storing nonce: NUDoTsQweN4CWvMKFvC1s3Qht6VnNuRD4T4udhtdTvu_aQmz-J8
2025-05-09 13:51:18,606:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-05-09 13:51:19,610:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:19,614:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900127:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJOVURvVHNRd2VONENXdk1LRnZDMXMzUWh0NlZuTnVSRDRUNHVkaHRkVHZ1X2FRbXotSjgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMTI3In0",
  "signature": "WY6N6RHyb3S00Vr3JG73idKFph-mT-W-5rtnWiQiodpyd36qIg9IF94kRs-MB-nNnlGQKOKaUe5_1Y7ukmEIBvyhcMZnMkIWw8lD2JUt3DP2CSe8fwub47hY5W3Sd239HpT9EhhJ42v8Mao5C2VM3HweNEDptuZZWik3DqIqdFc-iezEDjiXH7Px-79fe0ELI0eaWPN2EJBkUH66ejPovEoVu30KR7-GckLLJbsx-e1IZyPaXZ5BO7tFMdCMhM2jAbuSfnFBWpM9gJY_6SUx0YSwn9kok7CbkLvRjAFgvmTYjembhiQ0-v1Y76XSwLYQJi8q0tpwEwOE5Uu7giadwQ",
  "payload": ""
}
2025-05-09 13:51:19,778:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900127 HTTP/1.1" 200 688
2025-05-09 13:51:19,778:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:19 GMT
Content-Type: application/json
Content-Length: 688
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: NUDoTsQwIF1Y9XJs8AsWaj3UllibezgqBzVwOdf3Xq7FAplQIc0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2025-05-16T09:53:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900127/5lS8cw",
      "status": "invalid",
      "validated": "2025-05-09T11:51:18Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"fXJfOlQOdPWwQ3x02YGzhrHarRyElerye4JAxKKDxa8\" found at _acme-challenge.cz-test.sawapp.cloud",
        "status": 403
      },
      "token": "tR86kal5h1ayZZW1JF_uB3kBkHcYM8viOcerJUa3IfA"
    }
  ],
  "wildcard": true
}
2025-05-09 13:51:19,779:DEBUG:acme.client:Storing nonce: NUDoTsQwIF1Y9XJs8AsWaj3UllibezgqBzVwOdf3Xq7FAplQIc0
2025-05-09 13:51:19,779:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:19,782:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJOVURvVHNRd0lGMVk5WEpzOEFzV2FqM1VsbGliZXpncUJ6VndPZGYzWHE3RkFwbFFJYzAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3In0",
  "signature": "kcq1HnzAxm2J8BW-Vm0NyTK-yHBTbj58oeYsRQAeUYLQeagj_QvqNm0OdmvaD-PxxOtmSaV_ga2kJWKxydNGY624TUvuVGKv5FKN41NSQF5mY3oq5il8-BDN4a3WdQyYvT-JZWPdVyDLDTmqAcRFP4j3HqSkrzAdEUFLuKGWWXIVIvvjPpUg2D2dXkjMVM0033YBc3kS9I5FqMOKI_FifP-xtTk2h_owlDMKIbs7VlILtyWAxWrhcIf1qoC9kCPQuwfwqaCqDLUF_QZlrZzABdqsefT_oD0xGo3RZC3jeqm_Xuz26Z-dLdihZt-spe8smDnn9EmRRmr7GMOd06-MWA",
  "payload": ""
}
2025-05-09 13:51:19,952:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900217 HTTP/1.1" 200 828
2025-05-09 13:51:19,952:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:19 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDptTjRmBhR94Lej7kmdtuBgFdtaTbN7JlHo-3RrZ-mhc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T09:53:39Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/d6GZQQ",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/ko8e1w",
      "status": "pending",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q"
    }
  ]
}
2025-05-09 13:51:19,953:DEBUG:acme.client:Storing nonce: yPpvzgaDptTjRmBhR94Lej7kmdtuBgFdtaTbN7JlHo-3RrZ-mhc
2025-05-09 13:51:19,953:INFO:certbot._internal.auth_handler:Challenge failed for domain cz-test.sawapp.cloud
2025-05-09 13:51:22,952:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:51:22,955:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJ5UHB2emdhRHB0VGpSbUJoUjk0TGVqN2ttZHR1QmdGZHRhVGJON0psSG8tM1JyWi1taGMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3In0",
  "signature": "Zwu5zJ-KqAUb3PE3dNO4Pc51avXIjh8H13oPn3jm--9kOMYu6YJIhUPKCehJJnF4gz_7JPgEF6bmopu4bxkRVzLpJ-uTrwzSFFD0jebcSdOj00y0qyerkleVwJKkbBenZFOEKZKUwtpvoJHyBNiSLFdG7IipFZmFPTqMtUjgmpOmDGkeU47oczq7yqWPN7YXVStva1RM2FMNrR2aQmAHARFgKP9LWqjpkUPnZ_8hCSeIjgGi-p_GqWznuJpJTQBbF9Ojq5OHsb8Ztraz_GJs8II7j0GnjzEo-g5ArntP4c5IAyiZ-C-ui1YDgN2A4vkW92xEF28GrM4q0U5gLqJccQ",
  "payload": ""
}
2025-05-09 13:51:23,135:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900217 HTTP/1.1" 200 523
2025-05-09 13:51:23,136:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:51:23 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: yPpvzgaDInNvtjmUpgKiFSxp8kQX4B5BdkQidGuoJjTnnoglnyk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:20Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
      "status": "valid",
      "validated": "2025-05-09T11:51:18Z",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 13:51:23,136:DEBUG:acme.client:Storing nonce: yPpvzgaDInNvtjmUpgKiFSxp8kQX4B5BdkQidGuoJjTnnoglnyk
2025-05-09 13:51:23,136:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-05-09 13:51:23,137:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: cz-test.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "fXJfOlQOdPWwQ3x02YGzhrHarRyElerye4JAxKKDxa8" found at _acme-challenge.cz-test.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2025-05-09 13:51:23,141:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2025-05-09 13:51:23,142:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-05-09 13:51:23,142:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-05-09 13:51:23,143:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1871, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1577, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 130, in _get_and_save_cert
    renewal.renew_cert(config, domains, le_client, lineage)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/renewal.py", line 399, in renew_cert
    new_cert, new_chain, new_key, _ = le_client.obtain_certificate(domains, new_key)
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 423, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 492, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/3.3.0/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2025-05-09 13:51:23,147:ERROR:certbot._internal.log:Some challenges have failed.
