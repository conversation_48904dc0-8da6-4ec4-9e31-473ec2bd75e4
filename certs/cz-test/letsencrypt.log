2025-05-09 13:52:05,809:DEBUG:certbot._internal.main:certbot version: 3.3.0
2025-05-09 13:52:05,810:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-05-09 13:52:05,810:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2025-05-09 13:52:05,810:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-05-09 13:52:05,824:DEBUG:certbot._internal.log:Root logging level set at 30
2025-05-09 13:52:05,825:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 13:52:05,825:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x102e71be0>
Prep: True
2025-05-09 13:52:05,825:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x102e71be0> and installer None
2025-05-09 13:52:05,825:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-05-09 13:52:05,861:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=datetime.timezone.utc), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-05-09 13:52:05,870:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-05-09 13:52:05,873:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-05-09 13:52:06,384:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/1.1" 200 1012
2025-05-09 13:52:06,385:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:52:06 GMT
Content-Type: application/json
Content-Length: 1012
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "https://letsencrypt.org/docs/profiles#classic",
      "shortlived": "https://letsencrypt.org/docs/profiles#shortlived (not yet generally available)",
      "tlsserver": "https://letsencrypt.org/docs/profiles#tlsserver"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.5-February-24-2025.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "oBZMDj6C3PY": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-05-09 13:52:06,412:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-05-07 15:12:35 UTC.
2025-05-09 13:52:06,413:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-05-09 13:52:06,413:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2025-05-09 13:52:06,413:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 13:52:06,413:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 13:52:06,587:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 13:52:06,588:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:52:06 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB23xqgUkmyhuYcSV9xajYJ71WTNuaC_fVPBXho_HCEmqA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 13:52:06,588:DEBUG:acme.client:Storing nonce: QmouDpB23xqgUkmyhuYcSV9xajYJ71WTNuaC_fVPBXho_HCEmqA
2025-05-09 13:52:06,588:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2025-05-09 13:52:06,592:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "F8yFDgi4G7GnqgCFGAir0pEVvpilA4IdUmwiRWSp9IyNV6VWBXk9PtSDufP1doqYMKABnhrNn8WsiGE6tUfeqKLZkh3R1snISY05v-62DxGfMWSS38QBj78sHVaY531OQrxYi6rbFgQbKhogfApnUJ0PQq9KAJOLjfKzP92EB2F_UMrpO1V2ZbgWTZJ-S8xhVFwn0o5qlDkSPbTEgtF0Mu8CHBTf-p1tjr6bgcZDYVBZtRm0ENN1XaFmMB6LHp6-0IPZbPO5LrGqetaimwdGxvZVVpH5Fq_sjUA9qmT3KZIwgaEcmI0iOMmbhTGGMB_Q4aNb7PcNWQWQTTo3AyahyA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2025-05-09 13:52:06,798:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/1.1" 201 507
2025-05-09 13:52:06,799:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Fri, 09 May 2025 11:52:06 GMT
Content-Type: application/json
Content-Length: 507
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382359008497
Replay-Nonce: QmouDpB2n-OyX0IH83KO6WYI0zBGYWy4YqYwM_ePvRozZDuHIGY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-05-16T11:52:06Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382359008497"
}
2025-05-09 13:52:06,799:DEBUG:acme.client:Storing nonce: QmouDpB2n-OyX0IH83KO6WYI0zBGYWy4YqYwM_ePvRozZDuHIGY
2025-05-09 13:52:06,800:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:52:06,802:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJRbW91RHBCMm4tT3lYMElIODNLTzZXWUkwekJHWVd5NFlxWXdNX2VQdlJvelpEdUhJR1kiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3In0",
  "signature": "fZ6c-vTg6DEc9wRB8hujJZrCecQm6E6kXgdV3v-ptZhWusJDzUpCx1LFNgRVKwdRNvcCMQAZ0vNxerhkObhuNzaPpUtx6EqiTTWxrKc8Z_F1d6U5-83ojkVyelxHCJaARkVFD9FHjDYh6z46bYv4QyKmOkM9iBwXX87riz9D5uS56zCAj1aezNvjvAlItPQI9MVHMfJSQTmymnfctBVb5OejWndZRbapF_PzyaGgQPv2VU-HKx2YnETljNk6CpDg2oOlK8t2oKgg13-GseBRBphAFsKsYSiGLX47vPfaGb-pNuWwGnlu4iNHLEO6NzV9hDnJ1uY4omLfAIzINDuZVA",
  "payload": ""
}
2025-05-09 13:52:06,985:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900217 HTTP/1.1" 200 523
2025-05-09 13:52:06,986:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:52:06 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2OVWUOQjEs1PTY9EUKssgJQICoym0DWLmpJwC7t579Mg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:20Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
      "status": "valid",
      "validated": "2025-05-09T11:51:18Z",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 13:52:06,986:DEBUG:acme.client:Storing nonce: QmouDpB2OVWUOQjEs1PTY9EUKssgJQICoym0DWLmpJwC7t579Mg
2025-05-09 13:52:06,986:DEBUG:acme.client:JWS payload:
b''
2025-05-09 13:52:06,989:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "X-ilI0vfPy5hDNpRPTJ8Mrto_J2kcltudFWrqNoC9BxTUdXFsz50qnw5NiaShw7Fnrmd3FedDPy7PhOcrJ0icXwzwnBAppjne6-BwwREOuaeWlVsrkRJGYeq75XgN0G0ZN1cxvnCnOqVxmsUHoAGa3OTNfw2oBfE0rmiw_8dpB6RiBPGwUFTu0-aPUZ7hzfgkouyNFfDyo3m1dFuvuH4lR-RsLawQSo1tTYvJ7JvqkG2G95pqUeDvNJeD7UhjjcNm3iEtEMNN9wTD1nKWn_qzs3hhn2BJz377xderF6j0efgs0x_kzoDnL-7ciTUE4jlqOKwGxO7g9Sm_AfXMorIag",
  "payload": ""
}
2025-05-09 13:52:07,159:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791150787 HTTP/1.1" 200 402
2025-05-09 13:52:07,159:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 11:52:07 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2EhtuyBqG5FH2dqIHPdd1biXXaJPtvOUXNlbUyR7WRpQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T11:52:06Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg",
      "status": "pending",
      "token": "gsgFP3SYnFRfrke3NBaKRHQ093nyRGK3rx6EhEIqG5s"
    }
  ],
  "wildcard": true
}
2025-05-09 13:52:07,159:DEBUG:acme.client:Storing nonce: QmouDpB2EhtuyBqG5FH2dqIHPdd1biXXaJPtvOUXNlbUyR7WRpQ
2025-05-09 13:52:07,160:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-05-09 13:52:07,160:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-05-09 13:52:07,161:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

pXVqW-o0ZTq4mh_BZGKR3vlykttlWLAPROx3Ws3KGLg

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-05-09 14:45:24,758:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 14:45:24,764:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "G-Z1K5TCujYscBr6uw-A8xl26kGQi_9U2JTS-KgumUPCS2ibw1x5qd5ifTPqv4d24b4C4_Bf9XR7JvG_rAZX1uBHy6hJd53jPMtYH0mnPIln21N2zyDb1KLTGgToQ97odCRW7SWzavjEi1jtRj726mSfBgr6lRLYEzsVb5JrbahtDWUqTKE7999TdteBCMZ78aYIbFRU9EKF0VL2xrlNlaOWx5icMDJM-UhWI3gE2-UpGG-dpoDcy_lN5Jt9DVgfrR3LALA7pECoIIEqouH5cfRDQNGFgX8RoULOrqNmXDnSswU3Agvtqp7uheCBfOBRocVltJnOnUCrOCtq2BMi4w",
  "payload": "e30"
}
2025-05-09 14:45:24,768:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-05-09 14:45:25,284:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517791150787/nG8BIg HTTP/1.1" 400 203
2025-05-09 14:45:25,285:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Fri, 09 May 2025 12:45:25 GMT
Content-Type: application/problem+json
Content-Length: 203
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB26VGPJwBVo2ux0Z6HMGEhaDYtRqcPxvafHd12W0b06xo

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "Unable to validate JWS :: JWS has an invalid anti-replay nonce: \"QmouDpB2EhtuyBqG5FH2dqIHPdd1biXXaJPtvOUXNlbUyR7WRpQ\"",
  "status": 400
}
2025-05-09 14:45:25,286:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: Unable to validate JWS :: JWS has an invalid anti-replay nonce: "QmouDpB2EhtuyBqG5FH2dqIHPdd1biXXaJPtvOUXNlbUyR7WRpQ"
2025-05-09 14:45:25,286:DEBUG:acme.client:Requesting fresh nonce
2025-05-09 14:45:25,286:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-05-09 14:45:25,452:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/1.1" 200 0
2025-05-09 14:45:25,453:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:25 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6NZ1t0lb_cSIonH_MinVXm21rfLykY0ZuXFKBwTiWn-DQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-05-09 14:45:25,454:DEBUG:acme.client:Storing nonce: ww5-sb6NZ1t0lb_cSIonH_MinVXm21rfLykY0ZuXFKBwTiWn-DQ
2025-05-09 14:45:25,454:DEBUG:acme.client:JWS payload:
b'{}'
2025-05-09 14:45:25,456:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJ3dzUtc2I2TloxdDBsYl9jU0lvbkhfTWluVlhtMjFyZkx5a1kwWnVYRktCd1RpV24tRFEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNTYyNDgyOTcvNTE3NzkxMTUwNzg3L25HOEJJZyJ9",
  "signature": "OXX2wvxHFd8ZMJgQ7lSeQXz0QpWeqTiL8kfO5sNgDVCU_LoHSwW6dbkGt1tRgNd4jfz5s6faoPFA-b5WItUsHs_Jn_N5zBk9EQ-5E-erYQx8tGkul8LpkB5DgpGX6fQEs2ENdr-bWApH-YGwOgnAvwYSBQ9Y7IsSmjoLs0rbOq7-m7h6a-E2yA7abNOXQ0qGLQozsHExJo9cw9Bf6MhD_l7kzNIoZ5xKSvY3xiajewmftPEkc4bbeRVyrm0QwfbheNR3jPSUGGkgzBeAa2kXHAU-d00MWJFpZTHE9Pa4w6FmeOmG_FUt1ajIFqzREAyt4_J8t_Gfx6vcL3MDJG0G3w",
  "payload": "e30"
}
2025-05-09 14:45:25,627:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/517791150787/nG8BIg HTTP/1.1" 200 194
2025-05-09 14:45:25,628:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:25 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg
Replay-Nonce: QmouDpB2Ra-tG5RidUvryyIujpqJGwJWxiqua6l021ZlH4neBA8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg",
  "status": "pending",
  "token": "gsgFP3SYnFRfrke3NBaKRHQ093nyRGK3rx6EhEIqG5s"
}
2025-05-09 14:45:25,628:DEBUG:acme.client:Storing nonce: QmouDpB2Ra-tG5RidUvryyIujpqJGwJWxiqua6l021ZlH4neBA8
2025-05-09 14:45:25,629:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-05-09 14:45:26,634:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:26,638:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJRbW91RHBCMlJhLXRHNVJpZFV2cnl5SXVqcHFKR3dKV3hpcXVhNmwwMjFabEg0bmVCQTgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzUzOTAwMjE3In0",
  "signature": "RIJ7WMv02CKyf5xDgK4abr_t4Q693H7ycJPaYtxEuXPzYIj0SgTvU1QCA2Ju9ilAgIQxaqAseoYdxD364NMvu3Xhv85p6HkKWhaEw3ldZgRtlOYz_3rG9DvjPQgg89FO_8-qHA3YVcCcRUzBT7wzM8vLpzefUrKS--McYPwdKz8FCEDztTjp6WeFP17VAxSR4mRGp6H8bU12J8k9DcDER2l9LRIA4E4vdIvX9Qf5tq6FgihqzRk7EHUA7RVF4uNTcnYHvnUhr-XNjNpuIVg_lpRCE9YxhaK0BYRcKZY_0nD-5nkBm5g3zgkZocOM7qkuzFIPvQxehijRkkNudC6JnA",
  "payload": ""
}
2025-05-09 14:45:26,814:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517753900217 HTTP/1.1" 200 523
2025-05-09 14:45:26,815:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:26 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2NLhePyxlWpMIZ-2tkI-3NXza_la5TuOB2h_0qOOG21Y
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T11:51:20Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517753900217/IaRjJA",
      "status": "valid",
      "validated": "2025-05-09T11:51:18Z",
      "token": "KoS3XzOlFrqHALVzsLwM4mMqU7iZqocAxic8spxtS4Q",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-05-09 14:45:26,815:DEBUG:acme.client:Storing nonce: QmouDpB2NLhePyxlWpMIZ-2tkI-3NXza_la5TuOB2h_0qOOG21Y
2025-05-09 14:45:26,816:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:26,819:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJRbW91RHBCMk5MaGVQeXhsV3BNSVotMnRrSS0zTlh6YV9sYTVUdU9CMmhfMHFPT0cyMVkiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzkxMTUwNzg3In0",
  "signature": "PVJYdG-WDOUlsVBFvC4qIYU3SVvH2NuBrkAh7q9G-mG3nEu3hxyh1am3-Z3FDj7iO2GoNIGGMCzD1ZaSofLe8YhRpyuQgEus2P2u9F_LIU_PM_OsKYFQHmKgufsmUZr7wHg_lwWQ2eCqiHsv5hH5ZqA26cwnHb2ngaffwaUD3c88OdVLkF4zbgr2MOItQP1rfVqT4mLTAzeuFKJosG6H6t-mvi7h7QPcGbgWjBdlpBlKV0hko_V7JwmOHOtLZf3Fi_irwE2H0S130flYPRz1taIXTUAao1z_EQ-wc8j2s9ZGK4OrEPgUWCO5AGAfgPKF_nDeQPWRy7uG1pXEcat9KA",
  "payload": ""
}
2025-05-09 14:45:26,989:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791150787 HTTP/1.1" 200 402
2025-05-09 14:45:26,990:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:26 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6NDbMkjON07TVjHpPs0zNawWlEXaV0XAxtg0Qd3X0Bg9k
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-05-16T11:52:06Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg",
      "status": "pending",
      "token": "gsgFP3SYnFRfrke3NBaKRHQ093nyRGK3rx6EhEIqG5s"
    }
  ],
  "wildcard": true
}
2025-05-09 14:45:26,990:DEBUG:acme.client:Storing nonce: ww5-sb6NDbMkjON07TVjHpPs0zNawWlEXaV0XAxtg0Qd3X0Bg9k
2025-05-09 14:45:29,993:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:29,996:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJ3dzUtc2I2TkRiTWtqT04wN1RWakhwUHMwek5hd1dsRVhhVjBYQXh0ZzBRZDNYMEJnOWsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNTE3NzkxMTUwNzg3In0",
  "signature": "lvc7JkjcoRV87gSovOma15nvDNeGVpes7PkTFxxm1gaHClqOTynTm53_GmsUOjgD32II3zWBtONoaCc14sJP_fm_09SAIqhvKcLjAlXJk1JykTqbpcTqqTN_QzAjaEVzaDMKzEZRngwaGXzQZx9ihUSorThASmEga2KqTOY60hZNxSTnscSyzifBj5bFQ2xz9S1gVI504CLkvhBrNCbEk8jUKrHV2WvGk-FG2aYgpD9XOOCehcBd2v4jka12y7EXBckwzxCrtoOnyN9CQBHGypkSci-RAw1krPIKz-UQgEmSIJdf_KvUrhM0VGd83ZEEGVlGzkFdqsCKTeItncgiew",
  "payload": ""
}
2025-05-09 14:45:30,180:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/517791150787 HTTP/1.1" 200 543
2025-05-09 14:45:30,181:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:30 GMT
Content-Type: application/json
Content-Length: 543
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: ww5-sb6N-0vlHF1sqLwx1vwNHeNiRvd6OpiR-6p3GbpBYF0TJZc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-06-08T12:45:27Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/517791150787/nG8BIg",
      "status": "valid",
      "validated": "2025-05-09T12:45:25Z",
      "token": "gsgFP3SYnFRfrke3NBaKRHQ093nyRGK3rx6EhEIqG5s",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2025-05-09 14:45:30,181:DEBUG:acme.client:Storing nonce: ww5-sb6N-0vlHF1sqLwx1vwNHeNiRvd6OpiR-6p3GbpBYF0TJZc
2025-05-09 14:45:30,182:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-05-09 14:45:30,183:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-05-09 14:45:30,183:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBBTCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENWxamnZdU791\njIL6hr9D55Hd1AdJlwrguHLv1Id4VPDMPpoW0GtqAgTE/Ob2SGfxqFaUPE2mZtL6\nfT6UF6ozAaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0\nLnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwID\nSAAwRQIhAPKeCz6KsrOUL4Sx7kXsdDvyoFE9FZ7TZQKAnVM6iwTOAiBH90jk4Mgc\ntdAHUovfsfytNHocjYOfBHPn6Pv7u4nkjA==\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2025-05-09 14:45:30,184:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2025-05-09 14:47:00.184762
2025-05-09 14:45:30,188:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBBTCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENWxamnZdU791jIL6hr9D55Hd1AdJlwrguHLv1Id4VPDMPpoW0GtqAgTE_Ob2SGfxqFaUPE2mZtL6fT6UF6ozAaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0LnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwIDSAAwRQIhAPKeCz6KsrOUL4Sx7kXsdDvyoFE9FZ7TZQKAnVM6iwTOAiBH90jk4MgctdAHUovfsfytNHocjYOfBHPn6Pv7u4nkjA"\n}'
2025-05-09 14:45:30,190:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382359008497:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJ3dzUtc2I2Ti0wdmxIRjFzcUx3eDF2d05IZU5pUnZkNk9waVItNnAzR2JwQllGMFRKWmMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNTYyNDgyOTcvMzgyMzU5MDA4NDk3In0",
  "signature": "Y9C69ZWg-Zz_knihWd3-eHBwHiXBE9vuD71hdWE2-xuKlRYf84Qay-rGNexCSc9oO8wucQKTx2EhCiUtLzTODPZ8rFYiVMlJck83CbE3tJwR2EeGfl7At6qK-R-u_CHwmyELYq5qpnxDrzV_0RSHgW8Z9-lPe4xQEX_ArTeRWm400LECTc6V2YtFxA0nBU6BzMfNX0JzpoDjTxFd82Fhde7Qlg-h-_y4Jm3U74w1_njxs8jQP0fpPe4luHQ2qFG0wFWbfd9IIR0j7L1AgbuodXU3lB4Bcg1ypvL5V1JQ2rIuNN-dOjRC1OijlpQ3fQ303Hj9TFbzs0gHfhzkLWFd-Q",
  "payload": "ewogICJjc3IiOiAiTUlJQkJUQ0JyQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRU5XeGFtblpkVTc5MWpJTDZocjlENTVIZDFBZEpsd3JndUhMdjFJZDRWUERNUHBvVzBHdHFBZ1RFX09iMlNHZnhxRmFVUEUybVp0TDZmVDZVRjZvekFhQktNRWdHQ1NxR1NJYjNEUUVKRGpFN01Ea3dOd1lEVlIwUkJEQXdMb0lXS2k1amVpMTBaWE4wTG5OaGQyRndjQzVqYkc5MVpJSVVZM290ZEdWemRDNXpZWGRoY0hBdVkyeHZkV1F3Q2dZSUtvWkl6ajBFQXdJRFNBQXdSUUloQVBLZUN6Nktzck9VTDRTeDdrWHNkRHZ5b0ZFOUZaN1RaUUtBblZNNml3VE9BaUJIOTBqazRNZ2N0ZEFIVW92ZnNmeXROSG9jallPZkJIUG42UHY3dTRua2pBIgp9"
}
2025-05-09 14:45:32,874:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/382359008497 HTTP/1.1" 200 609
2025-05-09 14:45:32,875:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:32 GMT
Content-Type: application/json
Content-Length: 609
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/382359008497
Replay-Nonce: QmouDpB2jvAT4-ebEI6WWiJ98RB5RzZ-9NpyphcN_8WpZYbOUP8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-05-16T11:52:06Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382359008497",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/055cb66f50b8bcc1ffb4b630f72d5b0c6be6"
}
2025-05-09 14:45:32,875:DEBUG:acme.client:Storing nonce: QmouDpB2jvAT4-ebEI6WWiJ98RB5RzZ-9NpyphcN_8WpZYbOUP8
2025-05-09 14:45:33,879:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:33,882:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/382359008497:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJRbW91RHBCMmp2QVQ0LWViRUk2V1dpSjk4UkI1UnpaLTlOcHlwaGNOXzhXcFpZYk9VUDgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNTYyNDgyOTcvMzgyMzU5MDA4NDk3In0",
  "signature": "K-Pmkz7RWFEqh4yj-srCKNVkXTanDa9sF2fEeQIKCvFtGX2WRqOXmO55WPjYoTpq5U_Mjm8UPizkFX1IFWZPDSypi-5gVdD5mJdA0w7LxxZ12-pU_4jfuEkZP8s3htB3FiVWp-K2ZG2oVDTiAbTFC6HTyP45VMfLRZ6rCw1jmf-2xRalL-g0yKV0UVTKDrIl1-1r8NPcy7qkJaOI8SepwxLSE3CHrIQj8dUcPyDXeCR94AT0WMvZnYfCgr2mo3WG92Ncpq1rSJMjFAIq765rUAgi7RlY-_9kQuPZgP5MAFVPZmiZ1KaKsKLUY0b0oTGMnsCgjIFjASHj0EBmPeZF9g",
  "payload": ""
}
2025-05-09 14:45:34,054:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/382359008497 HTTP/1.1" 200 609
2025-05-09 14:45:34,054:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:34 GMT
Content-Type: application/json
Content-Length: 609
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: QmouDpB2w3KB1BemrOd9dSYUck-vovAr2l07nhgG6OPfWznlvOA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-05-16T11:52:06Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517753900217",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/517791150787"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/382359008497",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/055cb66f50b8bcc1ffb4b630f72d5b0c6be6"
}
2025-05-09 14:45:34,055:DEBUG:acme.client:Storing nonce: QmouDpB2w3KB1BemrOd9dSYUck-vovAr2l07nhgG6OPfWznlvOA
2025-05-09 14:45:34,055:DEBUG:acme.client:JWS payload:
b''
2025-05-09 14:45:34,058:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/055cb66f50b8bcc1ffb4b630f72d5b0c6be6:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJRbW91RHBCMnczS0IxQmVtck9kOWRTWVVjay12b3ZBcjJsMDduaGdHNk9QZld6bmx2T0EiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDU1Y2I2NmY1MGI4YmNjMWZmYjRiNjMwZjcyZDViMGM2YmU2In0",
  "signature": "DFPBSSlgiRXqee-tMeqvoyvs5wBr4JDpZ-XgJOiwiENMbTFljBZ9IkPyqCi1LQoytoWDt3qmDnMbt2_s0E936dAT_Zl56CnsohxteVvZ6-oMUVzDuSLZBXsPsYKAvYOqAlf8fhP04d-xeBMyik59GbZ4ZLSjUq00ofGpKs57pDhNW4IpYUS5wUie62gUDSx5SJ4zivSwSQmOB1vJysgY6E6hWNF7d0CuQunX-wCeGlNf7ewYS5yLsnGNEEv1ETzbukOWZ7fRbkMWRinlvavj7v7iQhYPuQV_MbII-6Q3jKtNrf_RgjO-OrJifi7WUaOxqENH7i6qZeBHbF9iYH1BZA",
  "payload": ""
}
2025-05-09 14:45:34,232:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/055cb66f50b8bcc1ffb4b630f72d5b0c6be6 HTTP/1.1" 200 2905
2025-05-09 14:45:34,233:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Fri, 09 May 2025 12:45:34 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2905
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/055cb66f50b8bcc1ffb4b630f72d5b0c6be6/1>;rel="alternate"
Replay-Nonce: QmouDpB2B3wVA1ALRaVjK-5PIuQ4NoC8-ThG8QHFfmfX_H5ERKM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDsDCCAzWgAwIBAgISBVy2b1C4vMH/tLYw9y1bDGvmMAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NTAeFw0yNTA1MDkxMTQ3MDBaFw0yNTA4MDcxMTQ2NTlaMCExHzAdBgNVBAMMFiou
Y3otdGVzdC5zYXdhcHAuY2xvdWQwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAQ1
bFqadl1Tv3WMgvqGv0Pnkd3UB0mXCuC4cu/Uh3hU8Mw+mhbQa2oCBMT85vZIZ/Go
VpQ8TaZm0vp9PpQXqjMBo4ICOjCCAjYwDgYDVR0PAQH/BAQDAgeAMB0GA1UdJQQW
MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBQx
OscOMN5EjyJ2RRDTWJ7ozPLG7zAfBgNVHSMEGDAWgBSfK1/PPCFPnQS37SssxMZw
i9LXDTAyBggrBgEFBQcBAQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly9lNS5pLmxl
bmNyLm9yZy8wNwYDVR0RBDAwLoIWKi5jei10ZXN0LnNhd2FwcC5jbG91ZIIUY3ot
dGVzdC5zYXdhcHAuY2xvdWQwEwYDVR0gBAwwCjAIBgZngQwBAgEwLgYDVR0fBCcw
JTAjoCGgH4YdaHR0cDovL2U1LmMubGVuY3Iub3JnLzEwMy5jcmwwggEDBgorBgEE
AdZ5AgQCBIH0BIHxAO8AdgAS8U40vVNyTIQGGcOPP3oT+Oe1YoeInG0wBYTr5YYm
OgAAAZa1FFOlAAAEAwBHMEUCIHAiX6N/Y+D4SaX9AQu8C9jceClxU7XBBavR8WXk
qak0AiEA1pUo4h0Cjz7W/55fOjxwTugywjCV8Z7/dIw1O2S0JNoAdQCvGBoo1oyj
4KmKTJxnqwn4u7wiuq68sTijoZ3T+bYDDQAAAZa1FFaIAAAEAwBGMEQCIF/tocCH
jGQQQOal3AuF+cbDR/sIHb1ljT/l80TdynuJAiA59H8vDrjb/ZoqQbFvLwy/FPIm
gv2ZbnEb8Ql0yA69JzAKBggqhkjOPQQDAwNpADBmAjEA3htnakWoY86Kf86A6cjE
FI7SX9p6PXMy2Q0egVuuAhVO1fvoZiPikbnend2j3yOFAjEAkLO32iTHSGJuCAcP
Ax+GW+KyLO7W6xZagsbvVK8dnH+GDFsWvFmYMjdSKQoTx9Ow
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRAIOPbGPOsTmMYgZigxXJ/d4wDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTUwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNCzqK
a2GOtu/cX1jnxkJFVKtj9mZhSAouWXW0gQI3ULc/FnncmOyhKJdyIBwsz9V8UiBO
VHhbhBRrwJCuhezAUUE8Wod/Bk3U/mDR+mwt4X2VEIiiCFQPmRpM5uoKrNijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSfK1/PPCFPnQS37SssxMZw
i9LXDTAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAH3KdNEVCQdqk0LKyuNImTKdRJY1C
2uw2SJajuhqkyGPY8C+zzsufZ+mgnhnq1A2KVQOSykOEnUbx1cy637rBAihx97r+
bcwbZM6sTDIaEriR/PLk6LKs9Be0uoVxgOKDcpG9svD33J+G9Lcfv1K9luDmSTgG
6XNFIN5vfI5gs/lMPyojEMdIzK9blcl2/1vKxO8WGCcjvsQ1nJ/Pwt8LQZBfOFyV
XP8ubAp/au3dc4EKWG9MO5zcx1qT9+NXRGdVWxGvmBFRAajciMfXME1ZuGmk3/GO
koAM7ZkjZmleyokP1LGzmfJcUd9s7eeu1/9/eg5XlXd/55GtYjAM+C4DG5i7eaNq
cm2F+yxYIPt6cbbtYVNJCGfHWqHEQ4FYStUyFnv8sjyqU8ypgZaNJ9aVcWSICLOI
E1/Qv/7oKsnZCWJ926wU6RqG1OYPGOi1zuABhLw61cuPVDT28nQS/e6z95cJXq0e
K1BcaJ6fJZsmbjRgD5p3mvEf5vdQM7MCEvU0tHbsx2I5mHHJoABHb8KVBgWp/lcX
GWiWaeOyB7RP+OfDtvi2OsapxXiV7vNVs7fMlrRjY1joKaqmmycnBvAq14AEbtyL
sVfOS66B8apkeFX2NY4XPEYV4ZSCe8VHPrdrERk2wILG3T/EGmSIkCYVUMSnjmJd
VQD9F6Na/+zmXCc=
-----END CERTIFICATE-----

2025-05-09 14:45:34,233:DEBUG:acme.client:Storing nonce: QmouDpB2B3wVA1ALRaVjK-5PIuQ4NoC8-ThG8QHFfmfX_H5ERKM
2025-05-09 14:45:34,236:DEBUG:certbot._internal.storage:Writing new private key to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/privkey3.pem.
2025-05-09 14:45:34,237:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert3.pem.
2025-05-09 14:45:34,237:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/chain3.pem.
2025-05-09 14:45:34,238:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/fullchain3.pem.
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var account=672abefd0655bb9562de0097bd749ed5 (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2025-05-09 14:45:34,250:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 14:45:34,250:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-05-09 14:45:34,251:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/cz-test/renewal/cz-test.sawapp.cloud.conf.new.
2025-05-09 14:45:34,255:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/privkey.pem
This certificate expires on 2025-08-07.
These files will be updated when the certificate renews.
2025-05-09 14:45:34,256:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2025-05-09 14:45:34,256:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2025-05-09 14:45:34,256:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
