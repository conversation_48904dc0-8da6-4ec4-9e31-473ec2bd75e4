2024-11-14 13:15:39,873:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-14 13:15:39,873:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-14 13:15:39,873:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2024-11-14 13:15:39,873:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-14 13:15:39,885:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-14 13:15:39,885:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 13:15:39,885:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x1030ee510>
Prep: True
2024-11-14 13:15:39,885:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x1030ee510> and installer None
2024-11-14 13:15:39,885:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-14 13:15:39,919:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-14 13:15:39,926:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-14 13:15:39,930:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-14 13:15:40,460:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-14 13:15:40,461:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:40 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "XljT-S-PmMg": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-14 13:15:40,463:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2024-11-14 13:15:40,468:DEBUG:acme.client:Requesting fresh nonce
2024-11-14 13:15:40,468:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-14 13:15:40,613:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-14 13:15:40,614:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:40 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJGON0L5atB8YMLIXxMWk6vjdAi_Hx30coio12YoSsqxU8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-14 13:15:40,615:DEBUG:acme.client:Storing nonce: bBPm6dJGON0L5atB8YMLIXxMWk6vjdAi_Hx30coio12YoSsqxU8
2024-11-14 13:15:40,615:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2024-11-14 13:15:40,621:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "N_DXEpJUGffACeZjuFeYH7anbftd5YwD9leq8WQM3DnxdiMJBw6Scmrm6M81Atu58eLZwzVXhHGnpuv8oUGax32S-bMQ-1CM3I_0-pVrsjOpXfgamBGk2hloGcdrGJThBAxTZ-ZH2ubBsErTXzMf2xc1eg7z7pvDJ5MwHmZPUZl5-zvhgJAbDUB9G-_w1-jZYfmc_LCy18TU6B-kUeZRrlHxos6ldyWobpoExgENdOZ5oe2KFVo89HQgOV2tR8BV--32wG9f2_-DGUiV-qv9ND3nFcw1DMWpo76Ip2LoUikGpsoyRfvk9tMfGM9UX6_QrB1-bqL7p44zAtfBG14qYA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2024-11-14 13:15:41,028:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" ************-11-14 13:15:41,028:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 14 Nov 2024 12:15:40 GMT
Content-Type: application/json
Content-Length: 491
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/323031246857
Replay-Nonce: bBPm6dJGlqr_Oy8IUOtFURZpnwlYF1pEcflPtAU-B2DloG3WRZo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-21T12:15:40Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/430020975477"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323031246857"
}
2024-11-14 13:15:41,029:DEBUG:acme.client:Storing nonce: bBPm6dJGlqr_Oy8IUOtFURZpnwlYF1pEcflPtAU-B2DloG3WRZo
2024-11-14 13:15:41,029:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:41,033:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "********************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "I5YVge2Fevax9gM2T7j6MgGYzjYfA-JW3di_B86QLxEoimqnarhXNefYCGLQ86_VjeA62UTJZlFgqnohwqDmoPqOktST7fA4QdmnSoCYTPIfsSOiRCrQjmf5wlekP1ZU2F93g8b9UsdhvlnBjfPLbU5vEQZ7PD5Yvl_IwFo5UEKlQ1ngkz_PE_3ldFhz83DZkiwM7GMS8QTe3yl9CqwV9BBiN52H9PD1bCLjGUv80yKUekWilGDIYCM6ZAUCwUBdDG976Wx5C59eg8LOHPzwuGL-6nlOhZtYWZaeFaxaIUm1BfXksJlN9iHCM8GRI4EC6iAXSxdhHZnuOg5V0A42Bg",
  "payload": ""
}
2024-11-14 13:15:41,196:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" ************-11-14 13:15:41,197:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:41 GMT
Content-Type: application/json
Content-Length: 515
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJGtbgVZ8ZpphRVLZh9B-Ajru9uS5SzyWoUMTe9U9VPrpw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:15:31Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "valid",
      "validated": "2024-11-14T12:15:30Z",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-14 13:15:41,197:DEBUG:acme.client:Storing nonce: bBPm6dJGtbgVZ8ZpphRVLZh9B-Ajru9uS5SzyWoUMTe9U9VPrpw
2024-11-14 13:15:41,198:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:41,203:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430020975477:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJiQlBtNmRKR3RiZ1ZaOFpwcGhSVkxaaDlCLUFqcnU5dVM1U3p5V29VTVRlOVU5VlBycHciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMDk3NTQ3NyJ9",
  "signature": "KuPeQuwY3bbeHb6LD59DsSe8Wa09mgUkr4OZOJ7ziXGBOsSEew9RJqh6ZwfdmwWeuPJM8LgTbPE3H8NcymbWHZaBdLsBP_TCIv0oa9BcG873y7jJ9uBF0RNtywCcZsbdcAM9ePu8w1412kWjTJKyY1SMjQR_UCIUJ2Y25mIi2579yPyayPakkjn-y4FLuJzCFwI47j56_V3y9NEbELRMWFHyHs-YIwXeREYcn2nf5LA31AfYSKncLatrvxM_w7Pzbem0rnnxtHb7BVK243fyoGzXlIQ4mAH_FwSdQCcMV4XiL8uE53xe1_dcpx3nYqX4zJJxrmj5LhjjFP3olgQWMw",
  "payload": ""
}
2024-11-14 13:15:41,363:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430020975477 HTTP/11" 200 394
2024-11-14 13:15:41,363:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:41 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJGsOGwseYGHgsRBWxuDdt37-bLyk6P1wzoWiRA4SatYfE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:40Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430020975477/h5rYxA",
      "status": "pending",
      "token": "Lxkjv7_GjzbkcGcC53ycuLuOXJoqrnzJ1W1C9HX_vGU"
    }
  ],
  "wildcard": true
}
2024-11-14 13:15:41,364:DEBUG:acme.client:Storing nonce: bBPm6dJGsOGwseYGHgsRBWxuDdt37-bLyk6P1wzoWiRA4SatYfE
2024-11-14 13:15:41,364:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-14 13:15:41,365:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 13:15:41,366:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

em5EvLAAJ35hWMvgkN-MtJ2CvGAl1sDQj9EvoDCNDqk

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-14 13:15:45,795:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:15:45,798:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/430020975477/h5rYxA:
{
  "protected": "******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "N_KPl4kMcINUobqfueJ9NHoNnOGExQHT2pR-8wgcVTyjq-FMVXHggacE5e-WB5UQe30dtwEZxZpPUAorGk5D5Tfc8X5XSH4e8QZ6la03YzYAR9T-oH7yzlLhnjB0gyOP7cyZOAPe75hPVc5skdf9FdMYpZOmjt2ukiUr4K8tUzg3Hoqct8s-fNKNWCAGBdaHCMGw2yWN9e4ZduLBuLkIdXdsWBZE_MbIvcPwezDaXNbDiOJVzXKeYlZ3wQu9JJkEzgXSNtb4eqdD_6j-5FLFFnHZHEtONEpq5uKkOzbFb3IAXLTWglPuY5z-9ManUl7yjLAlpiGgu-qqlIoV6xaBbg",
  "payload": "e30"
}
2024-11-14 13:15:45,980:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/430020975477/h5rYxA HTTP/11" 200 186
2024-11-14 13:15:45,981:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:45 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/430020975477>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/430020975477/h5rYxA
Replay-Nonce: SEOe4Whp2timc0GA-aCG7aIyiRoQUgzqOhp5raXoLSuqd3qf5yc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430020975477/h5rYxA",
  "status": "pending",
  "token": "Lxkjv7_GjzbkcGcC53ycuLuOXJoqrnzJ1W1C9HX_vGU"
}
2024-11-14 13:15:45,982:DEBUG:acme.client:Storing nonce: SEOe4Whp2timc0GA-aCG7aIyiRoQUgzqOhp5raXoLSuqd3qf5yc
2024-11-14 13:15:45,982:INFO:certbot._internal.auth_handler:Waiting for verification...
2024-11-14 13:15:46,988:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:46,991:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTRU9lNFdocDJ0aW1jMEdBLWFDRzdhSXlpUm9RVWd6cU9ocDVyYVhvTFN1cWQzcWY1eWMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyOTk5MTU5Nzc5NyJ9",
  "signature": "VemvtjgNiY1Hk-K8FP2E44m3-vvLol3MXRvIKTO4vmZ8UW5BVrPFKexKq9uvfcLAZBDrTkDGYgVtLcNoraac5Lx3B1M_wfuQv5H9qAIcIYFVYke1GPWiRLtOpU10gmgyIknKaKF5f3LzWcjHt36imoJnIAi44-NF-teLujuT2-1BdndNBHp0LU4ymAHHJMqA2NI_iTCsoaOW1PboMcNnp6rE5FHQgwKARDWXNY5UUOGK3kkrvTHA3SFcAURxzD-hceUb3OvE6FJvHRgaZUU8HyvKN3aELpvTp1d3u5Csy3h0UNVrgR1BDekMT_Xbzkle83oAWGW2XHRGjHRfFl43Bg",
  "payload": ""
}
2024-11-14 13:15:48,034:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" ************-11-14 13:15:48,035:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:47 GMT
Content-Type: application/json
Content-Length: 515
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJGtXdnEXPD5uUfsb-qPqPYkHJNAA8FiAsmMCCevmU-J7Y
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:15:31Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "valid",
      "validated": "2024-11-14T12:15:30Z",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-14 13:15:48,036:DEBUG:acme.client:Storing nonce: bBPm6dJGtXdnEXPD5uUfsb-qPqPYkHJNAA8FiAsmMCCevmU-J7Y
2024-11-14 13:15:48,037:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:48,041:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430020975477:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJiQlBtNmRKR3RYZG5FWFBENXVVZnNiLXFQcVBZa0hKTkFBOEZpQXNtTUNDZXZtVS1KN1kiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMDk3NTQ3NyJ9",
  "signature": "ipuPRrf83wgN3rTQmMQ05KyqrHQVxp_mlrpv6-SU06D06uzXNwYRZ_18j7wBKR8VLKBnU4syw0vWYViifpiXRwHxYEf4-kndGMPBoq0fkXqHLOBmTwaM0KSY1A-L8isfuqCTQEUhi_ytKe3laVP8rljRDrn1lDadvTLSz6FxbJR-ymGF3xINTWWZ-UEfmVof2RsQLM6VeHZNTqDOlUMOi7aZNmUIE5b4uBn-vWhLEwEK1IDjiI3HXmQDDpeODo_M95p0hsyr4pJSMS4gY1tYZfc5Uu88hjASZBPqsfHXrw3RJU0xKOvdMBvBnK8HH878ZcoUXQKB0Jdy6QX1abnd2Q",
  "payload": ""
}
2024-11-14 13:15:48,219:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430020975477 HTTP/11" 200 680
2024-11-14 13:15:48,220:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:48 GMT
Content-Type: application/json
Content-Length: 680
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJG0nLLykiBgXd-cGOM7vQjoMHWj3Hfo6WqFJQh8nEpcH8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2024-11-21T12:15:40Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430020975477/h5rYxA",
      "status": "invalid",
      "validated": "2024-11-14T12:15:45Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"gLQJof3PkugsMHF0LRFPPFUcrq56KP0G6I0ZT4qJ7nI\" found at _acme-challenge.cz-test.sawapp.cloud",
        "status": 403
      },
      "token": "Lxkjv7_GjzbkcGcC53ycuLuOXJoqrnzJ1W1C9HX_vGU"
    }
  ],
  "wildcard": true
}
2024-11-14 13:15:48,220:DEBUG:acme.client:Storing nonce: bBPm6dJG0nLLykiBgXd-cGOM7vQjoMHWj3Hfo6WqFJQh8nEpcH8
2024-11-14 13:15:48,221:INFO:certbot._internal.auth_handler:Challenge failed for domain cz-test.sawapp.cloud
2024-11-14 13:15:48,221:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 13:15:48,222:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: cz-test.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "gLQJof3PkugsMHF0LRFPPFUcrq56KP0G6I0ZT4qJ7nI" found at _acme-challenge.cz-test.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2024-11-14 13:15:48,225:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2024-11-14 13:15:48,225:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-14 13:15:48,225:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-14 13:15:48,226:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1600, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 143, in _get_and_save_cert
    lineage = le_client.obtain_and_enroll_certificate(domains, certname)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 517, in obtain_and_enroll_certificate
    cert, chain, key, _ = self.obtain_certificate(domains)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 428, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 496, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2024-11-14 13:15:48,230:ERROR:certbot._internal.log:Some challenges have failed.
