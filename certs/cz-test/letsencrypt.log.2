2025-02-06 16:53:22,672:DEBUG:certbot._internal.main:certbot version: 2.11.0
2025-02-06 16:53:22,673:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-02-06 16:53:22,673:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2025-02-06 16:53:22,673:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-02-06 16:53:22,686:DEBUG:certbot._internal.log:Root logging level set at 30
2025-02-06 16:53:22,686:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 16:53:22,686:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x1023523c0>
Prep: True
2025-02-06 16:53:22,686:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x1023523c0> and installer None
2025-02-06 16:53:22,686:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-02-06 16:53:22,722:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-02-06 16:53:22,730:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-02-06 16:53:22,735:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-02-06 16:53:23,275:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 828
2025-02-06 16:53:23,275:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:23 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "iy5gW4KYAJE": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "The same profile you're accustomed to"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2025-02-06 16:53:23,287:DEBUG:urllib3.connectionpool:Starting new HTTP connection (1): e5.o.lencr.org:80
2025-02-06 16:53:23,318:DEBUG:urllib3.connectionpool:http://e5.o.lencr.org:80 "POST / HTTP/11" 200 346
2025-02-06 16:53:23,318:DEBUG:certbot.ocsp:OCSP response for certificate /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert1.pem is signed by the certificate's issuer.
2025-02-06 16:53:23,320:DEBUG:certbot.ocsp:OCSP certificate status for /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert1.pem is: OCSPCertStatus.GOOD
2025-02-06 16:53:23,323:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-02-12 12:00:43 UTC.
2025-02-06 16:53:23,323:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-02-06 16:53:23,323:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2025-02-06 16:53:23,325:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 16:53:23,325:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 16:53:23,481:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 16:53:23,481:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:23 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyycIdyaLCR-pO7dT8PEPETSmbCL7rZG5T_P5nf5sgtHDM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 16:53:23,481:DEBUG:acme.client:Storing nonce: aTttXYyycIdyaLCR-pO7dT8PEPETSmbCL7rZG5T_P5nf5sgtHDM
2025-02-06 16:53:23,482:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2025-02-06 16:53:23,484:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "GHhYsU1tmucgXGct0bQKbIMCMC0-b98eP6L99-f2i2AFrehLxYm3-z0UVA4cbLc2h7ljjAufnxD09Qv02LqrriLqLdBUKg2OmggSK4AXMY6m1rBsIWg-bdVSBsSvZkS3nreQ27Gq9x4NnBkQvvZ1B1918Rcxniu2tsjpdJO881E1O4nv_Ql2oo-B6A7HbJ1-Yjaiwl1x3kbc7vqBeWRabVwizVN6Dsbr8x06Gcy6sO064gWBwKvDtXjkMfgnMps0Ohg8SDH-EjdAOX5AtxzJpbtXPcV-Bcd5V9GfevS2t0dADUAaavAVgjm3zhSUi8--bvD8C-_drTXBCfTWd1a-nA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2025-02-06 16:53:23,681:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 507
2025-02-06 16:53:23,681:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 06 Feb 2025 15:53:23 GMT
Content-Type: application/json
Content-Length: 507
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351789955845
Replay-Nonce: aTttXYyynFHkjE7h41ypwT343TKz7f0ZdHl83WQKKRMbnabboUE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-02-13T15:53:23Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351789955845"
}
2025-02-06 16:53:23,682:DEBUG:acme.client:Storing nonce: aTttXYyynFHkjE7h41ypwT343TKz7f0ZdHl83WQKKRMbnabboUE
2025-02-06 16:53:23,682:DEBUG:acme.client:JWS payload:
b''
2025-02-06 16:53:23,684:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhVHR0WFl5eW5GSGtqRTdoNDF5cHdUMzQzVEt6N2YwWmRIbDgzV1FLS1JNYm5hYmJvVUUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTE1In0",
  "signature": "WE4OKo01Hce2N5PNgtO7EIfDzI3aG-czVOWXorz8QOKYBFqfh76wBNYTXbjEPBG_1SkzTngJLtcZ2hNv13UeuPfq4iefXnK3Z5HWVQoIecczzlsuKbSgE1wfp0d0cpF3LByN9XVC_6lV0NgjdVQ9pMP3eL5KpZqbgBAyd_wAzfy_2U1cOX8-DP9r9u4lo7_qVtYSh63_9LhdeFUANOOwaPc8wL6PdSMTlLGiRGFYXq2dPTtebUO2BKN_eXn68uhhf759VEPM2NLBNBA-60f4Ab4poL1VO_DbpML6WnE2_J2SDwXg3T5CoInE25Bu8PDFjPn_zTeU-M2vFr9LNII6SA",
  "payload": ""
}
2025-02-06 16:53:23,857:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975115 HTTP/11" 200 523
2025-02-06 16:53:23,857:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:23 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyeAYX5xfFHYAyUVpUbceCpe_cAuOdxN7_YobynD3ea0g
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T15:53:05Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
      "status": "valid",
      "validated": "2025-02-06T15:53:04Z",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 16:53:23,858:DEBUG:acme.client:Storing nonce: aTttXYyyeAYX5xfFHYAyUVpUbceCpe_cAuOdxN7_YobynD3ea0g
2025-02-06 16:53:23,858:DEBUG:acme.client:JWS payload:
b''
2025-02-06 16:53:23,860:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "GK5KBijw1iNtRfNGl2vmsb56ulgQFWHw1rpMDvyI1oatqn4mUTkkRqdpzFFv_qRWc_UObJvRBHS03aJmszpZbH_dQyEtKSx8EeE4-x8uMj_1VNSLOClCmsUVCcEChityQL8iEfOIL61hk_lpoTVJped0xhZ0hvwPLqXg9pKu0yjNnpPJOkCAF4q5tDTFF6A_2UkSrXZ3_G1-OgrtWGqxErJHJkDl3qvG1AsGcuZ6rxXxj_z7U5gezTot7JDTolF1YS35F5wxTgNDfXyR0XzXCz_WOgRPXtk_cD9bcwhyh9z9a9pm9q9vW6NwG1TX_7ajwAVh1NqcbPJ2bSmsKdBbMQ",
  "payload": ""
}
2025-02-06 16:53:24,021:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471960016665 HTTP/11" 200 402
2025-02-06 16:53:24,022:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:23 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aTttXYyyycsBREpPatqQ2wp3_5o93cMav0Bx3QKVv3w0BHViB_g
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T15:53:23Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w",
      "status": "pending",
      "token": "RxFt0tjM1O4a1f2zryFnIidX0jtkz6EEtqdidw0gYtQ"
    }
  ],
  "wildcard": true
}
2025-02-06 16:53:24,022:DEBUG:acme.client:Storing nonce: aTttXYyyycsBREpPatqQ2wp3_5o93cMav0Bx3QKVv3w0BHViB_g
2025-02-06 16:53:24,022:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-02-06 16:53:24,022:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-02-06 16:53:24,023:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

jChkGgaTy9O3xWrCDNzF3TXAURJKIyv_-mrWOZZc6_k

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-02-06 17:11:01,518:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 17:11:01,522:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "I9ujE2l_57N8J5kx7ahf7Dlhb4lJFtd05R1Vx6yiP1W25ZlSwf2TAkKpp7_ZE07Hb-Xeqtv0Zi5nF26YqCEnTXovTep8o20Bx-e1OHyi1QU8f19BzcVwti89UB-EdAx4WF02TEvAzp7pk_3vKAH_BuzlFhp_1k2rEAyNGgWtw7B5kiXbAUAVFKRS8mtsyY3GhQ9trA3XBCx1UjMx9FhtDL0QRjjgRXGDw67DGiKxO_vNpWdaaLLvACo2aJC9gUu3JjmjdxMlOzjSeBSthmkngOWx1vvuP1L8TN7RqRFTdm6ClF5rb3EfF11IsyihZ4L0t0a1mqbsiBSBsoidi3us-w",
  "payload": "e30"
}
2025-02-06 17:11:01,526:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-02-06 17:11:01,998:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471960016665/abs3-w HTTP/11" 200 194
2025-02-06 17:11:01,999:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:01 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w
Replay-Nonce: TUC11O6XCPJCO5osZc3IyBH0oaEjnQCopqVao6HzU483naIZT04
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w",
  "status": "pending",
  "token": "RxFt0tjM1O4a1f2zryFnIidX0jtkz6EEtqdidw0gYtQ"
}
2025-02-06 17:11:01,999:DEBUG:acme.client:Storing nonce: TUC11O6XCPJCO5osZc3IyBH0oaEjnQCopqVao6HzU483naIZT04
2025-02-06 17:11:01,999:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-02-06 17:11:03,000:DEBUG:acme.client:JWS payload:
b''
2025-02-06 17:11:03,002:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WENQSkNPNW9zWmMzSXlCSDBvYUVqblFDb3BxVmFvNkh6VTQ4M25hSVpUMDQiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTE1In0",
  "signature": "RcfuPwmq3cGJ7cRfCx3eSP70dSRWs6uziCIpzuIOSJIy2uWd6q7-MeZYN8-DrCS9cDOStF5bcyrbGKdsdiJ6E5aO9uCEh1ZMvcgH45XDlvel7Z8YMRnH3eg-WS16hsx4M1UEIbvsJKJBwCeaxw5sdJpojgeMytFfl35rhUUQxmsWbxtmiLD1dhQokbcJj0bf3H_1yY19lKPiKQ2ZUkvGtCP01kkjlCVs6ROCigd1FvvYSNG-dKxQHMHd4hXgLLRIJO6snGkRR7pYjsAqpDLmttEJ2orqCA3cPYVhAD-bK0V9j2ok5s8kQ3vf5PieL9YFO_WhOLpAtYVdhCWkHmMNiA",
  "payload": ""
}
2025-02-06 17:11:03,156:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975115 HTTP/11" 200 523
2025-02-06 17:11:03,157:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:03 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XbKXeogTCAlClEooA49oB6jy0RMznNMs2vuqyI99l4yU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T15:53:05Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
      "status": "valid",
      "validated": "2025-02-06T15:53:04Z",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 17:11:03,157:DEBUG:acme.client:Storing nonce: TUC11O6XbKXeogTCAlClEooA49oB6jy0RMznNMs2vuqyI99l4yU
2025-02-06 17:11:03,157:DEBUG:acme.client:JWS payload:
b''
2025-02-06 17:11:03,159:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WGJLWGVvZ1RDQWxDbEVvb0E0OW9CNmp5MFJNem5OTXMydnVxeUk5OWw0eVUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxOTYwMDE2NjY1In0",
  "signature": "a0hobU528sAyqdn8oz2emx64I8jFn-7TZ6rCyC5-Jq89ZjPlwt9PflHKgnJfqhTJ6bT_-dDuyXCWRcPlICXRB9U1GnjZqC5faGPMisYUBesNhtoLhiTO3kkApXthAMqnfizX9CW-l47H-fFHsZPcLOv7hWHt7CaZPwUx_GP90v0D8nAgGUlwIsioUt6iqSpBxho3mGypKtA0kylr3kH-uTjm50cjQdkvAiCRKcRxKVawSIHSRtlyNxVdHR5wtEjIi9sq3qU1SbxUWsH66rhvoz4TyRcOOYr-7GMfak6xGat4KgcY4J9W36b09rF2hhQw54gJ39bI-vn6BnrhhGJ6cw",
  "payload": ""
}
2025-02-06 17:11:03,316:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471960016665 HTTP/11" 200 402
2025-02-06 17:11:03,317:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:03 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6X6zsZu76Dcww38j5_qa4VMnhCpMRF8YbXFAEnXDKwVRM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T15:53:23Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w",
      "status": "pending",
      "token": "RxFt0tjM1O4a1f2zryFnIidX0jtkz6EEtqdidw0gYtQ"
    }
  ],
  "wildcard": true
}
2025-02-06 17:11:03,317:DEBUG:acme.client:Storing nonce: TUC11O6X6zsZu76Dcww38j5_qa4VMnhCpMRF8YbXFAEnXDKwVRM
2025-02-06 17:11:06,319:DEBUG:acme.client:JWS payload:
b''
2025-02-06 17:11:06,321:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WDZ6c1p1NzZEY3d3MzhqNV9xYTRWTW5oQ3BNUkY4WWJYRkFFblhES3dWUk0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxOTYwMDE2NjY1In0",
  "signature": "E3WRBMOhDV8k8w_DmVS7YIq-8gekmfr_k1TufBADupNS4Jp6-YkE-io048A_zfkS6mqDd1fIS-lTJf4HmJSFEDIGrQMxJEdlXmpKzOenatcWZopjl5iQiT_IgEYGkbUlAgxnLmrxLmpfw_H4iU8FlqZT-rafD11ziTKTfdzxLU331oriRBeEezz21O5b9VHAUgLDuR1XbSW1w1FaEhNPNJ8vbYdFM4-W5LjyS_fU3r8kobahC9RP_GvGp95X1ipgjYlBTmXjQ540hqWR8v94WGMCJfxpe_JJb68-bX15VDBIc79Vj4Lk4wwNs0Sw_LlG-o1OiTrXgQ6cqA_lli-vOA",
  "payload": ""
}
2025-02-06 17:11:06,477:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471960016665 HTTP/11" 200 543
2025-02-06 17:11:06,477:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:06 GMT
Content-Type: application/json
Content-Length: 543
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XQC8JmoOsItWzbJEJMrzhxwCCos6p0R5CB8lLMqztviM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T16:11:03Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471960016665/abs3-w",
      "status": "valid",
      "validated": "2025-02-06T16:11:01Z",
      "token": "RxFt0tjM1O4a1f2zryFnIidX0jtkz6EEtqdidw0gYtQ",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2025-02-06 17:11:06,478:DEBUG:acme.client:Storing nonce: TUC11O6XQC8JmoOsItWzbJEJMrzhxwCCos6p0R5CB8lLMqztviM
2025-02-06 17:11:06,478:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-02-06 17:11:06,478:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-02-06 17:11:06,478:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBBTCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEb4nF0EUQO2cH\nDEChwKh0U2jiU9/YoPmZToG2wG5to8kENcDjrDATuPb32ISanhZTJ2JHQqtrG7Xz\nWlUB0T5qPaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0\nLnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwID\nSAAwRQIhAJiMLEp2Ag89IXt14HMIBrDjfxzrrEIlTPcdWm6FIAaPAiAsNMnI09Ol\nDvsx9F+eNI3ZF9f2WPnFDuZc5tfUIWV+2w==\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2025-02-06 17:11:06,479:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2025-02-06 17:12:36.479363
2025-02-06 17:11:06,480:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBBTCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEb4nF0EUQO2cHDEChwKh0U2jiU9_YoPmZToG2wG5to8kENcDjrDATuPb32ISanhZTJ2JHQqtrG7XzWlUB0T5qPaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0LnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwIDSAAwRQIhAJiMLEp2Ag89IXt14HMIBrDjfxzrrEIlTPcdWm6FIAaPAiAsNMnI09OlDvsx9F-eNI3ZF9f2WPnFDuZc5tfUIWV-2w"\n}'
2025-02-06 17:11:06,481:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351789955845:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WFFDOEptb09zSXRXemJKRUpNcnpoeHdDQ29zNnAwUjVDQjhsTE1xenR2aU0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNTYyNDgyOTcvMzUxNzg5OTU1ODQ1In0",
  "signature": "iDCbIh5PYEW6u25oTN0-MTmZO8tBCT2aLPg-2TitbpMtdzgbxz35kmCTliqgn3jJOaJ60Os8DOPrMv2D46n4ZGM8OLE5YDYCxYjHfSvZWmohq2vaWs5T9B74VpINZ4yPVckbCIszwEFs5dZaQKq3XZpoCsfA-W9YQ02FdC7U7Whb4SeBrYl5rgCE2gxPptstJkqdIhz-Ie14m5vfucg-lMUB4FiEaj9KpacCRXFFV-Yh_Vlom8jbj23RRRoPQwd3IxKeUUT_rMecpUXJimDGECDYxRtBLsKg0ZAmJqPW28frJUEYZa-fQUrY7X8_lfRSFgq60tV28zybcyepXKnlsA",
  "payload": "ewogICJjc3IiOiAiTUlJQkJUQ0JyQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRWI0bkYwRVVRTzJjSERFQ2h3S2gwVTJqaVU5X1lvUG1aVG9HMndHNXRvOGtFTmNEanJEQVR1UGIzMklTYW5oWlRKMkpIUXF0ckc3WHpXbFVCMFQ1cVBhQktNRWdHQ1NxR1NJYjNEUUVKRGpFN01Ea3dOd1lEVlIwUkJEQXdMb0lXS2k1amVpMTBaWE4wTG5OaGQyRndjQzVqYkc5MVpJSVVZM290ZEdWemRDNXpZWGRoY0hBdVkyeHZkV1F3Q2dZSUtvWkl6ajBFQXdJRFNBQXdSUUloQUppTUxFcDJBZzg5SVh0MTRITUlCckRqZnh6cnJFSWxUUGNkV202RklBYVBBaUFzTk1uSTA5T2xEdnN4OUYtZU5JM1pGOWYyV1BuRkR1WmM1dGZVSVdWLTJ3Igp9"
}
2025-02-06 17:11:07,482:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/351789955845 HTTP/11" 200 609
2025-02-06 17:11:07,483:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:07 GMT
Content-Type: application/json
Content-Length: 609
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351789955845
Replay-Nonce: TUC11O6XFMg7X4gGVpXxXUQ7-xykLnrE0NudT7HOz58UGg8R4hQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-02-13T15:53:23Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351789955845",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/04f3f9c18d3159776f4746ea4195384557c6"
}
2025-02-06 17:11:07,483:DEBUG:acme.client:Storing nonce: TUC11O6XFMg7X4gGVpXxXUQ7-xykLnrE0NudT7HOz58UGg8R4hQ
2025-02-06 17:11:08,487:DEBUG:acme.client:JWS payload:
b''
2025-02-06 17:11:08,489:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/351789955845:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WEZNZzdYNGdHVnBYeFhVUTcteHlrTG5yRTBOdWRUN0hPejU4VUdnOFI0aFEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNTYyNDgyOTcvMzUxNzg5OTU1ODQ1In0",
  "signature": "iQoeqEN7tiJKdQNchF_DhAPLRLNY6gSvD2djunffip5X-m-h1cSd3F9t7fMjga4V1NnNpNkUxPzBc4bI2q9h7l57UIAw3hbjiosp_fqEVwpTtT-xvabhmxnuBBkhXX3sH_7nAkQi4dmojRk5li9YaDCRpVVh-X1w4gwqp_PLhOJMitHKUgbiv2RTH75e_W0n1mbjH6br6ml9jnhhO3ZCA6r1QK2bq0niDL2KG64dlk2UiURtNTgnmd_5j7qwPB3Y7oIlAfU9HftWioccBiba5LbDh02_he0jLaJQabfiPdwsONj3YdmdQVXKwB00qKL8bKAX1VZmQE7nlJc86dkFeQ",
  "payload": ""
}
2025-02-06 17:11:08,647:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/351789955845 HTTP/11" 200 609
2025-02-06 17:11:08,647:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:08 GMT
Content-Type: application/json
Content-Length: 609
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6X__qsw0sx1oNFCvTfMzVU4-oyBDrJyi-o77mDkurS9no
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2025-02-13T15:53:23Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471960016665"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351789955845",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/04f3f9c18d3159776f4746ea4195384557c6"
}
2025-02-06 17:11:08,647:DEBUG:acme.client:Storing nonce: TUC11O6X__qsw0sx1oNFCvTfMzVU4-oyBDrJyi-o77mDkurS9no
2025-02-06 17:11:08,647:DEBUG:acme.client:JWS payload:
b''
2025-02-06 17:11:08,649:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/04f3f9c18d3159776f4746ea4195384557c6:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WF9fcXN3MHN4MW9ORkN2VGZNelZVNC1veUJEckp5aS1vNzdtRGt1clM5bm8iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDRmM2Y5YzE4ZDMxNTk3NzZmNDc0NmVhNDE5NTM4NDU1N2M2In0",
  "signature": "doTcpP-ficj2LxHBZacr8iew_diZWCduQkq1ulksAU_thAy6_E_a9El0SMHjY9ls5eo7ZoKmDOBqD2U7WGtrT2Bd_zkYgP_5Q6b6HsHpu0kxDm1XBghbwASADwgWDRoL6Pbko_uaEWgeUgviiyW8g7HaLt0MN8ntSOh3X0XZ6Jkqx5D8DuaFQ3hOwFL4eGTZDuDChuZ-3pKt2UzUok0bob2yWDtfVPpvgoc-9UQZVMqsU5K5gMtkdHqJg2jmIdHoS8UKb9aY8s1PTwEqNCJEHju19WFFgqwatZMXWewiXs6GmgyxBcMR-odq5fJWMqYLHet62bdUGX0rj-zH-KTW3g",
  "payload": ""
}
2025-02-06 17:11:08,808:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/04f3f9c18d3159776f4746ea4195384557c6 HTTP/11" 200 2889
2025-02-06 17:11:08,808:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 16:11:08 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2889
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/04f3f9c18d3159776f4746ea4195384557c6/1>;rel="alternate"
Replay-Nonce: UvlUZ57PiyFynOEXbRARsRmy6Zp0WsSm5hkn7yJ_sqSTWnbmRMo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDojCCAyigAwIBAgISBPP5wY0xWXdvR0bqQZU4RVfGMAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NTAeFw0yNTAyMDYxNTEyMzZaFw0yNTA1MDcxNTEyMzVaMCExHzAdBgNVBAMMFiou
Y3otdGVzdC5zYXdhcHAuY2xvdWQwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAARv
icXQRRA7ZwcMQKHAqHRTaOJT39ig+ZlOgbbAbm2jyQQ1wOOsMBO49vfYhJqeFlMn
YkdCq2sbtfNaVQHRPmo9o4ICLTCCAikwDgYDVR0PAQH/BAQDAgeAMB0GA1UdJQQW
MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBS4
fDOR7BqC+zLxyOBp2FQiXYdmvzAfBgNVHSMEGDAWgBSfK1/PPCFPnQS37SssxMZw
i9LXDTBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9lNS5vLmxl
bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL2U1LmkubGVuY3Iub3JnLzA3BgNV
HREEMDAughYqLmN6LXRlc3Quc2F3YXBwLmNsb3VkghRjei10ZXN0LnNhd2FwcC5j
bG91ZDATBgNVHSAEDDAKMAgGBmeBDAECATCCAQMGCisGAQQB1nkCBAIEgfQEgfEA
7wB1AMz7D2qFcQll/pWbU87psnwi6YVcDZeNtql+VMD+TA2wAAABlNwHgZAAAAQD
AEYwRAIgFYBVCYJu5dIYfaosQiikfK9x7drkwQRh0uDEpmTXsjQCIESV1om98kk6
2g1naS8/X5M/pUx4+lIRDzz2UdYHV/eVAHYAouMK5EXvva2bfjjtR2d3U9eCW4SU
1yteGyzEuVCkR+cAAAGU3AeBjgAABAMARzBFAiEAoMXwRvfs8VB/YgVfH0M3nUdz
soH3Ur8sxBF4/ik6T/4CIASDrTMzcMRH0Nd2c0YX2zpFS4N0QQr2C6UTk/nVKd+S
MAoGCCqGSM49BAMDA2gAMGUCMQCh75gLknmxpuR7bHt7X01xPI5JeqSsKQ0g2ZGt
ZX2oloXEEbUYYMCSKL/mr3t660ACMAXt1Po9ZLXrX35ZOd4mKsGkXEvWja5ZH60t
vjkfCm8it/ohAZwWoMmEvalIRCIkTg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRAIOPbGPOsTmMYgZigxXJ/d4wDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTUwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNCzqK
a2GOtu/cX1jnxkJFVKtj9mZhSAouWXW0gQI3ULc/FnncmOyhKJdyIBwsz9V8UiBO
VHhbhBRrwJCuhezAUUE8Wod/Bk3U/mDR+mwt4X2VEIiiCFQPmRpM5uoKrNijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSfK1/PPCFPnQS37SssxMZw
i9LXDTAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAH3KdNEVCQdqk0LKyuNImTKdRJY1C
2uw2SJajuhqkyGPY8C+zzsufZ+mgnhnq1A2KVQOSykOEnUbx1cy637rBAihx97r+
bcwbZM6sTDIaEriR/PLk6LKs9Be0uoVxgOKDcpG9svD33J+G9Lcfv1K9luDmSTgG
6XNFIN5vfI5gs/lMPyojEMdIzK9blcl2/1vKxO8WGCcjvsQ1nJ/Pwt8LQZBfOFyV
XP8ubAp/au3dc4EKWG9MO5zcx1qT9+NXRGdVWxGvmBFRAajciMfXME1ZuGmk3/GO
koAM7ZkjZmleyokP1LGzmfJcUd9s7eeu1/9/eg5XlXd/55GtYjAM+C4DG5i7eaNq
cm2F+yxYIPt6cbbtYVNJCGfHWqHEQ4FYStUyFnv8sjyqU8ypgZaNJ9aVcWSICLOI
E1/Qv/7oKsnZCWJ926wU6RqG1OYPGOi1zuABhLw61cuPVDT28nQS/e6z95cJXq0e
K1BcaJ6fJZsmbjRgD5p3mvEf5vdQM7MCEvU0tHbsx2I5mHHJoABHb8KVBgWp/lcX
GWiWaeOyB7RP+OfDtvi2OsapxXiV7vNVs7fMlrRjY1joKaqmmycnBvAq14AEbtyL
sVfOS66B8apkeFX2NY4XPEYV4ZSCe8VHPrdrERk2wILG3T/EGmSIkCYVUMSnjmJd
VQD9F6Na/+zmXCc=
-----END CERTIFICATE-----

2025-02-06 17:11:08,808:DEBUG:acme.client:Storing nonce: UvlUZ57PiyFynOEXbRARsRmy6Zp0WsSm5hkn7yJ_sqSTWnbmRMo
2025-02-06 17:11:08,811:DEBUG:certbot._internal.storage:Writing new private key to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/privkey2.pem.
2025-02-06 17:11:08,811:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert2.pem.
2025-02-06 17:11:08,812:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/chain2.pem.
2025-02-06 17:11:08,812:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/fullchain2.pem.
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var account=672abefd0655bb9562de0097bd749ed5 (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2025-02-06 17:11:08,822:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 17:11:08,822:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 17:11:08,823:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/cz-test/renewal/cz-test.sawapp.cloud.conf.new.
2025-02-06 17:11:08,825:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/privkey.pem
This certificate expires on 2025-05-07.
These files will be updated when the certificate renews.
2025-02-06 17:11:08,825:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2025-02-06 17:11:08,825:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2025-02-06 17:11:08,826:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
