2025-02-06 13:51:41,763:DEBUG:certbot._internal.main:certbot version: 2.11.0
2025-02-06 13:51:41,763:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2025-02-06 13:51:41,763:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2025-02-06 13:51:41,763:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2025-02-06 13:51:41,777:DEBUG:certbot._internal.log:Root logging level set at 30
2025-02-06 13:51:41,777:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2025-02-06 13:51:41,777:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x1044da3c0>
Prep: True
2025-02-06 13:51:41,777:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x1044da3c0> and installer None
2025-02-06 13:51:41,778:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2025-02-06 13:51:41,813:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff=None))>
2025-02-06 13:51:41,820:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2025-02-06 13:51:41,824:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2025-02-06 13:51:42,374:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 828
2025-02-06 13:51:42,375:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:42 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "profiles": {
      "classic": "The same profile you're accustomed to"
    },
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert",
  "wOmas7rRz7I": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417"
}
2025-02-06 13:51:42,388:DEBUG:urllib3.connectionpool:Starting new HTTP connection (1): e5.o.lencr.org:80
2025-02-06 13:51:42,595:DEBUG:urllib3.connectionpool:http://e5.o.lencr.org:80 "POST / HTTP/11" 200 346
2025-02-06 13:51:42,597:DEBUG:certbot.ocsp:OCSP response for certificate /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert1.pem is signed by the certificate's issuer.
2025-02-06 13:51:42,599:DEBUG:certbot.ocsp:OCSP certificate status for /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud/cert1.pem is: OCSPCertStatus.GOOD
2025-02-06 13:51:42,601:DEBUG:certbot._internal.storage:Should renew, less than 30 days before certificate expiry 2025-02-12 12:00:43 UTC.
2025-02-06 13:51:42,601:INFO:certbot._internal.renewal:Certificate is due for renewal, auto-renewing...
2025-02-06 13:51:42,601:DEBUG:certbot._internal.display.obj:Notifying user: Renewing an existing certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2025-02-06 13:51:42,603:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 13:51:42,603:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 13:51:42,749:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 13:51:42,749:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:42 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XmXdPniTh96q3zQ1F_bqWDFFHkwitEU6s64Yl2MgbyNU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 13:51:42,749:DEBUG:acme.client:Storing nonce: TUC11O6XmXdPniTh96q3zQ1F_bqWDFFHkwitEU6s64Yl2MgbyNU
2025-02-06 13:51:42,749:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2025-02-06 13:51:42,752:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "C-ILbHw6aVY-fKXzcgqB5SF_pMCDYjrOYfF1kQe_sbQALc69UNEuQiA4gQeZiBMNjNJ3WYOs_cqomwfVM2DPI9M6ZxxHFnHom1DPDxnnPbJJws6YJURYm84_4tHXG4N7cb33mFf1-yc3ldFVPUxpGW0-wAGuQD4l6W04E8Id7k_Tsovfeya_Zpj-lAaAf3ffIT7ChKRyAkGnQKc5aH_sCERfP-UDkDLEW8_uC-1ZyNn4LFLU7MQLcftysthK3JRBIBqdr8UIkpFlT5WBuGNJjazfKvijgD3tlIx6wLCi9B0hqYKHNLpzfMDE3D5df6TVVtM6tmSJbmL6Nmwru3fFDQ",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2025-02-06 13:51:43,045:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 507
2025-02-06 13:51:43,045:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 06 Feb 2025 12:51:42 GMT
Content-Type: application/json
Content-Length: 507
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/351750861205
Replay-Nonce: TUC11O6X8EoGEuWPQFYJT6XgT4ps8FQe366JoCmpscpzXHB5KXc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2025-02-13T12:51:42Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975105",
    "https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/351750861205"
}
2025-02-06 13:51:43,045:DEBUG:acme.client:Storing nonce: TUC11O6X8EoGEuWPQFYJT6XgT4ps8FQe366JoCmpscpzXHB5KXc
2025-02-06 13:51:43,046:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:43,048:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975105:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WDhFb0dFdVdQUUZZSlQ2WGdUNHBzOEZRZTM2NkpvQ21wc2NwelhIQjVLWGMiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTA1In0",
  "signature": "GM-26Y-9BhWfVbQ2UoBLueq42r1XpUBHfqDTzeuEcJAei8_2vuie79mWJzXEaFZUNS5JAX0EouJwHtHgihsT34L7jfVE2dtEz9Y39QnjwVX3z44CdPYRijH6xfL1XHbxyUoCJKaF4wsyNHBBGCcOLkIH6F3gSjWG4mYgONa1BVSPTgyduH0ACyFjT2xASytgaBJFKvnzlDXvTj1vPr-h5cvGre_4FtBoooNUTx5KphtBHuxlT-x9vALMAZ1Sw0yJ1vS2YNlTg_4BWvH1QQaJwLMnxP98cPrBlonuSdWFEDwx_fxWvcywDg0Nm5dQoLkh83sH9_BqQ1ZF0pfDRdhhHw",
  "payload": ""
}
2025-02-06 13:51:43,199:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975105 HTTP/11" 200 402
2025-02-06 13:51:43,199:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:43 GMT
Content-Type: application/json
Content-Length: 402
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57PD8XYwPK1n0oJLVyz0om1JCpPsr0OJDIZY7JPr5o9NjE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T12:51:42Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg",
      "status": "pending",
      "token": "i_sw7xjo6XHiYNYma-04h93m1-eg1Z9T3n8Ynt5yIFM"
    }
  ],
  "wildcard": true
}
2025-02-06 13:51:43,200:DEBUG:acme.client:Storing nonce: UvlUZ57PD8XYwPK1n0oJLVyz0om1JCpPsr0OJDIZY7JPr5o9NjE
2025-02-06 13:51:43,200:DEBUG:acme.client:JWS payload:
b''
2025-02-06 13:51:43,202:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115:
{
  "protected": "*******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "OeTMVkf-StrrBXJIGQvi18-TMhmFHnRUIA0onclyqazb4Y2Yj4XTIaZ2GXNdsYnqLXofkgXwpX9ANP1iYdTAD2kND6U5TEE8DHw95GId6Xvqr1Zyf2EkCS6j7UWwCvCuzZORBCYM9dKrxJ3GO42_ibeO8fDfSZ85hHFLv38GsMeCLoEd6R6cIYBs7EfIlPVptjvR3VO9mnYsUJvmxI-2-ONiupzejo5_07TQBosaspYiZ4bvqcyVFvViHf9JPRcugIqCamqDrNQp5Fgy0euvuo1MsqFP6YkbK6CytTmc-keTUkMq11RuuU9fz9nG1qBNE8p6ofOAYg2pczScie21dw",
  "payload": ""
}
2025-02-06 13:51:43,351:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975115 HTTP/11" 200 828
2025-02-06 13:51:43,352:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 12:51:43 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57P1S4fjF4xWf5W1WlIh5X02PGJs_PwarOPDsXGhyXHJjI
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T12:51:42Z",
  "challenges": [
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/29mjlg",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/cL58Iw",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    }
  ]
}
2025-02-06 13:51:43,352:DEBUG:acme.client:Storing nonce: UvlUZ57P1S4fjF4xWf5W1WlIh5X02PGJs_PwarOPDsXGhyXHJjI
2025-02-06 13:51:43,352:INFO:certbot._internal.auth_handler:Performing the following challenges:
2025-02-06 13:51:43,352:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-02-06 13:51:43,352:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-02-06 13:51:43,353:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

y8-cMLSohnUb4vc-EfTXl2x8Zxhp2KALSngK1Jo7gM8

2025-02-06 16:15:24,863:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

jOJKXJGhwe900sS26LhZvnh-03I0rBqqwOOiQEs24ZY

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2025-02-06 16:53:03,438:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 16:53:03,445:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "liMHcEEFdo3rji_d7_IUAnC9SYHormkOykM5HjGvI50JG0ZB8J_4Da8LNZJB1z5IRa86dF6ep1iCulrjQ11C6YZFdgZJFe8ngko-TvxOsjpemBepApEvVHKqDQ13qS55OCzhHFWPthgdkU3DA2eCM6DwyZ5DbZVLgIya1uKGvSYG9ZOt_ORjld5rU-61P800-75EkLHNPmHpn7t5AfN1v75n0A3Q_ABY6xHwnbZFAOzEjVbEPGHpRTZw1WtkGzgnO9TOk4bvBNzmXGl-PmT6APzYZbMYHnpAvFl_61YiyQM_DxpglKxTWEXwSaqtS1eAsMsB37HIW7PaIdPv8SFGMA",
  "payload": "e30"
}
2025-02-06 16:53:03,450:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2025-02-06 16:53:04,037:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471899975105/7BPtyg HTTP/11" 400 177
2025-02-06 16:53:04,037:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 06 Feb 2025 15:53:03 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6X1roJmeMOPO-_7216cgwucNQVySSWX0eyf1tvBS7QCUc

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"UvlUZ57P1S4fjF4xWf5W1WlIh5X02PGJs_PwarOPDsXGhyXHJjI\"",
  "status": 400
}
2025-02-06 16:53:04,038:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "UvlUZ57P1S4fjF4xWf5W1WlIh5X02PGJs_PwarOPDsXGhyXHJjI"
2025-02-06 16:53:04,038:DEBUG:acme.client:Requesting fresh nonce
2025-02-06 16:53:04,038:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2025-02-06 16:53:04,184:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2025-02-06 16:53:04,184:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:04 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XkKQB0ed4ZtrlvQQ4AEfk96SLAHQaC38pKC41_E-fLi0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2025-02-06 16:53:04,184:DEBUG:acme.client:Storing nonce: TUC11O6XkKQB0ed4ZtrlvQQ4AEfk96SLAHQaC38pKC41_E-fLi0
2025-02-06 16:53:04,184:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 16:53:04,186:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WGtLUUIwZWQ0WnRybHZRUTRBRWZrOTZTTEFIUWFDMzhwS0M0MV9FLWZMaTAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNTYyNDgyOTcvNDcxODk5OTc1MTA1LzdCUHR5ZyJ9",
  "signature": "fis-8RnzKFna2qWSpm_t50YidBq4CspUx7PLdEKMA-ioCCzILseAKBLq5JAHM8HKYm_GLDs1x_hnAu--c4JSZRmmOVqAGkPiNqIyhazKaUEoD-72vIvGQo9FH25bA_jybrpjPN_54-jm3m3ZtkFj3rIF7gvzyplLs-mI-X3sGTghY_NnDhuZzy0Kib8BFklJ6P6qXYo5KvKH--BqLB3-yKeK_k3kRH7i7tD8HIg_sEtcTH9-BYOjf4vsAm7PpCYxFXtUfr-FosreJ6DsM0Sr5H3fgrcT5RYJjsd7XFHTw2UojNbOpSk1JWo_9EDERhUJqUwEyD4CziKK5RZBCu9ePw",
  "payload": "e30"
}
2025-02-06 16:53:04,353:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471899975105/7BPtyg HTTP/11" 200 194
2025-02-06 16:53:04,354:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:04 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975105>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg
Replay-Nonce: TUC11O6XdY2Gh0jO9BRWYUEv7EABUZp37w17CEovE3XAOSF_cqQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg",
  "status": "pending",
  "token": "i_sw7xjo6XHiYNYma-04h93m1-eg1Z9T3n8Ynt5yIFM"
}
2025-02-06 16:53:04,354:DEBUG:acme.client:Storing nonce: TUC11O6XdY2Gh0jO9BRWYUEv7EABUZp37w17CEovE3XAOSF_cqQ
2025-02-06 16:53:04,354:DEBUG:acme.client:JWS payload:
b'{}'
2025-02-06 16:53:04,356:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WGRZMkdoMGpPOUJSV1lVRXY3RUFCVVpwMzd3MTdDRW92RTNYQU9TRl9jcVEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLzIwNTYyNDgyOTcvNDcxODk5OTc1MTE1LzFYZl9EQSJ9",
  "signature": "UKim0vP6r-1tor8rf0qCxqGggQP_l3cB8AWX7PbZ0JkReUeT7sypMC63gxm8CoH7knWT7FJflX03gvI5gjY1iLIvsuz1bK-_Aj6C-L-8NLv_ZfxtvYKw7YRWMfBwVqHspjiL9X8ZMr8F32YAFuKD-hxvs32QLj6hofD7r5YwtvELG6hZXmBoqUv1TKTOJw4d_BuSRAwB7KH6dUfiQtEhhlPzpPp9JeX7MQgEaFe6ZX8IXxwEH9aezdqxIS20wdlf_PGFuCZe-N6yx0MxQyeKHekuI20zKCtfOZRM3steyh33ELjMJ8_YUB7APuNAgwRMQRdhXqbG5hJVd1gGEu75Gg",
  "payload": "e30"
}
2025-02-06 16:53:04,512:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall/**********/471899975115/1Xf_DA HTTP/11" 200 194
2025-02-06 16:53:04,512:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:04 GMT
Content-Type: application/json
Content-Length: 194
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA
Replay-Nonce: TUC11O6XAsKO97ML48w2kXjuWbAPf1Jz3Eoc-Sgy775g_uQxVVg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
  "status": "pending",
  "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
}
2025-02-06 16:53:04,513:DEBUG:acme.client:Storing nonce: TUC11O6XAsKO97ML48w2kXjuWbAPf1Jz3Eoc-Sgy775g_uQxVVg
2025-02-06 16:53:04,513:INFO:certbot._internal.auth_handler:Waiting for verification...
2025-02-06 16:53:05,515:DEBUG:acme.client:JWS payload:
b''
2025-02-06 16:53:05,517:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975105:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WEFzS085N01MNDh3MmtYanVXYkFQZjFKejNFb2MtU2d5Nzc1Z191UXhWVmciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTA1In0",
  "signature": "VZgcjUJzRpm6Io0-8pLawsxX_svx2x2t7afgrvuUtpgJobGoLco3hV4oQ4HD99f1rOaRv0CY7utcebOMcKD_ZQGsJqmmBFYS_Cgsd2SDXFIETnOMi8avDjBZclAtu79wwd3AXOmr6_2yfq5F7ggINetmSwtQMJ4BcCTizQHEuEHDY0eVDd8BbyhIV05VY7PEUXJYjQgrBgQLkwpZr8xp2h79kFilHf-2fOxQKNRvoba1GS22E_xOkiHZuR35u_zfJb92hQCoaxE0BA76k3NJzVSet14edxadJ4PsGEuSFA_oz-CLB6iQ-7F88QucqmhpWA2vgo8Z5AH44faFgJytRQ",
  "payload": ""
}
2025-02-06 16:53:05,669:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975105 HTTP/11" 200 688
2025-02-06 16:53:05,669:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:05 GMT
Content-Type: application/json
Content-Length: 688
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57P3oKN1PIzT0ASo8cyxGrGKA-eT8aMguyhbN8vRkL-VE0
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2025-02-13T12:51:42Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975105/7BPtyg",
      "status": "invalid",
      "validated": "2025-02-06T15:53:04Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"jOJKXJGhwe900sS26LhZvnh-03I0rBqqwOOiQEs24ZY\" found at _acme-challenge.cz-test.sawapp.cloud",
        "status": 403
      },
      "token": "i_sw7xjo6XHiYNYma-04h93m1-eg1Z9T3n8Ynt5yIFM"
    }
  ],
  "wildcard": true
}
2025-02-06 16:53:05,669:DEBUG:acme.client:Storing nonce: UvlUZ57P3oKN1PIzT0ASo8cyxGrGKA-eT8aMguyhbN8vRkL-VE0
2025-02-06 16:53:05,669:DEBUG:acme.client:JWS payload:
b''
2025-02-06 16:53:05,671:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJVdmxVWjU3UDNvS04xUEl6VDBBU284Y3l4R3JHS0EtZVQ4YU1ndXloYk44dlJrTC1WRTAiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTE1In0",
  "signature": "N3NsXSl-Q8ZLOuuaZrQqLdvP_Zo6mR-CwXrwEBEyie4rICjC__0vaWtuJdx8rqXfTQakqiDEX7M_txq3VB2GDavoILWgXccfDBsQ1Ase7k8dmr04WJrUMkuTeHpKrZbOegQiTNRhxpWtpPBGSwInLwLksVjkpi3F6tZPWm4I9RGOCxT0If95uoyxFtGpyOMx7X9-AvQeBtQ5gHz9bea_-RkMHaLcwX1F1v0j4DiXw6iSyanVaT5u-P_lJXSJJyxa-21K-1aI2za9cG9A8wpNYqGSDF8kG55XvcQqhfJkvDBPQc6iQIrssiz0wic-sptPuyjW7kSttrnZlPprKEpEWQ",
  "payload": ""
}
2025-02-06 16:53:05,822:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975115 HTTP/11" 200 828
2025-02-06 16:53:05,822:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:05 GMT
Content-Type: application/json
Content-Length: 828
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: TUC11O6XCddWQ9LfalHcBGHrqhz4uL8pmweqt4viJVP0gKscwa8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2025-02-13T12:51:42Z",
  "challenges": [
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/cL58Iw",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/29mjlg",
      "status": "pending",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY"
    }
  ]
}
2025-02-06 16:53:05,822:DEBUG:acme.client:Storing nonce: TUC11O6XCddWQ9LfalHcBGHrqhz4uL8pmweqt4viJVP0gKscwa8
2025-02-06 16:53:05,823:INFO:certbot._internal.auth_handler:Challenge failed for domain cz-test.sawapp.cloud
2025-02-06 16:53:08,828:DEBUG:acme.client:JWS payload:
b''
2025-02-06 16:53:08,830:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz/**********/471899975115:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJUVUMxMU82WENkZFdROUxmYWxIY0JHSHJxaHo0dUw4cG13ZXF0NHZpSlZQMGdLc2N3YTgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LzIwNTYyNDgyOTcvNDcxODk5OTc1MTE1In0",
  "signature": "lcj4qWTSMcOy46oWdk7-MuRWYJ4zRc8YWxa8bF2_EXpMi_f30oTuNIDaH3YazueFVDdax1fI5W3wp1NYvwaVGFiVIYknPSyAFm9kTTmOjnFqW2aJOSlbNVL314FHc7KHQu2KLB2qCKQWoNr6HOWeS5kfgsJ4FrXSyzsLcDh5EzfdhVTo5VdWslQccWBCudlorCjR3H3lAoftjw0XD80hboNyWXq_opaYUM08aHbxK4MA-XW9rI0cSSM-7r6SQ1TZpv5WQc_xI-63Iw_wDt7bMgr7hJQqq-w4HFR_BTJQ6aL4yA8N34DHqAvfdrSyatOw1aM7BbDjmJwAzva8lFpnLA",
  "payload": ""
}
2025-02-06 16:53:08,982:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz/**********/471899975115 HTTP/11" 200 523
2025-02-06 16:53:08,982:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 06 Feb 2025 15:53:08 GMT
Content-Type: application/json
Content-Length: 523
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: UvlUZ57Ps0KN5Grz2YlnDYXSwOihlec0Q0-5omxIE_ooGrxVprg
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2025-03-08T15:53:05Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall/**********/471899975115/1Xf_DA",
      "status": "valid",
      "validated": "2025-02-06T15:53:04Z",
      "token": "0J4CFeooKFbJgYzDl_AWSMBxzX0dJfi-lvvVpdV2xaY",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2025-02-06 16:53:08,982:DEBUG:acme.client:Storing nonce: UvlUZ57Ps0KN5Grz2YlnDYXSwOihlec0Q0-5omxIE_ooGrxVprg
2025-02-06 16:53:08,983:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2025-02-06 16:53:08,984:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: cz-test.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "jOJKXJGhwe900sS26LhZvnh-03I0rBqqwOOiQEs24ZY" found at _acme-challenge.cz-test.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2025-02-06 16:53:08,988:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2025-02-06 16:53:08,988:DEBUG:certbot._internal.error_handler:Calling registered functions
2025-02-06 16:53:08,989:INFO:certbot._internal.auth_handler:Cleaning up challenges
2025-02-06 16:53:08,989:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1600, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 131, in _get_and_save_cert
    renewal.renew_cert(config, domains, le_client, lineage)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/renewal.py", line 399, in renew_cert
    new_cert, new_chain, new_key, _ = le_client.obtain_certificate(domains, new_key)
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 428, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 496, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2025-02-06 16:53:08,995:ERROR:certbot._internal.log:Some challenges have failed.
