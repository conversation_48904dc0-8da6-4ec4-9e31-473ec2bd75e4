2024-11-14 11:41:59,509:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-14 11:41:59,510:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-14 11:41:59,510:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2024-11-14 11:41:59,510:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-14 11:41:59,521:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-14 11:41:59,522:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 11:41:59,522:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x1042be510>
Prep: True
2024-11-14 11:41:59,522:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x1042be510> and installer None
2024-11-14 11:41:59,522:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-14 11:41:59,552:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-14 11:41:59,558:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-14 11:42:00,061:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-14 11:42:00,062:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 10:42:00 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "M34juc4kZic": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-14 11:42:00,063:DEBUG:acme.client:Requesting fresh nonce
2024-11-14 11:42:00,063:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-14 11:42:00,220:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-14 11:42:00,221:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 10:42:00 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVB6FPoDscL4jIxADxDWOgSsE-YOp8z5V7JH-SxbR3dPY
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-14 11:42:00,222:DEBUG:acme.client:Storing nonce: aHUr_lcVB6FPoDscL4jIxADxDWOgSsE-YOp8z5V7JH-SxbR3dPY
2024-11-14 11:42:00,222:DEBUG:acme.client:JWS payload:
b'{\n  "contact": [\n    "mailto:<EMAIL>"\n  ],\n  "termsOfServiceAgreed": true\n}'
2024-11-14 11:42:00,228:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-acct:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAiandrIjogeyJuIjogIm04V1JLeG8yb2Zib0JtekdFNHhoTjNudHk1eUFwbzNWWE55dGoyMlZGWGhZNWdCWW9OTkh2dnFRNE55QV9zV1h2M2xtbmxvNWttZ1BneUJJTjFoVFFPMjRUVUVoOTUzRzl2OC13UUI2VXRJTXJoNG1NUWR2aUd6VWpzWkViSWNpVEtZNG5IejNjU09XXzZmWXpYdnVxcU1wRFFKREdPeHREb3NYOGlDZXF1WE1lZTQ0QTZ3TUlxVC1xNl9ycW5lbGRWbW1mQUlKMENHZ1lsNUFjMFE4ckhPQnhOd2UzWDBRNzVBd3ExWUU4bUd1cTJOelp3SXVMRDNSVWszd3VQVTQ3MGpRZVVtVm5vUEh4cWhiTzczZEEyUnpQd3N3U1JDYldRZVRCNDZlRExjcnRGZmZXbUlON0YtNnFBVGJmRDdvVHVlb1hzVGxXYk9tWHNXU3lXbnFOdyIsICJlIjogIkFRQUIiLCAia3R5IjogIlJTQSJ9LCAibm9uY2UiOiAiYUhVcl9sY1ZCNkZQb0RzY0w0akl4QUR4RFdPZ1NzRS1ZT3A4ejVWN0pILVN4YlIzZFBZIiwgInVybCI6ICJodHRwczovL2FjbWUtdjAyLmFwaS5sZXRzZW5jcnlwdC5vcmcvYWNtZS9uZXctYWNjdCJ9",
  "signature": "DsYuChf4J4iHFJkGgW6vAobPldpcOFG18UASnXLFALjCFQqpNt6yewa4c15RrY20H0smV-d7C4slTkGHXD7TkQaxw7j0O-vhDYfKQhcUgBM2lkldIzWiyy0NY1hS7amOiCMUwBIiaI9-RepwHPC6wDpcDhUg4yKWGoPjMpwgf_RyIltUlxmZLTyEx07GpkDHHL9uOkdtMgK7kQuH7ZnoSFJG01-jOcxVjPhmK8nleG9gS2HdlbwFzCN7ok08q8q8geJr8g-790VFGNxKeoVaPlAQ1j-U7VbEVLZu0Hhrj_NbLGcnm2ciXACkA-4jwwbpsFPh9i6gsP6VPSrBgjG3pA",
  "payload": "*******************************************************************************************************************************"
}
2024-11-14 11:42:00,399:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-acct HTTP/11" 201 570
2024-11-14 11:42:00,400:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 14 Nov 2024 10:42:00 GMT
Content-Type: application/json
Content-Length: 570
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf>;rel="terms-of-service"
Location: https://acme-v02.api.letsencrypt.org/acme/acct/**********
Replay-Nonce: SHEOpopMDMKB4dhPF09MKnTXGy3mmwOZb9TEVZybGA80jb0lNvc
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "key": {
    "kty": "RSA",
    "n": "m8WRKxo2ofboBmzGE4xhN3nty5yApo3VXNytj22VFXhY5gBYoNNHvvqQ4NyA_sWXv3lmnlo5kmgPgyBIN1hTQO24TUEh953G9v8-wQB6UtIMrh4mMQdviGzUjsZEbIciTKY4nHz3cSOW_6fYzXvuqqMpDQJDGOxtDosX8iCequXMee44A6wMIqT-q6_rqneldVmmfAIJ0CGgYl5Ac0Q8rHOBxNwe3X0Q75Awq1YE8mGuq2NzZwIuLD3RUk3wuPU470jQeUmVnoPHxqhbO73dA2RzPwswSRCbWQeTB46eDLcrtFffWmIN7F-6qATbfD7oTueoXsTlWbOmXsWSyWnqNw",
    "e": "AQAB"
  },
  "contact": [
    "mailto:<EMAIL>"
  ],
  "initialIp": "*************",
  "createdAt": "2024-11-14T10:42:00.338538345Z",
  "status": "valid"
}
2024-11-14 11:42:00,400:DEBUG:acme.client:Storing nonce: SHEOpopMDMKB4dhPF09MKnTXGy3mmwOZb9TEVZybGA80jb0lNvc
2024-11-14 11:42:06,712:DEBUG:certbot._internal.display.obj:Notifying user: Account registered.
2024-11-14 11:42:06,712:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=JWKRSA(key=<ComparableRSAKey(<cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey object at 0x104869b10>)>), contact=('mailto:<EMAIL>',), agreement=None, status='valid', terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service='https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf'), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-14 11:42:06,714:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2024-11-14 11:42:06,719:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2024-11-14 11:42:06,721:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "d79pHKohOcIAPpOgan10_TZSClH9y3nYReqB7pZVhFm5pIrAfdDWZoTpe_Y9pXOJlUCfTsSU2IeT0wDb-ZNwoWmV5M6WMt_W-3sW2IXazXeY6mYQNEQhoGecIKxC1py61S4Zq-KLPLVTe0_gG5fjhgtcgjOXEK43rlEOruVWNmkhS9eJFkl08ms4yyo3MU2HpoRktovzjFmfg8oLHgiiCydYsjz3CapXZLnD2ed1acySbc7z74D-DdssmYIuX24VeEhK1v4Zt2xFrnBVTBvrxE1R-Ce2I1d0_ZR2HFxi6vmHZ7HvkJg-k-A8OtwLlYrF1AQ6aIcQ4xJhvsphV3DgMQ",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2024-11-14 11:42:06,916:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 491
2024-11-14 11:42:06,917:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 14 Nov 2024 10:42:06 GMT
Content-Type: application/json
Content-Length: 491
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/323011306227
Replay-Nonce: SHEOpopMXdw5MJaA7FCXL7QgRm3B89vE4R6uKYz4ylfp6Ys7nOo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-21T10:42:06Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597787",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323011306227"
}
2024-11-14 11:42:06,917:DEBUG:acme.client:Storing nonce: SHEOpopMXdw5MJaA7FCXL7QgRm3B89vE4R6uKYz4ylfp6Ys7nOo
2024-11-14 11:42:06,919:DEBUG:acme.client:JWS payload:
b''
2024-11-14 11:42:06,921:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597787:
{
  "protected": "********************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "kJPZkqBqBtAZAHUqBrbbguBAMhV2mz_oheMKcnXMGoefjdXCHvkBirNPeBzGbwtbDEKJO5ZYFr4K0tngP2zUszyAzsl1aUHDH9ghZDiFtU0YWm7lIz56NB8GEU4Soair7nJTmiOaLpa4nRjNjHuQkPDlHUKaq5RlxkE2MheqTKgQ5hJqAGmS-0MV5m5Kx40Anav7ZwAKf44lh5bEa2HmUmi5b5gAfjQ72qzvTDmfw6huKvnFmG0Yo40oRrCbwBgcADi1_p-DbuPRCbP3Uh3OWiS5pOKPwwv898fhEOJ-Y_Tvfygs4rFPsXXX9MWmjT0kEnlTL5kbOP2byT_JZWXszQ",
  "payload": ""
}
2024-11-14 11:42:07,084:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597787 HTTP/11" 200 394
2024-11-14 11:42:07,085:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 10:42:07 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopMysxM88RnCWXBcHPGzx6fkAntSwxlHUBJrK_o4cTmEsQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T10:42:06Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ",
      "status": "pending",
      "token": "tTEJ_AgvpaiWJTdT5ZzTzaW7GiIkoA6M7BJCoABpnlE"
    }
  ],
  "wildcard": true
}
2024-11-14 11:42:07,085:DEBUG:acme.client:Storing nonce: SHEOpopMysxM88RnCWXBcHPGzx6fkAntSwxlHUBJrK_o4cTmEsQ
2024-11-14 11:42:07,086:DEBUG:acme.client:JWS payload:
b''
2024-11-14 11:42:07,089:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "********************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "ZXI8DZTzLNWBNEXHQNETv9aED7W_ZHe3GyAZhDnXEnagHTg2jiHxeQ2j2hAe5SrGATHXr32LI2TF8OTa1EbnbnuEyoGF3CAHbjUysrgytNq8ypSJFUNHcwRLfuDfvDbW1wmYbafqdNnqR5oRm_B8Y_26akhxuVpff4qA4ocWyZ8ZiegQyu7-xw7VBaMMJn0Jz2M0fxW2o0RWqkcX67WMtSR8PVJfpqod3Ton2ZJjr89XzbeVu6gMYZ3JZhVvZoMbqGoXqNfKNm0DzH9R08hCCnlSxW7vB285chn_5vJeaeGX_egFM7BwsOZBzbcHitoVNDKhdq36LfM0r-JYnsNt7g",
  "payload": ""
}
2024-11-14 11:42:07,250:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" 200 804
2024-11-14 11:42:07,251:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 10:42:07 GMT
Content-Type: application/json
Content-Length: 804
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcV5JnvRfr89dhtuRixEZOjK5EcgBkKBXpQG68zuc3sxTk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T10:42:06Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/sylmAA",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    },
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/Y8uSDg",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    }
  ]
}
2024-11-14 11:42:07,251:DEBUG:acme.client:Storing nonce: aHUr_lcV5JnvRfr89dhtuRixEZOjK5EcgBkKBXpQG68zuc3sxTk
2024-11-14 11:42:07,252:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-14 11:42:07,252:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 11:42:07,252:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 11:42:07,254:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

qAILAWUbsnpzPXsSv-r_U5WrXpgUIM48webPM8v6TgI

2024-11-14 12:31:23,580:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

gLQJof3PkugsMHF0LRFPPFUcrq56KP0G6I0ZT4qJ7nI

(This must be set up in addition to the previous challenges; do not remove,
replace, or undo the previous challenge tasks yet. Note that you might be
asked to create multiple distinct TXT records with the same name. This is
permitted by DNS standards.)

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-14 13:15:29,460:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:15:29,465:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ:
{
  "protected": "******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "AyD0fC0jb0uCuocj4N1sc39U7LNfpxC5QEgUIF_U8UoMCLbHWWAcUFWfiwQmBW1URKbrJ4zBVkC4AOuwaakf3l24rNSjJhIYyYrYrEUNxfHPvdolTOnA_1Pw--AXTb8Vx6Hx2Hb0lGF6WUFwyKoEaGPjNPLYyz1suolE1Bx_cAAXI-ILp2c9SjHv64OzP1rWkQz2fDhzbWMRfgOo6ZkWjyJkvk1PLNsoOr7YaSNZ_wylH9sbZOzkjTJ1R-J0BZACJO6LgSHSv42FSzPtk0EJF3cltCy8AgrbU9pd-qa377XQa3aE0M4vW3K_5Uz8k7FUKSYOjUwWouwd8jTWvJpmDA",
  "payload": "e30"
}
2024-11-14 13:15:29,470:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2024-11-14 13:15:30,057:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/429991597787/tt56SQ HTTP/11" 400 177
2024-11-14 13:15:30,058:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 14 Nov 2024 12:15:30 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: bBPm6dJGlruL7xXH3iKtXh3tZcB__ParB8OmFckuvE1Ceekc0FU

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"aHUr_lcV5JnvRfr89dhtuRixEZOjK5EcgBkKBXpQG68zuc3sxTk\"",
  "status": 400
}
2024-11-14 13:15:30,059:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "aHUr_lcV5JnvRfr89dhtuRixEZOjK5EcgBkKBXpQG68zuc3sxTk"
2024-11-14 13:15:30,059:DEBUG:acme.client:Requesting fresh nonce
2024-11-14 13:15:30,059:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-14 13:15:30,201:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-14 13:15:30,201:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:30 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SEOe4WhpYoiOZJb44d02ckG6MDw2RNvM3kNgHHGxnDGnoU48aoE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-14 13:15:30,202:DEBUG:acme.client:Storing nonce: SEOe4WhpYoiOZJb44d02ckG6MDw2RNvM3kNgHHGxnDGnoU48aoE
2024-11-14 13:15:30,202:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:15:30,205:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTRU9lNFdocFlvaU9aSmI0NGQwMmNrRzZNRHcyUk52TTNrTmdISEd4bkRHbm9VNDhhb0UiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQyOTk5MTU5Nzc4Ny90dDU2U1EifQ",
  "signature": "AqBzem1SzIpfnxjqovAvBaiGI4Nqi1Hh6QSNgrtXiE4eNU32gvwn9cmH_ogXIu2OdO7w-ND5ApQ7GZ30uw1WB-n3YrdGkZ8rkm_MhkEK_67VT6mi7wRoQXmpzspvObDZyt_TpPB852WSN-yoDNkH4fL78U73FVl84lA5u840VT2lDcOhWkuqhQmBDC2a6g0PjZIH8VV-Zv3ncU3GtJaxj-rci__75RWAIaLjMdbWQgSP3vfOgDwIEYjGvzp3iZH1ushNJSKYQd3vkth9-v9EEBex5wm_VYew0PkzyacTMeqQhWRThxzCVDBvnPmpDt3J3-Hkczs7jypm10-L1_oiLQ",
  "payload": "e30"
}
2024-11-14 13:15:30,382:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/429991597787/tt56SQ HTTP/11" 200 186
2024-11-14 13:15:30,383:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:30 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597787>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ
Replay-Nonce: bBPm6dJGugLXeSPJ_wrg5pEEXlXVtiZpf4gj27eVlsmtBj1ybe8
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ",
  "status": "pending",
  "token": "tTEJ_AgvpaiWJTdT5ZzTzaW7GiIkoA6M7BJCoABpnlE"
}
2024-11-14 13:15:30,383:DEBUG:acme.client:Storing nonce: bBPm6dJGugLXeSPJ_wrg5pEEXlXVtiZpf4gj27eVlsmtBj1ybe8
2024-11-14 13:15:30,384:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:15:30,387:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJiQlBtNmRKR3VnTFhlU1BKX3dyZzVwRUVYbFhWdGlacGY0Z2oyN2VWbHNtdEJqMXliZTgiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQyOTk5MTU5Nzc5Ny9ObkM5MWcifQ",
  "signature": "Mey9Onqhzk6u3-HQ6rOcPODf_p5xexLpww6oxwdkChLyr4wrvWz3ArUdc8MrpFPrXTIVvv1wAKc6gAL5ZQhAxZK5__WVQpxClT8gXzWEuDl-XYw6YzwyGS-OgBYnuymGyVLTUA4ZsBj4KkT_HD6SsAFYYb7i-CExqyKqONsb2ZbfOA1s2orVlB8szLopjLdbhKnul9x-m_UWKgf48P-ymzwvW357t-FW5FvsBB9EJP9rq4f-dTlwCsl3i77u7sh0bNcZTustuflyVV7ShwYbKO8w__ryg7OISka6tRenFLlaMYl3d8Jv-ke_gLCX6p5Dbs9xvk00_eSdXCtHu2bfhQ",
  "payload": "e30"
}
2024-11-14 13:15:30,559:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/429991597797/NnC91g HTTP/11" 200 186
2024-11-14 13:15:30,560:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:30 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g
Replay-Nonce: bBPm6dJGAc3csJgcJrfCmZn3Ix6ZWXvcBkVpqOOylrFTleKhxdE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
  "status": "pending",
  "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
}
2024-11-14 13:15:30,560:DEBUG:acme.client:Storing nonce: bBPm6dJGAc3csJgcJrfCmZn3Ix6ZWXvcBkVpqOOylrFTleKhxdE
2024-11-14 13:15:30,561:INFO:certbot._internal.auth_handler:Waiting for verification...
2024-11-14 13:15:31,566:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:31,570:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597787:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJiQlBtNmRKR0FjM2NzSmdjSnJmQ21abjNJeDZaV1h2Y0JrVnBxT095bHJGVGxlS2h4ZEUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyOTk5MTU5Nzc4NyJ9",
  "signature": "bjR_2Vkqfek7Yz2X_CYazXMiqqDGgi7TFVlsDIXekewgrP7BCuS_yzX7TEFh5uzDjy5UR4ifI3v3fXzj1SYmaynVvt7A8p5K5Dr_qp387kmKHKP4u4Sbe5WuVTm4FZSDiNoUkx4W4HA5Q25a7vAJjQsBwMXfSWY2iTSfT07CGYRt8iZF3ZHYcqdcUG8gkYGpdWRTVWwASuC0QeS1wKYttQIa2blbHf2Yj0IPYQ_hc6rr8DxwfaT_8_3ZkqOufWrPeppoc3w5pdvCNWtDDaFqjqRVTmns0eC_hiVTygPf1gxDTdFXryECINkHk7j39HhShOhvvDeVEa4DFt1MwlXeDw",
  "payload": ""
}
2024-11-14 13:15:31,754:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597787 HTTP/11" 200 680
2024-11-14 13:15:31,755:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:31 GMT
Content-Type: application/json
Content-Length: 680
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SEOe4WhpwXzkDqqrDC5RwZKqJ8pVXuh3c9i037qj3ZuQsZNjzvM
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "invalid",
  "expires": "2024-11-21T10:42:06Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597787/tt56SQ",
      "status": "invalid",
      "validated": "2024-11-14T12:15:30Z",
      "error": {
        "type": "urn:ietf:params:acme:error:unauthorized",
        "detail": "Incorrect TXT record \"gLQJof3PkugsMHF0LRFPPFUcrq56KP0G6I0ZT4qJ7nI\" found at _acme-challenge.cz-test.sawapp.cloud",
        "status": 403
      },
      "token": "tTEJ_AgvpaiWJTdT5ZzTzaW7GiIkoA6M7BJCoABpnlE"
    }
  ],
  "wildcard": true
}
2024-11-14 13:15:31,755:DEBUG:acme.client:Storing nonce: SEOe4WhpwXzkDqqrDC5RwZKqJ8pVXuh3c9i037qj3ZuQsZNjzvM
2024-11-14 13:15:31,756:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:31,761:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTRU9lNFdocHdYemtEcXFyREM1UndaS3FKOHBWWHVoM2M5aTAzN3FqM1p1UXNaTmp6dk0iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyOTk5MTU5Nzc5NyJ9",
  "signature": "e35fIZHssKTA-avJlftukTeJ30Dfw8Kg9aDMXetdqWjzRvn75OWjYAW4WrB5qZo9Ap-38Y6miEWNQQnNuR5uOD38vL7oV3TeHkX8al5oziH-qCND2rjsms5n44FL8oX8tuUkcyG48-fApMun_deSLCYKeqGwVW2msRy-xxBG0PSYkK7DnlOOdbJj4I5wcmsWJRamuatKukNgnOPHPe0RMiZkz3awKOkWnZ6Cryo6vw_fJbwZM3WOnz7HTVlbD_oJ9nx73UHpGaY3s2d7S3rioRiOlOYKEZ34d1TD9pw7l_J-722ugiywxseDgMbHD7RgdwMFPz4UIF4l_yGGlTJe7w",
  "payload": ""
}
2024-11-14 13:15:31,928:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" 200 804
2024-11-14 13:15:31,929:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:31 GMT
Content-Type: application/json
Content-Length: 804
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SEOe4WhpMt7UHL7158piDYX-jh-3IhfDidrnwMgAD8fbvh55SVQ
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T10:42:06Z",
  "challenges": [
    {
      "type": "http-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/Y8uSDg",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    },
    {
      "type": "tls-alpn-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/sylmAA",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    },
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "pending",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U"
    }
  ]
}
2024-11-14 13:15:31,930:DEBUG:acme.client:Storing nonce: SEOe4WhpMt7UHL7158piDYX-jh-3IhfDidrnwMgAD8fbvh55SVQ
2024-11-14 13:15:31,930:INFO:certbot._internal.auth_handler:Challenge failed for domain cz-test.sawapp.cloud
2024-11-14 13:15:34,935:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:34,940:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTRU9lNFdocE10N1VITDcxNThwaURZWC1qaC0zSWhmRGlkcm53TWdBRDhmYnZoNTVTVlEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyOTk5MTU5Nzc5NyJ9",
  "signature": "mmC337MhJcYgVTIp7vWcfgiE09qUn63UUMsjUzqiw5LrJDbBHhKaUpVPZp9zwppk3t6dgL4iJBFHHaxltdJiZipc7gpJYzYz2DinwVC1nCwWLhysYT82mSTqyixSc9KToqe-XmO6NlqIPC90nClcfsttnE-jRMH-h-eUpBFdW_hQhwcvLqkVmznlAxX_R-ZymYq8tICLCgBaFWKAM0oZujE0IXo3V8OisWyNdxKyJU3KqBE07KQGsizpKIia0uXRKKPxnC2PdPryG5QoAR_QjEw8mvQOOPMnkJwCcqmS5e_qz4j4NQxaEZEra4PJdz16VAK4U9XMR311ETheycZiyQ",
  "payload": ""
}
2024-11-14 13:15:35,104:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" 200 515
2024-11-14 13:15:35,105:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:35 GMT
Content-Type: application/json
Content-Length: 515
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SEOe4WhpfxrFlFnxfI7WSf1FJcjwABr2T8qKEHhX7viMsLg1m5M
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:15:31Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "valid",
      "validated": "2024-11-14T12:15:30Z",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-14 13:15:35,105:DEBUG:acme.client:Storing nonce: SEOe4WhpfxrFlFnxfI7WSf1FJcjwABr2T8qKEHhX7viMsLg1m5M
2024-11-14 13:15:35,106:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 13:15:35,107:DEBUG:certbot._internal.display.obj:Notifying user: 
Certbot failed to authenticate some domains (authenticator: manual). The Certificate Authority reported these problems:
  Domain: cz-test.sawapp.cloud
  Type:   unauthorized
  Detail: Incorrect TXT record "gLQJof3PkugsMHF0LRFPPFUcrq56KP0G6I0ZT4qJ7nI" found at _acme-challenge.cz-test.sawapp.cloud

Hint: The Certificate Authority failed to verify the manually created DNS TXT records. Ensure that you created these in the correct location, or try waiting longer for DNS propagation on the next attempt.

2024-11-14 13:15:35,113:DEBUG:certbot._internal.error_handler:Encountered exception:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.

2024-11-14 13:15:35,113:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-14 13:15:35,113:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-14 13:15:35,114:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1600, in certonly
    lineage = _get_and_save_cert(le_client, config, domains, certname, lineage)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 143, in _get_and_save_cert
    lineage = le_client.obtain_and_enroll_certificate(domains, certname)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 517, in obtain_and_enroll_certificate
    cert, chain, key, _ = self.obtain_certificate(domains)
                          ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 428, in obtain_certificate
    orderr = self._get_order_and_authorizations(csr.data, self.config.allow_subset_of_names)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/client.py", line 496, in _get_order_and_authorizations
    authzr = self.auth_handler.handle_authorizations(orderr, self.config, best_effort)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 108, in handle_authorizations
    self._poll_authorizations(authzrs, max_retries, max_time_mins, best_effort)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/auth_handler.py", line 212, in _poll_authorizations
    raise errors.AuthorizationError('Some challenges have failed.')
certbot.errors.AuthorizationError: Some challenges have failed.
2024-11-14 13:15:35,119:ERROR:certbot._internal.log:Some challenges have failed.
