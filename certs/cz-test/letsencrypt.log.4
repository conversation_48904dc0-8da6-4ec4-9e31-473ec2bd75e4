2024-11-14 13:15:55,782:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-14 13:15:55,782:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-14 13:15:55,782:DEBUG:certbot._internal.main:Arguments: ['--email', '<EMAIL>', '--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2024-11-14 13:15:55,782:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-14 13:15:55,794:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-14 13:15:55,794:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 13:15:55,794:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x106d26510>
Prep: True
2024-11-14 13:15:55,794:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x106d26510> and installer None
2024-11-14 13:15:55,794:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-14 13:15:55,828:DEBUG:certbot._internal.main:Picked account: <Account(RegistrationResource(body=Registration(key=None, contact=(), agreement=None, status=None, terms_of_service_agreed=None, only_return_existing=None, external_account_binding=None), uri='https://acme-v02.api.letsencrypt.org/acme/acct/**********', new_authzr_uri=None, terms_of_service=None), 672abefd0655bb9562de0097bd749ed5, Meta(creation_dt=datetime.datetime(2024, 11, 14, 10, 42, tzinfo=<UTC>), creation_host='zdenek-mac-mini.robotea.cz', register_to_eff='<EMAIL>'))>
2024-11-14 13:15:55,835:DEBUG:acme.client:Sending GET request to https://acme-v02.api.letsencrypt.org/directory.
2024-11-14 13:15:55,838:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): acme-v02.api.letsencrypt.org:443
2024-11-14 13:15:56,329:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "GET /directory HTTP/11" 200 746
2024-11-14 13:15:56,330:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:56 GMT
Content-Type: application/json
Content-Length: 746
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "2Efp7Jf0FL8": "https://community.letsencrypt.org/t/adding-random-entries-to-the-directory/33417",
  "keyChange": "https://acme-v02.api.letsencrypt.org/acme/key-change",
  "meta": {
    "caaIdentities": [
      "letsencrypt.org"
    ],
    "termsOfService": "https://letsencrypt.org/documents/LE-SA-v1.4-April-3-2024.pdf",
    "website": "https://letsencrypt.org"
  },
  "newAccount": "https://acme-v02.api.letsencrypt.org/acme/new-acct",
  "newNonce": "https://acme-v02.api.letsencrypt.org/acme/new-nonce",
  "newOrder": "https://acme-v02.api.letsencrypt.org/acme/new-order",
  "renewalInfo": "https://acme-v02.api.letsencrypt.org/draft-ietf-acme-ari-03/renewalInfo",
  "revokeCert": "https://acme-v02.api.letsencrypt.org/acme/revoke-cert"
}
2024-11-14 13:15:56,331:DEBUG:certbot._internal.display.obj:Notifying user: Requesting a certificate for *.cz-test.sawapp.cloud and cz-test.sawapp.cloud
2024-11-14 13:15:56,336:DEBUG:acme.client:Requesting fresh nonce
2024-11-14 13:15:56,336:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-14 13:15:56,496:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-14 13:15:56,497:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:56 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopMVdFtm75IatoUvUrUghJqMiceaXQ3aGzRrEl5H9PxK5o
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-14 13:15:56,498:DEBUG:acme.client:Storing nonce: SHEOpopMVdFtm75IatoUvUrUghJqMiceaXQ3aGzRrEl5H9PxK5o
2024-11-14 13:15:56,498:DEBUG:acme.client:JWS payload:
b'{\n  "identifiers": [\n    {\n      "type": "dns",\n      "value": "*.cz-test.sawapp.cloud"\n    },\n    {\n      "type": "dns",\n      "value": "cz-test.sawapp.cloud"\n    }\n  ]\n}'
2024-11-14 13:15:56,503:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/new-order:
{
  "protected": "****************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "V9voqZeC9wd3NdQOIizuiJIiybBhtCXx5FyrY59IpoZPl7qryKivx79_IpqQ3pE0CFSvynq_h14EFj-1SjXXUyPtgbyGF-Qy8z_-3z9Yv1xUku96_vFda73wZMR-x6g3k7JMuKM9czMbXIp2KozXvPV7GFATsPHs0KMt6SJFpxoKOBiXhqIo3HBA8wOmxNmuH2LjI4fUpT1_xLLxZxmRtnV4wpzOD0PCKcmnuu0YDq-OyNtCll3Xjj9q0HYUXSoxV1lNxs7HGK9mOk0230zv2ttomuGGZvTnZXyNCZpeUL2xqXjYe-rN8ow5Hvq6FoTsdZsfxkY3gV5oAUtRQIzNXA",
  "payload": "ewogICJpZGVudGlmaWVycyI6IFsKICAgIHsKICAgICAgInR5cGUiOiAiZG5zIiwKICAgICAgInZhbHVlIjogIiouY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9LAogICAgewogICAgICAidHlwZSI6ICJkbnMiLAogICAgICAidmFsdWUiOiAiY3otdGVzdC5zYXdhcHAuY2xvdWQiCiAgICB9CiAgXQp9"
}
2024-11-14 13:15:56,711:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/new-order HTTP/11" 201 491
2024-11-14 13:15:56,712:DEBUG:acme.client:Received response:
HTTP 201
Server: nginx
Date: Thu, 14 Nov 2024 12:15:56 GMT
Content-Type: application/json
Content-Length: 491
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/323031299657
Replay-Nonce: aHUr_lcVbmF11TLPAB0r0GGEXVPIXIWdYitX_1Nao3iGP87BYoU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323031299657"
}
2024-11-14 13:15:56,712:DEBUG:acme.client:Storing nonce: aHUr_lcVbmF11TLPAB0r0GGEXVPIXIWdYitX_1Nao3iGP87BYoU
2024-11-14 13:15:56,713:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:56,717:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "********************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "BOGnCpHQ4cD-FaJBNeKNhEKsc5Vuf-aVvXWtwQHDw5uj0nDjOFCYIiYyIH_0nFiI_k7QHLV4jOOFcEAYXoaPP2Js68mvoV3xta716GdtAH-i1uf5Cv4ZwyaHlUjnJXY101R7uI7LQ11f33WYjrgIqblccEsupPQLyxVuF6orBMS3nHQpX7-fMV3_BzT8bcerch2SXHLY47NiDTJRuHVhMqLbVl4VQlLHAQWA-7a0-sTHX4rYNeI2bKekEqSbmZZAcBZDbiqh4dMp8uwFun-1VGblHJq-I_5n0Q_zQzuszYOE3GREnhYbEeNUecL1algWYUnmYpFa8W-u7AaboXIqoA",
  "payload": ""
}
2024-11-14 13:15:56,886:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" 200 515
2024-11-14 13:15:56,886:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:56 GMT
Content-Type: application/json
Content-Length: 515
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopM9zhBryQ1zHoo7WBGnO8trGa-ZTeq6BOufNjy1KkNdUE
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:15:31Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "valid",
      "validated": "2024-11-14T12:15:30Z",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-14 13:15:56,886:DEBUG:acme.client:Storing nonce: SHEOpopM9zhBryQ1zHoo7WBGnO8trGa-ZTeq6BOufNjy1KkNdUE
2024-11-14 13:15:56,887:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:15:56,889:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTSEVPcG9wTTl6aEJyeVExekhvbzdXQkduTzh0ckdhLVpUZXE2Qk91Zk5qeTFLa05kVUUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "E1I0rqVD22Oop7zvS8rGP58gQf1sud8SW8XJrHy8uJtQl8K-vYJAduyhOkNbrpgurKRwtNY38NeKr8EL5JsgVRpPdVRYBpHQ3CRbcJxhaKngowYEA-wSjsKtLP7rAFmEe2yRZUrtKmUEyhBL4wOKfDZcD_ULCm_KNE88WeE7f9mc5y9fw5mqdjQdgPlYhfog4cvXPoiRjxzrHeMNgjSU1GQr1FsdPETGlEA4aENE_Uk1NBZhCwpfD_kh_2V0dk5N8cTPEMmzaGSS1ATXZ930oNZALoeHfWwVyxl08r5Dlp4CcLUHA6-qlYb5Iqi0Aq06LyKM0HayZQXqXnjnPZb8Vw",
  "payload": ""
}
2024-11-14 13:15:57,055:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 394
2024-11-14 13:15:57,056:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:15:57 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopMzKWMKlFqCi0X5yKTREGPRelx4Ki1MhjU_ajdoR-g_dI
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "pending",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
    }
  ],
  "wildcard": true
}
2024-11-14 13:15:57,056:DEBUG:acme.client:Storing nonce: SHEOpopMzKWMKlFqCi0X5yKTREGPRelx4Ki1MhjU_ajdoR-g_dI
2024-11-14 13:15:57,057:INFO:certbot._internal.auth_handler:Performing the following challenges:
2024-11-14 13:15:57,057:INFO:certbot._internal.auth_handler:dns-01 challenge for cz-test.sawapp.cloud
2024-11-14 13:15:57,059:DEBUG:certbot._internal.display.obj:Notifying user: Please deploy a DNS TXT record under the name:

_acme-challenge.cz-test.sawapp.cloud.

with the following value:

sR1Jouk5vNsSrm7f6uSlkIoNZ10IKLsf23OdTiG5ggM

Before continuing, verify the TXT record has been deployed. Depending on the DNS
provider, this may take some time, from a few seconds to multiple minutes. You can
check if it has finished deploying with aid of online tools, such as the Google
Admin Toolbox: https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.cz-test.sawapp.cloud.
Look for one or more bolded line(s) below the line ';ANSWER'. It should show the
value(s) you've just added.

2024-11-14 13:58:58,961:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:58:58,966:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ:
{
  "protected": "******************************************************************************************************************************************************************************************************************************************************************************************************************",
  "signature": "govFz7CnzWFfCN8W_6kLlKuk5L6KdSkHy_svoNdHVvvAfQhugMyzsYFr7czWGWFiBfY0F4YfZ5hE_WnOOmi_FRMoWpz0acHU4hWu4kJjnzsmU8FZDa4CIv2cGGGjggr9UgjqKQVlWlFuq9NLmTgM3u7p6k4tT4y_o10qyxGdtNfObDn4X-qqbfLwYETpjBw2OJCu1K6Aiuz-f3OvwZetZruXWPzdS_4vnziG50VlhxQZ625KkDCtK6da75FpEuoOD9hlwumblajfooqOnb5I_VVOn1ad_0cZD85hlR8YAKUQeI3sfP5yMP3ej_C3OAfYBM3psMlNdOtKDvmUS4G1lQ",
  "payload": "e30"
}
2024-11-14 13:58:58,970:DEBUG:urllib3.connectionpool:Resetting dropped connection: acme-v02.api.letsencrypt.org
2024-11-14 13:58:59,546:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/430021057027/Du2EpQ HTTP/11" 400 177
2024-11-14 13:58:59,547:DEBUG:acme.client:Received response:
HTTP 400
Server: nginx
Date: Thu, 14 Nov 2024 12:58:59 GMT
Content-Type: application/problem+json
Content-Length: 177
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVLA9DF23QlV51KLQYSpY8soUFxUtPZzrkSY8PVRHrnG0

{
  "type": "urn:ietf:params:acme:error:badNonce",
  "detail": "JWS has an invalid anti-replay nonce: \"SHEOpopMzKWMKlFqCi0X5yKTREGPRelx4Ki1MhjU_ajdoR-g_dI\"",
  "status": 400
}
2024-11-14 13:58:59,548:DEBUG:acme.client:Retrying request after error:
urn:ietf:params:acme:error:badNonce :: The client sent an unacceptable anti-replay nonce :: JWS has an invalid anti-replay nonce: "SHEOpopMzKWMKlFqCi0X5yKTREGPRelx4Ki1MhjU_ajdoR-g_dI"
2024-11-14 13:58:59,548:DEBUG:acme.client:Requesting fresh nonce
2024-11-14 13:58:59,548:DEBUG:acme.client:Sending HEAD request to https://acme-v02.api.letsencrypt.org/acme/new-nonce.
2024-11-14 13:58:59,704:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "HEAD /acme/new-nonce HTTP/11" 200 0
2024-11-14 13:58:59,705:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:58:59 GMT
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopMT8kuuU4eteeybQy7zZU11Qy-8SbEGyyQ2TaBBw7BEqA
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800


2024-11-14 13:58:59,705:DEBUG:acme.client:Storing nonce: SHEOpopMT8kuuU4eteeybQy7zZU11Qy-8SbEGyyQ2TaBBw7BEqA
2024-11-14 13:58:59,706:DEBUG:acme.client:JWS payload:
b'{}'
2024-11-14 13:58:59,708:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTSEVPcG9wTVQ4a3V1VTRldGVleWJReTd6WlUxMVF5LThTYkVHeXlRMlRhQkJ3N0JFcUEiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NoYWxsLXYzLzQzMDAyMTA1NzAyNy9EdTJFcFEifQ",
  "signature": "kctPwmkok2mohMTCTMkitdJPwy4hcM88llEAUwrsd57SDQ_WUEB8oa0AjscY1Io4KC1QvKiw_lRQAoAqzcQ7Vl4njfA3vVd96favABB4XkSY9t9BVUXPnRGGBq7RB2QoCwAZjx3nVGfv4oKCINvqwrPEpaMqWKIZDCC4TvSUqEYl14BUJNnf9OPUknLxcZTIf6c97sUezRK00cSWW0AjVzdcWlUpBE0lFwmTCYM2f_IvkiBwJLZ8X6Os-u323Kn80wOrWquqzptWTLHLmR5fPJsI4PKKV5J_cfpBHvUNivzb-LcJGZEdfMbdS4v7SNaLWAbeLMBEIb35Mz-jmPyu3g",
  "payload": "e30"
}
2024-11-14 13:58:59,874:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/chall-v3/430021057027/Du2EpQ HTTP/11" 200 186
2024-11-14 13:58:59,875:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:58:59 GMT
Content-Type: application/json
Content-Length: 186
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027>;rel="up"
Location: https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ
Replay-Nonce: SHEOpopMaT_G8tezjMJ61SxS5r75tUOaXxkPulRv23TL-tumggk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "type": "dns-01",
  "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
  "status": "pending",
  "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
}
2024-11-14 13:58:59,875:DEBUG:acme.client:Storing nonce: SHEOpopMaT_G8tezjMJ61SxS5r75tUOaXxkPulRv23TL-tumggk
2024-11-14 13:58:59,876:INFO:certbot._internal.auth_handler:Waiting for verification...
2024-11-14 13:59:00,878:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:00,882:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTSEVPcG9wTWFUX0c4dGV6ak1KNjFTeFM1cjc1dFVPYVh4a1B1bFJ2MjNUTC10dW1nZ2siLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQyOTk5MTU5Nzc5NyJ9",
  "signature": "OzSltqhfDFvK_Lg632UL8VBLFUJeJU0KB4lnaeg_s_YRkm6rSKHt-OrWdVi64cttK3_UXkqTWy052FOe3vgChHGB8AY5LFBeRGdDWX5MFymXG-vWPB_LxvbEhsGUw5w5uLU55aAA4NLK9Y0UtMLpjnqXGK2gg4xXN8NvAWbXKLNYWbl7bZ0W2UYnXsjsHSEUa9yII0TutpIyTlWgg3vdAKeMKCkEN-W9B17vImScMzCzjF8OXCowrbXQLV4dWDr0O-e0DIF4crWKjAgzObD9xo-ZD3FEcbQUNHzvH0DNF9lV7Iybegs_2ATdIJ2iHyEw3fqrR2W7hWbhJ0zAH0Qv8A",
  "payload": ""
}
2024-11-14 13:59:01,047:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/429991597797 HTTP/11" 200 515
2024-11-14 13:59:01,048:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:00 GMT
Content-Type: application/json
Content-Length: 515
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVW3E-5bpA2uSWsgQ7-GQmQZ8P6w_oOI30kK0A_9CBPMw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:15:31Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/429991597797/NnC91g",
      "status": "valid",
      "validated": "2024-11-14T12:15:30Z",
      "token": "HqXbsFbFHn0gGrpN1LENeobkAn7h4cMLJCRfVXcSz_U",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ]
}
2024-11-14 13:59:01,048:DEBUG:acme.client:Storing nonce: aHUr_lcVW3E-5bpA2uSWsgQ7-GQmQZ8P6w_oOI30kK0A_9CBPMw
2024-11-14 13:59:01,049:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:01,053:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVlczRS01YnBBMnVTV3NnUTctR1FtUVo4UDZ3X29PSTMwa0swQV85Q0JQTXciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "abpqxrZ8tBtpcn4zXuA-dFoGaWcsQffp34e4IUtwxmfYGbWCDIp31tYRqdBuJOl6huO9DsTyLLWtVd3joOXSwf2hH9ro4ZcWLmWdhB9Z3ZZZ5AteFHJt6vppDSsz3FVF55MsO-mFgEGTNfyOfampWoeVRfCul7LEiA3YnHD-MBLGea442AQjYjzT_bH_ACasGchMHk9MJj42AB60p6GJbtvO9qIiBKvIH1IESCkvDhq0x-PETk4ZlkZVRYBvUA-5g4_M6rtffZsbeUadJmbnqrWa9PPNKStiGbBYq-X5foN76Q8HkI7heuS5wsA8WDZapFh3DxZgW1Mom4Ln_J_X7A",
  "payload": ""
}
2024-11-14 13:59:01,212:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 394
2024-11-14 13:59:01,213:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:01 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVFNJW0MVctj0aQOHTEcvmHre7NFiIrvIWvXpVnLx1jag
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "pending",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
    }
  ],
  "wildcard": true
}
2024-11-14 13:59:01,213:DEBUG:acme.client:Storing nonce: aHUr_lcVFNJW0MVctj0aQOHTEcvmHre7NFiIrvIWvXpVnLx1jag
2024-11-14 13:59:04,218:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:04,223:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVkZOSlcwTVZjdGowYVFPSFRFY3ZtSHJlN05GaUlydklXdlhwVm5MeDFqYWciLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "ViSfZ2itk29aNF7r_f3s5XMANhp6O7mFHCBpuCMZbzZFL2sEbJQw5l6iHnkuNUtS14WLSR5SM3S--mZsdtAvS_P3xL3gJx5yOfOUZs4LrQy07PKp0WtpsXtAjYOK_UABFY_1p8qSocYjJYpoEIwx8ZGZwcvllv2D0JRgBBsGR218_ymbKBEfZISdf7zbFfdCcUFy16OcEZxuCKhkNG5RuQ8DNXU-BYVmcBPQFPtSXMtj5X5JRVqZaPR8q_fcisKQUQUBVfY_5IojiY5190tbyco7gKGbE3KnmezfHE3xbspYqYKFQtdU78ZCJo-9RJ_7rzUYS87OEHnGlBcEoYq3mg",
  "payload": ""
}
2024-11-14 13:59:04,382:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 394
2024-11-14 13:59:04,383:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:04 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVycNlDr1FynXy0kp4vmuk_KmExLqHVPZNZ10dyeo2IvU
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "pending",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
    }
  ],
  "wildcard": true
}
2024-11-14 13:59:04,383:DEBUG:acme.client:Storing nonce: aHUr_lcVycNlDr1FynXy0kp4vmuk_KmExLqHVPZNZ10dyeo2IvU
2024-11-14 13:59:07,389:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:07,395:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVnljTmxEcjFGeW5YeTBrcDR2bXVrX0ttRXhMcUhWUFpOWjEwZHllbzJJdlUiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "WokxCfO4-3YRS9Y-4-P06vzw3_EIwSrn59iUqjqQ5UW3_SNYyyZATH478PJohF86zhd3E4J3pxW9LUFE_HQY4p9jpxZKNdW1OlD7ydrNc10IuREaOogR0BYziw2OlbFN7ss7-oNfB-XCymtLd2KaI1pf9cJs41PEXd4wTl3K2t4h0kewSKuu23YAME0BajSnnuOojXT6qSH2PayMEPP4YY-_E3hPW6CBCQdmXPoV0UiWDDUxppMmnXD-kGmI6qyPEWcUlUqrz1Vp88nGzR0izApyFKJ2f46b_th2xXGOiHgChZNZ8l1PlnwKrF5mL0CgpQv2gnFBz4D7NxTQv8jR8w",
  "payload": ""
}
2024-11-14 13:59:07,560:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 394
2024-11-14 13:59:07,561:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:07 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: SHEOpopMlfxQKvIyLy5hxrUBeYebdCn5mFJxmP-QQI3caV89lM4
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "pending",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
    }
  ],
  "wildcard": true
}
2024-11-14 13:59:07,562:DEBUG:acme.client:Storing nonce: SHEOpopMlfxQKvIyLy5hxrUBeYebdCn5mFJxmP-QQI3caV89lM4
2024-11-14 13:59:10,566:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:10,571:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTSEVPcG9wTWxmeFFLdkl5THk1aHhyVUJlWWViZENuNW1GSnhtUC1RUUkzY2FWODlsTTQiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "l4SP43qC9F2mAKfaN6j3Hx5gVSalvUVIQmDEVkhlgeqO74z-1gTztOtnp-NwYdWTzO8X575MnFkr0J3fOIPHTC5x5qDqZ7fXvHpGoQ5wrclYkPWoSsmqPg0I1Ma0jttePOJ1R1Vs9BdBMpM89MHYqBc47OgaFmsZCZpdGWmawu694Fcil4Ef8tkIa4o4fTGB7Bl38TqYxHTqOoMiVgikgyNamRxOD_WaKXXxnM_a0_itNugxpqr46kfJAsyjmupff1dC67zoB1oldQyc4zxmRf569f66HX4961lZJtctaKVspa6fZdjrQZ0Wdt-x2UsC4_u4WJI4B8D99k0ihsLrAg",
  "payload": ""
}
2024-11-14 13:59:10,738:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 394
2024-11-14 13:59:10,739:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:10 GMT
Content-Type: application/json
Content-Length: 394
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVul4szmQrKcGPNvQKmql2PN5cW5kzgMfnHCJJUenajZk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "pending",
  "expires": "2024-11-21T12:15:56Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "pending",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280"
    }
  ],
  "wildcard": true
}
2024-11-14 13:59:10,739:DEBUG:acme.client:Storing nonce: aHUr_lcVul4szmQrKcGPNvQKmql2PN5cW5kzgMfnHCJJUenajZk
2024-11-14 13:59:13,744:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:13,749:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVnVsNHN6bVFyS2NHUE52UUttcWwyUE41Y1c1a3pnTWZuSENKSlVlbmFqWmsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2F1dGh6LXYzLzQzMDAyMTA1NzAyNyJ9",
  "signature": "mQ5yc0SlkEbjzBuuso23Ha67g_lfRJArVZstPlr8hQMllGBjPWeKUOdeQ25OUUim38GJu-d4Te6Ygj1ysUZcnZrR2N2m3S1LI3QLjKprgvX6ZLxDC1LNNf6sUB9qIhueIZT0Li2_hHFUeyfbquxM9qkfnX-kGnCIq8_pzJCyVDR_kCxZXATT2zSC91aR34OXD34y1TXjf532ZY1_N21oEhrgTy9al_OdeTBy9tIo1hJW1j-NMXBHS0KavYTTOsOQ2RSevDUza_csn5fPDPGsXsIOP5JFm1bQY0kdw4DH1UYmkWMff9eEtQeIVTdyjbtpfua_XGL0XvgMorGIf4HVCw",
  "payload": ""
}
2024-11-14 13:59:13,916:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/authz-v3/430021057027 HTTP/11" 200 535
2024-11-14 13:59:13,917:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:13 GMT
Content-Type: application/json
Content-Length: 535
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVUbPQI767jcwOvhcpUmCWd69pMr6TBt9H00Vh2FJj3qo
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "identifier": {
    "type": "dns",
    "value": "cz-test.sawapp.cloud"
  },
  "status": "valid",
  "expires": "2024-12-14T12:59:11Z",
  "challenges": [
    {
      "type": "dns-01",
      "url": "https://acme-v02.api.letsencrypt.org/acme/chall-v3/430021057027/Du2EpQ",
      "status": "valid",
      "validated": "2024-11-14T12:58:59Z",
      "token": "8kYCVa0pYwCKjFthM8bXKJRrQ8MglkAzUDihaf4Y280",
      "validationRecord": [
        {
          "hostname": "cz-test.sawapp.cloud"
        }
      ]
    }
  ],
  "wildcard": true
}
2024-11-14 13:59:13,917:DEBUG:acme.client:Storing nonce: aHUr_lcVUbPQI767jcwOvhcpUmCWd69pMr6TBt9H00Vh2FJj3qo
2024-11-14 13:59:13,918:DEBUG:certbot._internal.error_handler:Calling registered functions
2024-11-14 13:59:13,919:INFO:certbot._internal.auth_handler:Cleaning up challenges
2024-11-14 13:59:13,920:DEBUG:certbot._internal.client:CSR: CSR(file=None, data=b'-----BEGIN CERTIFICATE REQUEST-----\nMIIBBDCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBxSivLy0b0zC\nPLCoQGTKRFeytEEEOOxnh+F8p5sTWejIbVCO/20Dek6FcnP5G/5kZ1V9BGM4oi+4\nWbzNWjU1CaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0\nLnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwID\nRwAwRAIgZhkFcuZMXh6DZuJJTJlEliq2iopeSF7YXJlrGIKGUv0CIE4AIVTFxalL\nmdGw/CilC8y6w5I2et8xi0tiALx1RUxN\n-----END CERTIFICATE REQUEST-----\n', form='pem')
2024-11-14 13:59:13,921:DEBUG:certbot._internal.client:Will poll for certificate issuance until 2024-11-14 14:00:43.921522
2024-11-14 13:59:13,922:DEBUG:acme.client:JWS payload:
b'{\n  "csr": "MIIBBDCBrAIBADAAMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBxSivLy0b0zCPLCoQGTKRFeytEEEOOxnh-F8p5sTWejIbVCO_20Dek6FcnP5G_5kZ1V9BGM4oi-4WbzNWjU1CaBKMEgGCSqGSIb3DQEJDjE7MDkwNwYDVR0RBDAwLoIWKi5jei10ZXN0LnNhd2FwcC5jbG91ZIIUY3otdGVzdC5zYXdhcHAuY2xvdWQwCgYIKoZIzj0EAwIDRwAwRAIgZhkFcuZMXh6DZuJJTJlEliq2iopeSF7YXJlrGIKGUv0CIE4AIVTFxalLmdGw_CilC8y6w5I2et8xi0tiALx1RUxN"\n}'
2024-11-14 13:59:13,924:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323031299657:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVlViUFFJNzY3amN3T3ZoY3BVbUNXZDY5cE1yNlRCdDlIMDBWaDJGSmozcW8iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2ZpbmFsaXplLzIwNTYyNDgyOTcvMzIzMDMxMjk5NjU3In0",
  "signature": "fl-BjaGAnwDvS__nUTVyfyhAnwLKvmLcBNxK8JBlzq9aySWHMn-s8iIiWIOyDnMi3N6bkekO2zCHaLR8Y6hy_Mo9soA3QQGbPVVQ5gQBlgaZnxd0T_kXsD0tIJZ-4Kcv_dcm2NCq6uGak83Ql7Mm7mkyVPgUqbUBDyS7zRp4w_yNxf3NirsfOTY2uKlRHce8_fk_2c5jaDd7YO_psa6RC5sxmGwdwIyXfEmcFIyr1QvieeIOFxYjVwIV5_ltEVVSW4QjOfjQepq0uCp5BanmJFYg5xbNqe5WSmhwVyPhZEknZP3czCptv6P-7uQZHsCujtWC9jz86thEmdrUexVPUg",
  "payload": "ewogICJjc3IiOiAiTUlJQkJEQ0JyQUlCQURBQU1Ga3dFd1lIS29aSXpqMENBUVlJS29aSXpqMERBUWNEUWdBRUJ4U2l2THkwYjB6Q1BMQ29RR1RLUkZleXRFRUVPT3huaC1GOHA1c1RXZWpJYlZDT18yMERlazZGY25QNUdfNWtaMVY5QkdNNG9pLTRXYnpOV2pVMUNhQktNRWdHQ1NxR1NJYjNEUUVKRGpFN01Ea3dOd1lEVlIwUkJEQXdMb0lXS2k1amVpMTBaWE4wTG5OaGQyRndjQzVqYkc5MVpJSVVZM290ZEdWemRDNXpZWGRoY0hBdVkyeHZkV1F3Q2dZSUtvWkl6ajBFQXdJRFJ3QXdSQUlnWmhrRmN1Wk1YaDZEWnVKSlRKbEVsaXEyaW9wZVNGN1lYSmxyR0lLR1V2MENJRTRBSVZURnhhbExtZEd3X0NpbEM4eTZ3NUkyZXQ4eGkwdGlBTHgxUlV4TiIKfQ"
}
2024-11-14 13:59:14,638:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/finalize/**********/323031299657 HTTP/11" 200 593
2024-11-14 13:59:14,639:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:14 GMT
Content-Type: application/json
Content-Length: 593
Connection: keep-alive
Boulder-Requester: **********
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Location: https://acme-v02.api.letsencrypt.org/acme/order/**********/323031299657
Replay-Nonce: SHEOpopMD0Zb18D271n8e5VF9JtwKRO7T-rpmS2ut8SwbRZwP5o
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2024-11-21T12:15:56Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323031299657",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/0300c5a30419271c369e10089d90eae93c5b"
}
2024-11-14 13:59:14,640:DEBUG:acme.client:Storing nonce: SHEOpopMD0Zb18D271n8e5VF9JtwKRO7T-rpmS2ut8SwbRZwP5o
2024-11-14 13:59:15,645:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:15,650:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/order/**********/323031299657:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJTSEVPcG9wTUQwWmIxOEQyNzFuOGU1VkY5SnR3S1JPN1QtcnBtUzJ1dDhTd2JSWndQNW8iLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL29yZGVyLzIwNTYyNDgyOTcvMzIzMDMxMjk5NjU3In0",
  "signature": "Q-d3LRIlyNrBkdILx_dt4SBoJo1yTlt6zBODl5fk-v2Zy_jp9J16JSVEUIOyCVzcOJM4gPFUNJ3AdBkdx3ozRj3ysuW02osRtMleT6ZoYC_idXyc9KDuhaYhxcf_JvwGrYrRYvCVp7C_x-vyaLYulnl33tDEmmanH7bw8SssE3JVPYb4UzpeviqD5lw_z1KNMNj1wghdpQRf6bvGB1EoN1s4NpA0G4LkMrXzykdodiFH3X59guy3kS3eEVaCRpa7H6OFAE70U9cPXoEgocs0mSfWo47qk27kUd4aV4EZqF7-acYPe9bQEqd3zijMHyiExrL8FTaf_fWKHLMuaxivOQ",
  "payload": ""
}
2024-11-14 13:59:15,821:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/order/**********/323031299657 HTTP/11" 200 593
2024-11-14 13:59:15,822:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:15 GMT
Content-Type: application/json
Content-Length: 593
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index"
Replay-Nonce: aHUr_lcVk67sxUdpWhKtcleqIs2HHmkvlt_KPiPF7i0GeAu66Fk
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

{
  "status": "valid",
  "expires": "2024-11-21T12:15:56Z",
  "identifiers": [
    {
      "type": "dns",
      "value": "cz-test.sawapp.cloud"
    },
    {
      "type": "dns",
      "value": "*.cz-test.sawapp.cloud"
    }
  ],
  "authorizations": [
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/429991597797",
    "https://acme-v02.api.letsencrypt.org/acme/authz-v3/430021057027"
  ],
  "finalize": "https://acme-v02.api.letsencrypt.org/acme/finalize/**********/323031299657",
  "certificate": "https://acme-v02.api.letsencrypt.org/acme/cert/0300c5a30419271c369e10089d90eae93c5b"
}
2024-11-14 13:59:15,822:DEBUG:acme.client:Storing nonce: aHUr_lcVk67sxUdpWhKtcleqIs2HHmkvlt_KPiPF7i0GeAu66Fk
2024-11-14 13:59:15,823:DEBUG:acme.client:JWS payload:
b''
2024-11-14 13:59:15,827:DEBUG:acme.client:Sending POST request to https://acme-v02.api.letsencrypt.org/acme/cert/0300c5a30419271c369e10089d90eae93c5b:
{
  "protected": "eyJhbGciOiAiUlMyNTYiLCAia2lkIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2FjY3QvMjA1NjI0ODI5NyIsICJub25jZSI6ICJhSFVyX2xjVms2N3N4VWRwV2hLdGNsZXFJczJISG1rdmx0X0tQaVBGN2kwR2VBdTY2RmsiLCAidXJsIjogImh0dHBzOi8vYWNtZS12MDIuYXBpLmxldHNlbmNyeXB0Lm9yZy9hY21lL2NlcnQvMDMwMGM1YTMwNDE5MjcxYzM2OWUxMDA4OWQ5MGVhZTkzYzViIn0",
  "signature": "Yzo9DNbm0XYwbeSOWEa1p7xGjtHbb3FGrR4wGFJHDBNtGyxM2p2CHQlJJ1EAfn_yHoxYK7Nz9hAQenBBTnvqNv6gNsf11j-9r5500W91Nn6BEXSDEuX-G7XkQx6r-6K0cDxLpvDdByvh6p6UNLjyUC_O3TLhWCA4CCmdakTgIJmzntqFQkhdyOIzh6C8pkvo-eAe18-qBEVg9w-ioXlZFkuSfhNkEPj-BWe5sbZIAkwuSKPYRoxyKqUvXkS_K-oT--XQdDfjK_JIrCc9QT5dkHTkXcczjPDlwMiUi6y4yu-3PsL_a9cKTQ0s1MiOvRVPvL-nfDdj7Nyt0j7W6nwZbw",
  "payload": ""
}
2024-11-14 13:59:15,989:DEBUG:urllib3.connectionpool:https://acme-v02.api.letsencrypt.org:443 "POST /acme/cert/0300c5a30419271c369e10089d90eae93c5b HTTP/11" 200 2889
2024-11-14 13:59:15,990:DEBUG:acme.client:Received response:
HTTP 200
Server: nginx
Date: Thu, 14 Nov 2024 12:59:15 GMT
Content-Type: application/pem-certificate-chain
Content-Length: 2889
Connection: keep-alive
Cache-Control: public, max-age=0, no-cache
Link: <https://acme-v02.api.letsencrypt.org/directory>;rel="index", <https://acme-v02.api.letsencrypt.org/acme/cert/0300c5a30419271c369e10089d90eae93c5b/1>;rel="alternate"
Replay-Nonce: aHUr_lcV8YG03Htv28XKVVC_aT9PLOhQxjt88AKxhLtiGd9eTdw
X-Frame-Options: DENY
Strict-Transport-Security: max-age=604800

-----BEGIN CERTIFICATE-----
MIIDozCCAyigAwIBAgISAwDFowQZJxw2nhAInZDq6TxbMAoGCCqGSM49BAMDMDIx
CzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQDEwJF
NTAeFw0yNDExMTQxMjAwNDRaFw0yNTAyMTIxMjAwNDNaMCExHzAdBgNVBAMMFiou
Y3otdGVzdC5zYXdhcHAuY2xvdWQwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAQH
FKK8vLRvTMI8sKhAZMpEV7K0QQQ47GeH4XynmxNZ6MhtUI7/bQN6ToVyc/kb/mRn
VX0EYziiL7hZvM1aNTUJo4ICLTCCAikwDgYDVR0PAQH/BAQDAgeAMB0GA1UdJQQW
MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBQY
i2qvAa6ou/giHHVgQA9A3IcajjAfBgNVHSMEGDAWgBSfK1/PPCFPnQS37SssxMZw
i9LXDTBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9lNS5vLmxl
bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL2U1LmkubGVuY3Iub3JnLzA3BgNV
HREEMDAughYqLmN6LXRlc3Quc2F3YXBwLmNsb3VkghRjei10ZXN0LnNhd2FwcC5j
bG91ZDATBgNVHSAEDDAKMAgGBmeBDAECATCCAQMGCisGAQQB1nkCBAIEgfQEgfEA
7wB1AObSMWNAd4zBEEEG13G5zsHSQPaWhIb7uocyHf0eN45QAAABkyrBpUYAAAQD
AEYwRAIgPYEbyPoQD/TDQUQFPWERhNHMHybnMpvPC1pBS4l8k44CIEqFOuLF6Dr+
DesDXaQvoaknhMyKPcN8W6kD/OvGo1FEAHYAE0rfGrWYQgl4DG/vTHqRpBa3I0nO
WFdq367ap8Kr4CIAAAGTKsGl+QAABAMARzBFAiEA6iy6zmVOwbu4LHSd+bzuJ+I2
//MrOsSfCs+OPHktLBwCIAJRiWag407O7x0UFTOIO6m4YBpafEsxdhuccYeB/2jf
MAoGCCqGSM49BAMDA2kAMGYCMQCGGCoj4ZHoSQ/fpObOBe70Jii/n5dAvcK5aici
VRhcB012UNbcNCgxXvSAZkNJRMcCMQDhvhRFrAW9BIp5Vs3xI+gWfU54gdDs7mia
/YHsUtyWkTSs+/6a7FoDUptToEd8cUI=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIEVzCCAj+gAwIBAgIRAIOPbGPOsTmMYgZigxXJ/d4wDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw
WhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCRTUwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNCzqK
a2GOtu/cX1jnxkJFVKtj9mZhSAouWXW0gQI3ULc/FnncmOyhKJdyIBwsz9V8UiBO
VHhbhBRrwJCuhezAUUE8Wod/Bk3U/mDR+mwt4X2VEIiiCFQPmRpM5uoKrNijgfgw
gfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD
ATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSfK1/PPCFPnQS37SssxMZw
i9LXDTAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB
AQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g
BAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu
Y3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAH3KdNEVCQdqk0LKyuNImTKdRJY1C
2uw2SJajuhqkyGPY8C+zzsufZ+mgnhnq1A2KVQOSykOEnUbx1cy637rBAihx97r+
bcwbZM6sTDIaEriR/PLk6LKs9Be0uoVxgOKDcpG9svD33J+G9Lcfv1K9luDmSTgG
6XNFIN5vfI5gs/lMPyojEMdIzK9blcl2/1vKxO8WGCcjvsQ1nJ/Pwt8LQZBfOFyV
XP8ubAp/au3dc4EKWG9MO5zcx1qT9+NXRGdVWxGvmBFRAajciMfXME1ZuGmk3/GO
koAM7ZkjZmleyokP1LGzmfJcUd9s7eeu1/9/eg5XlXd/55GtYjAM+C4DG5i7eaNq
cm2F+yxYIPt6cbbtYVNJCGfHWqHEQ4FYStUyFnv8sjyqU8ypgZaNJ9aVcWSICLOI
E1/Qv/7oKsnZCWJ926wU6RqG1OYPGOi1zuABhLw61cuPVDT28nQS/e6z95cJXq0e
K1BcaJ6fJZsmbjRgD5p3mvEf5vdQM7MCEvU0tHbsx2I5mHHJoABHb8KVBgWp/lcX
GWiWaeOyB7RP+OfDtvi2OsapxXiV7vNVs7fMlrRjY1joKaqmmycnBvAq14AEbtyL
sVfOS66B8apkeFX2NY4XPEYV4ZSCe8VHPrdrERk2wILG3T/EGmSIkCYVUMSnjmJd
VQD9F6Na/+zmXCc=
-----END CERTIFICATE-----

2024-11-14 13:59:15,990:DEBUG:acme.client:Storing nonce: aHUr_lcV8YG03Htv28XKVVC_aT9PLOhQxjt88AKxhLtiGd9eTdw
2024-11-14 13:59:15,992:INFO:certbot._internal.client:Non-standard path(s), might not work with crontab installed by your operating system package manager
2024-11-14 13:59:15,994:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/cz-test/archive.
2024-11-14 13:59:15,994:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/cz-test/live.
2024-11-14 13:59:15,995:DEBUG:certbot._internal.storage:Writing README to /Users/<USER>/dev/saw/certs/cz-test/live/README.
2024-11-14 13:59:15,995:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud.
2024-11-14 13:59:15,996:DEBUG:certbot._internal.storage:Creating directory /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud.
2024-11-14 13:59:15,996:DEBUG:certbot._internal.storage:Writing certificate to /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/cert.pem.
2024-11-14 13:59:15,997:DEBUG:certbot._internal.storage:Writing private key to /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/privkey.pem.
2024-11-14 13:59:15,997:DEBUG:certbot._internal.storage:Writing chain to /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/chain.pem.
2024-11-14 13:59:15,997:DEBUG:certbot._internal.storage:Writing full chain to /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/fullchain.pem.
2024-11-14 13:59:15,997:DEBUG:certbot._internal.storage:Writing README to /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/README.
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var account=672abefd0655bb9562de0097bd749ed5 (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var pref_challs=['dns-01'] (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var config_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var work_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var logs_dir=/Users/<USER>/dev/saw/certs/cz-test (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot.configuration:Var server=https://acme-v02.api.letsencrypt.org/directory (set by user).
2024-11-14 13:59:16,007:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 13:59:16,007:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 13:59:16,008:DEBUG:certbot._internal.storage:Writing new config /Users/<USER>/dev/saw/certs/cz-test/renewal/cz-test.sawapp.cloud.conf.
2024-11-14 13:59:16,010:DEBUG:certbot._internal.display.obj:Notifying user: 
Successfully received certificate.
Certificate is saved at: /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/fullchain.pem
Key is saved at:         /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/privkey.pem
This certificate expires on 2025-02-12.
These files will be updated when the certificate renews.
2024-11-14 13:59:16,010:DEBUG:certbot._internal.display.obj:Notifying user: NEXT STEPS:
2024-11-14 13:59:16,010:DEBUG:certbot._internal.display.obj:Notifying user: - This certificate will not be renewed automatically. Autorenewal of --manual certificates requires the use of an authentication hook script (--manual-auth-hook) but one was not provided. To renew this certificate, repeat this same certbot command before the certificate's expiry date.
2024-11-14 13:59:16,010:INFO:certbot._internal.eff:Subscribe to the EFF mailing list (email: <EMAIL>).
2024-11-14 13:59:16,010:DEBUG:certbot._internal.eff:Sending POST request to https://supporters.eff.org/subscribe/certbot:
{'data_type': 'json', 'email': '<EMAIL>', 'form_id': 'eff_supporters_library_subscribe_form'}
2024-11-14 13:59:16,011:DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): supporters.eff.org:443
2024-11-14 13:59:16,871:DEBUG:urllib3.connectionpool:https://supporters.eff.org:443 "POST /subscribe/certbot HTTP/11" 307 180
2024-11-14 13:59:17,051:DEBUG:urllib3.connectionpool:https://supporters.eff.org:443 "POST /donate/functions/certbot_subscribe HTTP/11" 200 None
2024-11-14 13:59:17,053:DEBUG:certbot._internal.eff:Received response:
b'{"status":true,"message":"Subscribed"}'
2024-11-14 13:59:17,057:DEBUG:certbot._internal.display.obj:Notifying user: If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
