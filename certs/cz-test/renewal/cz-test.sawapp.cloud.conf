# renew_before_expiry = 30 days
version = 3.3.0
archive_dir = /Users/<USER>/dev/saw/certs/cz-test/archive/cz-test.sawapp.cloud
cert = /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/cert.pem
privkey = /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/privkey.pem
chain = /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/chain.pem
fullchain = /Users/<USER>/dev/saw/certs/cz-test/live/cz-test.sawapp.cloud/fullchain.pem

# Options used in the renewal process
[renewalparams]
account = 672abefd0655bb9562de0097bd749ed5
pref_challs = dns-01,
config_dir = /Users/<USER>/dev/saw/certs/cz-test
work_dir = /Users/<USER>/dev/saw/certs/cz-test
logs_dir = /Users/<USER>/dev/saw/certs/cz-test
server = https://acme-v02.api.letsencrypt.org/directory
authenticator = manual
key_type = ecdsa
