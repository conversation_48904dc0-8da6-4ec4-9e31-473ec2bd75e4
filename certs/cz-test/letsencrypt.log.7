2024-11-14 11:35:31,567:DEBUG:certbot._internal.main:certbot version: 2.11.0
2024-11-14 11:35:31,567:DEBUG:certbot._internal.main:Location of certbot entry point: /opt/homebrew/bin/certbot
2024-11-14 11:35:31,567:DEBUG:certbot._internal.main:Arguments: ['--server', 'https://acme-v02.api.letsencrypt.org/directory', '--manual', '-d', '*.cz-test.sawapp.cloud', '-d', 'cz-test.sawapp.cloud', '--agree-tos', '--preferred-challenges', 'dns', '--config-dir', 'certs/cz-test', '--work-dir', 'certs/cz-test', '--logs-dir', 'certs/cz-test']
2024-11-14 11:35:31,567:DEBUG:certbot._internal.main:Discovered plugins: PluginsRegistry(PluginEntryPoint#apache,PluginEntryPoint#manual,PluginEntryPoint#nginx,PluginEntryPoint#null,PluginEntryPoint#standalone,PluginEntryPoint#webroot)
2024-11-14 11:35:31,580:DEBUG:certbot._internal.log:Root logging level set at 30
2024-11-14 11:35:31,581:DEBUG:certbot._internal.plugins.selection:Requested authenticator manual and installer None
2024-11-14 11:35:31,581:DEBUG:certbot._internal.plugins.selection:Single candidate plugin: * manual
Description: Manual configuration or run your own shell scripts
Interfaces: Authenticator, Plugin
Entry point: EntryPoint(name='manual', value='certbot._internal.plugins.manual:Authenticator', group='certbot.plugins')
Initialized: <certbot._internal.plugins.manual.Authenticator object at 0x102306510>
Prep: True
2024-11-14 11:35:31,581:DEBUG:certbot._internal.plugins.selection:Selected authenticator <certbot._internal.plugins.manual.Authenticator object at 0x102306510> and installer None
2024-11-14 11:35:31,581:INFO:certbot._internal.plugins.selection:Plugins selected: Authenticator manual, Installer None
2024-11-14 11:35:37,776:ERROR:certbot.util:Invalid email address: .
2024-11-14 11:35:49,522:DEBUG:certbot._internal.log:Exiting abnormally:
Traceback (most recent call last):
  File "/opt/homebrew/bin/certbot", line 8, in <module>
    sys.exit(main())
             ~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/main.py", line 19, in main
    return internal_main.main(cli_args)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1894, in main
    return config.func(config, plugins)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 1582, in certonly
    le_client = _init_le_client(config, auth, installer)
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 833, in _init_le_client
    acc, acme = _determine_account(config)
                ~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/_internal/main.py", line 739, in _determine_account
    config.email = display_ops.get_email()
                   ~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/certbot/2.11.0_2/libexec/lib/python3.13/site-packages/certbot/display/ops.py", line 63, in get_email
    raise errors.Error(
        "An e-mail address or "
        "--register-unsafely-without-email must be provided.")
certbot.errors.Error: An e-mail address or --register-unsafely-without-email must be provided.
2024-11-14 11:35:49,528:ERROR:certbot._internal.log:An e-mail address or --register-unsafely-without-email must be provided.
