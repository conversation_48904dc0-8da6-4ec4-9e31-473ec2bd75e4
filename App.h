//
// Created by <PERSON><PERSON><PERSON> on 18.12.21.
//

#ifndef SAW_ALL_APP_H
#define SAW_ALL_APP_H

#include <atomic>
#include "SAWSettings.h"
#include "ameliteui/AMEliteApp.h"
#include "model/Menu.h"
#include "view/AppView.h"
#include "amuitable/AMUITableCache.h"
#include "svr/AMSvrSessionForTunnel.h"

union SDL_Event;

class LoginDialog;
class ChangePasswordDialog;


struct LoginRequest;

class App:public AMEliteUI::AMEliteApp {

public:
    static App& instance();
    static App& initInstance();
    ~App();

    //void onRender() override;
    //void onProcessEvent(SDL_Event* event) override;
    void onPerform() override;
    void onCreate() override;
    void onStart() override;
    void onResume() override;
    void onPause() override;
    //void onDestroy() override;
    AMUI::AMUIIView& getMainView() override;
    AMUI::AMUIRect<float> getMainViewPort() override;


    void runLogger();
    void runUserDegrees();
    void runCategories();
    void runPeople();

    friend class MenuView;


    void *downloadHttpSyncPrepare(std::string url, bool important, const std::string &strVoidParam, std::string post = std::string(), std::string method = std::string()) override;

    void handleError(int status, std::string message) override;

protected:
    App();
    AppView view;
    GLuint m_saw_icon;
public:
    Menu menu;
    bool showTestWindow;

    //==========================================
    // Login / Logout
    //==========================================
public:
    void login(std::string domain, ImVec2 dialogOrigin = ImVec2(FLT_MIN, FLT_MIN), ImVec2 dialogSize = ImVec2(450.0f, 330.0f));
    void logout(std::string domain);
    std::string sessionId();
    [[nodiscard]] const AMSvrSessionForTunnel &session() const;
    [[nodiscard]] const AMSvrSessionForTunnel *sessionForCustomer(std::string domain) const;
    std::string openedCustomer();
    void switchOpenedCustomer(std::string domain);
    void setInitialUrl(std::string url);
    void changePassword(std::string domain, bool forceChange = false, ImVec2 dialogOrigin = ImVec2(FLT_MIN, FLT_MIN), ImVec2 dialogSize = ImVec2(450.0f, 330.0f));
    void resetPassword(std::string domain, int user);
protected:
    void onLoginDialogClose(AMDialogs::AMUIDialog *);
    void onChangePasswordDialogClose(AMDialogs::AMUIDialog *);
    void performLogin();
    void performChangePassword();
    void *prepareDataLogin(std::string username, std::string password, std::string domain);
    void *prepareDataLogout(std::string _domain);
    void *prepareDataChangePassword(std::string oldPassword, std::string newPassword, std::string domain);
    void *prepareDataResetPassword(int user, std::string domain);
    void parseLogin(std::tuple<std::unique_ptr<char>, int> data, std::string domain);
    void parseLogout(std::tuple<std::unique_ptr<char>, int> data, std::string domain);
    void parseChangePassword(std::tuple<std::unique_ptr<char>, int> data, std::string domain, bool forceChange);
    void parseResetPassword(std::tuple<std::unique_ptr<char>, int> data, std::string domain);
    void runDelayedLogin(void* data);
    void runDelayedChangePassword(void *data);
    //std::string m_runLoginMessage;
    //std::string m_runLoginDomain;
    AMSvrSessionForTunnel     m_session;
    std::map<std::string, AMSvrSessionForTunnel> m_sessionForCustomer;
    std::string m_openedCustomer;
    LoginDialog *m_loginDialog;
    std::set<LoginRequest> m_loginRequests;
    std::string m_initialUrl;
    ChangePasswordDialog *m_changePasswordDialog;
    std::set<LoginRequest> m_changePasswordRequests;
    //std::string m_runChangePasswordDomain;
    //bool m_runChangePasswordForceChange;
};


#endif //SAW_ALL_APP_H
