# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: saw-svr
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/
# =============================================================================
# Object build statements for EXECUTABLE target saw-svr


#############################################
# Order-only phony target for saw-svr

build cmake_object_order_depends_target_saw-svr: phony || CMakeFiles/saw-svr.dir

build CMakeFiles/saw-svr.dir/svr/SAWServer.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServer.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/SAWServer.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/AMSvrDB.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/AMSvrDB.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/TwoWayTable.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/TwoWayTable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/main.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/main.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/main.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/model/Acl.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Acl.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/model/Acl.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/generated/acl.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/generated/acl.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/generated/acl_tables.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/generated/acl_tables.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/model/TablesSvr.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TablesSvr.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/model/TablesSvr.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/model/TextUtils.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TextUtils.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/model/TextUtils.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLogger.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLogger.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMTextUtils.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMTextUtils.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMNullable.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMNullable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIConfig.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIConfig.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/sha-2/sha-256.c.o: C_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/sha-2/sha-256.c.o.d
  FLAGS = -DDEBUG  -g
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/sha-2
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/AMSvrUser.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/AMSvrUser.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/SAWSettings.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/SAWSettings.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/SAWSettings.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/MainPage.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/MainPage.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/MainPage.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/Installer.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/Installer.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/Installer.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/AMThread.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMThread.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/AMThread.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/LongOpItem.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LongOpItem.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/LongOpItem.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/AMSvrObject.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/AMSvrObject.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/LORefresh.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LORefresh.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/LORefresh.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/LOInstall.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LOInstall.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/LOInstall.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/LOMaintenance.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/LOMaintenance.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/InstallRequest.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/InstallRequest.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/InstallRequest.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CCategorizations.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CCategorizations.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CCategorizations.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CCitizenships.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CCitizenships.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CCitizenships.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CConnectedAccounts.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CConnectedAccounts.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/ConnectedAccountsPerformers.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/ConnectedAccountsPerformers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CEmployers.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployers.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CEmployers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CEmployerOsvcs.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CEmployerOsvcs.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CEmployerRelationships.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CEmployerRelationships.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CExaminations.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminations.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CExaminations.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CExaminationsBigTable.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CExaminationsBigTable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CExaminationTypes.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CExaminationTypes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitness.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitness.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CMedicalChecks.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CMedicalChecks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/COccupationalHazards.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/COccupationalHazards.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/COccupationalHazardsEnum.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/COccupationalHazardsEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CPeople.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPeople.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CPeople.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CPeopleData.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPeopleData.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CPeopleData.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CPersonalRisks.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CPersonalRisks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CPdfs.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPdfs.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CPdfs.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CPdfTypes.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CPdfTypes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CProfessions.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProfessions.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CProfessions.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CProfessionEducationAreas.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CProfessionEducationAreas.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CProvidersWMS.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CProvidersWMS.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditions.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditions.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CUsers.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CUsers.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CUsers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CWorkingModes.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CWorkingModes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/CWMSSpecifics2.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/CWMSSpecifics2.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/PdfMedicalCheck.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/PdfMedicalCheck.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/SAWMail.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/SAWMail.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/SAWMail.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/dependencies/amuitable
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/SAWHistorify.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/SAWHistorify.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/PersonalRisks.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/PersonalRisks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/class/MedicalCheck.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/class/MedicalCheck.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/SAWServerIniFile.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/SAWServerIniFile.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/SAWServerInit.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/SAWServerInit.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb

build CMakeFiles/saw-svr.dir/svr/SAWSvrEnums.cpp.o: CXX_COMPILER__saw-svr_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp || cmake_object_order_depends_target_saw-svr
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/saw-svr.dir/svr/SAWSvrEnums.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-svr.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_PDB = build/svr/saw-svr.pdb


# =============================================================================
# Link build statements for EXECUTABLE target saw-svr


#############################################
# Link the executable build/svr/saw-svr

build build/svr/saw-svr: CXX_EXECUTABLE_LINKER__saw-svr_Debug CMakeFiles/saw-svr.dir/svr/SAWServer.cpp.o CMakeFiles/saw-svr.dir/svr/AMSvrDB.cpp.o CMakeFiles/saw-svr.dir/svr/TwoWayTable.cpp.o CMakeFiles/saw-svr.dir/svr/main.cpp.o CMakeFiles/saw-svr.dir/model/Acl.cpp.o CMakeFiles/saw-svr.dir/generated/acl.cpp.o CMakeFiles/saw-svr.dir/generated/acl_tables.cpp.o CMakeFiles/saw-svr.dir/model/TablesSvr.cpp.o CMakeFiles/saw-svr.dir/model/TextUtils.cpp.o CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLogger.cpp.o CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMTextUtils.cpp.o CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMNullable.cpp.o CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIConfig.cpp.o CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o CMakeFiles/saw-svr.dir/dependencies/sha-2/sha-256.c.o CMakeFiles/saw-svr.dir/svr/AMSvrUser.cpp.o CMakeFiles/saw-svr.dir/SAWSettings.cpp.o CMakeFiles/saw-svr.dir/svr/MainPage.cpp.o CMakeFiles/saw-svr.dir/svr/Installer.cpp.o CMakeFiles/saw-svr.dir/svr/AMThread.cpp.o CMakeFiles/saw-svr.dir/class/LongOpItem.cpp.o CMakeFiles/saw-svr.dir/svr/AMSvrObject.cpp.o CMakeFiles/saw-svr.dir/class/LORefresh.cpp.o CMakeFiles/saw-svr.dir/class/LOInstall.cpp.o CMakeFiles/saw-svr.dir/class/LOMaintenance.cpp.o CMakeFiles/saw-svr.dir/class/InstallRequest.cpp.o CMakeFiles/saw-svr.dir/class/CCategorizations.cpp.o CMakeFiles/saw-svr.dir/class/CCitizenships.cpp.o CMakeFiles/saw-svr.dir/class/CConnectedAccounts.cpp.o CMakeFiles/saw-svr.dir/class/ConnectedAccountsPerformers.cpp.o CMakeFiles/saw-svr.dir/class/CEmployers.cpp.o CMakeFiles/saw-svr.dir/class/CEmployerOsvcs.cpp.o CMakeFiles/saw-svr.dir/class/CEmployerRelationships.cpp.o CMakeFiles/saw-svr.dir/class/CExaminations.cpp.o CMakeFiles/saw-svr.dir/class/CExaminationsBigTable.cpp.o CMakeFiles/saw-svr.dir/class/CExaminationTypes.cpp.o CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitness.cpp.o CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o CMakeFiles/saw-svr.dir/class/CMedicalChecks.cpp.o CMakeFiles/saw-svr.dir/class/COccupationalHazards.cpp.o CMakeFiles/saw-svr.dir/class/COccupationalHazardsEnum.cpp.o CMakeFiles/saw-svr.dir/class/CPeople.cpp.o CMakeFiles/saw-svr.dir/class/CPeopleData.cpp.o CMakeFiles/saw-svr.dir/class/CPersonalRisks.cpp.o CMakeFiles/saw-svr.dir/class/CPdfs.cpp.o CMakeFiles/saw-svr.dir/class/CPdfTypes.cpp.o CMakeFiles/saw-svr.dir/class/CProfessions.cpp.o CMakeFiles/saw-svr.dir/class/CProfessionEducationAreas.cpp.o CMakeFiles/saw-svr.dir/class/CProvidersWMS.cpp.o CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditions.cpp.o CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o CMakeFiles/saw-svr.dir/class/CUsers.cpp.o CMakeFiles/saw-svr.dir/class/CWorkingModes.cpp.o CMakeFiles/saw-svr.dir/class/CWMSSpecifics2.cpp.o CMakeFiles/saw-svr.dir/class/PdfMedicalCheck.cpp.o CMakeFiles/saw-svr.dir/class/SAWMail.cpp.o CMakeFiles/saw-svr.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o CMakeFiles/saw-svr.dir/svr/SAWHistorify.cpp.o CMakeFiles/saw-svr.dir/class/PersonalRisks.cpp.o CMakeFiles/saw-svr.dir/class/MedicalCheck.cpp.o CMakeFiles/saw-svr.dir/svr/SAWServerIniFile.cpp.o CMakeFiles/saw-svr.dir/svr/SAWServerInit.cpp.o CMakeFiles/saw-svr.dir/svr/SAWSvrEnums.cpp.o | /usr/local/lib/libPocoNetSSL.so.110 /usr/local/lib/libPocoNet.so.110 /usr/local/lib/libPocoCrypto.so.110 /usr/lib/x86_64-linux-gnu/libssl.so /usr/lib/x86_64-linux-gnu/libcrypto.so /usr/local/lib/libPocoUtil.so.110 /usr/local/lib/libPocoXML.so.110 /usr/local/lib/libPocoJSON.so.110 /usr/local/lib/libPocoFoundation.so.110
  FLAGS = -DDEBUG  -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/opt/libpqxx/lib:/opt/homebrew/opt/binutils/lib:/usr/local/opt/libpqxx/lib:/usr/local/opt/binutils/lib:/usr/local/lib  -ldl  -lpthread  -lpqxx  /usr/local/lib/libPocoNetSSL.so.110  -lbfd  -lcurl  /usr/local/lib/libPocoNet.so.110  /usr/local/lib/libPocoCrypto.so.110  /usr/lib/x86_64-linux-gnu/libssl.so  /usr/lib/x86_64-linux-gnu/libcrypto.so  /usr/local/lib/libPocoUtil.so.110  /usr/local/lib/libPocoXML.so.110  /usr/local/lib/libPocoJSON.so.110  /usr/local/lib/libPocoFoundation.so.110  -ldl  -lpthread  -lrt
  LINK_PATH = -L/opt/homebrew/opt/libpqxx/lib   -L/opt/homebrew/opt/binutils/lib   -L/usr/local/opt/libpqxx/lib   -L/usr/local/opt/binutils/lib
  OBJECT_DIR = CMakeFiles/saw-svr.dir
  POST_BUILD = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && cp -f ../../ClientBuildNumCache.txt build/svr/ClientBuildNumCache.txt && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && cp -f ../../saw-svr-debug.ini build/svr/saw-svr.ini
  PRE_LINK = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake -P /home/<USER>/projects/nalekarskou/CUpdateBuildNumberSvr.cmake && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr
  TARGET_COMPILE_PDB = CMakeFiles/saw-svr.dir/
  TARGET_FILE = build/svr/saw-svr
  TARGET_PDB = build/svr/saw-svr.pdb

# =============================================================================
# Object build statements for EXECUTABLE target TEST_SvrMedicalCkecks


#############################################
# Order-only phony target for TEST_SvrMedicalCkecks

build cmake_object_order_depends_target_TEST_SvrMedicalCkecks: phony || cmake_object_order_depends_target_gtest

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServer.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServer.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServer.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrDB.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrDB.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/TwoWayTable.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/TwoWayTable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/main.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/main.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/main.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/model/Acl.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Acl.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/model/Acl.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl_tables.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl_tables.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TablesSvr.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TablesSvr.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TablesSvr.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TextUtils.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TextUtils.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TextUtils.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLogger.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLogger.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMTextUtils.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMTextUtils.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMNullable.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMNullable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIConfig.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIConfig.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2/sha-256.c.o: C_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2/sha-256.c.o.d
  FLAGS = -DDEBUG  -g
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrUser.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrUser.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/SAWSettings.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/SAWSettings.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/SAWSettings.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/MainPage.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/MainPage.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/MainPage.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/Installer.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/Installer.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/Installer.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMThread.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMThread.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMThread.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LongOpItem.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LongOpItem.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LongOpItem.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrObject.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrObject.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LORefresh.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LORefresh.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LORefresh.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOInstall.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LOInstall.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOInstall.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOMaintenance.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOMaintenance.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/InstallRequest.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/InstallRequest.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/InstallRequest.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCategorizations.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CCategorizations.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCategorizations.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCitizenships.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CCitizenships.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCitizenships.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CConnectedAccounts.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CConnectedAccounts.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/ConnectedAccountsPerformers.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/ConnectedAccountsPerformers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployers.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployers.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerOsvcs.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerOsvcs.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerRelationships.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerRelationships.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminations.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminations.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminations.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationsBigTable.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationsBigTable.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationTypes.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationTypes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitness.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitness.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CMedicalChecks.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CMedicalChecks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazards.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazards.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazardsEnum.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazardsEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeople.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPeople.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeople.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeopleData.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPeopleData.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeopleData.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPersonalRisks.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPersonalRisks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfs.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPdfs.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfs.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfTypes.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfTypes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessions.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProfessions.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessions.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessionEducationAreas.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessionEducationAreas.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProvidersWMS.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProvidersWMS.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditions.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditions.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CUsers.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CUsers.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CUsers.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWorkingModes.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWorkingModes.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWMSSpecifics2.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWMSSpecifics2.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PdfMedicalCheck.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PdfMedicalCheck.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/SAWMail.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/SAWMail.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/SAWMail.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWHistorify.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWHistorify.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PersonalRisks.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PersonalRisks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/class/MedicalCheck.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/class/MedicalCheck.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/class
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerIniFile.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerIniFile.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerInit.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerInit.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWSvrEnums.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWSvrEnums.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/svr
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb

build CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks/test_MedicalChecks.cpp.o: CXX_COMPILER__TEST_SvrMedicalCkecks_unscanned_Debug /home/<USER>/projects/nalekarskou/test/svr/MedicalCkecks/test_MedicalChecks.cpp || cmake_object_order_depends_target_TEST_SvrMedicalCkecks
  DEFINES = -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500
  DEP_FILE = CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks/test_MedicalChecks.cpp.o.d
  FLAGS = -DDEBUG  -g -std=gnu++17
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  OBJECT_FILE_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_SvrMedicalCkecks


#############################################
# Link the executable build/svr/TEST_SvrMedicalCkecks

build build/svr/TEST_SvrMedicalCkecks: CXX_EXECUTABLE_LINKER__TEST_SvrMedicalCkecks_Debug CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServer.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrDB.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/TwoWayTable.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/main.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/Acl.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl_tables.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TablesSvr.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TextUtils.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLogger.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMTextUtils.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMNullable.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIConfig.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2/sha-256.c.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrUser.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/SAWSettings.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/MainPage.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/Installer.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMThread.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LongOpItem.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrObject.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LORefresh.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOInstall.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOMaintenance.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/InstallRequest.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCategorizations.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCitizenships.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CConnectedAccounts.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/ConnectedAccountsPerformers.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployers.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerOsvcs.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerRelationships.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminations.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationsBigTable.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationTypes.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitness.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CMedicalChecks.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazards.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazardsEnum.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeople.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeopleData.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPersonalRisks.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfs.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfTypes.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessions.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessionEducationAreas.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProvidersWMS.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditions.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CUsers.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWorkingModes.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWMSSpecifics2.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PdfMedicalCheck.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/SAWMail.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWHistorify.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PersonalRisks.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/MedicalCheck.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerIniFile.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerInit.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWSvrEnums.cpp.o CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks/test_MedicalChecks.cpp.o | lib/libgtest.a /usr/local/lib/libPocoNetSSL.so.110 /usr/local/lib/libPocoNet.so.110 /usr/local/lib/libPocoCrypto.so.110 /usr/lib/x86_64-linux-gnu/libssl.so /usr/lib/x86_64-linux-gnu/libcrypto.so /usr/local/lib/libPocoUtil.so.110 /usr/local/lib/libPocoXML.so.110 /usr/local/lib/libPocoJSON.so.110 /usr/local/lib/libPocoFoundation.so.110 || lib/libgtest.a
  FLAGS = -DDEBUG  -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/opt/libpqxx/lib:/opt/homebrew/opt/binutils/lib:/usr/local/opt/libpqxx/lib:/usr/local/opt/binutils/lib:/usr/local/lib  lib/libgtest.a  -lpthread  -ldl  -lpqxx  /usr/local/lib/libPocoNetSSL.so.110  -lbfd  -lcurl  /usr/local/lib/libPocoNet.so.110  /usr/local/lib/libPocoCrypto.so.110  /usr/lib/x86_64-linux-gnu/libssl.so  /usr/lib/x86_64-linux-gnu/libcrypto.so  /usr/local/lib/libPocoUtil.so.110  /usr/local/lib/libPocoXML.so.110  /usr/local/lib/libPocoJSON.so.110  /usr/local/lib/libPocoFoundation.so.110  -lpthread  -ldl  -lrt
  LINK_PATH = -L/opt/homebrew/opt/libpqxx/lib   -L/opt/homebrew/opt/binutils/lib   -L/usr/local/opt/libpqxx/lib   -L/usr/local/opt/binutils/lib
  OBJECT_DIR = CMakeFiles/TEST_SvrMedicalCkecks.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/TEST_SvrMedicalCkecks.dir/
  TARGET_FILE = build/svr/TEST_SvrMedicalCkecks
  TARGET_PDB = build/svr/TEST_SvrMedicalCkecks.pdb


#############################################
# Utility command for test

build CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build test: phony CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/nalekarskou -B/home/<USER>/projects/nalekarskou/build/cmake-debug-svr
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/projects/nalekarskou/CMakeSvr.cmake
# =============================================================================


#############################################
# Utility command for test

build dependencies/googletest/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build dependencies/googletest/test: phony dependencies/googletest/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build dependencies/googletest/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build dependencies/googletest/edit_cache: phony dependencies/googletest/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build dependencies/googletest/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/nalekarskou -B/home/<USER>/projects/nalekarskou/build/cmake-debug-svr
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build dependencies/googletest/rebuild_cache: phony dependencies/googletest/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build dependencies/googletest/list_install_components: phony


#############################################
# Utility command for install

build dependencies/googletest/CMakeFiles/install.util: CUSTOM_COMMAND dependencies/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build dependencies/googletest/install: phony dependencies/googletest/CMakeFiles/install.util


#############################################
# Utility command for install/local

build dependencies/googletest/CMakeFiles/install/local.util: CUSTOM_COMMAND dependencies/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build dependencies/googletest/install/local: phony dependencies/googletest/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build dependencies/googletest/CMakeFiles/install/strip.util: CUSTOM_COMMAND dependencies/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build dependencies/googletest/install/strip: phony dependencies/googletest/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/projects/nalekarskou/dependencies/googletest/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target gmock


#############################################
# Order-only phony target for gmock

build cmake_object_order_depends_target_gmock: phony || cmake_object_order_depends_target_gtest

build dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o: CXX_COMPILER__gmock_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock-all.cc || cmake_object_order_depends_target_gmock
  DEP_FILE = dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o.d
  FLAGS = -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock.dir
  OBJECT_FILE_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src
  TARGET_COMPILE_PDB = lib/libgmockpdb_debug_postfix-NOTFOUND.pdb
  TARGET_PDB = bin/libgmockpdb_debug_postfix-NOTFOUND.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target gmock


#############################################
# Link the static library lib/libgmock.a

build lib/libgmock.a: CXX_STATIC_LIBRARY_LINKER__gmock_Debug dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o || lib/libgtest.a
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = lib/libgmockpdb_debug_postfix-NOTFOUND.pdb
  TARGET_FILE = lib/libgmock.a
  TARGET_PDB = bin/libgmockpdb_debug_postfix-NOTFOUND.pdb

# =============================================================================
# Object build statements for STATIC_LIBRARY target gmock_main


#############################################
# Order-only phony target for gmock_main

build cmake_object_order_depends_target_gmock_main: phony || cmake_object_order_depends_target_gmock cmake_object_order_depends_target_gtest

build dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o: CXX_COMPILER__gmock_main_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock_main.cc || cmake_object_order_depends_target_gmock_main
  DEP_FILE = dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.d
  FLAGS = -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir
  OBJECT_FILE_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src
  TARGET_COMPILE_PDB = lib/libgmock_mainpdb_debug_postfix-NOTFOUND.pdb
  TARGET_PDB = bin/libgmock_mainpdb_debug_postfix-NOTFOUND.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target gmock_main


#############################################
# Link the static library lib/libgmock_main.a

build lib/libgmock_main.a: CXX_STATIC_LIBRARY_LINKER__gmock_main_Debug dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o || lib/libgmock.a lib/libgtest.a
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = lib/libgmock_mainpdb_debug_postfix-NOTFOUND.pdb
  TARGET_FILE = lib/libgmock_main.a
  TARGET_PDB = bin/libgmock_mainpdb_debug_postfix-NOTFOUND.pdb


#############################################
# Utility command for test

build dependencies/googletest/googlemock/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build dependencies/googletest/googlemock/test: phony dependencies/googletest/googlemock/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build dependencies/googletest/googlemock/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build dependencies/googletest/googlemock/edit_cache: phony dependencies/googletest/googlemock/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build dependencies/googletest/googlemock/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/nalekarskou -B/home/<USER>/projects/nalekarskou/build/cmake-debug-svr
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build dependencies/googletest/googlemock/rebuild_cache: phony dependencies/googletest/googlemock/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build dependencies/googletest/googlemock/list_install_components: phony


#############################################
# Utility command for install

build dependencies/googletest/googlemock/CMakeFiles/install.util: CUSTOM_COMMAND dependencies/googletest/googlemock/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build dependencies/googletest/googlemock/install: phony dependencies/googletest/googlemock/CMakeFiles/install.util


#############################################
# Utility command for install/local

build dependencies/googletest/googlemock/CMakeFiles/install/local.util: CUSTOM_COMMAND dependencies/googletest/googlemock/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build dependencies/googletest/googlemock/install/local: phony dependencies/googletest/googlemock/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build dependencies/googletest/googlemock/CMakeFiles/install/strip.util: CUSTOM_COMMAND dependencies/googletest/googlemock/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build dependencies/googletest/googlemock/install/strip: phony dependencies/googletest/googlemock/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target gtest


#############################################
# Order-only phony target for gtest

build cmake_object_order_depends_target_gtest: phony || dependencies/googletest/googletest/CMakeFiles/gtest.dir

build dependencies/googletest/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o: CXX_COMPILER__gtest_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest-all.cc || cmake_object_order_depends_target_gtest
  DEP_FILE = dependencies/googletest/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o.d
  FLAGS = -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = dependencies/googletest/googletest/CMakeFiles/gtest.dir
  OBJECT_FILE_DIR = dependencies/googletest/googletest/CMakeFiles/gtest.dir/src
  TARGET_COMPILE_PDB = lib/libgtestpdb_debug_postfix-NOTFOUND.pdb
  TARGET_PDB = bin/libgtestpdb_debug_postfix-NOTFOUND.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target gtest


#############################################
# Link the static library lib/libgtest.a

build lib/libgtest.a: CXX_STATIC_LIBRARY_LINKER__gtest_Debug dependencies/googletest/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = dependencies/googletest/googletest/CMakeFiles/gtest.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = lib/libgtestpdb_debug_postfix-NOTFOUND.pdb
  TARGET_FILE = lib/libgtest.a
  TARGET_PDB = bin/libgtestpdb_debug_postfix-NOTFOUND.pdb

# =============================================================================
# Object build statements for STATIC_LIBRARY target gtest_main


#############################################
# Order-only phony target for gtest_main

build cmake_object_order_depends_target_gtest_main: phony || cmake_object_order_depends_target_gtest

build dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o: CXX_COMPILER__gtest_main_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest_main.cc || cmake_object_order_depends_target_gtest_main
  DEP_FILE = dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.d
  FLAGS = -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers
  INCLUDES = -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest
  OBJECT_DIR = dependencies/googletest/googletest/CMakeFiles/gtest_main.dir
  OBJECT_FILE_DIR = dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src
  TARGET_COMPILE_PDB = lib/libgtest_mainpdb_debug_postfix-NOTFOUND.pdb
  TARGET_PDB = bin/libgtest_mainpdb_debug_postfix-NOTFOUND.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target gtest_main


#############################################
# Link the static library lib/libgtest_main.a

build lib/libgtest_main.a: CXX_STATIC_LIBRARY_LINKER__gtest_main_Debug dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o || lib/libgtest.a
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = dependencies/googletest/googletest/CMakeFiles/gtest_main.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = lib/libgtest_mainpdb_debug_postfix-NOTFOUND.pdb
  TARGET_FILE = lib/libgtest_main.a
  TARGET_PDB = bin/libgtest_mainpdb_debug_postfix-NOTFOUND.pdb


#############################################
# Utility command for test

build dependencies/googletest/googletest/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/ctest --force-new-ctest-process
  DESC = Running tests...
  pool = console
  restat = 1

build dependencies/googletest/googletest/test: phony dependencies/googletest/googletest/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build dependencies/googletest/googletest/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build dependencies/googletest/googletest/edit_cache: phony dependencies/googletest/googletest/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build dependencies/googletest/googletest/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/nalekarskou -B/home/<USER>/projects/nalekarskou/build/cmake-debug-svr
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build dependencies/googletest/googletest/rebuild_cache: phony dependencies/googletest/googletest/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build dependencies/googletest/googletest/list_install_components: phony


#############################################
# Utility command for install

build dependencies/googletest/googletest/CMakeFiles/install.util: CUSTOM_COMMAND dependencies/googletest/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build dependencies/googletest/googletest/install: phony dependencies/googletest/googletest/CMakeFiles/install.util


#############################################
# Utility command for install/local

build dependencies/googletest/googletest/CMakeFiles/install/local.util: CUSTOM_COMMAND dependencies/googletest/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build dependencies/googletest/googletest/install/local: phony dependencies/googletest/googletest/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build dependencies/googletest/googletest/CMakeFiles/install/strip.util: CUSTOM_COMMAND dependencies/googletest/googletest/all
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build dependencies/googletest/googletest/install/strip: phony dependencies/googletest/googletest/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build TEST_SvrMedicalCkecks: phony build/svr/TEST_SvrMedicalCkecks

build gmock: phony lib/libgmock.a

build gmock_main: phony lib/libgmock_main.a

build gtest: phony lib/libgtest.a

build gtest_main: phony lib/libgtest_main.a

build libgmock.a: phony lib/libgmock.a

build libgmock_main.a: phony lib/libgmock_main.a

build libgtest.a: phony lib/libgtest.a

build libgtest_main.a: phony lib/libgtest_main.a

build saw-svr: phony build/svr/saw-svr

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/nalekarskou/build/cmake-debug-svr

build all: phony build/svr/saw-svr build/svr/TEST_SvrMedicalCkecks dependencies/googletest/all

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest

build dependencies/googletest/all: phony dependencies/googletest/googlemock/all

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googlemock

build dependencies/googletest/googlemock/all: phony lib/libgmock.a lib/libgmock_main.a dependencies/googletest/googletest/all

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/nalekarskou/build/cmake-debug-svr/dependencies/googletest/googletest

build dependencies/googletest/googletest/all: phony lib/libgtest.a lib/libgtest_main.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/projects/nalekarskou/CMakeLists.txt /home/<USER>/projects/nalekarskou/CMakeSvr.cmake /home/<USER>/projects/nalekarskou/dependencies/googletest/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/cmake/gmock.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/cmake/gmock_main.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/Config.cmake.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/gtest.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/gtest_main.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/internal_utils.cmake /usr/local/lib/cmake/Poco/PocoConfig.cmake /usr/local/lib/cmake/Poco/PocoConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoCryptoConfig.cmake /usr/local/lib/cmake/Poco/PocoCryptoConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoCryptoTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoCryptoTargets.cmake /usr/local/lib/cmake/Poco/PocoFoundationConfig.cmake /usr/local/lib/cmake/Poco/PocoFoundationConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoFoundationTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoFoundationTargets.cmake /usr/local/lib/cmake/Poco/PocoJSONConfig.cmake /usr/local/lib/cmake/Poco/PocoJSONConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoJSONTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoJSONTargets.cmake /usr/local/lib/cmake/Poco/PocoNetConfig.cmake /usr/local/lib/cmake/Poco/PocoNetConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoNetSSLConfig.cmake /usr/local/lib/cmake/Poco/PocoNetSSLConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoNetSSLTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoNetSSLTargets.cmake /usr/local/lib/cmake/Poco/PocoNetTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoNetTargets.cmake /usr/local/lib/cmake/Poco/PocoUtilConfig.cmake /usr/local/lib/cmake/Poco/PocoUtilConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoUtilTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoUtilTargets.cmake /usr/local/lib/cmake/Poco/PocoXMLConfig.cmake /usr/local/lib/cmake/Poco/PocoXMLConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoXMLTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoXMLTargets.cmake /usr/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDependentOption.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/FindOpenSSL.cmake /usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/share/cmake-3.28/Modules/FindThreads.cmake /usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/projects/nalekarskou/CMakeLists.txt /home/<USER>/projects/nalekarskou/CMakeSvr.cmake /home/<USER>/projects/nalekarskou/dependencies/googletest/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/cmake/gmock.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/cmake/gmock_main.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/CMakeLists.txt /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/Config.cmake.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/gtest.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/gtest_main.pc.in /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/cmake/internal_utils.cmake /usr/local/lib/cmake/Poco/PocoConfig.cmake /usr/local/lib/cmake/Poco/PocoConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoCryptoConfig.cmake /usr/local/lib/cmake/Poco/PocoCryptoConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoCryptoTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoCryptoTargets.cmake /usr/local/lib/cmake/Poco/PocoFoundationConfig.cmake /usr/local/lib/cmake/Poco/PocoFoundationConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoFoundationTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoFoundationTargets.cmake /usr/local/lib/cmake/Poco/PocoJSONConfig.cmake /usr/local/lib/cmake/Poco/PocoJSONConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoJSONTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoJSONTargets.cmake /usr/local/lib/cmake/Poco/PocoNetConfig.cmake /usr/local/lib/cmake/Poco/PocoNetConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoNetSSLConfig.cmake /usr/local/lib/cmake/Poco/PocoNetSSLConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoNetSSLTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoNetSSLTargets.cmake /usr/local/lib/cmake/Poco/PocoNetTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoNetTargets.cmake /usr/local/lib/cmake/Poco/PocoUtilConfig.cmake /usr/local/lib/cmake/Poco/PocoUtilConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoUtilTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoUtilTargets.cmake /usr/local/lib/cmake/Poco/PocoXMLConfig.cmake /usr/local/lib/cmake/Poco/PocoXMLConfigVersion.cmake /usr/local/lib/cmake/Poco/PocoXMLTargets-relwithdebinfo.cmake /usr/local/lib/cmake/Poco/PocoXMLTargets.cmake /usr/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDependentOption.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/FindOpenSSL.cmake /usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/share/cmake-3.28/Modules/FindThreads.cmake /usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake /usr/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
