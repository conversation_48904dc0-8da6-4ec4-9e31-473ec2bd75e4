[{"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/SAWServer.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServer.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServer.cpp", "output": "CMakeFiles/saw-svr.dir/svr/SAWServer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/AMSvrDB.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp", "output": "CMakeFiles/saw-svr.dir/svr/AMSvrDB.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/TwoWayTable.cpp.o -c /home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp", "output": "CMakeFiles/saw-svr.dir/svr/TwoWayTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/main.cpp.o -c /home/<USER>/projects/nalekarskou/svr/main.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/main.cpp", "output": "CMakeFiles/saw-svr.dir/svr/main.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/model/Acl.cpp.o -c /home/<USER>/projects/nalekarskou/model/Acl.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Acl.cpp", "output": "CMakeFiles/saw-svr.dir/model/Acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/generated/acl.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl.cpp", "output": "CMakeFiles/saw-svr.dir/generated/acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/generated/acl_tables.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "output": "CMakeFiles/saw-svr.dir/generated/acl_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/model/TablesSvr.cpp.o -c /home/<USER>/projects/nalekarskou/model/TablesSvr.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TablesSvr.cpp", "output": "CMakeFiles/saw-svr.dir/model/TablesSvr.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/model/TextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/model/TextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TextUtils.cpp", "output": "CMakeFiles/saw-svr.dir/model/TextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLogger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLogger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMTextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMTextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amcore/src/AMNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/cc -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -o CMakeFiles/saw-svr.dir/dependencies/sha-2/sha-256.c.o -c /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "output": "CMakeFiles/saw-svr.dir/dependencies/sha-2/sha-256.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/AMSvrUser.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp", "output": "CMakeFiles/saw-svr.dir/svr/AMSvrUser.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/SAWSettings.cpp.o -c /home/<USER>/projects/nalekarskou/SAWSettings.cpp", "file": "/home/<USER>/projects/nalekarskou/SAWSettings.cpp", "output": "CMakeFiles/saw-svr.dir/SAWSettings.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/MainPage.cpp.o -c /home/<USER>/projects/nalekarskou/svr/MainPage.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/MainPage.cpp", "output": "CMakeFiles/saw-svr.dir/svr/MainPage.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/Installer.cpp.o -c /home/<USER>/projects/nalekarskou/svr/Installer.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/Installer.cpp", "output": "CMakeFiles/saw-svr.dir/svr/Installer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/AMThread.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMThread.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMThread.cpp", "output": "CMakeFiles/saw-svr.dir/svr/AMThread.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/LongOpItem.cpp.o -c /home/<USER>/projects/nalekarskou/class/LongOpItem.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LongOpItem.cpp", "output": "CMakeFiles/saw-svr.dir/class/LongOpItem.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/AMSvrObject.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp", "output": "CMakeFiles/saw-svr.dir/svr/AMSvrObject.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/LORefresh.cpp.o -c /home/<USER>/projects/nalekarskou/class/LORefresh.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LORefresh.cpp", "output": "CMakeFiles/saw-svr.dir/class/LORefresh.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/LOInstall.cpp.o -c /home/<USER>/projects/nalekarskou/class/LOInstall.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LOInstall.cpp", "output": "CMakeFiles/saw-svr.dir/class/LOInstall.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/LOMaintenance.cpp.o -c /home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp", "output": "CMakeFiles/saw-svr.dir/class/LOMaintenance.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/InstallRequest.cpp.o -c /home/<USER>/projects/nalekarskou/class/InstallRequest.cpp", "file": "/home/<USER>/projects/nalekarskou/class/InstallRequest.cpp", "output": "CMakeFiles/saw-svr.dir/class/InstallRequest.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CCategorizations.cpp.o -c /home/<USER>/projects/nalekarskou/class/CCategorizations.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CCategorizations.cpp", "output": "CMakeFiles/saw-svr.dir/class/CCategorizations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CCitizenships.cpp.o -c /home/<USER>/projects/nalekarskou/class/CCitizenships.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CCitizenships.cpp", "output": "CMakeFiles/saw-svr.dir/class/CCitizenships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CConnectedAccounts.cpp.o -c /home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp", "output": "CMakeFiles/saw-svr.dir/class/CConnectedAccounts.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/ConnectedAccountsPerformers.cpp.o -c /home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp", "output": "CMakeFiles/saw-svr.dir/class/ConnectedAccountsPerformers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CEmployers.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployers.cpp", "output": "CMakeFiles/saw-svr.dir/class/CEmployers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CEmployerOsvcs.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp", "output": "CMakeFiles/saw-svr.dir/class/CEmployerOsvcs.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CEmployerRelationships.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp", "output": "CMakeFiles/saw-svr.dir/class/CEmployerRelationships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CExaminations.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminations.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminations.cpp", "output": "CMakeFiles/saw-svr.dir/class/CExaminations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CExaminationsBigTable.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp", "output": "CMakeFiles/saw-svr.dir/class/CExaminationsBigTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CExaminationTypes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp", "output": "CMakeFiles/saw-svr.dir/class/CExaminationTypes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitness.cpp.o -c /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp", "output": "CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitness.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp", "output": "CMakeFiles/saw-svr.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CMedicalChecks.cpp.o -c /home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp", "output": "CMakeFiles/saw-svr.dir/class/CMedicalChecks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/COccupationalHazards.cpp.o -c /home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp", "file": "/home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp", "output": "CMakeFiles/saw-svr.dir/class/COccupationalHazards.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/COccupationalHazardsEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp", "output": "CMakeFiles/saw-svr.dir/class/COccupationalHazardsEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CPeople.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPeople.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPeople.cpp", "output": "CMakeFiles/saw-svr.dir/class/CPeople.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CPeopleData.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPeopleData.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPeopleData.cpp", "output": "CMakeFiles/saw-svr.dir/class/CPeopleData.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CPersonalRisks.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp", "output": "CMakeFiles/saw-svr.dir/class/CPersonalRisks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CPdfs.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPdfs.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPdfs.cpp", "output": "CMakeFiles/saw-svr.dir/class/CPdfs.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CPdfTypes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp", "output": "CMakeFiles/saw-svr.dir/class/CPdfTypes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CProfessions.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProfessions.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProfessions.cpp", "output": "CMakeFiles/saw-svr.dir/class/CProfessions.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CProfessionEducationAreas.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp", "output": "CMakeFiles/saw-svr.dir/class/CProfessionEducationAreas.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CProvidersWMS.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp", "output": "CMakeFiles/saw-svr.dir/class/CProvidersWMS.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditions.cpp.o -c /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp", "output": "CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditions.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp", "output": "CMakeFiles/saw-svr.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CUsers.cpp.o -c /home/<USER>/projects/nalekarskou/class/CUsers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CUsers.cpp", "output": "CMakeFiles/saw-svr.dir/class/CUsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CWorkingModes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp", "output": "CMakeFiles/saw-svr.dir/class/CWorkingModes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/CWMSSpecifics2.cpp.o -c /home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp", "output": "CMakeFiles/saw-svr.dir/class/CWMSSpecifics2.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/PdfMedicalCheck.cpp.o -c /home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp", "file": "/home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp", "output": "CMakeFiles/saw-svr.dir/class/PdfMedicalCheck.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/SAWMail.cpp.o -c /home/<USER>/projects/nalekarskou/class/SAWMail.cpp", "file": "/home/<USER>/projects/nalekarskou/class/SAWMail.cpp", "output": "CMakeFiles/saw-svr.dir/class/SAWMail.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp", "output": "CMakeFiles/saw-svr.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/SAWHistorify.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp", "output": "CMakeFiles/saw-svr.dir/svr/SAWHistorify.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/PersonalRisks.cpp.o -c /home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp", "output": "CMakeFiles/saw-svr.dir/class/PersonalRisks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/class/MedicalCheck.cpp.o -c /home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp", "file": "/home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp", "output": "CMakeFiles/saw-svr.dir/class/MedicalCheck.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/SAWServerIniFile.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp", "output": "CMakeFiles/saw-svr.dir/svr/SAWServerIniFile.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/SAWServerInit.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp", "output": "CMakeFiles/saw-svr.dir/svr/SAWServerInit.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/saw-svr.dir/svr/SAWSvrEnums.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp", "output": "CMakeFiles/saw-svr.dir/svr/SAWSvrEnums.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServer.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServer.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServer.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrDB.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrDB.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrDB.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/TwoWayTable.cpp.o -c /home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/TwoWayTable.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/TwoWayTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/main.cpp.o -c /home/<USER>/projects/nalekarskou/svr/main.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/main.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/main.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/Acl.cpp.o -c /home/<USER>/projects/nalekarskou/model/Acl.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Acl.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/model/Acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl_tables.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/generated/acl_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TablesSvr.cpp.o -c /home/<USER>/projects/nalekarskou/model/TablesSvr.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TablesSvr.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TablesSvr.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/model/TextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TextUtils.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/model/TextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLogger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLogger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLoggerSvr.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amloglite/src/AMLoggerSvr.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMTextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMTextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amcore/src/AMNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/cc -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2/sha-256.c.o -c /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/sha-2/sha-256.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrUser.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrUser.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrUser.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/SAWSettings.cpp.o -c /home/<USER>/projects/nalekarskou/SAWSettings.cpp", "file": "/home/<USER>/projects/nalekarskou/SAWSettings.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/SAWSettings.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/MainPage.cpp.o -c /home/<USER>/projects/nalekarskou/svr/MainPage.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/MainPage.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/MainPage.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/Installer.cpp.o -c /home/<USER>/projects/nalekarskou/svr/Installer.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/Installer.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/Installer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMThread.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMThread.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMThread.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMThread.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LongOpItem.cpp.o -c /home/<USER>/projects/nalekarskou/class/LongOpItem.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LongOpItem.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LongOpItem.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrObject.cpp.o -c /home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/AMSvrObject.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/AMSvrObject.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LORefresh.cpp.o -c /home/<USER>/projects/nalekarskou/class/LORefresh.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LORefresh.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LORefresh.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOInstall.cpp.o -c /home/<USER>/projects/nalekarskou/class/LOInstall.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LOInstall.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOInstall.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOMaintenance.cpp.o -c /home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp", "file": "/home/<USER>/projects/nalekarskou/class/LOMaintenance.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/LOMaintenance.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/InstallRequest.cpp.o -c /home/<USER>/projects/nalekarskou/class/InstallRequest.cpp", "file": "/home/<USER>/projects/nalekarskou/class/InstallRequest.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/InstallRequest.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCategorizations.cpp.o -c /home/<USER>/projects/nalekarskou/class/CCategorizations.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CCategorizations.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCategorizations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCitizenships.cpp.o -c /home/<USER>/projects/nalekarskou/class/CCitizenships.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CCitizenships.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CCitizenships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CConnectedAccounts.cpp.o -c /home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CConnectedAccounts.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CConnectedAccounts.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/ConnectedAccountsPerformers.cpp.o -c /home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/ConnectedAccountsPerformers.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/ConnectedAccountsPerformers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployers.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployers.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerOsvcs.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployerOsvcs.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerOsvcs.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerRelationships.cpp.o -c /home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CEmployerRelationships.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CEmployerRelationships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminations.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminations.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminations.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationsBigTable.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminationsBigTable.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationsBigTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationTypes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CExaminationTypes.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CExaminationTypes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitness.cpp.o -c /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitness.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitness.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CLegalRequirementsForMedicalFitnessEnum.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CLegalRequirementsForMedicalFitnessEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CMedicalChecks.cpp.o -c /home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CMedicalChecks.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CMedicalChecks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazards.cpp.o -c /home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp", "file": "/home/<USER>/projects/nalekarskou/class/COccupationalHazards.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazards.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazardsEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/COccupationalHazardsEnum.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/COccupationalHazardsEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeople.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPeople.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPeople.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeople.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeopleData.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPeopleData.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPeopleData.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPeopleData.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPersonalRisks.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPersonalRisks.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPersonalRisks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfs.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPdfs.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPdfs.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfs.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfTypes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CPdfTypes.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CPdfTypes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessions.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProfessions.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProfessions.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessions.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessionEducationAreas.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProfessionEducationAreas.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProfessionEducationAreas.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProvidersWMS.cpp.o -c /home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CProvidersWMS.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CProvidersWMS.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditions.cpp.o -c /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditions.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditions.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o -c /home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CRiskFactorsWorkingConditionsEnum.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CRiskFactorsWorkingConditionsEnum.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CUsers.cpp.o -c /home/<USER>/projects/nalekarskou/class/CUsers.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CUsers.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CUsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWorkingModes.cpp.o -c /home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CWorkingModes.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWorkingModes.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWMSSpecifics2.cpp.o -c /home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp", "file": "/home/<USER>/projects/nalekarskou/class/CWMSSpecifics2.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/CWMSSpecifics2.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PdfMedicalCheck.cpp.o -c /home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp", "file": "/home/<USER>/projects/nalekarskou/class/PdfMedicalCheck.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PdfMedicalCheck.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/SAWMail.cpp.o -c /home/<USER>/projects/nalekarskou/class/SAWMail.cpp", "file": "/home/<USER>/projects/nalekarskou/class/SAWMail.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/SAWMail.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/AMUISqlHistorify.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/dependencies/amuitable/AMUISqlHistorify.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWHistorify.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWHistorify.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWHistorify.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PersonalRisks.cpp.o -c /home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp", "file": "/home/<USER>/projects/nalekarskou/class/PersonalRisks.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/PersonalRisks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/class/MedicalCheck.cpp.o -c /home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp", "file": "/home/<USER>/projects/nalekarskou/class/MedicalCheck.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/class/MedicalCheck.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerIniFile.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServerIniFile.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerIniFile.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerInit.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWServerInit.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWServerInit.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWSvrEnums.cpp.o -c /home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp", "file": "/home/<USER>/projects/nalekarskou/svr/SAWSvrEnums.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/svr/SAWSvrEnums.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -DPOCO_ENABLE_CPP11 -DPOCO_ENABLE_CPP14 -DPOCO_HAVE_FD_EPOLL -DPOCO_OS_FAMILY_UNIX -DUNIT_TEST -DUTF8PROC_STATIC -DXML_DTD -DXML_GE -D_DEBUG -D_FILE_OFFSET_BITS=64 -D_LARGEFILE64_SOURCE -D_REENTRANT -D_THREAD_SAFE -D_XOPEN_SOURCE=500 -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/opt/homebrew/opt/libpqxx/include -I/usr/local/opt/libpqxx/include -I/usr/include/libdwarf -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies/json/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest  -DDEBUG  -g -std=gnu++17 -o CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks/test_MedicalChecks.cpp.o -c /home/<USER>/projects/nalekarskou/test/svr/MedicalCkecks/test_MedicalChecks.cpp", "file": "/home/<USER>/projects/nalekarskou/test/svr/MedicalCkecks/test_MedicalChecks.cpp", "output": "CMakeFiles/TEST_SvrMedicalCkecks.dir/test/svr/MedicalCkecks/test_MedicalChecks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o -c /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock-all.cc", "file": "/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock-all.cc", "output": "dependencies/googletest/googlemock/CMakeFiles/gmock.dir/src/gmock-all.cc.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o -c /home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock_main.cc", "file": "/home/<USER>/projects/nalekarskou/dependencies/googletest/googlemock/src/gmock_main.cc", "output": "dependencies/googletest/googlemock/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -I/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o dependencies/googletest/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o -c /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest-all.cc", "file": "/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest-all.cc", "output": "dependencies/googletest/googletest/CMakeFiles/gtest.dir/src/gtest-all.cc.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-svr", "command": "/usr/bin/c++ -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/include -isystem /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest -g -Wall -Wshadow -Wundef -Wno-error=dangling-else -DGTEST_HAS_PTHREAD=1 -fexceptions -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -o dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o -c /home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest_main.cc", "file": "/home/<USER>/projects/nalekarskou/dependencies/googletest/googletest/src/gtest_main.cc", "output": "dependencies/googletest/googletest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o"}]