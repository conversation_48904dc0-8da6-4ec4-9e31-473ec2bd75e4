[{"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/main.cpp.o -c /home/<USER>/projects/nalekarskou/main.cpp", "file": "/home/<USER>/projects/nalekarskou/main.cpp", "output": "CMakeFiles/saw-wasm.dir/main.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/App.cpp.o -c /home/<USER>/projects/nalekarskou/App.cpp", "file": "/home/<USER>/projects/nalekarskou/App.cpp", "output": "CMakeFiles/saw-wasm.dir/App.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/SAWSettings.cpp.o -c /home/<USER>/projects/nalekarskou/SAWSettings.cpp", "file": "/home/<USER>/projects/nalekarskou/SAWSettings.cpp", "output": "CMakeFiles/saw-wasm.dir/SAWSettings.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNameBase.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNameBase.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMTextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMTextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amloglite/src/AMLogger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amloglite/src/AMLogger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMFuture.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMFuture.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgenda.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgenda.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgendaView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgendaView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIApp.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIApp.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAppView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIControllerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIControllerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIFonts.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIFonts.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIIView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIIView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIRenderer.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIRenderer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUISystemWindow.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUISystemWindow.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIForm.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIForm.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableImport.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableImport.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_draw.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_draw.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_demo.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_demo.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_widgets.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_widgets.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_tables.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp", "output": "CMakeFiles/saw-wasm.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Acl.cpp.o -c /home/<USER>/projects/nalekarskou/model/Acl.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Acl.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/AclClient.cpp.o -c /home/<USER>/projects/nalekarskou/model/AclClient.cpp", "file": "/home/<USER>/projects/nalekarskou/model/AclClient.cpp", "output": "CMakeFiles/saw-wasm.dir/model/AclClient.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Citizenships.cpp.o -c /home/<USER>/projects/nalekarskou/model/Citizenships.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Citizenships.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Citizenships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Enumerations.cpp.o -c /home/<USER>/projects/nalekarskou/model/Enumerations.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Enumerations.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Enumerations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Menu.cpp.o -c /home/<USER>/projects/nalekarskou/model/Menu.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Menu.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Menu.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/People.cpp.o -c /home/<USER>/projects/nalekarskou/model/People.cpp", "file": "/home/<USER>/projects/nalekarskou/model/People.cpp", "output": "CMakeFiles/saw-wasm.dir/model/People.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Person.cpp.o -c /home/<USER>/projects/nalekarskou/model/Person.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Person.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Person.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/HeartbeatRefresher.cpp.o -c /home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp", "file": "/home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp", "output": "CMakeFiles/saw-wasm.dir/model/HeartbeatRefresher.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Sex.cpp.o -c /home/<USER>/projects/nalekarskou/model/Sex.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Sex.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Sex.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/TwoWayTable.cpp.o -c /home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp", "output": "CMakeFiles/saw-wasm.dir/model/TwoWayTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/User.cpp.o -c /home/<USER>/projects/nalekarskou/model/User.cpp", "file": "/home/<USER>/projects/nalekarskou/model/User.cpp", "output": "CMakeFiles/saw-wasm.dir/model/User.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Users.cpp.o -c /home/<USER>/projects/nalekarskou/model/Users.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Users.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Users.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/UserDegree.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserDegree.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserDegree.cpp", "output": "CMakeFiles/saw-wasm.dir/model/UserDegree.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/UserDegrees.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserDegrees.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserDegrees.cpp", "output": "CMakeFiles/saw-wasm.dir/model/UserDegrees.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Category.cpp.o -c /home/<USER>/projects/nalekarskou/model/Category.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Category.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Category.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/Categories.cpp.o -c /home/<USER>/projects/nalekarskou/model/Categories.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Categories.cpp", "output": "CMakeFiles/saw-wasm.dir/model/Categories.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/UserRoles.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserRoles.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserRoles.cpp", "output": "CMakeFiles/saw-wasm.dir/model/UserRoles.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/model/AMUITableConfig.cpp.o -c /home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp", "output": "CMakeFiles/saw-wasm.dir/model/AMUITableConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/view/AppView.cpp.o -c /home/<USER>/projects/nalekarskou/view/AppView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/AppView.cpp", "output": "CMakeFiles/saw-wasm.dir/view/AppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/view/MenuView.cpp.o -c /home/<USER>/projects/nalekarskou/view/MenuView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/MenuView.cpp", "output": "CMakeFiles/saw-wasm.dir/view/MenuView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/view/Resources.cpp.o -c /home/<USER>/projects/nalekarskou/view/Resources.cpp", "file": "/home/<USER>/projects/nalekarskou/view/Resources.cpp", "output": "CMakeFiles/saw-wasm.dir/view/Resources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/view/UserDegreeView.cpp.o -c /home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp", "output": "CMakeFiles/saw-wasm.dir/view/UserDegreeView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/controller/PersonController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/PersonController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/PersonController.cpp", "output": "CMakeFiles/saw-wasm.dir/controller/PersonController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/controller/UserDegreeController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp", "output": "CMakeFiles/saw-wasm.dir/controller/UserDegreeController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/controller/CategoryController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/CategoryController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/CategoryController.cpp", "output": "CMakeFiles/saw-wasm.dir/controller/CategoryController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/controller/UserController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/UserController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/UserController.cpp", "output": "CMakeFiles/saw-wasm.dir/controller/UserController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/generated/acl.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl.cpp", "output": "CMakeFiles/saw-wasm.dir/generated/acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/generated/acl_tables.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "output": "CMakeFiles/saw-wasm.dir/generated/acl_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/generated/TableCS.cpp.o -c /home/<USER>/projects/nalekarskou/generated/TableCS.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/TableCS.cpp", "output": "CMakeFiles/saw-wasm.dir/generated/TableCS.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/generated/CCategories.cpp.o -c /home/<USER>/projects/nalekarskou/generated/CCategories.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/CCategories.cpp", "output": "CMakeFiles/saw-wasm.dir/generated/CCategories.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/sha-2/sha-256.c.o -c /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/sha-2/sha-256.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/adler32.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/adler32.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/crc32.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/crc32.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/deflate.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/deflate.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/infback.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/infback.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/inffast.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/inffast.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/inflate.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/inflate.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/inftrees.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/inftrees.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/trees.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/trees.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/zutil.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/zutil.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/compress.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/compress.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/uncompr.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/uncompr.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzclose.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/gzclose.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzlib.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/gzlib.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzread.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/gzread.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzwrite.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/gzwrite.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/ioapi.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/ioapi.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/zip.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c", "output": "CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/zip.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/icon_log_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/icon_log_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/icon_log_download_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/icon_log_download_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Login_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Login_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Login_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Login_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Logout_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logout_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Logout_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Logout_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Logout_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Logo_SAW_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Logo_SAW_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Calendar_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Calendar_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/User_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/User_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Close_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Close_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Close_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Close_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Maximize_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Maximize_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Minimize_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Minimize_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Info_cr_fr_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Info_cr_fr_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Bookmark_Add_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Bookmark_Add_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Medical_Cross_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Medical_Cross_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Stethoscope_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Stethoscope_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Crown_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Crown_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Crown_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Boxes_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Boxes_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/User_Group_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/User_Group_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/User_Group_Filled_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/User_Group_Filled_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Home_O3_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Home_O3_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/New_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/New_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/New_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/New_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Save_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Save_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Save_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Save_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm.dir/build/assets/File_Download_in_lc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c", "output": "CMakeFiles/saw-wasm.dir/build/assets/File_Download_in_lc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/main.cpp.o -c /home/<USER>/projects/nalekarskou/main.cpp", "file": "/home/<USER>/projects/nalekarskou/main.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/main.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/App.cpp.o -c /home/<USER>/projects/nalekarskou/App.cpp", "file": "/home/<USER>/projects/nalekarskou/App.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/App.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/SAWSettings.cpp.o -c /home/<USER>/projects/nalekarskou/SAWSettings.cpp", "file": "/home/<USER>/projects/nalekarskou/SAWSettings.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/SAWSettings.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNameBase.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNameBase.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMTextUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMTextUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src/AMLogger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src/AMLogger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMFuture.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMFuture.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgenda.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgenda.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgendaView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgendaView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIApp.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIApp.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAppView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIControllerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIControllerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIFonts.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIFonts.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIIView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIIView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIRenderer.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIRenderer.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUISystemWindow.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUISystemWindow.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIForm.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIForm.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableImport.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableImport.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_draw.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_draw.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_demo.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_demo.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_widgets.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_widgets.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_tables.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o -c /home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp", "file": "/home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Acl.cpp.o -c /home/<USER>/projects/nalekarskou/model/Acl.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Acl.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/AclClient.cpp.o -c /home/<USER>/projects/nalekarskou/model/AclClient.cpp", "file": "/home/<USER>/projects/nalekarskou/model/AclClient.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/AclClient.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Citizenships.cpp.o -c /home/<USER>/projects/nalekarskou/model/Citizenships.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Citizenships.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Citizenships.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Enumerations.cpp.o -c /home/<USER>/projects/nalekarskou/model/Enumerations.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Enumerations.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Enumerations.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Menu.cpp.o -c /home/<USER>/projects/nalekarskou/model/Menu.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Menu.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Menu.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/People.cpp.o -c /home/<USER>/projects/nalekarskou/model/People.cpp", "file": "/home/<USER>/projects/nalekarskou/model/People.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/People.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Person.cpp.o -c /home/<USER>/projects/nalekarskou/model/Person.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Person.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Person.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/HeartbeatRefresher.cpp.o -c /home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp", "file": "/home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/HeartbeatRefresher.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Sex.cpp.o -c /home/<USER>/projects/nalekarskou/model/Sex.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Sex.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Sex.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/TwoWayTable.cpp.o -c /home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp", "file": "/home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/TwoWayTable.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/User.cpp.o -c /home/<USER>/projects/nalekarskou/model/User.cpp", "file": "/home/<USER>/projects/nalekarskou/model/User.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/User.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Users.cpp.o -c /home/<USER>/projects/nalekarskou/model/Users.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Users.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Users.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/UserDegree.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserDegree.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserDegree.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/UserDegree.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/UserDegrees.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserDegrees.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserDegrees.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/UserDegrees.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Category.cpp.o -c /home/<USER>/projects/nalekarskou/model/Category.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Category.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Category.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/Categories.cpp.o -c /home/<USER>/projects/nalekarskou/model/Categories.cpp", "file": "/home/<USER>/projects/nalekarskou/model/Categories.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/Categories.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/UserRoles.cpp.o -c /home/<USER>/projects/nalekarskou/model/UserRoles.cpp", "file": "/home/<USER>/projects/nalekarskou/model/UserRoles.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/UserRoles.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/model/AMUITableConfig.cpp.o -c /home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp", "file": "/home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/model/AMUITableConfig.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/view/AppView.cpp.o -c /home/<USER>/projects/nalekarskou/view/AppView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/AppView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/view/AppView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/view/MenuView.cpp.o -c /home/<USER>/projects/nalekarskou/view/MenuView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/MenuView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/view/MenuView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/view/Resources.cpp.o -c /home/<USER>/projects/nalekarskou/view/Resources.cpp", "file": "/home/<USER>/projects/nalekarskou/view/Resources.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/view/Resources.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/view/UserDegreeView.cpp.o -c /home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp", "file": "/home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/view/UserDegreeView.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/controller/PersonController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/PersonController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/PersonController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/controller/PersonController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/controller/UserDegreeController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/controller/UserDegreeController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/controller/CategoryController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/CategoryController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/CategoryController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/controller/CategoryController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/controller/UserController.cpp.o -c /home/<USER>/projects/nalekarskou/controller/UserController.cpp", "file": "/home/<USER>/projects/nalekarskou/controller/UserController.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/controller/UserController.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/generated/acl.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/generated/acl.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/generated/acl_tables.cpp.o -c /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/acl_tables.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/generated/acl_tables.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/generated/TableCS.cpp.o -c /home/<USER>/projects/nalekarskou/generated/TableCS.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/TableCS.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/generated/TableCS.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/generated/CCategories.cpp.o -c /home/<USER>/projects/nalekarskou/generated/CCategories.cpp", "file": "/home/<USER>/projects/nalekarskou/generated/CCategories.cpp", "output": "CMakeFiles/saw-wasm-nmt.dir/generated/CCategories.cpp.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2/sha-256.c.o -c /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2/sha-256.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/adler32.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/adler32.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/crc32.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/crc32.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/deflate.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/deflate.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/infback.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/infback.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inffast.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inffast.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inflate.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inflate.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inftrees.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inftrees.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/trees.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/trees.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/zutil.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/zutil.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/compress.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/compress.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/uncompr.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/uncompr.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzclose.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzclose.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzlib.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzlib.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzread.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzread.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzwrite.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzwrite.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/ioapi.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/ioapi.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/zip.c.o -c /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c", "file": "/home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c", "output": "CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/zip.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_download_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_download_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Login_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Login_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Login_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Login_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logout_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logo_SAW_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Logo_SAW_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Calendar_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Calendar_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/User_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Close_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Close_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Close_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Close_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Maximize_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Maximize_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Minimize_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Minimize_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Info_cr_fr_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Info_cr_fr_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Bookmark_Add_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Bookmark_Add_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Medical_Cross_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Medical_Cross_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Stethoscope_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Stethoscope_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Crown_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Crown_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Crown_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Boxes_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Boxes_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_Filled_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_Filled_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Home_O3_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Home_O3_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_gray_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_gray_png.c.o"}, {"directory": "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm", "command": "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1 -o CMakeFiles/saw-wasm-nmt.dir/build/assets/File_Download_in_lc_png.c.o -c /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c", "file": "/home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c", "output": "CMakeFiles/saw-wasm-nmt.dir/build/assets/File_Download_in_lc_png.c.o"}]