# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: saw
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/

#############################################
# Utility command for rc

build rc: phony

# =============================================================================
# Object build statements for EXECUTABLE target saw-wasm


#############################################
# Order-only phony target for saw-wasm

build cmake_object_order_depends_target_saw-wasm: phony || /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c /home/<USER>/projects/nalekarskou/build/assets/Close_png.c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c /home/<USER>/projects/nalekarskou/build/assets/Login_png.c /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c /home/<USER>/projects/nalekarskou/build/assets/User_png.c /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c create-version rc

build CMakeFiles/saw-wasm.dir/main.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/main.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/main.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/App.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/App.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/App.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/SAWSettings.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/SAWSettings.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/SAWSettings.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNullable.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNameBase.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNameBase.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMTextUtils.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMTextUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amloglite/src/AMLogger.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amloglite/src/AMLogger.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMFuture.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMFuture.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgenda.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgenda.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgendaView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgendaView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIApp.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIApp.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAppView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIConfig.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIControllerView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIControllerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIFonts.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIFonts.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIIView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIIView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIRenderer.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIRenderer.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUISystemWindow.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUISystemWindow.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIForm.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIForm.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableImport.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableImport.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_draw.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_draw.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_demo.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_demo.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_widgets.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_widgets.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_tables.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_tables.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/imgui/backends
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Acl.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Acl.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Acl.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/AclClient.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/AclClient.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/AclClient.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Citizenships.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Citizenships.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Citizenships.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Enumerations.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Enumerations.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Enumerations.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Menu.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Menu.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Menu.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/People.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/People.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/People.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Person.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Person.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Person.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/HeartbeatRefresher.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/HeartbeatRefresher.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Sex.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Sex.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Sex.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/TwoWayTable.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/TwoWayTable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/User.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/User.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/User.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/Users.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Users.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/Users.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/UserDegree.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserDegree.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/UserDegree.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/UserDegrees.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserDegrees.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/UserDegrees.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/UserRoles.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserRoles.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/UserRoles.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/model/AMUITableConfig.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/model/AMUITableConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/view/AppView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/view/AppView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/view/AppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/view/MenuView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/view/MenuView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/view/MenuView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/view/Resources.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/view/Resources.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/view/Resources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/view/UserDegreeView.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/view/UserDegreeView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/controller/PersonController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/PersonController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/controller/PersonController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/controller/UserDegreeController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/controller/UserDegreeController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/controller/UserController.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/UserController.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/controller/UserController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/generated/acl.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/generated/acl.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/generated/acl_tables.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/generated/acl_tables.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/generated/TableCS.cpp.o: CXX_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/TableCS.cpp || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/generated/TableCS.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/sha-2/sha-256.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/sha-2/sha-256.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/sha-2
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/adler32.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/adler32.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/crc32.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/crc32.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/deflate.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/deflate.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/infback.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/infback.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/inffast.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/inffast.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/inflate.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/inflate.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/inftrees.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/inftrees.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/trees.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/trees.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/zutil.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/zutil.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/compress.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/compress.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/uncompr.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/uncompr.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/gzclose.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/gzclose.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/gzlib.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/gzlib.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/gzread.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/gzread.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/gzwrite.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/gzwrite.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/ioapi.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/ioapi.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/zip.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/zip.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/icon_log_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/icon_log_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/icon_log_download_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/icon_log_download_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Login_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Login_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Login_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Logout_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Logout_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Logout_gray_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Logout_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Logo_SAW_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Logo_SAW_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Calendar_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Calendar_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/User_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/User_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Close_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Close_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Close_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Maximize_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Maximize_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Minimize_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Minimize_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Info_cr_fr_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Info_cr_fr_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Bookmark_Add_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Bookmark_Add_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Medical_Cross_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Medical_Cross_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Stethoscope_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Stethoscope_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Crown_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Crown_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Boxes_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Boxes_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/User_Group_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/User_Group_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/User_Group_Filled_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/User_Group_Filled_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Home_O3_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Home_O3_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/New_doc_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/New_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/New_doc_gray_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/New_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Save_doc_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Save_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Save_doc_gray_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Save_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_gray_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_gray_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb

build CMakeFiles/saw-wasm.dir/build/assets/File_Download_in_lc_png.c.o: C_COMPILER__saw-wasm_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c || cmake_object_order_depends_target_saw-wasm
  DEP_FILE = CMakeFiles/saw-wasm.dir/build/assets/File_Download_in_lc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_PDB = build/wasm/saw-wasm.pdb


# =============================================================================
# Link build statements for EXECUTABLE target saw-wasm


#############################################
# Link the executable build/wasm/saw-wasm.js

build build/wasm/saw-wasm.js: CXX_EXECUTABLE_LINKER__saw-wasm_Debug CMakeFiles/saw-wasm.dir/main.cpp.o CMakeFiles/saw-wasm.dir/App.cpp.o CMakeFiles/saw-wasm.dir/SAWSettings.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNullable.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMNameBase.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amcore/src/AMTextUtils.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amloglite/src/AMLogger.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMFuture.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgenda.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAgendaView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIApp.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIAppView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIConfig.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIController.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIControllerView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIFonts.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIIView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIRenderer.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUISystemWindow.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerController.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amui/src/AMUILoggerView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteController.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o CMakeFiles/saw-wasm.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIForm.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableImport.cpp.o CMakeFiles/saw-wasm.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_draw.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_demo.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_widgets.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/imgui_tables.cpp.o CMakeFiles/saw-wasm.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o CMakeFiles/saw-wasm.dir/model/Acl.cpp.o CMakeFiles/saw-wasm.dir/model/AclClient.cpp.o CMakeFiles/saw-wasm.dir/model/Citizenships.cpp.o CMakeFiles/saw-wasm.dir/model/Enumerations.cpp.o CMakeFiles/saw-wasm.dir/model/Menu.cpp.o CMakeFiles/saw-wasm.dir/model/People.cpp.o CMakeFiles/saw-wasm.dir/model/Person.cpp.o CMakeFiles/saw-wasm.dir/model/HeartbeatRefresher.cpp.o CMakeFiles/saw-wasm.dir/model/Sex.cpp.o CMakeFiles/saw-wasm.dir/model/TwoWayTable.cpp.o CMakeFiles/saw-wasm.dir/model/User.cpp.o CMakeFiles/saw-wasm.dir/model/Users.cpp.o CMakeFiles/saw-wasm.dir/model/UserDegree.cpp.o CMakeFiles/saw-wasm.dir/model/UserDegrees.cpp.o CMakeFiles/saw-wasm.dir/model/UserRoles.cpp.o CMakeFiles/saw-wasm.dir/model/AMUITableConfig.cpp.o CMakeFiles/saw-wasm.dir/view/AppView.cpp.o CMakeFiles/saw-wasm.dir/view/MenuView.cpp.o CMakeFiles/saw-wasm.dir/view/Resources.cpp.o CMakeFiles/saw-wasm.dir/view/UserDegreeView.cpp.o CMakeFiles/saw-wasm.dir/controller/PersonController.cpp.o CMakeFiles/saw-wasm.dir/controller/UserDegreeController.cpp.o CMakeFiles/saw-wasm.dir/controller/UserController.cpp.o CMakeFiles/saw-wasm.dir/generated/acl.cpp.o CMakeFiles/saw-wasm.dir/generated/acl_tables.cpp.o CMakeFiles/saw-wasm.dir/generated/TableCS.cpp.o CMakeFiles/saw-wasm.dir/dependencies/sha-2/sha-256.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/adler32.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/crc32.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/deflate.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/infback.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/inffast.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/inflate.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/inftrees.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/trees.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/zutil.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/compress.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/uncompr.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzclose.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzlib.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzread.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/gzwrite.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/ioapi.c.o CMakeFiles/saw-wasm.dir/dependencies/zlib/contrib/minizip/zip.c.o CMakeFiles/saw-wasm.dir/build/assets/icon_log_png.c.o CMakeFiles/saw-wasm.dir/build/assets/icon_log_download_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Login_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Logout_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Logout_gray_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Logo_SAW_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Calendar_png.c.o CMakeFiles/saw-wasm.dir/build/assets/User_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Close_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Maximize_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Minimize_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Info_cr_fr_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Bookmark_Add_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Medical_Cross_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Stethoscope_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Crown_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Boxes_png.c.o CMakeFiles/saw-wasm.dir/build/assets/User_Group_png.c.o CMakeFiles/saw-wasm.dir/build/assets/User_Group_Filled_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Home_O3_png.c.o CMakeFiles/saw-wasm.dir/build/assets/New_doc_png.c.o CMakeFiles/saw-wasm.dir/build/assets/New_doc_gray_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Save_doc_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Save_doc_gray_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Copy_doc_gray_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_png.c.o CMakeFiles/saw-wasm.dir/build/assets/Delete_doc_gray_png.c.o CMakeFiles/saw-wasm.dir/build/assets/File_Download_in_lc_png.c.o || create-version rc
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g
  LINK_FLAGS = -s TOTAL_MEMORY=503316480         -s FETCH=1         -lidbfs.js         -s ASSERTIONS=2         -s NO_DISABLE_EXCEPTION_CATCHING         -g -gsource-map --source-map-base='./'         -sEXPORTED_FUNCTIONS="['_main', '_browserInit','_browserDestroy', '_jsOpenDialogCallback', '_jsUploadFileCallback', '_loggerDump', '_webglContextLost', '_webglContextRestore']"         -sEXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']"         -sEXPORT_EXCEPTION_HANDLING_HELPERS         -fexceptions         --preload-file ./../../dependencies/imgui/misc/fonts/DroidSans.ttf@assets/DroidSans.ttf --preload-file ./../../assets/droid-sans-bold.ttf@assets/droid-sans-bold.ttf --preload-file ./../../assets/ods/META-INF/manifest.xml@assets/ods/META-INF/manifest.xml --preload-file ./../../assets/ods/Thumbnails/thumbnail.png@assets/ods/Thumbnails/thumbnail.png --preload-file ./../../assets/ods/manifest.rdf@assets/ods/manifest.rdf --preload-file ./../../assets/ods/meta.xml@assets/ods/meta.xml --preload-file ./../../assets/ods/mimetype@assets/ods/mimetype --preload-file ./../../assets/ods/settings.xml@assets/ods/settings.xml --preload-file ./../../assets/ods/styles.xml@assets/ods/styles.xml --preload-file ./../../assets/xlsx/docProps/app.xml@assets/xlsx/docProps/app.xml --preload-file ./../../assets/xlsx/docProps/core.xml@assets/xlsx/docProps/core.xml --preload-file ./../../assets/xlsx/_rels/.rels@assets/xlsx/_rels/.rels --preload-file ./../../assets/xlsx/xl/_rels/workbook.xml.rels@assets/xlsx/xl/_rels/workbook.xml.rels --preload-file ./../../assets/xlsx/xl/styles.xml@assets/xlsx/xl/styles.xml --preload-file ./../../assets/xlsx/xl/workbook.xml@assets/xlsx/xl/workbook.xml --preload-file ./../../assets/xlsx/[Content_Types].xml@assets/xlsx/[Content_Types].xml           -s TOTAL_MEMORY=503316480         -s FETCH=1         -lidbfs.js         -s ASSERTIONS=2         -s NO_DISABLE_EXCEPTION_CATCHING         -g -gsource-map --source-map-base='./'         -sEXPORTED_FUNCTIONS="['_main', '_browserInit','_browserDestroy', '_jsOpenDialogCallback', '_jsUploadFileCallback', '_loggerDump', '_webglContextLost', '_webglContextRestore']"         -sEXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']"         -sEXPORT_EXCEPTION_HANDLING_HELPERS         -fexceptions         --preload-file ./../../dependencies/imgui/misc/fonts/DroidSans.ttf@assets/DroidSans.ttf --preload-file ./../../assets/droid-sans-bold.ttf@assets/droid-sans-bold.ttf --preload-file ./../../assets/ods/META-INF/manifest.xml@assets/ods/META-INF/manifest.xml --preload-file ./../../assets/ods/Thumbnails/thumbnail.png@assets/ods/Thumbnails/thumbnail.png --preload-file ./../../assets/ods/manifest.rdf@assets/ods/manifest.rdf --preload-file ./../../assets/ods/meta.xml@assets/ods/meta.xml --preload-file ./../../assets/ods/mimetype@assets/ods/mimetype --preload-file ./../../assets/ods/settings.xml@assets/ods/settings.xml --preload-file ./../../assets/ods/styles.xml@assets/ods/styles.xml --preload-file ./../../assets/xlsx/docProps/app.xml@assets/xlsx/docProps/app.xml --preload-file ./../../assets/xlsx/docProps/core.xml@assets/xlsx/docProps/core.xml --preload-file ./../../assets/xlsx/_rels/.rels@assets/xlsx/_rels/.rels --preload-file ./../../assets/xlsx/xl/_rels/workbook.xml.rels@assets/xlsx/xl/_rels/workbook.xml.rels --preload-file ./../../assets/xlsx/xl/styles.xml@assets/xlsx/xl/styles.xml --preload-file ./../../assets/xlsx/xl/workbook.xml@assets/xlsx/xl/workbook.xml --preload-file ./../../assets/xlsx/[Content_Types].xml@assets/xlsx/[Content_Types].xml      -sUSE_PTHREADS=1 -s PTHREAD_POOL_SIZE=80 -sASYNCIFY -sASYNCIFY_STACK_SIZE=128000 -sASYNCIFY_IMPORTS=[jscopy,jspaste,jsurl,jsgotourl,jsdownload,jssavedataasfile,jsopenfiledialog]
  OBJECT_DIR = CMakeFiles/saw-wasm.dir
  POST_BUILD = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm.wasm ../cmake-debug-svr/build/svr/www/wasm/saw-wasm.wasm && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm.js ../cmake-debug-svr/build/svr/www/wasm/saw-wasm.js && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm.data ../cmake-debug-svr/build/svr/www/wasm/saw-wasm.data && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm.wasm.map ../cmake-debug-svr/build/svr/www/wasm/saw-wasm.wasm.map && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f ../../ClientVersion.txt ../cmake-debug-svr/build/svr/ClientVersion.txt && cd /home/<USER>/projects/nalekarskou && ./mkdatadebug.sh
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm.dir/
  TARGET_FILE = build/wasm/saw-wasm.js
  TARGET_PDB = build/wasm/saw-wasm.pdb

# =============================================================================
# Object build statements for EXECUTABLE target saw-wasm-nmt


#############################################
# Order-only phony target for saw-wasm-nmt

build cmake_object_order_depends_target_saw-wasm-nmt: phony || /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c /home/<USER>/projects/nalekarskou/build/assets/Close_png.c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c /home/<USER>/projects/nalekarskou/build/assets/Login_png.c /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c /home/<USER>/projects/nalekarskou/build/assets/User_png.c /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c create-version rc

build CMakeFiles/saw-wasm-nmt.dir/main.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/main.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/main.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/App.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/App.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/App.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/SAWSettings.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/SAWSettings.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/SAWSettings.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNullable.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNullable.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNameBase.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMNameBase.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNameBase.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMTextUtils.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amcore/src/AMTextUtils.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMTextUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src/AMLogger.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amloglite/src/AMLogger.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src/AMLogger.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMFuture.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMFuture.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMFuture.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgenda.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgenda.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgenda.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgendaView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAgendaView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgendaView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIApp.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIApp.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIApp.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAppView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIAppView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIConfig.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIConfig.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIControllerView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIControllerView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIControllerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIDirectoryUtils.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIFonts.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIFonts.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIFonts.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGarbageManager.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIGLWrappers.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIIView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIIView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIIView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIRenderer.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIRenderer.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIRenderer.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUISystemWindow.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUISystemWindow.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUISystemWindow.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtils.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUIUrlUtilsInt.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amui/src/AMUILoggerView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgenda.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAgendaView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteApp.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteAppView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteConfig.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteControllerView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteImageManager.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteResources.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatus.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStatusView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteStbIncluder.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialog.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetime.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogManager.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIDialogView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIForm.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIForm.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIForm.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElement.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementButton.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementFloat.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementInteger.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementNatural.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementPassword.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementRadios.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementString.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormCallbacks.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormConfig.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormResources.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIFormView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIParsers.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUISynthesizers.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amdialogs/src/AMUIWidgetState.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIDetailProvider.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableImport.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableImport.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableImport.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/amuitable/src/AMUITableIResultProvider.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_draw.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_draw.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_draw.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_demo.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_demo.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_demo.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_widgets.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_widgets.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_widgets.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_tables.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/imgui_tables.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_tables.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/imgui/backends/imgui_impl_sdl2.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Acl.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Acl.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Acl.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/AclClient.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/AclClient.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/AclClient.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Citizenships.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Citizenships.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Citizenships.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Enumerations.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Enumerations.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Enumerations.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Menu.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Menu.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Menu.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/People.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/People.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/People.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Person.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Person.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Person.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/HeartbeatRefresher.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/HeartbeatRefresher.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/HeartbeatRefresher.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Sex.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Sex.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Sex.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/TwoWayTable.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/TwoWayTable.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/TwoWayTable.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/User.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/User.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/User.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/Users.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/Users.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/Users.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/UserDegree.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserDegree.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/UserDegree.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/UserDegrees.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserDegrees.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/UserDegrees.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/UserRoles.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/UserRoles.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/UserRoles.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/model/AMUITableConfig.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/model/AMUITableConfig.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/model/AMUITableConfig.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/model
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/view/AppView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/view/AppView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/view/AppView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/view/MenuView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/view/MenuView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/view/MenuView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/view/Resources.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/view/Resources.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/view/Resources.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/view/UserDegreeView.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/view/UserDegreeView.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/view/UserDegreeView.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/view
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/controller/PersonController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/PersonController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/controller/PersonController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/controller/UserDegreeController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/UserDegreeController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/controller/UserDegreeController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/controller/UserController.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/controller/UserController.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/controller/UserController.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/controller
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/generated/acl.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/generated/acl.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/generated/acl_tables.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/acl_tables.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/generated/acl_tables.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/generated/TableCS.cpp.o: CXX_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/generated/TableCS.cpp || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/generated/TableCS.cpp.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g -std=gnu++17 -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/generated
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2/sha-256.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/sha-2/sha-256.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2/sha-256.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/adler32.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/adler32.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/adler32.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/crc32.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/crc32.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/crc32.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/deflate.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/deflate.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/deflate.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/infback.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/infback.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/infback.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inffast.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inffast.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inffast.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inflate.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inflate.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inflate.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inftrees.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/inftrees.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inftrees.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/trees.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/trees.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/trees.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/zutil.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/zutil.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/zutil.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/compress.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/compress.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/compress.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/uncompr.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/uncompr.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/uncompr.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzclose.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzclose.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzclose.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzlib.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzlib.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzlib.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzread.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzread.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzread.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzwrite.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/gzwrite.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzwrite.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/ioapi.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/ioapi.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/ioapi.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/zip.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/dependencies/zlib/contrib/minizip/zip.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/zip.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_download_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_download_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Login_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Login_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Login_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_gray_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Logo_SAW_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Logo_SAW_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Calendar_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Calendar_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/User_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/User_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Close_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Close_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Close_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Maximize_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Maximize_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Minimize_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Minimize_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Info_cr_fr_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Info_cr_fr_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Bookmark_Add_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Bookmark_Add_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Medical_Cross_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Medical_Cross_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Stethoscope_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Stethoscope_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Crown_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Crown_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Boxes_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Boxes_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_Filled_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_Filled_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Home_O3_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Home_O3_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_gray_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_gray_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_gray_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_gray_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_gray_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb

build CMakeFiles/saw-wasm-nmt.dir/build/assets/File_Download_in_lc_png.c.o: C_COMPILER__saw-wasm-nmt_unscanned_Debug /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c || cmake_object_order_depends_target_saw-wasm-nmt
  DEP_FILE = CMakeFiles/saw-wasm-nmt.dir/build/assets/File_Download_in_lc_png.c.o.d
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG  -g -gsource-map --source-map-base='./' -fexceptions  -g -s USE_PTHREADS=1
  INCLUDES = -I/home/<USER>/projects/nalekarskou/. -I/home/<USER>/projects/nalekarskou/dependencies -I/home/<USER>/projects/nalekarskou/dependencies/imgui -I/home/<USER>/projects/nalekarskou/dependencies/zlib -I/home/<USER>/projects/nalekarskou/dependencies/json/include
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  OBJECT_FILE_DIR = CMakeFiles/saw-wasm-nmt.dir/build/assets
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb


# =============================================================================
# Link build statements for EXECUTABLE target saw-wasm-nmt


#############################################
# Link the executable build/wasm/saw-wasm-nmt.js

build build/wasm/saw-wasm-nmt.js: CXX_EXECUTABLE_LINKER__saw-wasm-nmt_Debug CMakeFiles/saw-wasm-nmt.dir/main.cpp.o CMakeFiles/saw-wasm-nmt.dir/App.cpp.o CMakeFiles/saw-wasm-nmt.dir/SAWSettings.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNullable.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMNameBase.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amcore/src/AMTextUtils.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amloglite/src/AMLogger.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMFuture.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgenda.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAgendaView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIApp.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIAppView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIConfig.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIController.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIControllerView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIDirectoryUtils.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIFonts.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGarbageManager.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIGLWrappers.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIIView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIRenderer.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUISystemWindow.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtils.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUIUrlUtilsInt.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerController.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amui/src/AMUILoggerView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgenda.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAgendaView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteApp.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteAppView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteConfig.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteController.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteControllerView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteImageManager.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerController.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteResources.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatus.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStatusView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteStbIncluder.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialog.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetime.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogManager.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIDialogView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIForm.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElement.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementButton.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetime.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementFloat.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementInteger.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementNatural.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementPassword.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementRadios.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementString.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormCallbacks.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormConfig.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormResources.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIFormView.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIParsers.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUISynthesizers.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amdialogs/src/AMUIWidgetState.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUIHeartbeatProvider.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIDetailProvider.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableImport.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/amuitable/src/AMUITableIResultProvider.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_draw.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_demo.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_widgets.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/imgui_tables.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/imgui/backends/imgui_impl_sdl2.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Acl.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/AclClient.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Citizenships.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Enumerations.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Menu.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/People.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Person.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/HeartbeatRefresher.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Sex.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/TwoWayTable.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/User.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/Users.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/UserDegree.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/UserDegrees.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/UserRoles.cpp.o CMakeFiles/saw-wasm-nmt.dir/model/AMUITableConfig.cpp.o CMakeFiles/saw-wasm-nmt.dir/view/AppView.cpp.o CMakeFiles/saw-wasm-nmt.dir/view/MenuView.cpp.o CMakeFiles/saw-wasm-nmt.dir/view/Resources.cpp.o CMakeFiles/saw-wasm-nmt.dir/view/UserDegreeView.cpp.o CMakeFiles/saw-wasm-nmt.dir/controller/PersonController.cpp.o CMakeFiles/saw-wasm-nmt.dir/controller/UserDegreeController.cpp.o CMakeFiles/saw-wasm-nmt.dir/controller/UserController.cpp.o CMakeFiles/saw-wasm-nmt.dir/generated/acl.cpp.o CMakeFiles/saw-wasm-nmt.dir/generated/acl_tables.cpp.o CMakeFiles/saw-wasm-nmt.dir/generated/TableCS.cpp.o CMakeFiles/saw-wasm-nmt.dir/dependencies/sha-2/sha-256.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/adler32.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/crc32.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/deflate.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/infback.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inffast.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inflate.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/inftrees.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/trees.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/zutil.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/compress.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/uncompr.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzclose.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzlib.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzread.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/gzwrite.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/ioapi.c.o CMakeFiles/saw-wasm-nmt.dir/dependencies/zlib/contrib/minizip/zip.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/icon_log_download_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Login_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logout_gray_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Logo_SAW_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Calendar_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Close_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Maximize_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Minimize_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Info_cr_fr_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Bookmark_Add_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Medical_Cross_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Stethoscope_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Crown_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Boxes_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/User_Group_Filled_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Home_O3_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/New_doc_gray_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Save_doc_gray_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Copy_doc_gray_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/Delete_doc_gray_png.c.o CMakeFiles/saw-wasm-nmt.dir/build/assets/File_Download_in_lc_png.c.o || create-version rc
  FLAGS = -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -s USE_SDL=2  -D__EMSCRIPTEN__ -fexceptions  -DDEBUG -g -gsource-map --source-map-base='./' -fexceptions  -g
  LINK_FLAGS = -s TOTAL_MEMORY=503316480         -s FETCH=1         -lidbfs.js         -s ASSERTIONS=2         -s NO_DISABLE_EXCEPTION_CATCHING         -g -gsource-map --source-map-base='./'         -sEXPORTED_FUNCTIONS="['_main', '_browserInit','_browserDestroy', '_jsOpenDialogCallback', '_jsUploadFileCallback', '_loggerDump', '_webglContextLost', '_webglContextRestore']"         -sEXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']"         -sEXPORT_EXCEPTION_HANDLING_HELPERS         -fexceptions         --preload-file ./../../dependencies/imgui/misc/fonts/DroidSans.ttf@assets/DroidSans.ttf --preload-file ./../../assets/droid-sans-bold.ttf@assets/droid-sans-bold.ttf --preload-file ./../../assets/ods/META-INF/manifest.xml@assets/ods/META-INF/manifest.xml --preload-file ./../../assets/ods/Thumbnails/thumbnail.png@assets/ods/Thumbnails/thumbnail.png --preload-file ./../../assets/ods/manifest.rdf@assets/ods/manifest.rdf --preload-file ./../../assets/ods/meta.xml@assets/ods/meta.xml --preload-file ./../../assets/ods/mimetype@assets/ods/mimetype --preload-file ./../../assets/ods/settings.xml@assets/ods/settings.xml --preload-file ./../../assets/ods/styles.xml@assets/ods/styles.xml --preload-file ./../../assets/xlsx/docProps/app.xml@assets/xlsx/docProps/app.xml --preload-file ./../../assets/xlsx/docProps/core.xml@assets/xlsx/docProps/core.xml --preload-file ./../../assets/xlsx/_rels/.rels@assets/xlsx/_rels/.rels --preload-file ./../../assets/xlsx/xl/_rels/workbook.xml.rels@assets/xlsx/xl/_rels/workbook.xml.rels --preload-file ./../../assets/xlsx/xl/styles.xml@assets/xlsx/xl/styles.xml --preload-file ./../../assets/xlsx/xl/workbook.xml@assets/xlsx/xl/workbook.xml --preload-file ./../../assets/xlsx/[Content_Types].xml@assets/xlsx/[Content_Types].xml           -s TOTAL_MEMORY=503316480         -s FETCH=1         -lidbfs.js         -s ASSERTIONS=2         -s NO_DISABLE_EXCEPTION_CATCHING         -g -gsource-map --source-map-base='./'         -sEXPORTED_FUNCTIONS="['_main', '_browserInit','_browserDestroy', '_jsOpenDialogCallback', '_jsUploadFileCallback', '_loggerDump', '_webglContextLost', '_webglContextRestore']"         -sEXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']"         -sEXPORT_EXCEPTION_HANDLING_HELPERS         -fexceptions         --preload-file ./../../dependencies/imgui/misc/fonts/DroidSans.ttf@assets/DroidSans.ttf --preload-file ./../../assets/droid-sans-bold.ttf@assets/droid-sans-bold.ttf --preload-file ./../../assets/ods/META-INF/manifest.xml@assets/ods/META-INF/manifest.xml --preload-file ./../../assets/ods/Thumbnails/thumbnail.png@assets/ods/Thumbnails/thumbnail.png --preload-file ./../../assets/ods/manifest.rdf@assets/ods/manifest.rdf --preload-file ./../../assets/ods/meta.xml@assets/ods/meta.xml --preload-file ./../../assets/ods/mimetype@assets/ods/mimetype --preload-file ./../../assets/ods/settings.xml@assets/ods/settings.xml --preload-file ./../../assets/ods/styles.xml@assets/ods/styles.xml --preload-file ./../../assets/xlsx/docProps/app.xml@assets/xlsx/docProps/app.xml --preload-file ./../../assets/xlsx/docProps/core.xml@assets/xlsx/docProps/core.xml --preload-file ./../../assets/xlsx/_rels/.rels@assets/xlsx/_rels/.rels --preload-file ./../../assets/xlsx/xl/_rels/workbook.xml.rels@assets/xlsx/xl/_rels/workbook.xml.rels --preload-file ./../../assets/xlsx/xl/styles.xml@assets/xlsx/xl/styles.xml --preload-file ./../../assets/xlsx/xl/workbook.xml@assets/xlsx/xl/workbook.xml --preload-file ./../../assets/xlsx/[Content_Types].xml@assets/xlsx/[Content_Types].xml      -sUSE_PTHREADS=1 -s PTHREAD_POOL_SIZE=80 -sASYNCIFY -sASYNCIFY_STACK_SIZE=128000 -sASYNCIFY_IMPORTS=[jscopy,jspaste,jsurl,jsgotourl,jsdownload,jssavedataasfile,jsopenfiledialog]
  OBJECT_DIR = CMakeFiles/saw-wasm-nmt.dir
  POST_BUILD = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm-nmt.wasm ../cmake-debug-svr/build/svr/www/wasm/saw-wasm-nmt.wasm && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm-nmt.js ../cmake-debug-svr/build/svr/www/wasm/saw-wasm-nmt.js && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm-nmt.data ../cmake-debug-svr/build/svr/www/wasm/saw-wasm-nmt.data && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f build/wasm/saw-wasm-nmt.wasm.map ../cmake-debug-svr/build/svr/www/wasm/saw-wasm-nmt.wasm.map && cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && cp -f ../../ClientVersion.txt ../cmake-debug-svr/build/svr/ClientVersion.txt && cd /home/<USER>/projects/nalekarskou && ./mkdatadebug.sh
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/saw-wasm-nmt.dir/
  TARGET_FILE = build/wasm/saw-wasm-nmt.js
  TARGET_PDB = build/wasm/saw-wasm-nmt.pdb


#############################################
# Utility command for create-version

build create-version: phony CMakeFiles/create-version


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/projects/nalekarskou -B/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c

build /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/icon-log.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i icon-log.png > ../build/assets/icon_log_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/icon_log_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c

build /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/icon-log-download.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i icon-log-download.png > ../build/assets/icon_log_download_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/icon_log_download_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Login_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Login_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Login.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Login.png > ../build/assets/Login_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Login_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Logout.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Logout.png > ../build/assets/Logout_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Logout_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Logout_gray.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Logout_gray.png > ../build/assets/Logout_gray_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Logout_gray_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Logo_SAW.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Logo_SAW.png > ../build/assets/Logo_SAW_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Logo_SAW_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Calendar.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Calendar.png > ../build/assets/Calendar_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Calendar_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/User_png.c

build /home/<USER>/projects/nalekarskou/build/assets/User_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/User.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i User.png > ../build/assets/User_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/User_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Close_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Close_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Close.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Close.png > ../build/assets/Close_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Close_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Maximize.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Maximize.png > ../build/assets/Maximize_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Maximize_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Minimize.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Minimize.png > ../build/assets/Minimize_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Minimize_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Info$ cr-fr.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Info\ cr-fr.png > ../build/assets/Info_cr_fr_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Info_cr_fr_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Bookmark$ Add.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Bookmark\ Add.png > ../build/assets/Bookmark_Add_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Bookmark_Add_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Medical$ Cross.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Medical\ Cross.png > ../build/assets/Medical_Cross_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Medical_Cross_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Stethoscope.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Stethoscope.png > ../build/assets/Stethoscope_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Stethoscope_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Crown.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Crown.png > ../build/assets/Crown_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Crown_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Boxes.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Boxes.png > ../build/assets/Boxes_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Boxes_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c

build /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/User$ Group.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i User\ Group.png > ../build/assets/User_Group_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/User_Group_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c

build /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/User$ Group$ Filled.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i User\ Group\ Filled.png > ../build/assets/User_Group_Filled_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/User_Group_Filled_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Home$ O3.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Home\ O3.png > ../build/assets/Home_O3_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Home_O3_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c

build /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/New_doc.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i New_doc.png > ../build/assets/New_doc_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/New_doc_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c

build /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/New_doc_gray.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i New_doc_gray.png > ../build/assets/New_doc_gray_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/New_doc_gray_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Save_doc.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Save_doc.png > ../build/assets/Save_doc_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Save_doc_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Save_doc_gray.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Save_doc_gray.png > ../build/assets/Save_doc_gray_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Save_doc_gray_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Copy_doc.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Copy_doc.png > ../build/assets/Copy_doc_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Copy_doc_gray.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Copy_doc_gray.png > ../build/assets/Copy_doc_gray_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Copy_doc_gray_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Delete_doc.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Delete_doc.png > ../build/assets/Delete_doc_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c

build /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/Delete_doc_gray.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i Delete_doc_gray.png > ../build/assets/Delete_doc_gray_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/Delete_doc_gray_png.c
  restat = 1


#############################################
# Custom command for /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c

build /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c: CUSTOM_COMMAND /home/<USER>/projects/nalekarskou/assets/File$ Download$ in-lc.png || create-version rc
  COMMAND = cd /home/<USER>/projects/nalekarskou/assets && xxd -i File\ Download\ in-lc.png > ../build/assets/File_Download_in_lc_png.c
  DESC = Generating /home/<USER>/projects/nalekarskou/build/assets/File_Download_in_lc_png.c
  restat = 1


#############################################
# Custom command for CMakeFiles/create-version

build CMakeFiles/create-version | ${cmake_ninja_workdir}CMakeFiles/create-version: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm && /usr/bin/cmake -P /home/<USER>/projects/nalekarskou/CUpdateBuildNumberClient.cmake

# =============================================================================
# Target aliases.

build saw-wasm: phony build/wasm/saw-wasm.js

build saw-wasm-nmt: phony build/wasm/saw-wasm-nmt.js

build saw-wasm-nmt.js: phony build/wasm/saw-wasm-nmt.js

build saw-wasm.js: phony build/wasm/saw-wasm.js

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm

build all: phony rc build/wasm/saw-wasm.js build/wasm/saw-wasm-nmt.js

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/projects/nalekarskou/CMakeLists.txt /home/<USER>/projects/nalekarskou/CMakeListsI.cmake /home/<USER>/projects/nalekarskou/CMakeListsJ.cmake /home/<USER>/projects/nalekarskou/CMakeResources.cmake /home/<USER>/projects/nalekarskou/CMakeWasm.cmake /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake /usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-C.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/projects/nalekarskou/CMakeLists.txt /home/<USER>/projects/nalekarskou/CMakeListsI.cmake /home/<USER>/projects/nalekarskou/CMakeListsJ.cmake /home/<USER>/projects/nalekarskou/CMakeResources.cmake /home/<USER>/projects/nalekarskou/CMakeWasm.cmake /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake /usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-C.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
