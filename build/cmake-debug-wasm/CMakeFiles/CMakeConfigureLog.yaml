
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:228 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Linux - 6.14.0-29-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.js"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.wasm"
      
      The C compiler identification could not be found in:
        /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/3.28.3/CompilerIdC/a.out.js
      
      The C compiler identification is Clang, found in:
        /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/3.28.3/CompilerIdC/a.out.wasm
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.js"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.wasm"
      
      The CXX compiler identification could not be found in:
        /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/3.28.3/CompilerIdCXX/a.out.js
      
      The CXX compiler identification is Clang, found in:
        /home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/3.28.3/CompilerIdCXX/a.out.wasm
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj"
      binary: "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emscan-deps"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj'
        
        Run Build Command(s): ninja -v cmTC_8a020
        [1/2] /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc   -v -MD -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang -target wasm32-unknown-emscripten -fignore-exceptions -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr --sysroot=/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -DEMSCRIPTEN -Xclang -iwithsysroot/include/fakesdl -Xclang -iwithsysroot/include/compat -v -MD -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -oCMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c
        clang version 21.0.0git (https:/github.com/llvm/llvm-project 4775e6d9099467df9363e1a3cd5950cc3d2fde05)
        Target: wasm32-unknown-emscripten
        Thread model: posix
        InstalledDir: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin
         (in-process)
         "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang-21" -cc1 -triple wasm32-unknown-emscripten -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model static -mframe-pointer=none -ffp-contract=on -fno-rounding-math -mconstructor-aliases -target-cpu generic -fvisibility=hidden -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj -v -fcoverage-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj -resource-dir /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21 -dependency-file CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -sys-header-deps -D EMSCRIPTEN -isysroot /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fignore-exceptions -iwithsysroot/include/fakesdl -iwithsysroot/include/compat -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr -o CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -x c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c
        clang -cc1 version 21.0.0git based upon LLVM 21.0.0git default target x86_64-unknown-linux-gnu
        ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include
        End of search list.
        [2/2] : && /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc  -v CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -o cmTC_8a020.js   && :
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang --version
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld -o cmTC_8a020.wasm CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib -lGL-getprocaddr -lal -lhtml5 -lstubs-debug -lnoexit -lc-debug -ldlmalloc-debug -lcompiler_rt -lc++-noexcept -lc++abi-debug-noexcept -lsockets -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr /tmp/tmp4h7pd2j4libemscripten_js_symbols.so --strip-debug --export=emscripten_stack_get_end --export=emscripten_stack_get_free --export=emscripten_stack_get_base --export=emscripten_stack_get_current --export=emscripten_stack_init --export=_emscripten_stack_alloc --export=__wasm_call_ctors --export=_emscripten_stack_restore --export-if-defined=__start_em_asm --export-if-defined=__stop_em_asm --export-if-defined=__start_em_lib_deps --export-if-defined=__stop_em_lib_deps --export-if-defined=__start_em_js --export-if-defined=__stop_em_js --export-if-defined=main --export-if-defined=__main_argc_argv --export-if-defined=fflush --export-table -z stack-size=65536 --no-growable-memory --initial-heap=16777216 --no-entry --stack-first --table-base=1
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/llvm-objcopy cmTC_8a020.wasm cmTC_8a020.wasm --remove-section=.debug* --remove-section=producers --remove-section=name
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/node/20.18.0_64bit/bin/node /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/tools/compiler.mjs -
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        end of search list found
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        implicit include dirs: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj']
        ignore line: []
        ignore line: [Run Build Command(s): ninja -v cmTC_8a020]
        ignore line: [[1/2] /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc   -v -MD -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang -target wasm32-unknown-emscripten -fignore-exceptions -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr --sysroot=/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -DEMSCRIPTEN -Xclang -iwithsysroot/include/fakesdl -Xclang -iwithsysroot/include/compat -v -MD -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -oCMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [clang version 21.0.0git (https:/github.com/llvm/llvm-project 4775e6d9099467df9363e1a3cd5950cc3d2fde05)]
        ignore line: [Target: wasm32-unknown-emscripten]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin]
        ignore line: [ (in-process)]
        ignore line: [ "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang-21" -cc1 -triple wasm32-unknown-emscripten -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model static -mframe-pointer=none -ffp-contract=on -fno-rounding-math -mconstructor-aliases -target-cpu generic -fvisibility=hidden -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj -v -fcoverage-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-oklJNj -resource-dir /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21 -dependency-file CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -sys-header-deps -D EMSCRIPTEN -isysroot /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fignore-exceptions -iwithsysroot/include/fakesdl -iwithsysroot/include/compat -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr -o CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -x c /usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 21.0.0git based upon LLVM 21.0.0git default target x86_64-unknown-linux-gnu]
        ignore line: [ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emcc  -v CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -o cmTC_8a020.js   && :]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang --version]
        link line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld -o cmTC_8a020.wasm CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib -lGL-getprocaddr -lal -lhtml5 -lstubs-debug -lnoexit -lc-debug -ldlmalloc-debug -lcompiler_rt -lc++-noexcept -lc++abi-debug-noexcept -lsockets -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr /tmp/tmp4h7pd2j4libemscripten_js_symbols.so --strip-debug --export=emscripten_stack_get_end --export=emscripten_stack_get_free --export=emscripten_stack_get_base --export=emscripten_stack_get_current --export=emscripten_stack_init --export=_emscripten_stack_alloc --export=__wasm_call_ctors --export=_emscripten_stack_restore --export-if-defined=__start_em_asm --export-if-defined=__stop_em_asm --export-if-defined=__start_em_lib_deps --export-if-defined=__stop_em_lib_deps --export-if-defined=__start_em_js --export-if-defined=__stop_em_js --export-if-defined=main --export-if-defined=__main_argc_argv --export-if-defined=fflush --export-table -z stack-size=65536 --no-growable-memory --initial-heap=16777216 --no-entry --stack-first --table-base=1]
          arg [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_8a020.wasm] ==> ignore
          arg [CMakeFiles/cmTC_8a020.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten] ==> dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten]
          arg [-L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib] ==> dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
          arg [-lGL-getprocaddr] ==> lib [GL-getprocaddr]
          arg [-lal] ==> lib [al]
          arg [-lhtml5] ==> lib [html5]
          arg [-lstubs-debug] ==> lib [stubs-debug]
          arg [-lnoexit] ==> lib [noexit]
          arg [-lc-debug] ==> lib [c-debug]
          arg [-ldlmalloc-debug] ==> lib [dlmalloc-debug]
          arg [-lcompiler_rt] ==> lib [compiler_rt]
          arg [-lc++-noexcept] ==> lib [c++-noexcept]
          arg [-lc++abi-debug-noexcept] ==> lib [c++abi-debug-noexcept]
          arg [-lsockets] ==> lib [sockets]
          arg [-mllvm] ==> ignore
          arg [-combiner-global-alias-analysis=false] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-emscripten-sjlj] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-disable-lsr] ==> ignore
          arg [/tmp/tmp4h7pd2j4libemscripten_js_symbols.so] ==> ignore
          arg [--strip-debug] ==> ignore
          arg [--export=emscripten_stack_get_end] ==> ignore
          arg [--export=emscripten_stack_get_free] ==> ignore
          arg [--export=emscripten_stack_get_base] ==> ignore
          arg [--export=emscripten_stack_get_current] ==> ignore
          arg [--export=emscripten_stack_init] ==> ignore
          arg [--export=_emscripten_stack_alloc] ==> ignore
          arg [--export=__wasm_call_ctors] ==> ignore
          arg [--export=_emscripten_stack_restore] ==> ignore
          arg [--export-if-defined=__start_em_asm] ==> ignore
          arg [--export-if-defined=__stop_em_asm] ==> ignore
          arg [--export-if-defined=__start_em_lib_deps] ==> ignore
          arg [--export-if-defined=__stop_em_lib_deps] ==> ignore
          arg [--export-if-defined=__start_em_js] ==> ignore
          arg [--export-if-defined=__stop_em_js] ==> ignore
          arg [--export-if-defined=main] ==> ignore
          arg [--export-if-defined=__main_argc_argv] ==> ignore
          arg [--export-if-defined=fflush] ==> ignore
          arg [--export-table] ==> ignore
          arg [-zstack-size=65536] ==> ignore
          arg [--no-growable-memory] ==> ignore
          arg [--initial-heap=16777216] ==> ignore
          arg [--no-entry] ==> ignore
          arg [--stack-first] ==> ignore
          arg [--table-base=1] ==> ignore
        collapse library dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten]
        collapse library dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
        implicit libs: [GL-getprocaddr;al;html5;stubs-debug;noexit;c-debug;dlmalloc-debug;compiler_rt;c++-noexcept;c++abi-debug-noexcept;sockets]
        implicit objs: []
        implicit dirs: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb"
      binary: "/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/emscan-deps"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cmake/Modules"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb'
        
        Run Build Command(s): ninja -v cmTC_df889
        [1/2] /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++   -v -MD -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
         "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang++" -target wasm32-unknown-emscripten -fignore-exceptions -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr --sysroot=/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -DEMSCRIPTEN -Xclang -iwithsysroot/include/fakesdl -Xclang -iwithsysroot/include/compat -v -MD -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -oCMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        clang version 21.0.0git (https:/github.com/llvm/llvm-project 4775e6d9099467df9363e1a3cd5950cc3d2fde05)
        Target: wasm32-unknown-emscripten
        Thread model: posix
        InstalledDir: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin
         (in-process)
         "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang-21" -cc1 -triple wasm32-unknown-emscripten -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mframe-pointer=none -ffp-contract=on -fno-rounding-math -mconstructor-aliases -target-cpu generic -fvisibility=hidden -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb -v -fcoverage-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb -resource-dir /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21 -dependency-file CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D EMSCRIPTEN -isysroot /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten/c++/v1 -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1 -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fignore-exceptions -fexceptions -iwithsysroot/include/fakesdl -iwithsysroot/include/compat -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr -o CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 21.0.0git based upon LLVM 21.0.0git default target x86_64-unknown-linux-gnu
        ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten/c++/v1"
        ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include
        End of search list.
        [2/2] : && /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++  -v CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_df889.js   && :
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang --version
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld -o cmTC_df889.wasm CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib -lGL-getprocaddr -lal -lhtml5 -lstubs-debug -lnoexit -lc-debug -ldlmalloc-debug -lcompiler_rt -lc++-noexcept -lc++abi-debug-noexcept -lsockets -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr /tmp/tmp49zfb1n1libemscripten_js_symbols.so --strip-debug --export=emscripten_stack_get_end --export=emscripten_stack_get_free --export=emscripten_stack_get_base --export=emscripten_stack_get_current --export=emscripten_stack_init --export=_emscripten_stack_alloc --export=__wasm_call_ctors --export=_emscripten_stack_restore --export-if-defined=__start_em_asm --export-if-defined=__stop_em_asm --export-if-defined=__start_em_lib_deps --export-if-defined=__stop_em_lib_deps --export-if-defined=__start_em_js --export-if-defined=__stop_em_js --export-if-defined=main --export-if-defined=__main_argc_argv --export-if-defined=fflush --export-table -z stack-size=65536 --no-growable-memory --initial-heap=16777216 --no-entry --stack-first --table-base=1
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/llvm-objcopy cmTC_df889.wasm cmTC_df889.wasm --remove-section=.debug* --remove-section=producers --remove-section=name
         /home/<USER>/projects/nalekarskou/dependencies/emsdk/node/20.18.0_64bit/bin/node /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/tools/compiler.mjs -
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
          add: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        end of search list found
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
        collapse include dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        implicit include dirs: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb']
        ignore line: []
        ignore line: [Run Build Command(s): ninja -v cmTC_df889]
        ignore line: [[1/2] /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++   -v -MD -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [ "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang++" -target wasm32-unknown-emscripten -fignore-exceptions -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr --sysroot=/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -DEMSCRIPTEN -Xclang -iwithsysroot/include/fakesdl -Xclang -iwithsysroot/include/compat -v -MD -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -oCMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang version 21.0.0git (https:/github.com/llvm/llvm-project 4775e6d9099467df9363e1a3cd5950cc3d2fde05)]
        ignore line: [Target: wasm32-unknown-emscripten]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin]
        ignore line: [ (in-process)]
        ignore line: [ "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang-21" -cc1 -triple wasm32-unknown-emscripten -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mframe-pointer=none -ffp-contract=on -fno-rounding-math -mconstructor-aliases -target-cpu generic -fvisibility=hidden -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb -v -fcoverage-compilation-dir=/home/<USER>/projects/nalekarskou/build/cmake-debug-wasm/CMakeFiles/CMakeScratch/TryCompile-dwW7Bb -resource-dir /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21 -dependency-file CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D EMSCRIPTEN -isysroot /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten/c++/v1 -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1 -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten -internal-isystem /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fignore-exceptions -fexceptions -iwithsysroot/include/fakesdl -iwithsysroot/include/compat -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr -o CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 21.0.0git based upon LLVM 21.0.0git default target x86_64-unknown-linux-gnu]
        ignore line: [ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten/c++/v1"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/wasm32-emscripten"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/fakesdl]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/compat]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/lib/clang/21/include]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/include]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/em++  -v CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_df889.js   && :]
        ignore line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/clang --version]
        link line: [ /home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld -o cmTC_df889.wasm CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten -L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib -lGL-getprocaddr -lal -lhtml5 -lstubs-debug -lnoexit -lc-debug -ldlmalloc-debug -lcompiler_rt -lc++-noexcept -lc++abi-debug-noexcept -lsockets -mllvm -combiner-global-alias-analysis=false -mllvm -enable-emscripten-sjlj -mllvm -disable-lsr /tmp/tmp49zfb1n1libemscripten_js_symbols.so --strip-debug --export=emscripten_stack_get_end --export=emscripten_stack_get_free --export=emscripten_stack_get_base --export=emscripten_stack_get_current --export=emscripten_stack_init --export=_emscripten_stack_alloc --export=__wasm_call_ctors --export=_emscripten_stack_restore --export-if-defined=__start_em_asm --export-if-defined=__stop_em_asm --export-if-defined=__start_em_lib_deps --export-if-defined=__stop_em_lib_deps --export-if-defined=__start_em_js --export-if-defined=__stop_em_js --export-if-defined=main --export-if-defined=__main_argc_argv --export-if-defined=fflush --export-table -z stack-size=65536 --no-growable-memory --initial-heap=16777216 --no-entry --stack-first --table-base=1]
          arg [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/bin/wasm-ld] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_df889.wasm] ==> ignore
          arg [CMakeFiles/cmTC_df889.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten] ==> dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten]
          arg [-L/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib] ==> dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
          arg [-lGL-getprocaddr] ==> lib [GL-getprocaddr]
          arg [-lal] ==> lib [al]
          arg [-lhtml5] ==> lib [html5]
          arg [-lstubs-debug] ==> lib [stubs-debug]
          arg [-lnoexit] ==> lib [noexit]
          arg [-lc-debug] ==> lib [c-debug]
          arg [-ldlmalloc-debug] ==> lib [dlmalloc-debug]
          arg [-lcompiler_rt] ==> lib [compiler_rt]
          arg [-lc++-noexcept] ==> lib [c++-noexcept]
          arg [-lc++abi-debug-noexcept] ==> lib [c++abi-debug-noexcept]
          arg [-lsockets] ==> lib [sockets]
          arg [-mllvm] ==> ignore
          arg [-combiner-global-alias-analysis=false] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-emscripten-sjlj] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-disable-lsr] ==> ignore
          arg [/tmp/tmp49zfb1n1libemscripten_js_symbols.so] ==> ignore
          arg [--strip-debug] ==> ignore
          arg [--export=emscripten_stack_get_end] ==> ignore
          arg [--export=emscripten_stack_get_free] ==> ignore
          arg [--export=emscripten_stack_get_base] ==> ignore
          arg [--export=emscripten_stack_get_current] ==> ignore
          arg [--export=emscripten_stack_init] ==> ignore
          arg [--export=_emscripten_stack_alloc] ==> ignore
          arg [--export=__wasm_call_ctors] ==> ignore
          arg [--export=_emscripten_stack_restore] ==> ignore
          arg [--export-if-defined=__start_em_asm] ==> ignore
          arg [--export-if-defined=__stop_em_asm] ==> ignore
          arg [--export-if-defined=__start_em_lib_deps] ==> ignore
          arg [--export-if-defined=__stop_em_lib_deps] ==> ignore
          arg [--export-if-defined=__start_em_js] ==> ignore
          arg [--export-if-defined=__stop_em_js] ==> ignore
          arg [--export-if-defined=main] ==> ignore
          arg [--export-if-defined=__main_argc_argv] ==> ignore
          arg [--export-if-defined=fflush] ==> ignore
          arg [--export-table] ==> ignore
          arg [-zstack-size=65536] ==> ignore
          arg [--no-growable-memory] ==> ignore
          arg [--initial-heap=16777216] ==> ignore
          arg [--no-entry] ==> ignore
          arg [--stack-first] ==> ignore
          arg [--table-base=1] ==> ignore
        collapse library dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten]
        collapse library dir [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib] ==> [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
        implicit libs: [GL-getprocaddr;al;html5;stubs-debug;noexit;c-debug;dlmalloc-debug;compiler_rt;c++-noexcept;c++abi-debug-noexcept;sockets]
        implicit objs: []
        implicit dirs: [/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/cache/sysroot/lib/wasm32-emscripten;/home/<USER>/projects/nalekarskou/dependencies/emsdk/upstream/emscripten/src/lib]
        implicit fwks: []
      
      
...
