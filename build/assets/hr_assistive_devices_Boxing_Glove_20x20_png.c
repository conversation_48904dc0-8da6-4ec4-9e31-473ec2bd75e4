unsigned char hr_assistive_devices_Boxing_Glove_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x8c, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0xd3, 0xcf, 0x8b, 0xcd, 0x51, 0x18,
  0xc7, 0xf1, 0xd7, 0xb9, 0xc3, 0x24, 0x6e, 0x61, 0x23, 0xb3, 0x12, 0xeb,
  0x29, 0x2b, 0x0b, 0x29, 0x7b, 0x35, 0x1b, 0xa5, 0x24, 0x5b, 0x12, 0xe5,
  0x47, 0x36, 0x16, 0x56, 0xfe, 0x00, 0xb1, 0x20, 0x16, 0x96, 0x2c, 0x6d,
  0x65, 0xc3, 0xc2, 0x06, 0xcb, 0xd9, 0x62, 0x71, 0x8b, 0x52, 0x93, 0x8c,
  0x48, 0x31, 0xc3, 0x7d, 0x2c, 0xbe, 0xcf, 0xad, 0xef, 0xfd, 0xce, 0xf7,
  0xdc, 0x51, 0x9c, 0x7a, 0x3a, 0xa7, 0xf3, 0x7c, 0xce, 0xbb, 0xe7, 0x7c,
  0xce, 0x79, 0x4a, 0x44, 0xf8, 0x9f, 0x63, 0x4b, 0x2d, 0x51, 0x4a, 0x59,
  0xc4, 0x3e, 0x6c, 0xc7, 0x17, 0x2c, 0x47, 0xc4, 0xca, 0xa6, 0xc4, 0x88,
  0x98, 0x0a, 0x1c, 0xc0, 0x4b, 0x44, 0x27, 0xd6, 0x71, 0x1b, 0xf3, 0xdd,
  0x33, 0xed, 0x98, 0xaa, 0xb0, 0x94, 0x32, 0xc4, 0xd3, 0x84, 0xde, 0xc5,
  0x2b, 0xfc, 0xc4, 0x6e, 0x9c, 0xc6, 0xe5, 0x94, 0x5e, 0xf9, 0xab, 0x0a,
  0x71, 0x2e, 0xab, 0xb9, 0xd8, 0x53, 0xf9, 0x1c, 0x5e, 0x60, 0x0d, 0x7b,
  0x6a, 0x15, 0x76, 0x0f, 0x3d, 0x48, 0xe0, 0xce, 0x5e, 0x31, 0x67, 0x33,
  0xff, 0x09, 0x9f, 0x71, 0x13, 0x83, 0xb6, 0x66, 0xd0, 0x29, 0x78, 0x98,
  0xf3, 0xb7, 0xca, 0x85, 0xbe, 0x4e, 0x2e, 0x86, 0x0f, 0xb8, 0x8a, 0x33,
  0x6d, 0x41, 0x17, 0x38, 0x48, 0x1b, 0xc6, 0x15, 0xe0, 0x64, 0xff, 0x1a,
  0x8e, 0xe4, 0xfa, 0xe8, 0x2c, 0xe0, 0x18, 0x4a, 0x29, 0x73, 0x15, 0xe0,
  0xaf, 0x9c, 0x57, 0x35, 0x5e, 0x6e, 0x18, 0x5d, 0xe0, 0x44, 0xb4, 0xb5,
  0x02, 0x7c, 0x86, 0xeb, 0x39, 0x2f, 0xe6, 0xde, 0x68, 0x4a, 0xd1, 0x31,
  0xfd, 0x8e, 0xc6, 0x9f, 0xfd, 0xb3, 0xfe, 0x5a, 0x6a, 0x1f, 0xa5, 0xf6,
  0xd0, 0xac, 0x57, 0x3e, 0x95, 0xa2, 0x5b, 0x33, 0x40, 0x05, 0x37, 0x52,
  0xf7, 0xb8, 0x9b, 0x2f, 0xed, 0x5e, 0x4e, 0xef, 0x9e, 0x6b, 0x8c, 0x1e,
  0xe1, 0xbd, 0xc6, 0xd7, 0xf5, 0x8c, 0x1f, 0xd8, 0x8b, 0xc3, 0x78, 0x8d,
  0x63, 0x11, 0xb1, 0x5a, 0xbd, 0x72, 0xc2, 0x8f, 0xe3, 0xb7, 0x8d, 0xad,
  0x37, 0x89, 0x31, 0xee, 0x61, 0xc7, 0xa6, 0xad, 0x97, 0xe3, 0x82, 0xa6,
  0xdd, 0x4e, 0x68, 0xba, 0xa3, 0x60, 0x1b, 0xe6, 0x71, 0x12, 0x4b, 0x78,
  0x18, 0x11, 0xdf, 0xfb, 0x5e, 0xad, 0x0f, 0xb8, 0x80, 0x51, 0x44, 0x3c,
  0xe9, 0x26, 0x4a, 0x29, 0x6b, 0x09, 0x5c, 0xe8, 0x83, 0xd5, 0x80, 0xef,
  0xb0, 0x54, 0x4a, 0xb9, 0x84, 0x8f, 0x6d, 0x1e, 0xce, 0xe7, 0xfa, 0x6d,
  0x0d, 0xd8, 0xe7, 0xe1, 0x41, 0xac, 0xa8, 0x7b, 0x78, 0x7f, 0xd6, 0x77,
  0x9a, 0x7a, 0xe5, 0xd6, 0xd5, 0x76, 0x69, 0x5e, 0x72, 0xd8, 0x49, 0xbd,
  0x89, 0x88, 0xe5, 0x6a, 0x75, 0xf4, 0x03, 0xff, 0x65, 0xfc, 0x01, 0xa7,
  0xeb, 0x67, 0xd5, 0xab, 0x6d, 0x19, 0x41, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_assistive_devices_Boxing_Glove_20x20_png_len = 511;
