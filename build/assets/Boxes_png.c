unsigned char Boxes_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x01, 0x1d, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xed, 0x53, 0x3b,
  0x6e, 0xc2, 0x40, 0x10, 0x7d, 0xbb, 0x4e, 0x11, 0x47, 0x4a, 0xb4, 0x71,
  0x9a, 0x88, 0x34, 0xa1, 0xcb, 0x11, 0x72, 0x1c, 0x6e, 0x90, 0xa4, 0xa3,
  0xa3, 0x4e, 0x15, 0xe5, 0x06, 0xc9, 0x4d, 0xb8, 0x09, 0x54, 0x98, 0x02,
  0x8c, 0x25, 0x24, 0x5c, 0xe1, 0x61, 0x16, 0x10, 0x5a, 0x9b, 0xfd, 0x60,
  0xd1, 0x00, 0xe2, 0x49, 0x5e, 0xef, 0xcc, 0x78, 0x66, 0xd6, 0xb3, 0xef,
  0x01, 0xe7, 0x0e, 0x11, 0xfa, 0x20, 0x49, 0x5a, 0x3d, 0x08, 0xf1, 0x09,
  0x90, 0xaa, 0xa5, 0xe6, 0x10, 0xe5, 0x5f, 0x36, 0x49, 0xbf, 0x7c, 0xf9,
  0x91, 0xbf, 0xf8, 0xcb, 0x0f, 0x1f, 0xa1, 0xcb, 0xdb, 0x5b, 0x4b, 0x98,
  0x7d, 0xe2, 0x3d, 0x8e, 0xef, 0x45, 0x51, 0xcc, 0xfb, 0xae, 0x1a, 0x12,
  0x7e, 0x74, 0xf4, 0x52, 0x2e, 0xcb, 0x76, 0x36, 0x1d, 0xed, 0xfe, 0x56,
  0xef, 0xb5, 0x6f, 0x6d, 0x08, 0x7c, 0xf8, 0x0a, 0xf8, 0x1b, 0x88, 0xcd,
  0x58, 0xf2, 0x7c, 0x3c, 0xd4, 0x6f, 0x22, 0x1a, 0xea, 0xc7, 0xf4, 0x31,
  0x94, 0xaf, 0xc4, 0x0d, 0x1a, 0x60, 0x96, 0xa5, 0x6d, 0x34, 0x44, 0x68,
  0x44, 0x47, 0xe3, 0xfc, 0x1b, 0xec, 0xe9, 0xc0, 0xcd, 0xfb, 0x60, 0x29,
  0xab, 0x2e, 0xa2, 0x6a, 0x71, 0x2f, 0xef, 0x43, 0xb0, 0xea, 0xa2, 0x3e,
  0xa2, 0x8e, 0x5e, 0x6c, 0xbc, 0x0f, 0xd9, 0x2e, 0x5d, 0x54, 0x69, 0x6a,
  0xe1, 0x3d, 0x3c, 0x30, 0xe3, 0x3a, 0x27, 0x79, 0x6a, 0xe9, 0xad, 0x72,
  0x37, 0xa8, 0x21, 0xc4, 0xfb, 0x43, 0x74, 0x71, 0x69, 0x3a, 0x20, 0xa6,
  0x1a, 0x43, 0xa9, 0xe7, 0x57, 0x34, 0x84, 0x91, 0x93, 0x9b, 0xfe, 0xea,
  0x1d, 0x48, 0xfa, 0x05, 0xa1, 0x27, 0x23, 0x39, 0xd8, 0x5e, 0x98, 0x15,
  0x1c, 0x23, 0x67, 0x10, 0xf4, 0x6f, 0x5a, 0x15, 0x1d, 0x14, 0x8b, 0x79,
  0x3f, 0xbe, 0x7b, 0x78, 0xe4, 0x26, 0x6f, 0x4c, 0xb7, 0x66, 0x5a, 0x20,
  0x3e, 0xb9, 0xc4, 0x77, 0x36, 0x4d, 0xbb, 0xb8, 0xe2, 0xa4, 0xb0, 0x02,
  0x72, 0xf3, 0x7d, 0x83, 0x4a, 0xec, 0xdb, 0x42, 0x00, 0x00, 0x00, 0x00,
  0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Boxes_png_len = 392;
