unsigned char hr_dust_Square_Shape_Dotted_Left_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x0b, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xdd, 0x94, 0x41, 0x4e, 0x02, 0x41, 0x10,
  0x45, 0xdf, 0x77, 0x66, 0xe5, 0x18, 0x6e, 0x00, 0x77, 0x30, 0xf1, 0x18,
  0x80, 0x27, 0x10, 0xd7, 0xca, 0x92, 0x0d, 0xac, 0x74, 0x41, 0x62, 0x44,
  0xd7, 0xb2, 0x56, 0xe2, 0x09, 0xe4, 0x00, 0x7a, 0x01, 0x59, 0xe0, 0x01,
  0xc4, 0x8b, 0x34, 0x8b, 0xe9, 0x99, 0x7c, 0x0c, 0x04, 0x27, 0x19, 0x13,
  0x63, 0x25, 0x95, 0xfc, 0xbc, 0x54, 0x57, 0xfa, 0x77, 0x75, 0x4a, 0x21,
  0x04, 0xea, 0x8c, 0x83, 0x5a, 0xbb, 0x01, 0x84, 0x10, 0xca, 0x04, 0x4e,
  0x81, 0x4b, 0x20, 0x33, 0x96, 0x02, 0xe7, 0x31, 0x13, 0xe3, 0x47, 0x40,
  0x1f, 0xe8, 0x6e, 0xf4, 0xb0, 0x82, 0x2e, 0x10, 0x62, 0x3e, 0x19, 0x1f,
  0x19, 0x1f, 0x1a, 0x9f, 0x19, 0xef, 0x14, 0xdc, 0x2d, 0x1f, 0xef, 0xd0,
  0xcd, 0x2a, 0x3a, 0x35, 0xa8, 0x1d, 0xfa, 0x26, 0x1e, 0x08, 0x51, 0x17,
  0x31, 0x00, 0xee, 0x81, 0x2f, 0xe0, 0xb1, 0xa4, 0x66, 0xe1, 0xda, 0x2c,
  0x7c, 0xf8, 0xbb, 0x54, 0xc9, 0xda, 0xa7, 0xbc, 0xb7, 0xa1, 0xa4, 0x86,
  0xa4, 0x3b, 0x49, 0x13, 0x49, 0x0d, 0xe3, 0x2d, 0x49, 0x53, 0x49, 0x43,
  0x49, 0xc9, 0x8f, 0x2d, 0x03, 0x13, 0xe3, 0xb7, 0xc6, 0xe7, 0xc6, 0x7b,
  0xbf, 0x66, 0x39, 0xdd, 0x5f, 0xc2, 0x38, 0xde, 0xa2, 0xd0, 0x45, 0x5c,
  0x90, 0xff, 0xd1, 0x4f, 0xe0, 0xb9, 0xa4, 0xff, 0x62, 0xca, 0x2d, 0x49,
  0x73, 0x49, 0x2f, 0x92, 0x9a, 0xc6, 0x4f, 0x24, 0xbd, 0x4a, 0x9a, 0x49,
  0xca, 0xb6, 0x59, 0xbe, 0x32, 0xcb, 0x4b, 0xe3, 0x53, 0xe3, 0x0f, 0xc6,
  0xdf, 0x8c, 0xf7, 0xb7, 0x59, 0x5e, 0x98, 0x7e, 0x37, 0xbd, 0xaa, 0xa4,
  0xbf, 0xad, 0xaf, 0x0e, 0xf9, 0xf4, 0x0e, 0x8d, 0x25, 0x40, 0x0f, 0x38,
  0x63, 0x73, 0x7d, 0x65, 0xe4, 0xab, 0xae, 0xed, 0x3d, 0xf4, 0xe7, 0x37,
  0xf6, 0x1a, 0xd4, 0x0c, 0x25, 0x41, 0xb0, 0x5b, 0xeb, 0x78, 0x00, 0x00,
  0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_dust_Square_Shape_Dotted_Left_20x20_png_len = 382;
