unsigned char hr_vision_Monitor_03_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x44, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0x94, 0x4d, 0x4a, 0x03, 0x41, 0x10,
  0x85, 0xbf, 0xf2, 0x1f, 0x54, 0x70, 0x16, 0x2e, 0xc4, 0x85, 0xe0, 0x52,
  0x50, 0x90, 0x40, 0xb6, 0x2e, 0x72, 0x0c, 0x0f, 0x11, 0x97, 0x26, 0x17,
  0xd0, 0x8b, 0x78, 0x0f, 0x0f, 0xe0, 0xc2, 0x95, 0x64, 0xeb, 0x22, 0x88,
  0x3f, 0x10, 0x41, 0x44, 0x13, 0x75, 0xca, 0x45, 0x57, 0x0f, 0x3d, 0x6d,
  0x8f, 0x93, 0xd1, 0x14, 0x14, 0x05, 0xd5, 0xf3, 0x5e, 0xbf, 0xe9, 0x7e,
  0xd5, 0xa2, 0xaa, 0xcc, 0x32, 0xe6, 0x66, 0xca, 0x06, 0x2c, 0xc4, 0x0d,
  0x11, 0x39, 0x04, 0x8e, 0x80, 0xe5, 0x1a, 0xec, 0x18, 0xb8, 0x54, 0xd5,
  0xeb, 0x52, 0x57, 0x55, 0x8b, 0x04, 0xfa, 0x40, 0x0e, 0xe8, 0x94, 0x99,
  0x03, 0xbd, 0x90, 0x43, 0xfc, 0x19, 0x9a, 0xb2, 0x2b, 0xcb, 0x3e, 0xf0,
  0x5c, 0xa3, 0x70, 0x03, 0x38, 0x03, 0x5a, 0x40, 0xab, 0x50, 0x1a, 0xa8,
  0x3b, 0xb1, 0x5d, 0x3b, 0xe1, 0x8e, 0xbf, 0x25, 0xd0, 0x31, 0x4c, 0xd7,
  0xf7, 0xc2, 0x4b, 0x59, 0xb3, 0xfa, 0x58, 0xa3, 0x2c, 0x8c, 0xa7, 0x08,
  0x5b, 0x22, 0x9c, 0xb7, 0xfa, 0xd5, 0x80, 0xf0, 0xd3, 0x6a, 0x71, 0xb9,
  0x21, 0xa1, 0x34, 0x20, 0x8a, 0xa3, 0xc0, 0x86, 0x84, 0xf9, 0x1f, 0x88,
  0xfd, 0xb7, 0x1e, 0x5b, 0x22, 0xfc, 0xb0, 0xba, 0xd8, 0x80, 0xd0, 0x7f,
  0xeb, 0xb1, 0x25, 0xc2, 0x07, 0xab, 0xfb, 0x0d, 0x08, 0x0f, 0xac, 0xde,
  0xfb, 0x46, 0xe8, 0xc3, 0x4d, 0xe0, 0x06, 0x58, 0x01, 0x2e, 0x70, 0x3e,
  0x7c, 0x05, 0x26, 0x11, 0xc9, 0x12, 0xb0, 0x8a, 0xf3, 0xe1, 0x31, 0xf0,
  0x0e, 0xec, 0xa9, 0xaa, 0x73, 0x47, 0xe4, 0xab, 0x36, 0x30, 0x60, 0xfa,
  0x49, 0x19, 0x00, 0xed, 0xe4, 0xa4, 0x84, 0x21, 0x22, 0xdb, 0xb8, 0x59,
  0x5e, 0xc7, 0x59, 0xe2, 0xdc, 0x96, 0x4e, 0x71, 0x56, 0x79, 0x01, 0xc6,
  0xaa, 0x3a, 0x8c, 0xb1, 0x3f, 0x1e, 0x07, 0x53, 0x3d, 0x14, 0x91, 0x2d,
  0x20, 0xb3, 0xd6, 0x6e, 0x84, 0x79, 0x53, 0xd5, 0xbb, 0x14, 0xb6, 0x6a,
  0xa4, 0x32, 0xdc, 0xd9, 0x55, 0xfd, 0xea, 0x04, 0xc8, 0x52, 0xd8, 0x2a,
  0x85, 0x23, 0x11, 0xe9, 0x02, 0x3b, 0x49, 0x15, 0x70, 0xab, 0xaa, 0xa3,
  0xd4, 0x42, 0xf2, 0x0c, 0xff, 0x13, 0x33, 0x7f, 0xb1, 0xbf, 0x01, 0xda,
  0xae, 0xdb, 0x88, 0x7a, 0x64, 0xea, 0xe1, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_vision_Monitor_03_20x20_png_len = 439;
