unsigned char Crown_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x01, 0x31, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xed,
  0x94, 0x31, 0x4e, 0x02, 0x41, 0x14, 0x86, 0x3f, 0x08, 0x59, 0x29, 0xbc,
  0x01, 0xd0, 0x49, 0x61, 0x81, 0x24, 0xe2, 0x21, 0x94, 0xc6, 0x2b, 0xe8,
  0x29, 0xf0, 0x08, 0x96, 0x9e, 0x42, 0x12, 0xce, 0x00, 0x36, 0xc6, 0xd2,
  0x02, 0x14, 0x35, 0x96, 0x6a, 0x67, 0xa2, 0x31, 0xda, 0x19, 0x5c, 0x0b,
  0x7f, 0xe2, 0xcb, 0x64, 0x66, 0xd8, 0x31, 0x24, 0x36, 0xfc, 0xc9, 0xcb,
  0xcc, 0xbc, 0xf7, 0xff, 0xef, 0x7f, 0x3b, 0xbb, 0x59, 0x58, 0x61, 0x89,
  0xc8, 0x80, 0x0b, 0x45, 0xf6, 0xd7, 0x26, 0x23, 0xe0, 0x12, 0x68, 0x7a,
  0x6a, 0x3d, 0x20, 0x57, 0xf4, 0x3c, 0xf5, 0xa6, 0xb4, 0xc3, 0x98, 0xc1,
  0xb3, 0x1a, 0xbc, 0x02, 0x5d, 0x93, 0xaf, 0x03, 0xef, 0xc6, 0xe0, 0x03,
  0x68, 0x98, 0x7a, 0x57, 0x9a, 0x5c, 0x3d, 0x82, 0x38, 0x33, 0x4d, 0xbe,
  0x80, 0x63, 0xa0, 0x0c, 0x0c, 0x94, 0x1b, 0x38, 0xfb, 0x92, 0x9e, 0x66,
  0x66, 0x74, 0xa3, 0x98, 0xc1, 0x89, 0x48, 0x77, 0x46, 0x74, 0xee, 0x4c,
  0xdd, 0xd0, 0xde, 0xd6, 0x66, 0xd2, 0xe4, 0xea, 0x11, 0xc4, 0xa1, 0x48,
  0xa7, 0xc0, 0x2e, 0xf0, 0x82, 0xff, 0xde, 0x8f, 0x4c, 0xfe, 0x0d, 0xd8,
  0x07, 0xfa, 0x3a, 0x1f, 0xc4, 0x0c, 0x3a, 0x22, 0x4d, 0x75, 0xde, 0x00,
  0xae, 0x80, 0x7b, 0x60, 0xcd, 0xf0, 0x32, 0xe0, 0x56, 0x53, 0x6f, 0x2a,
  0x77, 0x23, 0xed, 0x76, 0xcc, 0xa0, 0x0a, 0x7c, 0x2a, 0xaa, 0xca, 0xad,
  0x03, 0x2d, 0x0f, 0xb7, 0xa5, 0x5a, 0x48, 0x17, 0xc4, 0x54, 0x93, 0x74,
  0x16, 0x11, 0x0d, 0x76, 0xa4, 0xb9, 0x76, 0x0b, 0x65, 0x0f, 0x79, 0xa2,
  0x75, 0x2b, 0xc1, 0x60, 0xce, 0x9d, 0xb8, 0x85, 0x7f, 0x31, 0x18, 0x6b,
  0x6d, 0x27, 0x18, 0xcc, 0xb9, 0xe3, 0x28, 0x4b, 0xa8, 0xf3, 0xfb, 0x09,
  0xa6, 0x46, 0xcd, 0x6d, 0x56, 0x0a, 0x98, 0xe4, 0x85, 0x67, 0x5f, 0xd0,
  0xaf, 0x92, 0x2a, 0x08, 0x20, 0x38, 0x90, 0xef, 0x1d, 0x2c, 0x15, 0x21,
  0x83, 0x27, 0xad, 0x45, 0xef, 0x1e, 0xe0, 0x31, 0xc5, 0x78, 0x4f, 0x82,
  0xa2, 0x06, 0x0f, 0xfc, 0xfc, 0xbb, 0x56, 0x48, 0xc7, 0x37, 0x5b, 0x27,
  0x77, 0xc8, 0x98, 0x0a, 0x28, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Crown_png_len = 378;
