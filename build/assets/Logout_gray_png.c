unsigned char Logout_gray_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x05,
  0xbe, 0x7a, 0x54, 0x58, 0x74, 0x52, 0x61, 0x77, 0x20, 0x70, 0x72, 0x6f,
  0x66, 0x69, 0x6c, 0x65, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x65, 0x78,
  0x69, 0x66, 0x00, 0x00, 0x78, 0xda, 0xe5, 0x57, 0x6b, 0x76, 0x33, 0x27,
  0x0c, 0xfd, 0xcf, 0x2a, 0xba, 0x04, 0x24, 0x10, 0x12, 0xcb, 0xe1, 0x79,
  0x4e, 0x77, 0xd0, 0xe5, 0xf7, 0xc2, 0x60, 0xc7, 0x4e, 0xdc, 0xd4, 0xf9,
  0xda, 0x3f, 0x3d, 0xf5, 0xc4, 0x86, 0xd1, 0x30, 0x42, 0xe8, 0x5e, 0x3d,
  0xe2, 0xc6, 0x1f, 0xbf, 0x4f, 0xf7, 0x1b, 0x3e, 0x1c, 0x2c, 0xb9, 0x28,
  0x6a, 0x29, 0xa7, 0xe4, 0xf1, 0x89, 0x39, 0x66, 0x2e, 0x98, 0x98, 0xbf,
  0x3e, 0x79, 0xff, 0x92, 0x8f, 0xfb, 0xf7, 0xba, 0xb9, 0x3d, 0xa3, 0x67,
  0xb9, 0xbb, 0x3f, 0x60, 0x88, 0x02, 0xc6, 0x70, 0xdd, 0x6a, 0x39, 0xeb,
  0x0b, 0xe4, 0xf2, 0xf1, 0xc2, 0x5d, 0x4f, 0x7d, 0x96, 0x3b, 0x3b, 0x4f,
  0xd8, 0x8e, 0xa2, 0xf3, 0xe0, 0xa6, 0x30, 0xac, 0x9d, 0x19, 0x93, 0xfe,
  0x68, 0x24, 0xe4, 0x7c, 0xc9, 0x29, 0x1e, 0x45, 0x79, 0x5c, 0x93, 0x94,
  0x4d, 0x1f, 0x4d, 0xad, 0x7c, 0x8d, 0xed, 0x2c, 0xdc, 0xa6, 0x9c, 0x6f,
  0xd0, 0xad, 0xfa, 0xae, 0x64, 0xdd, 0xbb, 0x47, 0x41, 0x54, 0x78, 0xa9,
  0x0b, 0x56, 0x05, 0xe6, 0x11, 0x28, 0xf8, 0xfd, 0x6b, 0x97, 0x05, 0x61,
  0x7d, 0x43, 0x28, 0x18, 0xcf, 0x2f, 0xd6, 0x11, 0xae, 0x82, 0xfb, 0xe4,
  0x30, 0x10, 0x34, 0x5e, 0x96, 0xc0, 0x21, 0x4f, 0xc7, 0xbb, 0x8d, 0xde,
  0x3f, 0x3a, 0xe8, 0xc9, 0xc9, 0xb7, 0x99, 0xfb, 0xec, 0xfd, 0xfb, 0xec,
  0x93, 0xf3, 0xb9, 0x1c, 0x79, 0xf8, 0xe4, 0xcb, 0x74, 0x7c, 0x84, 0xc9,
  0xcb, 0x07, 0x24, 0xaf, 0x9d, 0xbf, 0x5d, 0xfc, 0xb0, 0x71, 0xb8, 0x5b,
  0xc4, 0xcf, 0x0f, 0x00, 0xab, 0x7d, 0x39, 0xce, 0xf9, 0xce, 0xd9, 0x6d,
  0xce, 0x71, 0x9d, 0xae, 0xc4, 0x04, 0x8f, 0xa6, 0xc3, 0x28, 0xef, 0x6e,
  0xde, 0x59, 0xef, 0x60, 0x61, 0x85, 0xcb, 0xc3, 0x7e, 0x2d, 0xe1, 0x52,
  0x7c, 0x05, 0x73, 0xdd, 0x57, 0xc6, 0x65, 0xbe, 0xf8, 0x06, 0x70, 0xba,
  0x6f, 0xbe, 0xe2, 0x6a, 0x94, 0x89, 0x81, 0xca, 0x74, 0x14, 0xa9, 0x53,
  0xa1, 0x49, 0x63, 0x8f, 0x8d, 0x1a, 0x4c, 0x8c, 0x3c, 0x58, 0x31, 0x32,
  0x37, 0x0e, 0x5b, 0x66, 0x41, 0x39, 0x73, 0x0b, 0x0b, 0xa7, 0xb8, 0x2e,
  0x9a, 0xac, 0x21, 0x87, 0x1e, 0x0c, 0x58, 0x36, 0x1e, 0x0e, 0xd0, 0xc5,
  0xc0, 0x77, 0x5b, 0x68, 0xef, 0x9b, 0xf7, 0x7e, 0x0d, 0xac, 0xef, 0xbe,
  0x13, 0x96, 0x32, 0x41, 0x19, 0xe1, 0x95, 0xbf, 0xbc, 0xdc, 0x77, 0x0f,
  0x7f, 0x72, 0xb9, 0x39, 0xdb, 0x72, 0x11, 0x79, 0xbb, 0xfb, 0x0a, 0x76,
  0xf1, 0x62, 0x2e, 0xcc, 0x58, 0xc8, 0xad, 0x5f, 0xac, 0x02, 0x20, 0x34,
  0x0f, 0x6e, 0xb2, 0x1d, 0x7c, 0xbb, 0x0e, 0xfc, 0xfe, 0x81, 0x3f, 0xa0,
  0x2a, 0x10, 0x94, 0xed, 0x66, 0xc3, 0x01, 0x8b, 0xaf, 0x97, 0x8a, 0x2a,
  0xf4, 0xc1, 0xad, 0xb0, 0x71, 0x0e, 0x58, 0x27, 0x18, 0xaf, 0xa8, 0x20,
  0xa7, 0xfd, 0x28, 0x80, 0x8b, 0xb0, 0xb7, 0xc0, 0x18, 0xd0, 0x3e, 0x92,
  0x4f, 0x14, 0x84, 0x12, 0x79, 0x65, 0x56, 0x22, 0xf8, 0xd1, 0x00, 0x50,
  0x81, 0xe5, 0x1c, 0x22, 0x57, 0x20, 0x40, 0x22, 0xdc, 0x61, 0x24, 0x47,
  0x44, 0x08, 0x3b, 0x65, 0xe3, 0xb5, 0x37, 0xde, 0x51, 0xda, 0x6b, 0x59,
  0x38, 0xf1, 0x12, 0x23, 0x37, 0x01, 0x08, 0x09, 0x29, 0x28, 0xb0, 0xc9,
  0xa1, 0x00, 0xac, 0x18, 0x05, 0xfc, 0xd1, 0x68, 0xe0, 0x50, 0x91, 0x20,
  0x51, 0x44, 0x92, 0xa8, 0x98, 0x93, 0x2c, 0x25, 0x85, 0x14, 0x93, 0xa4,
  0x94, 0x34, 0xad, 0x24, 0x57, 0x34, 0x68, 0x54, 0xd1, 0xa4, 0xaa, 0xa6,
  0x59, 0x8b, 0x05, 0x8b, 0x26, 0x96, 0x4c, 0xcd, 0x2c, 0x5b, 0xc9, 0x9c,
  0x03, 0x72, 0xa0, 0xe4, 0x94, 0x35, 0x5b, 0xce, 0xb9, 0x14, 0x76, 0x05,
  0x1b, 0x15, 0xe8, 0x2a, 0x58, 0x5f, 0x20, 0xa9, 0x5c, 0x43, 0x8d, 0x55,
  0x6a, 0xaa, 0x5a, 0xad, 0xe6, 0x5a, 0x1a, 0xe8, 0xd3, 0x62, 0x93, 0x96,
  0x9a, 0x36, 0x6b, 0xb9, 0x95, 0xce, 0x3d, 0x74, 0xa4, 0x89, 0x9e, 0xba,
  0x76, 0xeb, 0xb9, 0x97, 0x41, 0x6e, 0x20, 0x53, 0x8c, 0x38, 0x64, 0xa4,
  0xa1, 0xc3, 0x46, 0x1e, 0x65, 0x82, 0x6b, 0x33, 0xcc, 0x38, 0x65, 0xa6,
  0xa9, 0xd3, 0x66, 0x9e, 0xe5, 0x8e, 0xda, 0x41, 0xf5, 0xcb, 0xf5, 0x03,
  0xd4, 0xe8, 0xa0, 0xc6, 0x1b, 0xa9, 0xb5, 0x4e, 0xef, 0xa8, 0x41, 0xea,
  0x54, 0x6f, 0x2a, 0x68, 0xa5, 0x13, 0x59, 0x98, 0x01, 0x31, 0x8e, 0x04,
  0xc4, 0x75, 0x21, 0x00, 0x42, 0xf3, 0xc2, 0xcc, 0x1b, 0xc5, 0xc8, 0x0b,
  0xb9, 0x85, 0x99, 0xcf, 0x2b, 0xcb, 0x09, 0xc3, 0x48, 0x59, 0xd8, 0xb8,
  0x4e, 0x0b, 0x31, 0x40, 0x18, 0x07, 0xb1, 0x4c, 0xba, 0x63, 0xf7, 0x81,
  0xdc, 0x5b, 0xb8, 0x39, 0xb1, 0xb7, 0x70, 0xe3, 0xbf, 0x43, 0xce, 0x2d,
  0xe8, 0xfe, 0x0d, 0xe4, 0x1c, 0xa0, 0xfb, 0x8a, 0xdb, 0x0b, 0xd4, 0xfa,
  0xaa, 0x73, 0x6d, 0x23, 0x76, 0x45, 0xe1, 0xf2, 0xa9, 0x0f, 0x73, 0x15,
  0xb2, 0x82, 0x3f, 0x77, 0x9b, 0xfc, 0xd3, 0xf1, 0xff, 0xa5, 0x48, 0x9b,
  0x4c, 0xb5, 0xd9, 0x19, 0x30, 0x5a, 0xe2, 0x59, 0x9b, 0x8e, 0x0c, 0xc2,
  0x84, 0x88, 0x92, 0x2c, 0xf8, 0x43, 0xca, 0x29, 0x7b, 0xf1, 0xe3, 0xe8,
  0xce, 0x04, 0x4c, 0xab, 0x51, 0x56, 0xd5, 0x66, 0xed, 0xc8, 0x32, 0x02,
  0x22, 0x14, 0x4d, 0xe2, 0x91, 0xef, 0x53, 0xae, 0x43, 0xca, 0x04, 0x31,
  0x10, 0x10, 0xb2, 0x75, 0xf1, 0x17, 0x5d, 0xee, 0x85, 0xf2, 0x6f, 0x47,
  0x02, 0x8d, 0x28, 0x8e, 0xd8, 0x63, 0x1e, 0x09, 0xda, 0x09, 0x56, 0xaf,
  0x73, 0xb8, 0x5c, 0x5b, 0x97, 0x09, 0x83, 0x34, 0x97, 0x3a, 0x2c, 0xe1,
  0x09, 0xe7, 0xde, 0xfc, 0x2b, 0xb9, 0x55, 0x43, 0x9f, 0x70, 0xdd, 0xdb,
  0x0c, 0x82, 0xec, 0xda, 0xb1, 0x42, 0x68, 0x75, 0x23, 0x08, 0x04, 0xdf,
  0x14, 0xad, 0x45, 0x1c, 0x0d, 0x52, 0x9a, 0x7d, 0x80, 0x8c, 0x25, 0x27,
  0x6c, 0x9e, 0x6e, 0xde, 0xcb, 0x48, 0xbe, 0x75, 0x39, 0x87, 0xeb, 0x3a,
  0x18, 0xe1, 0x68, 0x97, 0xbb, 0xfc, 0x7d, 0x74, 0x9f, 0x05, 0xbf, 0x3a,
  0x7e, 0x51, 0x24, 0x19, 0xb1, 0x47, 0xb0, 0xbb, 0x76, 0x2f, 0xd1, 0xac,
  0x21, 0x50, 0x11, 0x52, 0xd2, 0xbc, 0x6e, 0x63, 0x00, 0xdd, 0x02, 0x02,
  0x96, 0xe6, 0x27, 0xff, 0xb9, 0x97, 0x0e, 0x15, 0x89, 0xc8, 0x39, 0xbe,
  0xa8, 0xe6, 0xd4, 0x07, 0x44, 0x36, 0x07, 0x12, 0x67, 0x81, 0x38, 0x36,
  0x6f, 0xb5, 0x95, 0x34, 0xa1, 0x53, 0xd1, 0x96, 0xe1, 0x29, 0x68, 0x02,
  0x45, 0xab, 0xa7, 0x0c, 0x8c, 0x2c, 0x96, 0x83, 0x95, 0x84, 0x78, 0xe6,
  0x79, 0xe0, 0x4f, 0xd8, 0xf2, 0x18, 0xf1, 0xc6, 0xe8, 0xce, 0x24, 0x76,
  0x02, 0x7f, 0xc2, 0x56, 0x50, 0x92, 0xd5, 0x31, 0x10, 0xf3, 0x1d, 0x1c,
  0x0c, 0x63, 0x9f, 0x03, 0x5d, 0x49, 0xda, 0xf6, 0x82, 0x4b, 0x2f, 0xa9,
  0xed, 0xd6, 0xa4, 0x08, 0x4c, 0xdf, 0x6f, 0xd2, 0x61, 0x9e, 0x72, 0x31,
  0x41, 0xaa, 0xc4, 0x41, 0x64, 0x03, 0xb6, 0x66, 0xb7, 0x17, 0x53, 0xac,
  0x13, 0x79, 0xaf, 0x2f, 0x4a, 0xe4, 0x24, 0xb3, 0x96, 0x30, 0xaa, 0x5b,
  0x58, 0xa6, 0xe3, 0x6a, 0xf6, 0xef, 0xd3, 0x13, 0x98, 0x40, 0x83, 0x8e,
  0x2a, 0xe0, 0x12, 0x8e, 0xe3, 0xae, 0xf3, 0x80, 0x35, 0xda, 0xe9, 0xda,
  0xb8, 0xf4, 0xad, 0x35, 0xda, 0x5f, 0x79, 0x84, 0xd2, 0x45, 0x2b, 0x92,
  0xf8, 0xc1, 0x02, 0x77, 0x26, 0x41, 0x7a, 0x44, 0x47, 0xe4, 0x8b, 0xcd,
  0x0b, 0x12, 0x44, 0x29, 0x41, 0x97, 0xe9, 0x0b, 0x84, 0x16, 0x40, 0xf3,
  0xba, 0xb7, 0xc9, 0xb9, 0x62, 0xe7, 0xe8, 0xd6, 0xf6, 0x09, 0xb9, 0xf3,
  0x08, 0xe0, 0xa4, 0x70, 0x65, 0x6e, 0x1c, 0xa1, 0xa1, 0xf5, 0x3a, 0xf7,
  0xe1, 0x41, 0xbd, 0x3c, 0xe8, 0xef, 0x49, 0xb8, 0xa0, 0x43, 0x2c, 0xae,
  0xc9, 0x6a, 0xd8, 0xfa, 0x20, 0xc4, 0x05, 0xfc, 0x78, 0x59, 0x6f, 0x00,
  0xe0, 0x0e, 0xdd, 0xea, 0x23, 0x22, 0x32, 0x4b, 0x9c, 0x3d, 0x65, 0x34,
  0xc9, 0xf3, 0xe4, 0x92, 0xe7, 0x70, 0x71, 0xaf, 0xe8, 0xbe, 0xd8, 0x9d,
  0x11, 0x7d, 0xf5, 0xd1, 0x09, 0x1f, 0x23, 0xdc, 0x60, 0xc5, 0x9a, 0x2d,
  0x37, 0xf4, 0x34, 0xe3, 0xda, 0x38, 0x3b, 0x98, 0x0d, 0x3b, 0xaf, 0x7b,
  0x04, 0x76, 0x1f, 0x76, 0x05, 0x39, 0xb6, 0x82, 0x1f, 0xae, 0xfb, 0x13,
  0x30, 0x2b, 0xc8, 0xd3, 0x43, 0x00, 0xd1, 0x40, 0xb9, 0x2a, 0x00, 0x72,
  0xf8, 0xd8, 0x61, 0x11, 0xfc, 0x70, 0x09, 0xc2, 0xd1, 0x87, 0xb8, 0x42,
  0x71, 0xa3, 0xac, 0x63, 0x7a, 0x84, 0xc5, 0x6d, 0x7d, 0xaa, 0x01, 0x78,
  0x65, 0x90, 0xd4, 0xe7, 0x3b, 0x5b, 0xee, 0x2c, 0x70, 0x87, 0x06, 0xbf,
  0xc2, 0x02, 0x9e, 0xd9, 0x56, 0x6a, 0x42, 0xb9, 0x6c, 0xe4, 0x10, 0xdb,
  0x82, 0x86, 0x81, 0x9b, 0x1e, 0x77, 0x1a, 0x5a, 0x00, 0x9c, 0x9a, 0x51,
  0x8b, 0xcd, 0x0e, 0x99, 0x51, 0xeb, 0xef, 0x21, 0x58, 0x0a, 0x8a, 0xf4,
  0x18, 0xb6, 0x61, 0x88, 0xf9, 0xb6, 0x8f, 0xb3, 0xf7, 0xab, 0xc5, 0xb7,
  0x40, 0x3a, 0xfa, 0x3e, 0xf1, 0x7d, 0x1e, 0x03, 0xf2, 0xe7, 0x55, 0x56,
  0xb8, 0x47, 0x5f, 0x40, 0x9e, 0x01, 0x05, 0xb5, 0x35, 0xb7, 0xdd, 0xbb,
  0xbc, 0xfb, 0x12, 0x3d, 0x88, 0x3e, 0x02, 0x54, 0x43, 0xc8, 0x09, 0xff,
  0x07, 0xac, 0x74, 0xc3, 0x64, 0xcf, 0x2e, 0x73, 0x6f, 0xe4, 0x9a, 0xb7,
  0x52, 0x8c, 0xfb, 0x2e, 0xc7, 0xfc, 0x64, 0x74, 0x6f, 0x2e, 0xd4, 0x79,
  0x25, 0xdd, 0x82, 0x86, 0x8d, 0x4b, 0x35, 0x64, 0x20, 0x24, 0x00, 0x04,
  0xc0, 0x1d, 0xb5, 0x37, 0x08, 0xf3, 0xd6, 0xe8, 0xce, 0x04, 0xbe, 0x5f,
  0xe5, 0x2d, 0x95, 0x88, 0x34, 0xa0, 0xe0, 0xe8, 0xfa, 0xa7, 0x0b, 0xda,
  0xfb, 0x40, 0xbf, 0x09, 0xfe, 0x22, 0xfa, 0x65, 0xca, 0x2a, 0x7e, 0xaf,
  0xe5, 0x02, 0x1e, 0xad, 0x92, 0x8b, 0xba, 0x7a, 0x09, 0x90, 0x76, 0xea,
  0x58, 0x1e, 0x45, 0x7b, 0xf6, 0x50, 0x3a, 0x00, 0xe9, 0xda, 0x2c, 0xa5,
  0xdc, 0x77, 0x7a, 0x79, 0xea, 0x26, 0x62, 0xc3, 0xb6, 0x4e, 0xb4, 0xfc,
  0x2b, 0xfe, 0x76, 0xbf, 0xda, 0xc6, 0x7c, 0x8c, 0x71, 0x85, 0xb1, 0xb9,
  0x86, 0xde, 0x02, 0xa5, 0x34, 0x68, 0x6b, 0xbb, 0x67, 0x88, 0xf9, 0x10,
  0x70, 0x19, 0xf9, 0x83, 0x9a, 0xe0, 0x7e, 0xd4, 0xd3, 0xfc, 0x87, 0x15,
  0x05, 0x34, 0xe6, 0xa8, 0xf8, 0xee, 0x4f, 0xc4, 0x41, 0x76, 0xb2, 0x20,
  0x70, 0x50, 0xe5, 0x00, 0x00, 0x01, 0x84, 0x69, 0x43, 0x43, 0x50, 0x49,
  0x43, 0x43, 0x20, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x00, 0x00,
  0x78, 0x9c, 0x7d, 0x91, 0x3d, 0x48, 0xc3, 0x40, 0x18, 0x86, 0xdf, 0xa6,
  0x4a, 0xa5, 0x54, 0x1c, 0xec, 0x20, 0xc5, 0x21, 0x43, 0x75, 0xb2, 0x20,
  0x2a, 0xe2, 0xa8, 0x55, 0x28, 0x42, 0x85, 0x50, 0x2b, 0xb4, 0xea, 0x60,
  0x72, 0xe9, 0x1f, 0x34, 0x69, 0x48, 0x52, 0x5c, 0x1c, 0x05, 0xd7, 0x82,
  0x83, 0x3f, 0x8b, 0x55, 0x07, 0x17, 0x67, 0x5d, 0x1d, 0x5c, 0x05, 0x41,
  0xf0, 0x07, 0xc4, 0xd5, 0xc5, 0x49, 0xd1, 0x45, 0x4a, 0xfc, 0x2e, 0x29,
  0xb4, 0x88, 0xf1, 0x8e, 0xe3, 0x1e, 0xde, 0xfb, 0xde, 0x97, 0xbb, 0xef,
  0x00, 0xa1, 0x59, 0x65, 0x9a, 0xd5, 0x33, 0x0e, 0x68, 0xba, 0x6d, 0x66,
  0x52, 0x49, 0x31, 0x97, 0x5f, 0x15, 0x43, 0xaf, 0x08, 0xd2, 0x0c, 0x63,
  0x08, 0x31, 0x99, 0x59, 0xc6, 0x9c, 0x24, 0xa5, 0xe1, 0x3b, 0xbe, 0xee,
  0x11, 0xe0, 0xfb, 0x5d, 0x82, 0x67, 0xf9, 0xd7, 0xfd, 0x39, 0xfa, 0xd5,
  0x82, 0xc5, 0x80, 0x80, 0x48, 0x3c, 0xcb, 0x0c, 0xd3, 0x26, 0xde, 0x20,
  0x9e, 0xde, 0xb4, 0x0d, 0xce, 0xfb, 0xc4, 0x51, 0x56, 0x96, 0x55, 0xe2,
  0x73, 0xe2, 0x31, 0x93, 0x2e, 0x48, 0xfc, 0xc8, 0x75, 0xc5, 0xe3, 0x37,
  0xce, 0x25, 0x97, 0x05, 0x9e, 0x19, 0x35, 0xb3, 0x99, 0x79, 0xe2, 0x28,
  0xb1, 0x58, 0xea, 0x62, 0xa5, 0x8b, 0x59, 0xd9, 0xd4, 0x88, 0xa7, 0x88,
  0xe3, 0xaa, 0xa6, 0x53, 0xbe, 0x90, 0xf3, 0x58, 0xe5, 0xbc, 0xc5, 0x59,
  0xab, 0xd6, 0x59, 0xfb, 0x9e, 0xfc, 0x85, 0x91, 0x82, 0xbe, 0xb2, 0xcc,
  0x75, 0x5a, 0xc3, 0x48, 0x61, 0x11, 0x4b, 0x90, 0x20, 0x42, 0x41, 0x1d,
  0x15, 0x54, 0x61, 0x23, 0x41, 0xbb, 0x4e, 0x8a, 0x85, 0x0c, 0x9d, 0x27,
  0x7d, 0xfc, 0x31, 0xd7, 0x2f, 0x91, 0x4b, 0x21, 0x57, 0x05, 0x8c, 0x1c,
  0x0b, 0xa8, 0x41, 0x83, 0xec, 0xfa, 0xc1, 0xff, 0xe0, 0x77, 0x6f, 0xad,
  0xe2, 0xe4, 0x84, 0x97, 0x14, 0x49, 0x02, 0xbd, 0x2f, 0x8e, 0xf3, 0x31,
  0x02, 0x84, 0x76, 0x81, 0x56, 0xc3, 0x71, 0xbe, 0x8f, 0x1d, 0xa7, 0x75,
  0x02, 0x04, 0x9f, 0x81, 0x2b, 0xbd, 0xe3, 0xaf, 0x35, 0x81, 0x99, 0x4f,
  0xd2, 0x1b, 0x1d, 0x2d, 0x7e, 0x04, 0x0c, 0x6c, 0x03, 0x17, 0xd7, 0x1d,
  0x4d, 0xd9, 0x03, 0x2e, 0x77, 0x80, 0xa1, 0x27, 0x43, 0x36, 0x65, 0x57,
  0x0a, 0xd2, 0x12, 0x8a, 0x45, 0xe0, 0xfd, 0x8c, 0xbe, 0x29, 0x0f, 0x0c,
  0xde, 0x02, 0xe1, 0x35, 0xaf, 0x6f, 0xed, 0x73, 0x9c, 0x3e, 0x00, 0x59,
  0xea, 0x55, 0xfa, 0x06, 0x38, 0x38, 0x04, 0x46, 0x4b, 0x94, 0xbd, 0xee,
  0xf3, 0xee, 0xbe, 0xee, 0xbe, 0xfd, 0x5b, 0xd3, 0xee, 0xdf, 0x0f, 0x16,
  0x70, 0x72, 0x82, 0xa0, 0xef, 0xb9, 0xbf, 0x00, 0x00, 0x00, 0x06, 0x62,
  0x4b, 0x47, 0x44, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0xa0, 0xbd, 0xa7,
  0x93, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b,
  0x13, 0x00, 0x00, 0x0b, 0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00,
  0x00, 0x07, 0x74, 0x49, 0x4d, 0x45, 0x07, 0xe7, 0x03, 0x03, 0x0a, 0x24,
  0x11, 0x04, 0x4f, 0x10, 0x26, 0x00, 0x00, 0x01, 0x10, 0x49, 0x44, 0x41,
  0x54, 0x48, 0xc7, 0xcd, 0x95, 0xd1, 0x69, 0xc3, 0x40, 0x0c, 0x86, 0x3f,
  0x15, 0x3f, 0x9c, 0x21, 0x06, 0x8f, 0x70, 0xde, 0xa0, 0x1d, 0xc1, 0x1b,
  0x74, 0x94, 0x74, 0x82, 0x7a, 0x93, 0xd2, 0x49, 0x92, 0x4e, 0xd0, 0x11,
  0x7c, 0x23, 0x18, 0x62, 0xb8, 0x7b, 0xfb, 0xfb, 0x62, 0x97, 0x60, 0x9a,
  0x34, 0x76, 0x6d, 0x52, 0xc1, 0x3d, 0x9d, 0xa4, 0x1f, 0xe9, 0xff, 0x25,
  0x59, 0xd3, 0x34, 0x6c, 0x69, 0x0f, 0x6c, 0x6c, 0x9b, 0x03, 0x64, 0x73,
  0x9c, 0xf3, 0xbc, 0x68, 0x01, 0xff, 0xc3, 0x57, 0x27, 0x74, 0x44, 0xbc,
  0xa4, 0xd4, 0x87, 0xc5, 0x15, 0x08, 0x5d, 0xfa, 0x2a, 0x0d, 0x7b, 0x36,
  0xb3, 0x4f, 0xe7, 0x76, 0x7e, 0x31, 0x40, 0x8a, 0x7d, 0x15, 0xe3, 0xc9,
  0xa6, 0x4f, 0x52, 0x05, 0x7a, 0x07, 0x4a, 0x8c, 0xb7, 0xd5, 0x39, 0x48,
  0xa9, 0x0f, 0x52, 0xb6, 0x07, 0x30, 0xec, 0x71, 0x13, 0x92, 0x53, 0xea,
  0xba, 0xb1, 0x5d, 0xbf, 0x02, 0x38, 0xb7, 0xf3, 0x79, 0x5e, 0xb4, 0x03,
  0xa9, 0xeb, 0xca, 0xd4, 0xb9, 0x9d, 0x37, 0xb3, 0x03, 0xe0, 0xaf, 0x90,
  0xba, 0x0c, 0x60, 0x92, 0x3c, 0x20, 0xea, 0x55, 0xe7, 0x60, 0x4c, 0x3e,
  0x90, 0xe5, 0x31, 0xda, 0x3c, 0x2f, 0xae, 0xc5, 0x87, 0x18, 0x4f, 0xd5,
  0xcd, 0x15, 0xcc, 0x6d, 0xc9, 0x2d, 0xfe, 0xd9, 0x24, 0xa2, 0x96, 0xe9,
  0x60, 0x98, 0x07, 0x82, 0xa4, 0x7a, 0x3a, 0x99, 0x7f, 0xe2, 0x20, 0xa5,
  0x3e, 0x20, 0x6a, 0xa1, 0x00, 0x8c, 0x7c, 0xac, 0xab, 0xa2, 0x73, 0x90,
  0x35, 0x54, 0x94, 0x5d, 0x9a, 0x4c, 0xa0, 0xfa, 0x57, 0xeb, 0xfa, 0x6c,
  0xc9, 0x75, 0xab, 0x03, 0x38, 0xe7, 0x4a, 0x33, 0x7b, 0x1d, 0x94, 0xf5,
  0xb1, 0xc5, 0x3d, 0xf8, 0xbe, 0x0b, 0x88, 0xfd, 0x16, 0xf7, 0xa0, 0x13,
  0x3a, 0x4a, 0x7a, 0x9a, 0xca, 0x7a, 0x56, 0x05, 0x29, 0xf6, 0xd5, 0xdd,
  0x48, 0xbe, 0x1b, 0xc0, 0x17, 0xbc, 0x25, 0x7e, 0xfe, 0x06, 0x29, 0x4f,
  0xa7, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int Logout_gray_png_len = 2269;
