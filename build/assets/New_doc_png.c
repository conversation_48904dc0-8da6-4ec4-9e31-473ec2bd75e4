unsigned char New_doc_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x01, 0x6d, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xdd,
  0x94, 0x4d, 0x4e, 0x42, 0x31, 0x14, 0x85, 0x3f, 0x79, 0xe1, 0x4d, 0xc4,
  0x8d, 0x08, 0x9b, 0xf0, 0x77, 0x2a, 0x01, 0x85, 0x2d, 0x68, 0x24, 0x10,
  0xdd, 0x85, 0xe2, 0x12, 0x74, 0x46, 0x5c, 0x88, 0x84, 0x91, 0x71, 0x22,
  0x18, 0x9f, 0x41, 0x57, 0xf1, 0x1c, 0x88, 0x89, 0xe2, 0xa0, 0xb7, 0x79,
  0x77, 0xd0, 0xbe, 0x56, 0x13, 0x27, 0x9c, 0xa4, 0x69, 0x73, 0x7f, 0xce,
  0x6d, 0x4f, 0x7b, 0x0b, 0xab, 0x8e, 0x14, 0xe8, 0x02, 0xb7, 0x40, 0x06,
  0xbc, 0xcb, 0xc8, 0xc4, 0xd6, 0x91, 0x98, 0x3f, 0xa1, 0x0d, 0xbc, 0x01,
  0xcb, 0xc0, 0x78, 0x05, 0x5a, 0xbf, 0x21, 0x4e, 0x80, 0x2b, 0x45, 0xf0,
  0x08, 0x0c, 0x80, 0x3a, 0xb0, 0x2e, 0xa3, 0x2e, 0xb6, 0xa9, 0x8a, 0x1b,
  0x02, 0x95, 0x98, 0x02, 0x96, 0xfc, 0x03, 0x38, 0x09, 0x24, 0x25, 0xc0,
  0x29, 0xb0, 0x90, 0x9c, 0xcb, 0x10, 0x79, 0x5b, 0x91, 0x6f, 0xc5, 0xec,
  0x46, 0xb0, 0xad, 0x8a, 0x34, 0x7d, 0x41, 0x29, 0x85, 0xe6, 0xc7, 0x9e,
  0x98, 0x09, 0x30, 0xf6, 0xf8, 0x7a, 0x92, 0x3b, 0xc7, 0x73, 0xf1, 0x5d,
  0x0a, 0xcd, 0x7d, 0xb2, 0x58, 0xbd, 0x5d, 0x48, 0x80, 0x99, 0xf8, 0x8f,
  0xac, 0x51, 0x13, 0xd9, 0xa3, 0x5d, 0x03, 0xdf, 0x1e, 0x92, 0x32, 0x7c,
  0x01, 0x37, 0xb2, 0x3e, 0x70, 0x05, 0xbc, 0x48, 0xf5, 0xcd, 0x12, 0x92,
  0xb2, 0x13, 0x00, 0x34, 0xc4, 0x9f, 0xb9, 0x9c, 0xb9, 0x38, 0x6b, 0xca,
  0x36, 0x21, 0xdc, 0x07, 0xfa, 0x4e, 0x36, 0xc4, 0x96, 0x5b, 0x83, 0x4b,
  0xeb, 0x35, 0xb5, 0x8e, 0x91, 0x4a, 0x9f, 0xc8, 0xe6, 0x3a, 0xf3, 0xfe,
  0x45, 0x22, 0x7d, 0x82, 0x07, 0x99, 0xf7, 0x4a, 0x08, 0x42, 0xd8, 0x97,
  0xf9, 0xde, 0xe5, 0xec, 0x48, 0xf5, 0x29, 0xe6, 0xc9, 0xb9, 0x10, 0x7a,
  0xa6, 0x4f, 0xe2, 0x3f, 0x74, 0x05, 0xa4, 0x98, 0x8f, 0x6b, 0x89, 0x69,
  0x7f, 0x17, 0xc6, 0xc0, 0x9d, 0xc7, 0xd7, 0xa7, 0x68, 0xb4, 0xaa, 0x27,
  0x86, 0x96, 0x04, 0x2d, 0x80, 0x1d, 0x5f, 0x90, 0x03, 0xbb, 0xc0, 0xa7,
  0xe4, 0x3a, 0x7b, 0x40, 0x63, 0xa8, 0x8a, 0xf4, 0xf0, 0xcb, 0x85, 0xf8,
  0xfa, 0x8a, 0xfc, 0x22, 0x66, 0x37, 0x15, 0xcc, 0xaf, 0x68, 0xf5, 0x9e,
  0x01, 0x67, 0x98, 0x17, 0x52, 0x93, 0xd1, 0x00, 0xce, 0x29, 0x34, 0xb7,
  0xe4, 0x51, 0xdf, 0xb5, 0x45, 0x13, 0xa3, 0x67, 0xa8, 0xd1, 0xe6, 0x44,
  0xc8, 0xe2, 0x43, 0x15, 0xf3, 0x71, 0x8d, 0x80, 0x67, 0x4c, 0x87, 0xe6,
  0xb2, 0x1e, 0x61, 0x5e, 0x8b, 0xf7, 0x42, 0x57, 0x03, 0x3f, 0x77, 0x06,
  0x81, 0x36, 0xee, 0xcf, 0x03, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int New_doc_png_len = 438;
