unsigned char hr_physical_strain_general_Heart_Rate_Monitor_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x7f, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xcd, 0xd4, 0xbb, 0x6a, 0x95, 0x41, 0x10,
  0x07, 0xf0, 0xdf, 0x88, 0x06, 0xef, 0x88, 0x9d, 0x20, 0x04, 0x63, 0x11,
  0xa3, 0x69, 0xf3, 0x02, 0x22, 0x07, 0xb1, 0x15, 0x4b, 0xc1, 0xa7, 0x50,
  0xb1, 0x48, 0xa1, 0x01, 0x0d, 0xd8, 0x0b, 0xf6, 0x16, 0x82, 0x4f, 0x10,
  0xb0, 0xb1, 0x12, 0x2d, 0xd4, 0x22, 0xc6, 0x07, 0x10, 0x0b, 0x4b, 0xc5,
  0x0b, 0x39, 0xc2, 0x5a, 0x7c, 0xb3, 0x39, 0xdf, 0x59, 0xbf, 0x23, 0x01,
  0x53, 0x38, 0xb0, 0xcc, 0xee, 0xcc, 0xce, 0x7f, 0xe7, 0xb6, 0x13, 0xa5,
  0x14, 0x7b, 0x49, 0xfb, 0xf6, 0x14, 0x0d, 0xfb, 0x5b, 0x41, 0x44, 0xcc,
  0xe1, 0x12, 0xce, 0xe1, 0xc0, 0x0c, 0xbb, 0x31, 0xb6, 0xf0, 0xbc, 0x94,
  0xb2, 0x3d, 0xa5, 0x29, 0xa5, 0xec, 0x2c, 0x9c, 0xc5, 0x3b, 0x94, 0x5d,
  0xae, 0xb7, 0x58, 0xe8, 0x63, 0x44, 0xcd, 0x61, 0x7a, 0xf6, 0x1a, 0x4b,
  0x58, 0xc7, 0x06, 0xbe, 0xcf, 0xf0, 0xf0, 0x30, 0x46, 0xb8, 0x8d, 0x4d,
  0xac, 0x94, 0x52, 0xc6, 0x53, 0x1e, 0xe2, 0x4a, 0xbe, 0xba, 0xd6, 0x7f,
  0xf1, 0x6f, 0x0b, 0x6b, 0x69, 0x73, 0xb9, 0xca, 0xfa, 0x45, 0x59, 0x4c,
  0xbe, 0x31, 0xc3, 0x2b, 0x11, 0x31, 0x1f, 0x11, 0xcf, 0x22, 0x62, 0xbe,
  0xb9, 0xbb, 0x54, 0xef, 0xf4, 0x01, 0x0f, 0x25, 0xff, 0x32, 0x0b, 0x10,
  0xb7, 0x70, 0x15, 0x37, 0xf3, 0xfc, 0x35, 0xf9, 0xc1, 0x21, 0xc0, 0x5a,
  0xf1, 0x5f, 0x33, 0xbc, 0x3b, 0x81, 0xeb, 0x79, 0xbc, 0x11, 0x11, 0x27,
  0x75, 0xd5, 0xa6, 0xd7, 0x0d, 0xfd, 0xb6, 0x59, 0x49, 0xfe, 0x34, 0x22,
  0x7e, 0x34, 0x78, 0xeb, 0x38, 0x8d, 0xa3, 0xba, 0x30, 0x47, 0x78, 0xd9,
  0x03, 0xac, 0xb6, 0x7f, 0xf6, 0xe1, 0x00, 0x2d, 0xe2, 0x7e, 0xee, 0x3f,
  0xe3, 0x1a, 0x9e, 0xe0, 0x94, 0x49, 0x9a, 0x26, 0xd4, 0xab, 0xd8, 0xaa,
  0xae, 0x62, 0xcb, 0x4d, 0x25, 0xef, 0x99, 0xf4, 0xdd, 0xdd, 0x46, 0xb7,
  0x9c, 0xf2, 0xd5, 0xa1, 0x2a, 0xd7, 0xdc, 0xb5, 0x5e, 0x3f, 0xca, 0xd0,
  0xc6, 0x78, 0xdc, 0xe8, 0x6a, 0xee, 0x6a, 0xe8, 0x53, 0xc6, 0x35, 0x6f,
  0xc7, 0x9b, 0x08, 0x3e, 0x45, 0xc4, 0x9d, 0xdc, 0x7f, 0x6c, 0x00, 0x8f,
  0x25, 0xff, 0x39, 0x04, 0xf8, 0x21, 0xf9, 0x08, 0x2f, 0x1a, 0xd0, 0x87,
  0x86, 0x69, 0x94, 0x7c, 0xab, 0x0a, 0xda, 0xaf, 0xf7, 0x0a, 0x17, 0xf0,
  0xc0, 0xbf, 0x7e, 0xbd, 0x04, 0x5e, 0xd0, 0x7d, 0xf8, 0xdd, 0x0e, 0x87,
  0x37, 0x38, 0x33, 0x38, 0x1c, 0x76, 0x5c, 0xee, 0x3c, 0xbd, 0x88, 0xf3,
  0xba, 0xa4, 0xcf, 0xe1, 0x48, 0xaa, 0xbf, 0x61, 0x5b, 0x57, 0x84, 0xf7,
  0xba, 0xf1, 0x35, 0x9e, 0xb2, 0xff, 0xef, 0x27, 0xf6, 0x6f, 0xa6, 0xa3,
  0xe5, 0x4b, 0xb0, 0x85, 0xc3, 0x07, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_physical_strain_general_Heart_Rate_Monitor_20x20_png_len = 498;
