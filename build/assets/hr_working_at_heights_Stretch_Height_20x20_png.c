unsigned char hr_working_at_heights_Stretch_Height_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x5c, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0x94, 0xaf, 0x52, 0x82, 0x51, 0x10,
  0xc5, 0x7f, 0x47, 0xd4, 0x80, 0x81, 0xa8, 0x26, 0x0d, 0x04, 0xab, 0x06,
  0x87, 0xa8, 0x06, 0xba, 0x45, 0x83, 0xbe, 0x80, 0x46, 0x83, 0x3c, 0x05,
  0xd9, 0x17, 0xd0, 0x80, 0xc5, 0x60, 0x23, 0x88, 0x8d, 0x81, 0x80, 0xd5,
  0x60, 0xd0, 0x24, 0x46, 0x82, 0x06, 0x81, 0x59, 0xcb, 0x7e, 0xe3, 0xe5,
  0xfa, 0xf1, 0x21, 0xe2, 0x99, 0xd9, 0xd9, 0xb9, 0x7b, 0x77, 0xcf, 0x9e,
  0xfb, 0x67, 0x56, 0x66, 0xc6, 0x7f, 0x62, 0x3e, 0x2d, 0x28, 0x69, 0x15,
  0xd8, 0x04, 0x96, 0xc6, 0xd4, 0xbd, 0x03, 0x0f, 0x66, 0xf6, 0xfa, 0x63,
  0xc7, 0xcc, 0x46, 0x0c, 0xa8, 0x00, 0x7d, 0xc0, 0x26, 0x58, 0x1f, 0xa8,
  0xc4, 0xf5, 0x0a, 0x8f, 0x2c, 0x69, 0x07, 0x68, 0x00, 0x1d, 0xe0, 0x02,
  0xe8, 0x8d, 0x51, 0x58, 0x00, 0x4e, 0x80, 0x2d, 0x60, 0xd7, 0xcc, 0xee,
  0x53, 0x15, 0x02, 0x55, 0xef, 0x5e, 0x8c, 0x3b, 0xa7, 0x9c, 0xa4, 0xe8,
  0xb9, 0xd5, 0x30, 0x3e, 0x17, 0x75, 0x5e, 0x71, 0xff, 0x1c, 0xa8, 0x2e,
  0x48, 0x6a, 0x48, 0xba, 0x93, 0x54, 0x08, 0x72, 0x5f, 0xdc, 0x2f, 0x87,
  0x04, 0xf1, 0xa3, 0x2c, 0xb8, 0x1f, 0x26, 0x64, 0x40, 0x1d, 0xd8, 0xf6,
  0x78, 0x5d, 0x52, 0xd9, 0xcc, 0x7a, 0xc0, 0xc0, 0x63, 0x8b, 0x21, 0x41,
  0xac, 0x30, 0xb9, 0x86, 0xe4, 0x62, 0x0f, 0x9d, 0x6c, 0xe8, 0xb6, 0x0d,
  0x1c, 0x44, 0x39, 0x23, 0x48, 0x25, 0x0c, 0x70, 0x09, 0x94, 0x80, 0x47,
  0xb7, 0x12, 0x70, 0x95, 0x55, 0x90, 0xfa, 0x0f, 0x03, 0xa5, 0x1f, 0x40,
  0x4b, 0x52, 0xb2, 0x6e, 0x4d, 0x10, 0x30, 0x51, 0xe1, 0xd4, 0xc8, 0x24,
  0x94, 0x94, 0x97, 0x54, 0x0a, 0xd6, 0x25, 0x49, 0xf9, 0x3f, 0x13, 0x02,
  0xc7, 0x40, 0x13, 0xd8, 0x70, 0x6b, 0x02, 0x47, 0x53, 0x13, 0x2a, 0xb9,
  0x34, 0xa8, 0x01, 0x6d, 0x20, 0xe7, 0xd6, 0x06, 0xae, 0xa3, 0x9c, 0x11,
  0xc4, 0x8f, 0xd2, 0x77, 0x9f, 0x03, 0x06, 0x66, 0xd6, 0x93, 0x54, 0x06,
  0x6e, 0x3c, 0xbe, 0xef, 0x7f, 0x30, 0xac, 0xfd, 0xcc, 0x22, 0xec, 0xba,
  0x5f, 0x07, 0x9e, 0x00, 0x9c, 0x60, 0x2f, 0x45, 0xcc, 0x9a, 0xfb, 0xb7,
  0x2c, 0xc2, 0x5b, 0xe0, 0x0c, 0xa8, 0x49, 0x9a, 0x34, 0x1c, 0x4e, 0x83,
  0x9a, 0x6f, 0xcc, 0x38, 0xbe, 0xce, 0x33, 0xc7, 0x57, 0x82, 0x5f, 0x0e,
  0xd8, 0x8e, 0x99, 0x75, 0xe3, 0x8d, 0x54, 0xc2, 0x59, 0xf0, 0x05, 0x8c,
  0x5a, 0xd3, 0x3b, 0xc9, 0xd6, 0x0e, 0x33, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_working_at_heights_Stretch_Height_20x20_png_len = 463;
