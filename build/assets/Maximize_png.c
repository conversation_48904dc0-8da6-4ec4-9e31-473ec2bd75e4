unsigned char Maximize_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0xe6, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xd5, 0x96, 0x3d, 0x4f, 0x14, 0x51, 0x14,
  0x86, 0x9f, 0xf7, 0xba, 0x20, 0xc4, 0x64, 0x31, 0x36, 0x44, 0x8d, 0x8d,
  0x68, 0xb4, 0x25, 0x58, 0xa9, 0x85, 0x05, 0x85, 0x1d, 0xd4, 0xfe, 0x00,
  0x5a, 0x1b, 0x5b, 0xff, 0x80, 0x95, 0x7f, 0xc0, 0xc2, 0xc6, 0x90, 0x18,
  0x2d, 0xac, 0x2c, 0x2c, 0xa8, 0x2c, 0x21, 0x96, 0x6a, 0x36, 0x34, 0x68,
  0x42, 0xe1, 0x17, 0xf2, 0x21, 0xbb, 0xb0, 0xaf, 0xc5, 0xdc, 0x59, 0xef,
  0xce, 0xce, 0xcc, 0xee, 0x26, 0x50, 0x78, 0x92, 0x9b, 0x99, 0xe4, 0x7c,
  0xbc, 0xe7, 0x9c, 0xf7, 0x9e, 0x33, 0x23, 0xdb, 0x9c, 0xa6, 0x84, 0x53,
  0x8d, 0x0e, 0x34, 0x46, 0x31, 0x92, 0x34, 0x0d, 0x4c, 0x01, 0x4d, 0xe0,
  0x4c, 0xa2, 0x6a, 0x03, 0x7b, 0xc0, 0xae, 0xed, 0x4e, 0xa9, 0xb3, 0xed,
  0xca, 0x03, 0x2c, 0x02, 0x1b, 0x80, 0x87, 0x9c, 0x0e, 0xf0, 0x16, 0xb8,
  0x5e, 0x8c, 0xa1, 0x2a, 0x0e, 0x24, 0x2d, 0x01, 0xaf, 0x81, 0x3f, 0xc0,
  0x1a, 0xb0, 0x55, 0x55, 0x20, 0x70, 0x13, 0xb8, 0x03, 0xfc, 0x06, 0x16,
  0x6c, 0xb7, 0x7a, 0xca, 0x32, 0x00, 0x49, 0x0d, 0xe0, 0x4b, 0x6c, 0xcb,
  0x0a, 0xf0, 0x2d, 0x51, 0x77, 0x80, 0x5d, 0xe0, 0x10, 0xf8, 0x64, 0xbb,
  0x1d, 0x7d, 0x96, 0x63, 0x42, 0x6f, 0x6c, 0x2f, 0x57, 0xb6, 0x08, 0xb8,
  0x25, 0xe9, 0xa3, 0x24, 0x8f, 0x70, 0x7e, 0x00, 0x0f, 0x93, 0x44, 0xd7,
  0x81, 0x9d, 0x34, 0x5e, 0x1f, 0xc9, 0x92, 0x6e, 0x48, 0x5a, 0x03, 0x26,
  0x24, 0xbd, 0xe8, 0x76, 0xbb, 0x9f, 0x81, 0x83, 0xd8, 0x86, 0xbe, 0x52,
  0x43, 0x08, 0x57, 0x6c, 0x2f, 0x49, 0x7a, 0x6a, 0xfb, 0x2c, 0xf0, 0x04,
  0xd8, 0x04, 0xe6, 0x25, 0x4d, 0xe6, 0x95, 0xf5, 0x13, 0x22, 0xbd, 0x92,
  0xd4, 0x05, 0x16, 0xeb, 0xc8, 0x4f, 0xaa, 0x9d, 0x91, 0xd4, 0x92, 0x74,
  0x00, 0x9c, 0x07, 0x56, 0x63, 0x22, 0xe7, 0x72, 0x9b, 0xe2, 0x1c, 0xdc,
  0x8e, 0x7d, 0x7d, 0x57, 0x42, 0xe6, 0x80, 0xd8, 0xfe, 0x65, 0xfb, 0x79,
  0xe4, 0x6a, 0x1e, 0x38, 0x8a, 0xaa, 0x5e, 0x67, 0x8a, 0x00, 0x33, 0x64,
  0xe4, 0x8e, 0x23, 0x5f, 0x13, 0xdf, 0x01, 0x29, 0x02, 0x4c, 0x90, 0xdd,
  0x92, 0x71, 0xa4, 0x9d, 0xf8, 0x0e, 0x05, 0x08, 0xc0, 0xf1, 0x98, 0x00,
  0xb9, 0x7d, 0x83, 0x7f, 0x53, 0xde, 0x8b, 0x71, 0xd2, 0xbb, 0x68, 0x32,
  0x3e, 0xf3, 0xaa, 0x4e, 0x1c, 0xe0, 0x32, 0xb0, 0x4f, 0xd2, 0xe6, 0x22,
  0xc0, 0x31, 0x23, 0x2e, 0xc0, 0x44, 0x72, 0xfb, 0x39, 0x60, 0x01, 0x78,
  0xef, 0x64, 0x3d, 0x14, 0x01, 0xda, 0x54, 0x90, 0x55, 0x23, 0xcd, 0xf8,
  0x7c, 0x4c, 0x76, 0x4d, 0x1f, 0xa5, 0xca, 0x22, 0xc0, 0x77, 0xe0, 0xea,
  0x38, 0xd1, 0x43, 0x08, 0xd7, 0xe2, 0xeb, 0x16, 0x70, 0xcf, 0xf6, 0x87,
  0x3e, 0x83, 0x74, 0x32, 0x43, 0x08, 0xcf, 0x24, 0x19, 0x58, 0x19, 0x71,
  0x92, 0xe7, 0x24, 0x6d, 0x4b, 0xfa, 0x09, 0x4c, 0x95, 0xd9, 0xf4, 0x6d,
  0x53, 0x49, 0xb3, 0x92, 0x36, 0x80, 0x8b, 0xc0, 0xba, 0xa4, 0x16, 0xb0,
  0xc3, 0xe0, 0xd5, 0x6d, 0xda, 0xbe, 0x00, 0xdc, 0x05, 0xa6, 0x6d, 0x3f,
  0xb0, 0xbd, 0x5a, 0x5a, 0x62, 0x49, 0x56, 0x97, 0x24, 0xbd, 0x94, 0xb4,
  0x37, 0x64, 0x93, 0x1e, 0xc5, 0x64, 0xee, 0xd7, 0x55, 0x59, 0xf7, 0xc1,
  0x11, 0xd9, 0x02, 0xab, 0x92, 0x7d, 0xdb, 0x87, 0x35, 0xfa, 0x2c, 0xce,
  0x7f, 0xff, 0x57, 0xf1, 0x17, 0x69, 0xf9, 0x62, 0xfe, 0x53, 0x47, 0x65,
  0xc5, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int Maximize_png_len = 601;
