unsigned char User_Group_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x00, 0xec, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xed,
  0x93, 0x3b, 0x0e, 0x82, 0x40, 0x14, 0x45, 0x4f, 0x6c, 0x29, 0xb5, 0x13,
  0x6a, 0x0b, 0x57, 0xa0, 0xae, 0xd1, 0xb8, 0x0e, 0x0d, 0x09, 0x8d, 0xc1,
  0x16, 0xa5, 0xf0, 0xb3, 0x16, 0x35, 0x81, 0x10, 0x2b, 0x13, 0x2c, 0x78,
  0x04, 0x19, 0x67, 0x64, 0xc6, 0x4a, 0x13, 0x6f, 0x72, 0x9b, 0xf3, 0x7e,
  0xc3, 0x1b, 0x06, 0xfe, 0xfa, 0x66, 0x05, 0x40, 0x08, 0xe4, 0x40, 0x6a,
  0xe0, 0x39, 0x10, 0x01, 0xa3, 0x4f, 0x9a, 0x5f, 0x80, 0x52, 0xbc, 0x33,
  0xf0, 0xda, 0x57, 0x89, 0x59, 0x2b, 0x94, 0xc2, 0x35, 0x30, 0x7c, 0xc3,
  0x7d, 0x20, 0x16, 0xb6, 0x72, 0x19, 0x90, 0x4b, 0x91, 0x6f, 0xc1, 0x03,
  0x61, 0x99, 0xae, 0x51, 0xcf, 0x65, 0x6a, 0x87, 0x4a, 0x97, 0xe4, 0x48,
  0x0a, 0x62, 0xda, 0xa7, 0x55, 0x79, 0x00, 0x6c, 0x84, 0x2d, 0x5d, 0x06,
  0x8c, 0xa8, 0x2e, 0xae, 0xbe, 0xc4, 0xd4, 0xc0, 0x6b, 0x9f, 0x79, 0x5d,
  0x67, 0xa7, 0x02, 0xaa, 0x8b, 0xcb, 0x68, 0xfe, 0x22, 0x95, 0x67, 0x72,
  0x72, 0xe7, 0xe6, 0xbf, 0xab, 0x19, 0xb0, 0x05, 0x0a, 0xda, 0xaf, 0xf7,
  0x99, 0xab, 0xfb, 0xaf, 0x5d, 0x00, 0x09, 0x30, 0x7d, 0xd7, 0xfc, 0xce,
  0xeb, 0xeb, 0x55, 0x79, 0x97, 0xef, 0xc0, 0x44, 0x37, 0x20, 0x91, 0x84,
  0x39, 0xe0, 0x59, 0x70, 0x55, 0x1e, 0xb0, 0x90, 0xdc, 0xbd, 0x2e, 0x21,
  0x93, 0x60, 0xdf, 0x92, 0xeb, 0x34, 0x90, 0xdc, 0x9b, 0x2e, 0x78, 0xa2,
  0xfd, 0xa9, 0xa9, 0x81, 0xdb, 0xf8, 0xa0, 0x1b, 0x30, 0x06, 0x8e, 0x34,
  0xfb, 0xde, 0x19, 0x78, 0xd7, 0xfe, 0x0f, 0x52, 0xf3, 0xd7, 0x97, 0xe8,
  0x01, 0xa5, 0x3a, 0x81, 0x60, 0x09, 0x53, 0x2c, 0xdb, 0x00, 0x00, 0x00,
  0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int User_Group_png_len = 309;
