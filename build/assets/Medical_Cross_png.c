unsigned char Medical_Cross_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x01, 0x4b, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xed, 0x95, 0xb1,
  0x4e, 0xc3, 0x30, 0x10, 0x86, 0xef, 0x5c, 0x06, 0x82, 0x04, 0x4a, 0xc3,
  0x82, 0xca, 0x02, 0x1b, 0x8f, 0xc0, 0xe3, 0xf0, 0x06, 0x84, 0xad, 0x5b,
  0xc3, 0xc8, 0x84, 0x78, 0x03, 0x78, 0x93, 0xbe, 0x09, 0x4c, 0x2d, 0x43,
  0x1b, 0x2c, 0x55, 0x22, 0x13, 0x31, 0x77, 0x71, 0x2b, 0x6c, 0xe3, 0xd4,
  0x4e, 0xab, 0x76, 0xea, 0x2f, 0xb5, 0xb9, 0x5c, 0xf2, 0xdf, 0x39, 0xf7,
  0x39, 0x0a, 0xc0, 0x41, 0x01, 0x61, 0xcc, 0x4d, 0x59, 0x36, 0x18, 0x01,
  0x62, 0x0e, 0xa0, 0xd2, 0xa5, 0x4d, 0x02, 0xd6, 0xaf, 0xe5, 0x6c, 0xfa,
  0x10, 0xf2, 0xf6, 0xc2, 0xc5, 0x2f, 0x9f, 0x69, 0x19, 0x43, 0x0a, 0x8f,
  0x8d, 0x34, 0xc5, 0x78, 0x9b, 0x24, 0xa7, 0x58, 0x55, 0x8b, 0xf1, 0x3a,
  0xbf, 0x80, 0xb0, 0xee, 0xf8, 0xaf, 0xfe, 0xa9, 0xaf, 0xcb, 0xf9, 0x04,
  0xf9, 0xc7, 0x71, 0x73, 0x05, 0xe1, 0x3e, 0x64, 0x0e, 0x37, 0x40, 0x3d,
  0x16, 0x29, 0x3f, 0x3f, 0x56, 0x29, 0x23, 0x4e, 0x43, 0xf6, 0x98, 0x27,
  0xd8, 0x4a, 0x47, 0x6e, 0xe2, 0x3f, 0xd0, 0xf5, 0xca, 0xce, 0x07, 0x4a,
  0x47, 0x7e, 0xf0, 0xc2, 0x2e, 0xde, 0x00, 0x2d, 0x62, 0x8b, 0xdb, 0x22,
  0x8f, 0xc2, 0x9c, 0x16, 0x58, 0x98, 0x59, 0x74, 0x1a, 0x7c, 0xf1, 0xcc,
  0x19, 0xa2, 0x39, 0xf3, 0x18, 0xa5, 0xe9, 0xc5, 0x95, 0xe8, 0x89, 0x77,
  0x0a, 0x25, 0x6d, 0x84, 0xfe, 0x2a, 0x6f, 0x33, 0xf0, 0x00, 0x8d, 0x55,
  0x1b, 0xf8, 0x9d, 0x43, 0xb6, 0x1b, 0x28, 0x02, 0x05, 0xfa, 0x71, 0xa1,
  0xa3, 0x0c, 0x8f, 0x34, 0xf3, 0xf6, 0x2e, 0x12, 0xea, 0x05, 0x14, 0x8c,
  0x78, 0x96, 0xb4, 0x3b, 0xac, 0x4b, 0xfc, 0x82, 0x99, 0xe7, 0x7f, 0xbb,
  0xc7, 0x95, 0x7a, 0xb3, 0x4a, 0x5a, 0x45, 0x66, 0x93, 0x82, 0x40, 0x70,
  0x13, 0x09, 0x5d, 0xc5, 0x1e, 0x84, 0xc7, 0x72, 0x3e, 0xcd, 0x3b, 0xf9,
  0x78, 0xa5, 0xbe, 0xd5, 0xb6, 0xe5, 0x5d, 0xed, 0x19, 0xb2, 0x4f, 0x1e,
  0xf0, 0x6d, 0x40, 0x37, 0x6b, 0xc0, 0xe0, 0xf9, 0xa0, 0xc1, 0x37, 0x63,
  0x59, 0xbe, 0x50, 0xe0, 0x02, 0xf5, 0x29, 0xf8, 0x3d, 0xa8, 0xbe, 0x17,
  0xe3, 0xe4, 0xe4, 0xac, 0x4f, 0x10, 0x6f, 0x08, 0xa2, 0xfe, 0x26, 0x30,
  0x50, 0x01, 0x4f, 0x04, 0x74, 0x08, 0x07, 0x6d, 0xab, 0x5f, 0x21, 0x81,
  0x7b, 0x3a, 0x92, 0x00, 0x63, 0x86, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Medical_Cross_png_len = 438;
