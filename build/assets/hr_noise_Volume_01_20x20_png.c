unsigned char hr_noise_Volume_01_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x4e, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xe5, 0xd4, 0xbf, 0x4b, 0xd5, 0x51, 0x18,
  0xc7, 0xf1, 0xd7, 0x73, 0xf5, 0x0e, 0xa2, 0xee, 0xd1, 0xe0, 0x1c, 0x57,
  0xb4, 0x9a, 0xa3, 0xbf, 0x40, 0x68, 0x29, 0xee, 0x18, 0xa4, 0x48, 0x77,
  0x12, 0x71, 0xce, 0xc9, 0xa6, 0xa6, 0xc0, 0xad, 0x40, 0x43, 0xe8, 0x3f,
  0xa8, 0x21, 0x08, 0x69, 0xc9, 0x3f, 0x20, 0x0c, 0x5c, 0x04, 0x75, 0x34,
  0xfd, 0x13, 0xea, 0x38, 0xf8, 0xa0, 0xc7, 0xeb, 0xf7, 0xa2, 0x92, 0xb4,
  0xf4, 0x85, 0xc3, 0xe1, 0xf9, 0xf5, 0xe6, 0x39, 0xdf, 0xf3, 0x79, 0x4e,
  0x94, 0x52, 0xdc, 0xe6, 0xd7, 0xba, 0x55, 0xda, 0x3f, 0x05, 0x46, 0xc4,
  0x78, 0x44, 0x2c, 0x47, 0xc4, 0x46, 0x44, 0x74, 0x2b, 0x7f, 0x27, 0x22,
  0x56, 0x23, 0xe2, 0x71, 0x63, 0x61, 0x29, 0xe5, 0xd2, 0x42, 0x1b, 0xdf,
  0x50, 0xaa, 0xf5, 0x32, 0x63, 0x4f, 0xf0, 0x3b, 0x7d, 0x1f, 0x31, 0x7a,
  0xa1, 0x76, 0x00, 0xf0, 0x4d, 0x16, 0xac, 0xe2, 0x2e, 0x8e, 0xb1, 0x55,
  0xc5, 0xa7, 0xf0, 0x29, 0x73, 0xbe, 0x62, 0xb8, 0x11, 0x88, 0x07, 0x58,
  0xc2, 0x1f, 0x6c, 0xa1, 0x9d, 0xfe, 0x6d, 0x6c, 0xf7, 0xe5, 0xb6, 0xb0,
  0x9e, 0xd0, 0xc5, 0x4b, 0x40, 0xbc, 0xaf, 0x8e, 0x77, 0x84, 0x89, 0x2a,
  0x76, 0x06, 0xc4, 0x1d, 0xbc, 0xc0, 0x10, 0xc6, 0x70, 0x88, 0x03, 0xb4,
  0xce, 0x80, 0x78, 0x98, 0xa0, 0x4d, 0x74, 0x31, 0xdd, 0xd7, 0x4d, 0x0d,
  0x9c, 0xcb, 0xdc, 0x95, 0xb4, 0xdf, 0xa6, 0x3d, 0x59, 0x03, 0x9f, 0xa5,
  0xb3, 0x3b, 0xe0, 0x9f, 0xd6, 0xc0, 0x36, 0x7e, 0xe2, 0x28, 0xed, 0x5e,
  0xd6, 0xce, 0x94, 0x52, 0xfe, 0x4a, 0x87, 0xfd, 0x23, 0x56, 0x60, 0x38,
  0x8d, 0xdd, 0xdc, 0x7b, 0x11, 0x01, 0x3b, 0xa5, 0x94, 0x1f, 0x03, 0x40,
  0xcf, 0xd1, 0xc1, 0xeb, 0xb4, 0xef, 0xe5, 0xbe, 0x77, 0x8a, 0x3d, 0x3f,
  0xd6, 0x3b, 0xd7, 0xbf, 0x94, 0x59, 0xe7, 0x97, 0xf2, 0x0b, 0xfb, 0x88,
  0x26, 0xd9, 0xdc, 0x77, 0x33, 0xd9, 0x7c, 0xc8, 0x06, 0x16, 0x6e, 0x22,
  0xec, 0x09, 0xa7, 0xc2, 0xfe, 0xde, 0x27, 0xec, 0xcf, 0x99, 0xf3, 0xc5,
  0x20, 0x61, 0x5f, 0x31, 0x7a, 0xf3, 0x0d, 0xa3, 0xb7, 0x81, 0x91, 0x2b,
  0x47, 0x2f, 0x0b, 0xc7, 0xf0, 0x0a, 0x6b, 0x78, 0x5a, 0xf9, 0x3b, 0xd9,
  0xf9, 0xa3, 0xa6, 0xba, 0xf8, 0xff, 0x1e, 0xd8, 0x13, 0x80, 0xfa, 0x76,
  0x5b, 0xdd, 0x5c, 0x20, 0x7d, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e,
  0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_noise_Volume_01_20x20_png_len = 449;
