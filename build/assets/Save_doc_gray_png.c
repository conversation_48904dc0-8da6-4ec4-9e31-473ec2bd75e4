unsigned char Save_doc_gray_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x02, 0x16, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xbd, 0x95, 0xc1, 0x6b, 0x13, 0x51, 0x10,
  0xc6, 0x7f, 0x6f, 0x92, 0x9a, 0x43, 0x40, 0x31, 0xd8, 0x82, 0xe8, 0x21,
  0xa5, 0x20, 0x0a, 0x7a, 0x4a, 0xbd, 0x59, 0x11, 0x7a, 0xd3, 0xa3, 0xc4,
  0x7f, 0xc3, 0x50, 0x92, 0x90, 0x93, 0x4b, 0x2e, 0x6d, 0xd3, 0x5d, 0x4a,
  0xb1, 0x7f, 0x82, 0x27, 0x49, 0x4e, 0x22, 0xf5, 0xe2, 0xa5, 0xea, 0xb1,
  0xb9, 0x15, 0x7a, 0x52, 0x72, 0x51, 0xac, 0x62, 0x40, 0x24, 0x2c, 0x36,
  0xee, 0x3c, 0x0f, 0x26, 0x31, 0xdd, 0xec, 0xb6, 0x8b, 0xa4, 0x7e, 0xb0,
  0x87, 0x7d, 0x33, 0xf3, 0x7d, 0x6f, 0xe6, 0xbd, 0x79, 0x63, 0xac, 0xb5,
  0x9c, 0x25, 0xe4, 0x4c, 0xd9, 0x81, 0x74, 0xd4, 0xa2, 0xe7, 0x79, 0xf3,
  0x41, 0x20, 0xae, 0x31, 0xdc, 0x01, 0x2e, 0x01, 0xa9, 0x90, 0xcb, 0x4f,
  0x30, 0x5f, 0x41, 0x9f, 0x8b, 0x58, 0xa7, 0x5c, 0x2e, 0xf7, 0xe2, 0x04,
  0x4c, 0xb8, 0x44, 0x9e, 0xe7, 0xcd, 0xab, 0x4a, 0x1b, 0xb8, 0x98, 0x64,
  0x87, 0xc6, 0xd0, 0x36, 0x46, 0xef, 0x97, 0xcb, 0xe5, 0x2f, 0x89, 0x04,
  0x1a, 0x8d, 0xcd, 0x96, 0x31, 0x3c, 0x04, 0x5e, 0x1a, 0x13, 0x94, 0xf2,
  0xf9, 0x7c, 0xa7, 0x58, 0x2c, 0x06, 0xe3, 0x3e, 0x1b, 0x1b, 0x9b, 0xc3,
  0xa0, 0x03, 0xe0, 0x06, 0xb0, 0x2f, 0xa2, 0xcb, 0x51, 0x22, 0x13, 0x67,
  0x60, 0x0c, 0x77, 0x01, 0x54, 0xe5, 0x71, 0xa5, 0x52, 0x79, 0x1f, 0x26,
  0x3f, 0x16, 0x2c, 0x7a, 0x0f, 0xd8, 0x07, 0x6e, 0x5a, 0x2b, 0x3b, 0x9e,
  0xe7, 0x65, 0x4f, 0x15, 0x00, 0xce, 0x03, 0x64, 0xb3, 0xa9, 0x8f, 0x71,
  0xc4, 0xc0, 0x27, 0x80, 0x20, 0x30, 0xd7, 0x44, 0x74, 0x19, 0x38, 0xb0,
  0x96, 0x82, 0xaa, 0xa9, 0x27, 0x11, 0x98, 0x01, 0xe8, 0x76, 0xbb, 0xfd,
  0x13, 0x04, 0x9e, 0x01, 0x18, 0x63, 0xde, 0xaa, 0xca, 0x21, 0x7f, 0xca,
  0x04, 0x98, 0x47, 0x49, 0x04, 0x04, 0xc0, 0x71, 0x1c, 0x8d, 0x63, 0xf7,
  0xfd, 0x1f, 0x4f, 0x80, 0xc6, 0x30, 0x93, 0x31, 0xcc, 0x85, 0x7d, 0x23,
  0xaf, 0xe9, 0x69, 0x70, 0x1c, 0xe7, 0x08, 0xa8, 0x0d, 0x3e, 0x60, 0x74,
  0xf0, 0x99, 0x7f, 0x16, 0xf0, 0x3c, 0x6f, 0xce, 0x5a, 0xd9, 0xb1, 0x16,
  0x2b, 0xa2, 0x0f, 0xe2, 0xae, 0x65, 0x18, 0x89, 0x3a, 0x79, 0x6d, 0x6d,
  0x7b, 0x56, 0x55, 0x5e, 0x5b, 0x4b, 0x01, 0x58, 0xb4, 0x56, 0x76, 0x57,
  0x57, 0xb7, 0x2e, 0x4f, 0x4d, 0x20, 0x9d, 0xee, 0xbf, 0x02, 0x6e, 0x0d,
  0xff, 0xad, 0xe5, 0x7a, 0x3a, 0xad, 0x2f, 0xa6, 0x26, 0x60, 0x2d, 0x3e,
  0xb0, 0x37, 0xb6, 0xb4, 0x07, 0xf8, 0x89, 0x36, 0x97, 0xc4, 0xa9, 0x5a,
  0x5d, 0x59, 0x82, 0xbf, 0x1d, 0x5c, 0xad, 0xae, 0xdc, 0x4e, 0x12, 0x07,
  0xff, 0xe1, 0x35, 0x8d, 0x12, 0x50, 0x80, 0x7a, 0xbd, 0x9e, 0x58, 0x7c,
  0xcc, 0x77, 0xa2, 0x77, 0xa2, 0x4a, 0xf4, 0x0b, 0x38, 0x37, 0xb0, 0x1d,
  0x85, 0x6c, 0xef, 0x80, 0x89, 0x09, 0x95, 0xcb, 0xe5, 0x66, 0x7c, 0xbf,
  0x0f, 0x30, 0xd1, 0xfd, 0x51, 0x02, 0xdf, 0x81, 0xd9, 0x4c, 0xe6, 0xc2,
  0x55, 0xe0, 0xc3, 0xb8, 0x61, 0x78, 0x16, 0x61, 0xf4, 0x7a, 0xc1, 0x15,
  0x91, 0x51, 0xec, 0x31, 0x44, 0x94, 0xc1, 0xec, 0x02, 0x88, 0xd8, 0xa7,
  0xae, 0xeb, 0x2e, 0x34, 0x9b, 0xcd, 0xf0, 0xb0, 0x19, 0xa1, 0xd9, 0x6c,
  0xa6, 0x5c, 0xd7, 0x5d, 0x10, 0xd1, 0xed, 0x41, 0xec, 0x9b, 0x09, 0xb6,
  0xf0, 0x3c, 0x58, 0x5f, 0xdf, 0xca, 0x8b, 0x68, 0x1b, 0xc8, 0xc5, 0x11,
  0x47, 0xc3, 0x7e, 0x53, 0x4d, 0x2d, 0xd6, 0x6a, 0xa5, 0xce, 0x89, 0x19,
  0xd4, 0x6a, 0xa5, 0x8e, 0xaa, 0x14, 0xc0, 0xb4, 0x80, 0x43, 0x20, 0x76,
  0x1e, 0x0c, 0x6c, 0x9f, 0xc1, 0xb4, 0xa2, 0xc8, 0x23, 0x33, 0x98, 0x36,
  0xce, 0xbc, 0x0f, 0x7e, 0x03, 0xbe, 0x95, 0xc2, 0xdf, 0x00, 0x96, 0x2c,
  0x7c, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int Save_doc_gray_png_len = 649;
