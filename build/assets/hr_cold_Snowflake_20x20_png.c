unsigned char hr_cold_Snowflake_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x73, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0xd5, 0xb1, 0x4e, 0x5b, 0x31, 0x14,
  0x06, 0xe0, 0xcf, 0x25, 0x29, 0x79, 0x04, 0x86, 0x4a, 0x50, 0x89, 0x2e,
  0x44, 0x02, 0x1e, 0x00, 0xa9, 0x63, 0xbb, 0xb1, 0x76, 0x4e, 0x5f, 0xa0,
  0x1d, 0x82, 0x3a, 0x64, 0xad, 0x2a, 0xb1, 0xc1, 0x0c, 0x5b, 0x37, 0x56,
  0x76, 0xd8, 0xbb, 0x14, 0x06, 0x16, 0x50, 0x81, 0xaa, 0x6c, 0xad, 0x04,
  0x0b, 0x0b, 0x48, 0x66, 0xc8, 0xb9, 0xc8, 0xdc, 0x90, 0x10, 0x28, 0x96,
  0x8e, 0x7c, 0xfd, 0xdb, 0xe7, 0xf7, 0x39, 0xfe, 0x8f, 0x7d, 0x53, 0xce,
  0xd9, 0x73, 0xb6, 0x17, 0xe3, 0x2c, 0x4a, 0x29, 0xbd, 0x4e, 0x29, 0xcd,
  0x8c, 0xc5, 0x98, 0x73, 0x1e, 0x6a, 0x98, 0x42, 0x07, 0x3f, 0xc2, 0x3a,
  0x98, 0x1a, 0xe9, 0xf3, 0x00, 0x61, 0x07, 0xb9, 0x66, 0x9d, 0x47, 0x11,
  0xe2, 0x3d, 0x36, 0xe2, 0x38, 0x26, 0xf0, 0xbd, 0x20, 0xdb, 0x42, 0x23,
  0xe6, 0x36, 0xf0, 0x6e, 0x28, 0x21, 0x96, 0xf1, 0x07, 0x7b, 0xe1, 0xbc,
  0x18, 0x78, 0x03, 0xc7, 0x61, 0x8d, 0xc0, 0x16, 0x63, 0xcd, 0x1e, 0xce,
  0xb0, 0x5c, 0xf1, 0x94, 0xa2, 0x1c, 0xe1, 0x1a, 0xf3, 0x31, 0x7e, 0x1b,
  0x7d, 0xb3, 0x88, 0xb0, 0x59, 0x9b, 0x9b, 0xc7, 0x15, 0x0e, 0x07, 0x44,
  0xc1, 0x02, 0xce, 0x0b, 0xe7, 0x2b, 0xac, 0xe3, 0xb4, 0xc0, 0x4e, 0xb0,
  0x16, 0x73, 0x15, 0x76, 0x8e, 0x85, 0x8a, 0xa7, 0x51, 0x44, 0xf8, 0x0f,
  0xdb, 0xb1, 0xdb, 0x01, 0x56, 0xf0, 0x01, 0x93, 0xe8, 0x22, 0xa1, 0x17,
  0xd8, 0x4f, 0xac, 0x62, 0x0e, 0xb3, 0xf8, 0x3b, 0x10, 0xe1, 0x3d, 0xe2,
  0x2c, 0x45, 0x04, 0xdd, 0x02, 0x5b, 0x09, 0x6c, 0x69, 0x98, 0xdf, 0x58,
  0x85, 0xfd, 0x98, 0x96, 0xaa, 0xab, 0x97, 0x52, 0x7a, 0x85, 0x6f, 0xb5,
  0x94, 0xa7, 0xd1, 0xc2, 0xd7, 0x58, 0xdf, 0xc3, 0x25, 0x7e, 0x47, 0xca,
  0xed, 0x48, 0xf9, 0x4b, 0xce, 0xf9, 0xec, 0x4e, 0xca, 0xfa, 0xa2, 0x5c,
  0xb8, 0x2b, 0xca, 0x9a, 0xbe, 0x10, 0x15, 0x76, 0x6c, 0x50, 0x94, 0x0b,
  0x85, 0x28, 0x25, 0x61, 0xbb, 0xe6, 0xfc, 0x39, 0xf0, 0x16, 0x7e, 0x85,
  0xb5, 0x02, 0xfb, 0x54, 0xdb, 0x64, 0xee, 0xbe, 0x33, 0x9c, 0xd5, 0xaf,
  0xb3, 0xfd, 0x18, 0xef, 0x44, 0x7f, 0xad, 0xaf, 0x70, 0x8a, 0x6f, 0xd8,
  0x8d, 0x7e, 0x1f, 0x2f, 0xf1, 0xe6, 0x96, 0xe5, 0x09, 0x57, 0xaf, 0x19,
  0x73, 0x9b, 0x46, 0x5d, 0xbd, 0x21, 0xa5, 0xf3, 0xb1, 0x20, 0x7b, 0xda,
  0xe3, 0xf0, 0xbf, 0xcf, 0xd7, 0x6d, 0xd9, 0x8c, 0x6a, 0xd5, 0xe3, 0x9a,
  0x73, 0x3e, 0x79, 0x70, 0xed, 0x73, 0xff, 0x02, 0x6e, 0x00, 0x8f, 0xd5,
  0xb3, 0x66, 0x24, 0x88, 0x0a, 0x97, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_cold_Snowflake_20x20_png_len = 486;
