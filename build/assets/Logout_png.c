unsigned char Logout_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x01, 0x16, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xed, 0x55, 0xbb,
  0x11, 0xc2, 0x30, 0x0c, 0x95, 0x1d, 0x06, 0x30, 0xa4, 0x01, 0x2a, 0x33,
  0x02, 0x23, 0xb0, 0x01, 0xa3, 0xc0, 0x04, 0xac, 0xc2, 0x26, 0xc0, 0x04,
  0x8c, 0x40, 0x2a, 0x8e, 0x06, 0x50, 0xcd, 0x1d, 0x16, 0x12, 0x47, 0x38,
  0x27, 0x97, 0x0f, 0x26, 0xee, 0xe0, 0x35, 0x71, 0x2c, 0xe7, 0x3d, 0xe9,
  0x59, 0x8e, 0x01, 0xfe, 0x68, 0x81, 0x0a, 0x59, 0x3c, 0x48, 0xc7, 0x07,
  0x7e, 0xd8, 0x0a, 0x1a, 0x24, 0x70, 0x5b, 0xba, 0xd3, 0x12, 0xf1, 0x94,
  0xf9, 0x11, 0x0d, 0x01, 0x20, 0xa2, 0xba, 0x88, 0x51, 0xa0, 0xe6, 0x3a,
  0xd1, 0x7b, 0x63, 0x86, 0x16, 0x62, 0x43, 0x48, 0x07, 0xe9, 0x68, 0xcd,
  0x15, 0x52, 0x3f, 0x1d, 0x6d, 0xfc, 0x58, 0x90, 0x45, 0xcd, 0x22, 0xd6,
  0xe8, 0xe4, 0x76, 0xe5, 0x21, 0x5e, 0xce, 0xc7, 0x7e, 0x3e, 0x1f, 0x64,
  0x51, 0x13, 0x10, 0x33, 0xcc, 0xb5, 0xfc, 0xf9, 0x1e, 0x54, 0x66, 0x33,
  0xb4, 0xec, 0xe7, 0xb3, 0x54, 0xce, 0x66, 0x02, 0x1d, 0xa0, 0x1b, 0xc8,
  0x6d, 0xfd, 0xa6, 0x7e, 0x29, 0x50, 0x22, 0xcf, 0xc8, 0xd1, 0x0c, 0x3a,
  0xa2, 0x60, 0x51, 0x4e, 0x2e, 0x63, 0xa5, 0x94, 0x55, 0x89, 0x3a, 0x70,
  0x67, 0x34, 0x7d, 0x9f, 0xb5, 0x59, 0x58, 0xa8, 0x20, 0xd4, 0x92, 0x4f,
  0xd6, 0x17, 0x2a, 0x78, 0x5a, 0xa2, 0x61, 0x23, 0xd9, 0x4b, 0x76, 0xee,
  0xee, 0x66, 0xe5, 0x93, 0x19, 0x8a, 0x42, 0x05, 0x42, 0x26, 0x22, 0xe2,
  0x3f, 0xbf, 0xbe, 0x3b, 0x29, 0x9a, 0x40, 0x59, 0x24, 0x46, 0x17, 0x55,
  0x9e, 0x83, 0x97, 0x2d, 0x9d, 0xfa, 0x3f, 0x47, 0xb4, 0x93, 0xec, 0xfd,
  0xe4, 0x30, 0xba, 0x80, 0x61, 0xf0, 0x7e, 0xad, 0x64, 0x4c, 0x40, 0x3b,
  0x3f, 0xd6, 0x0b, 0x21, 0xaa, 0xbf, 0x0f, 0x5e, 0x20, 0x40, 0xde, 0xbf,
  0x85, 0x3f, 0x15, 0xe7, 0x3e, 0x10, 0x62, 0xa0, 0xad, 0x73, 0x6e, 0xda,
  0xb5, 0xad, 0x7f, 0x10, 0x0f, 0x0f, 0xa6, 0x7e, 0x58, 0x9f, 0xab, 0xd8,
  0x58, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int Logout_png_len = 385;
