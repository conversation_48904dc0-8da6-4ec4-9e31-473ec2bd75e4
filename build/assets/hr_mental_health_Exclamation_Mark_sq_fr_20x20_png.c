unsigned char hr_mental_health_Exclamation_Mark_sq_fr_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x47, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xcd, 0x94, 0x3d, 0x4f, 0x03, 0x31, 0x0c,
  0x86, 0x9f, 0x20, 0xa8, 0x5a, 0x10, 0xcc, 0xac, 0x94, 0x01, 0x0a, 0x33,
  0x5b, 0x27, 0x84, 0x2a, 0x04, 0x1b, 0xbf, 0x06, 0x75, 0xa5, 0x03, 0xfd,
  0x29, 0x9d, 0xd9, 0x2a, 0xb1, 0x30, 0xc2, 0x00, 0x0c, 0x7c, 0xfc, 0x0e,
  0x10, 0x1f, 0xea, 0x21, 0xcc, 0x10, 0xfb, 0x94, 0xbb, 0xf3, 0x49, 0x87,
  0xe8, 0x80, 0xa5, 0xc8, 0x91, 0xe3, 0xf7, 0xb5, 0xe3, 0xc4, 0x0e, 0x22,
  0xc2, 0x3c, 0x65, 0x61, 0xae, 0x6c, 0xc0, 0x62, 0xd9, 0x10, 0x42, 0x68,
  0x01, 0x07, 0xc0, 0x36, 0xb0, 0x54, 0x83, 0xcb, 0x80, 0x27, 0xe0, 0x52,
  0x44, 0x66, 0x85, 0x13, 0x11, 0xc9, 0x17, 0xb0, 0x09, 0xdc, 0x03, 0xd2,
  0x70, 0xdd, 0x01, 0xdd, 0x94, 0x23, 0x58, 0x0d, 0x35, 0xb3, 0x1b, 0xa0,
  0x07, 0x8c, 0x81, 0x29, 0xf0, 0x5e, 0x93, 0xe1, 0x32, 0x30, 0x00, 0x4e,
  0x81, 0x07, 0x60, 0x4f, 0x44, 0xb2, 0x42, 0x86, 0xc0, 0x91, 0x46, 0x1d,
  0xa5, 0x11, 0xf5, 0xac, 0x03, 0xb4, 0x1d, 0xfb, 0x48, 0x31, 0x87, 0x66,
  0x4b, 0x1f, 0x65, 0x4b, 0xf5, 0xd4, 0xc9, 0xe8, 0x42, 0x57, 0x59, 0xcc,
  0xb7, 0x67, 0x86, 0xf4, 0x51, 0x3a, 0xaa, 0x5f, 0x1c, 0xe0, 0xba, 0x7b,
  0x71, 0x78, 0x55, 0xdd, 0x36, 0x43, 0x9a, 0xa1, 0x91, 0x7f, 0xd5, 0x80,
  0x3d, 0xc9, 0x54, 0xe7, 0xbf, 0x21, 0x25, 0xb4, 0xfd, 0x6f, 0x7e, 0xba,
  0xf9, 0xe6, 0x3c, 0x95, 0x7f, 0x58, 0x23, 0x93, 0xa6, 0x81, 0x52, 0xc2,
  0x6f, 0xd5, 0xa1, 0x92, 0x86, 0xc8, 0x59, 0x0d, 0xde, 0x7c, 0x0d, 0x5b,
  0xb8, 0xb2, 0xd5, 0xce, 0xeb, 0x9e, 0x7e, 0x08, 0xa1, 0xef, 0x10, 0x5a,
  0xed, 0xac, 0x96, 0x05, 0xf0, 0x87, 0xea, 0x35, 0x07, 0x38, 0x24, 0x5e,
  0xf9, 0xb8, 0x64, 0x5f, 0x55, 0xfd, 0xe9, 0x11, 0x3e, 0xab, 0x1e, 0x00,
  0x57, 0x25, 0xe0, 0x89, 0x13, 0xc4, 0x7c, 0x21, 0xf6, 0x35, 0x40, 0xa5,
  0xf5, 0xae, 0x81, 0x5d, 0xe0, 0x9c, 0xbf, 0xb6, 0x9e, 0x12, 0x77, 0x89,
  0x0d, 0xdf, 0x74, 0x38, 0xdc, 0x02, 0x1b, 0xee, 0x70, 0xc8, 0x53, 0x8e,
  0x99, 0xee, 0x03, 0x3b, 0xc4, 0xa2, 0xb7, 0x80, 0x15, 0x3d, 0x7e, 0x03,
  0x66, 0xc4, 0x47, 0x78, 0x24, 0x8e, 0xaf, 0xac, 0x80, 0xff, 0xf7, 0x13,
  0xfb, 0x07, 0x3a, 0xd4, 0xae, 0x2b, 0x0d, 0x4e, 0x24, 0xf4, 0x00, 0x00,
  0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_mental_health_Exclamation_Mark_sq_fr_20x20_png_len = 442;
