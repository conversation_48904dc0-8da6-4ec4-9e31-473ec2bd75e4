unsigned char hr_working_hours_Alarm_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0xaa, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0xd5, 0x3f, 0x6b, 0x15, 0x41, 0x14,
  0x05, 0xf0, 0xdf, 0x55, 0x83, 0x3e, 0x05, 0x05, 0x0b, 0x21, 0x2a, 0xc4,
  0xf7, 0x10, 0xad, 0x8c, 0x16, 0x6a, 0x22, 0x16, 0x12, 0x41, 0x3f, 0x80,
  0xa0, 0x85, 0x7e, 0x84, 0xf8, 0x05, 0xd2, 0xc4, 0x7f, 0x9d, 0xa9, 0xc5,
  0x52, 0xb0, 0x34, 0x21, 0xd8, 0x2a, 0x76, 0x06, 0x82, 0x96, 0x12, 0x1b,
  0x1f, 0x49, 0x67, 0x95, 0x46, 0x4d, 0x61, 0x10, 0x74, 0x2c, 0x76, 0x9e,
  0xac, 0xbb, 0xf3, 0x9e, 0x16, 0x19, 0xb8, 0xdc, 0xdd, 0x33, 0xe7, 0x9e,
  0x99, 0x9d, 0x39, 0x33, 0x1b, 0x29, 0x25, 0x3b, 0xd9, 0x76, 0x35, 0x81,
  0x88, 0xe8, 0x45, 0x44, 0xf7, 0x5f, 0x85, 0x11, 0xd1, 0x8d, 0x88, 0x5e,
  0xab, 0x23, 0xa5, 0xf4, 0x27, 0x30, 0x8b, 0x6d, 0xbc, 0xae, 0xe3, 0xa5,
  0xc0, 0x9b, 0xcc, 0x9d, 0xfd, 0x0b, 0xaf, 0x11, 0xee, 0x20, 0xa1, 0x8f,
  0xb3, 0x19, 0xbb, 0x88, 0x79, 0x3c, 0xcb, 0x31, 0x8f, 0x0b, 0xb9, 0xef,
  0x5c, 0xe6, 0x26, 0xdc, 0x2e, 0x09, 0x4e, 0x60, 0x0e, 0x07, 0x31, 0x89,
  0x95, 0x4c, 0x2e, 0xc5, 0x5b, 0x9c, 0xc9, 0xdc, 0x39, 0x4c, 0xb4, 0x04,
  0x6b, 0xc2, 0xd7, 0xb1, 0x85, 0x1f, 0x78, 0x82, 0x29, 0x1c, 0xc8, 0x31,
  0x8d, 0xa7, 0xb9, 0x6f, 0x0b, 0xd7, 0x5a, 0xf5, 0x0d, 0xb1, 0x53, 0xf8,
  0x8a, 0x4d, 0x4c, 0xd7, 0xf0, 0x2e, 0x4e, 0xd4, 0xde, 0x2f, 0x65, 0xce,
  0x17, 0x9c, 0x1c, 0x25, 0xf8, 0x12, 0xbf, 0x70, 0xa5, 0x81, 0xaf, 0x62,
  0xb5, 0x81, 0xcd, 0x64, 0xee, 0xf2, 0xb0, 0x4d, 0x39, 0x9a, 0x09, 0x4b,
  0x85, 0x65, 0x58, 0xc3, 0x5a, 0x01, 0x5f, 0xc6, 0x4f, 0x8c, 0x0f, 0xb0,
  0xba, 0x0f, 0xaf, 0x22, 0xb0, 0xd8, 0xf2, 0xd6, 0xf0, 0xb6, 0xa8, 0xf2,
  0xf2, 0xcc, 0x00, 0xa8, 0x0b, 0x1e, 0xcb, 0x79, 0xbd, 0x50, 0x98, 0x70,
  0x38, 0x22, 0x0e, 0x35, 0xf0, 0x7e, 0xce, 0xc7, 0x4b, 0x82, 0xdb, 0x39,
  0xef, 0x2d, 0x08, 0x3e, 0xc7, 0x38, 0x5e, 0x35, 0x44, 0x3b, 0x39, 0x7f,
  0x2f, 0x09, 0x0e, 0x46, 0x3b, 0xdf, 0x9a, 0x5e, 0x4a, 0x0b, 0x78, 0xa4,
  0xb2, 0xd0, 0xad, 0x5a, 0xd7, 0x80, 0xdb, 0xaf, 0x93, 0x07, 0x0b, 0xdc,
  0xc1, 0x37, 0xd5, 0x06, 0xec, 0x1e, 0x72, 0xdc, 0xa6, 0xb0, 0x3f, 0x3f,
  0xef, 0xc1, 0x47, 0x95, 0xcd, 0xf6, 0x0d, 0xb3, 0xcd, 0x03, 0xd5, 0x7a,
  0xdd, 0xff, 0x8f, 0xb3, 0xfc, 0x30, 0x73, 0xef, 0x8d, 0xf2, 0x61, 0x07,
  0xef, 0x33, 0xf1, 0x71, 0x7d, 0xe4, 0x06, 0x67, 0x21, 0x73, 0xde, 0x35,
  0x39, 0xd1, 0xbc, 0x0f, 0x23, 0xe2, 0x88, 0xca, 0x5f, 0x97, 0xf1, 0x19,
  0x2f, 0xf0, 0x41, 0x65, 0xa9, 0x49, 0xdc, 0x54, 0x39, 0x62, 0x05, 0x37,
  0x52, 0x4a, 0x9b, 0xcd, 0x05, 0x2f, 0x7d, 0xce, 0x18, 0xee, 0x62, 0x43,
  0xfb, 0x62, 0x58, 0x57, 0x5d, 0x73, 0x63, 0xa5, 0xda, 0xd6, 0x0c, 0x9b,
  0x2d, 0x22, 0x4e, 0xa3, 0x97, 0xc5, 0x36, 0x52, 0x4a, 0x9f, 0x46, 0xf2,
  0x77, 0xfa, 0x17, 0xf0, 0x1b, 0x07, 0x3e, 0xb0, 0xfa, 0x46, 0x95, 0xeb,
  0x76, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int hr_working_hours_Alarm_20x20_png_len = 541;
