unsigned char hr_other_Medical_Chart_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x62, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0x94, 0x3b, 0x4b, 0x43, 0x51, 0x0c,
  0x80, 0xbf, 0xb4, 0x42, 0xf1, 0x01, 0x62, 0xf1, 0x81, 0x93, 0x05, 0x71,
  0x14, 0xf4, 0x07, 0xf8, 0x37, 0xdc, 0x5c, 0x2e, 0x4e, 0x8e, 0x05, 0x75,
  0x70, 0xd5, 0xb1, 0x83, 0x93, 0x60, 0x47, 0x27, 0x1d, 0x8a, 0xe2, 0x6f,
  0xe8, 0x20, 0x14, 0x11, 0x5c, 0x2c, 0x8a, 0x38, 0x56, 0x27, 0x1d, 0x44,
  0xb0, 0xd4, 0x38, 0x18, 0xed, 0x6d, 0x38, 0xf7, 0xd4, 0xa2, 0x81, 0x90,
  0x73, 0x4e, 0x92, 0xef, 0x26, 0xf7, 0x3c, 0x44, 0x55, 0xf9, 0x4f, 0x19,
  0xea, 0x17, 0x20, 0x22, 0x4b, 0xc0, 0x82, 0x4d, 0x6f, 0x55, 0xf5, 0x2a,
  0x9a, 0xa0, 0xaa, 0x99, 0x0a, 0x54, 0x01, 0x75, 0x7a, 0x18, 0xcd, 0x09,
  0x40, 0x0a, 0x40, 0x02, 0xd4, 0x0c, 0xd0, 0x00, 0x56, 0x4d, 0x1b, 0xb6,
  0x56, 0xb3, 0x98, 0x42, 0x14, 0x68, 0xb0, 0xba, 0xab, 0xe8, 0x14, 0x10,
  0x20, 0x67, 0xe3, 0xb4, 0xaf, 0xee, 0xa1, 0x1e, 0x98, 0x58, 0x60, 0x05,
  0x98, 0x01, 0x9a, 0x81, 0x96, 0x9b, 0xe6, 0xab, 0xd8, 0x3c, 0x89, 0x01,
  0xbf, 0xff, 0x59, 0xd1, 0xe6, 0xd3, 0xc0, 0x1e, 0x70, 0x62, 0xba, 0x0b,
  0x4c, 0x99, 0xaf, 0x68, 0xb1, 0xd5, 0x34, 0xc3, 0xef, 0xf2, 0x98, 0xd9,
  0x67, 0xdb, 0xb0, 0x27, 0x60, 0x27, 0x63, 0x3f, 0x5f, 0xcc, 0x8e, 0xa6,
  0x17, 0x73, 0x2e, 0x28, 0x6f, 0xa0, 0x8f, 0x0c, 0xc8, 0x8f, 0xa8, 0x6a,
  0xc7, 0x86, 0x3d, 0x45, 0x65, 0x9e, 0x43, 0x11, 0x29, 0x01, 0x47, 0xc0,
  0xb0, 0x73, 0xbd, 0x01, 0x6b, 0xaa, 0xfa, 0x10, 0xca, 0xf3, 0x15, 0xfe,
  0x56, 0x32, 0xaf, 0x57, 0x66, 0x85, 0x56, 0xc1, 0xca, 0xa0, 0x5f, 0x8a,
  0xb5, 0x3c, 0x02, 0xac, 0xd3, 0xdb, 0xf2, 0x99, 0xaa, 0xde, 0x0c, 0x0c,
  0x14, 0x11, 0x01, 0x16, 0x81, 0x7d, 0xe7, 0x9a, 0x04, 0x36, 0x53, 0x31,
  0x7d, 0x81, 0x6d, 0xb3, 0x79, 0x55, 0xbd, 0x10, 0x91, 0x79, 0x60, 0xc2,
  0xd6, 0x3a, 0xc0, 0x75, 0x20, 0xf7, 0x3d, 0x06, 0x6c, 0x99, 0x2d, 0x01,
  0x77, 0xaa, 0x7a, 0x1f, 0xaa, 0xc2, 0x64, 0xce, 0xec, 0x63, 0x0c, 0x78,
  0x0e, 0x94, 0x81, 0x63, 0x11, 0x39, 0xa0, 0x7b, 0x78, 0xbd, 0x8c, 0x03,
  0x1b, 0xa9, 0x9c, 0xae, 0x04, 0x5e, 0x9b, 0x6d, 0xbe, 0x5a, 0xf7, 0x77,
  0xd8, 0x6b, 0x1b, 0xd8, 0xf2, 0xf9, 0x12, 0x7a, 0xb1, 0x45, 0x64, 0x16,
  0x58, 0xc6, 0x5d, 0xab, 0x94, 0xbc, 0x02, 0x97, 0xaa, 0xda, 0xf2, 0x8e,
  0x20, 0xf0, 0x2f, 0xf2, 0x09, 0x5d, 0x10, 0x0b, 0x78, 0xd8, 0x62, 0x42,
  0x04, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int hr_other_Medical_Chart_20x20_png_len = 469;
