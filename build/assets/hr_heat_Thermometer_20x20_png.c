unsigned char hr_heat_Thermometer_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x57, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0x95, 0xbf, 0x2e, 0x44, 0x51, 0x10,
  0xc6, 0x7f, 0x1f, 0xdb, 0x21, 0x91, 0xd8, 0xc6, 0x6e, 0x21, 0x21, 0x11,
  0x0a, 0xa1, 0x57, 0x2b, 0x28, 0xa8, 0xd4, 0x24, 0x9e, 0x43, 0xe7, 0x01,
  0xb6, 0xd9, 0x46, 0x4b, 0x47, 0xe9, 0x01, 0xe8, 0x10, 0x22, 0xb1, 0xa5,
  0x10, 0x9d, 0x64, 0x83, 0x42, 0xa1, 0xb3, 0x9f, 0xc2, 0x90, 0xdd, 0xbb,
  0xe7, 0xec, 0x12, 0x3b, 0xc9, 0x64, 0xee, 0x99, 0x33, 0xf3, 0xdd, 0xf9,
  0x77, 0xe7, 0xca, 0x36, 0x83, 0xa4, 0x52, 0xee, 0x42, 0x52, 0x05, 0x58,
  0x02, 0x6e, 0x6c, 0x3f, 0x85, 0x6e, 0x18, 0x58, 0x01, 0xc6, 0xc2, 0xac,
  0x05, 0x9c, 0xd9, 0x7e, 0xfe, 0x71, 0xb4, 0xdd, 0xc5, 0xc0, 0x06, 0xf0,
  0x0e, 0x38, 0xe4, 0x7a, 0xe8, 0x97, 0x43, 0xd7, 0xce, 0xb5, 0x76, 0x5f,
  0xa5, 0x52, 0x96, 0x74, 0x07, 0x8c, 0x03, 0x7b, 0xc0, 0x2e, 0xf0, 0x6a,
  0x7b, 0x36, 0x13, 0xe1, 0xa9, 0xed, 0x97, 0x7e, 0x11, 0xb6, 0x80, 0xe3,
  0x78, 0x3e, 0x06, 0x5a, 0x29, 0xbb, 0x14, 0x0f, 0xe5, 0x4a, 0x18, 0xa0,
  0xdf, 0xe0, 0x8a, 0xc8, 0x17, 0x24, 0xbd, 0x49, 0xb2, 0xa4, 0xcb, 0x94,
  0x63, 0xb6, 0x29, 0x19, 0x7a, 0x02, 0x8e, 0x22, 0xe5, 0xeb, 0x7f, 0x03,
  0x46, 0x37, 0x77, 0x7a, 0xd9, 0xe4, 0x52, 0x4e, 0x92, 0xa4, 0xb2, 0xa4,
  0x9a, 0xa4, 0x89, 0x81, 0x00, 0x02, 0x93, 0xc0, 0x26, 0x50, 0xc9, 0x19,
  0x74, 0xa5, 0x2c, 0x29, 0x59, 0x06, 0x49, 0x25, 0xdb, 0x0d, 0xa0, 0xda,
  0xeb, 0x8d, 0xa9, 0x08, 0x0f, 0x43, 0xbe, 0x14, 0xe4, 0x41, 0x2f, 0xa0,
  0x1f, 0x2a, 0xcc, 0xdf, 0x1a, 0x5f, 0xd3, 0x7f, 0x02, 0x94, 0x42, 0x57,
  0x8a, 0xb3, 0x81, 0xd5, 0x7e, 0x73, 0x58, 0x04, 0xac, 0x87, 0xe3, 0x1c,
  0x50, 0x06, 0x6a, 0xc0, 0x04, 0x30, 0x1f, 0xfa, 0xfa, 0x5f, 0x07, 0x7b,
  0x34, 0x64, 0x93, 0xce, 0x06, 0x34, 0x0b, 0xf7, 0x59, 0x2a, 0x02, 0x5e,
  0x85, 0xdc, 0xb2, 0xdd, 0xb0, 0x5d, 0x8d, 0x46, 0x6c, 0x87, 0x3e, 0xf9,
  0x75, 0x74, 0x50, 0x21, 0xe5, 0x51, 0xe0, 0x01, 0xf8, 0x00, 0xf6, 0x03,
  0x68, 0x3f, 0xce, 0xf7, 0xc0, 0xc8, 0x9f, 0x6a, 0x18, 0xa0, 0x33, 0xc0,
  0x05, 0x9d, 0x2b, 0xea, 0x1c, 0x98, 0xfe, 0xcd, 0x72, 0x48, 0xae, 0x2f,
  0x00, 0x49, 0x8b, 0xc0, 0x14, 0xf0, 0x68, 0xfb, 0xb6, 0x6f, 0xaa, 0xdf,
  0x7e, 0x83, 0xfe, 0x05, 0x7c, 0x02, 0x43, 0x78, 0x40, 0xe0, 0x53, 0x51,
  0x0a, 0x51, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42,
  0x60, 0x82
};
unsigned int hr_heat_Thermometer_20x20_png_len = 458;
