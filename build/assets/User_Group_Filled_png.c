unsigned char User_Group_Filled_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x00, 0xcc, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xed,
  0x93, 0x3d, 0x0a, 0xc2, 0x40, 0x10, 0x85, 0x3f, 0xf4, 0x04, 0x5a, 0xab,
  0x6d, 0xbc, 0x82, 0x37, 0xd0, 0x23, 0xea, 0x0d, 0x0c, 0x01, 0x6b, 0x0b,
  0xcf, 0x61, 0xa3, 0x8d, 0xb6, 0x2a, 0x24, 0x4d, 0xca, 0x58, 0xec, 0x46,
  0x96, 0x61, 0x87, 0xfd, 0x01, 0x41, 0x21, 0x0f, 0x5e, 0xb1, 0xdf, 0xb2,
  0x33, 0x93, 0xec, 0x3e, 0x18, 0xf4, 0xcb, 0x9a, 0x03, 0x15, 0xd0, 0x00,
  0x57, 0x85, 0x37, 0xc0, 0x01, 0x28, 0x72, 0x8a, 0x3f, 0x81, 0xce, 0x7a,
  0xa7, 0xf0, 0xde, 0x2f, 0xbb, 0x17, 0xad, 0x4a, 0x14, 0x58, 0x29, 0xdc,
  0x75, 0x99, 0xd2, 0xa0, 0x11, 0x87, 0xa7, 0x0a, 0x77, 0x5d, 0xfb, 0x0a,
  0x8d, 0x52, 0xba, 0x06, 0xd4, 0xa5, 0x34, 0x38, 0x89, 0xf5, 0x52, 0xe1,
  0xae, 0x8e, 0x29, 0xd3, 0x14, 0x98, 0x8b, 0x93, 0x97, 0x2c, 0x79, 0xef,
  0x07, 0x30, 0x4b, 0x69, 0x00, 0xe6, 0x55, 0x94, 0x98, 0x7f, 0x7b, 0x51,
  0x78, 0x0d, 0xec, 0x73, 0x8a, 0xff, 0xaf, 0xdc, 0x94, 0x9e, 0x15, 0xae,
  0x3d, 0xd3, 0x60, 0xaa, 0x65, 0x4a, 0xb7, 0x0a, 0x0f, 0x59, 0x4d, 0x75,
  0x4e, 0x7a, 0x93, 0x52, 0x2d, 0x3f, 0x7f, 0xa2, 0xf0, 0x18, 0x7f, 0x52,
  0xed, 0x06, 0x4d, 0x46, 0x7d, 0xac, 0xf0, 0x18, 0x79, 0xcf, 0x6c, 0x80,
  0xbb, 0x9d, 0xa0, 0x05, 0x16, 0x1e, 0x1e, 0xe3, 0x1b, 0xb0, 0xce, 0x18,
  0x6a, 0xd0, 0x97, 0xf4, 0x06, 0x26, 0x03, 0x8b, 0x27, 0xf4, 0xcd, 0x81,
  0xae, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int User_Group_Filled_png_len = 277;
