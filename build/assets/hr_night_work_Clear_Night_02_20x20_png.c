unsigned char hr_night_work_Clear_Night_02_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0xaa, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0xd5, 0xbd, 0x6a, 0x15, 0x61, 0x10,
  0x06, 0xe0, 0x67, 0x0e, 0x6a, 0xe3, 0x0f, 0x24, 0x85, 0x04, 0x25, 0x72,
  0x2a, 0x8d, 0x44, 0xbc, 0x00, 0x21, 0x69, 0x95, 0x08, 0x12, 0x2b, 0x15,
  0xa3, 0x37, 0xe0, 0x2d, 0xd8, 0x06, 0x11, 0x6d, 0x44, 0x2f, 0x41, 0x10,
  0xc5, 0x42, 0x2c, 0x45, 0x04, 0x0d, 0x58, 0x69, 0x17, 0xc5, 0xbf, 0x4a,
  0x31, 0x28, 0x16, 0x12, 0xa2, 0xc5, 0x29, 0x74, 0x2c, 0xf6, 0x4b, 0xd8,
  0xec, 0xd9, 0x3d, 0x2a, 0x64, 0x61, 0x98, 0x65, 0xde, 0x9d, 0xf7, 0x9b,
  0x99, 0x6f, 0x66, 0x36, 0x32, 0xd3, 0x56, 0x3e, 0xdb, 0x46, 0x81, 0x11,
  0xd1, 0xc3, 0x34, 0x26, 0xf1, 0x2e, 0x33, 0x3f, 0xfc, 0x95, 0x31, 0x33,
  0x87, 0x04, 0xe3, 0xb8, 0x8a, 0x2f, 0xc8, 0x22, 0x4f, 0xdb, 0xbe, 0x1d,
  0xf2, 0x6d, 0x21, 0x9b, 0xc5, 0xb7, 0x42, 0xb2, 0x8c, 0xcf, 0xe5, 0xfd,
  0xf4, 0x7f, 0x13, 0x16, 0xb2, 0x01, 0xbe, 0xe3, 0x2c, 0x76, 0x62, 0x0d,
  0xcf, 0x0a, 0xde, 0xc7, 0x23, 0xf4, 0xbb, 0x08, 0x37, 0x6a, 0x18, 0x11,
  0xe3, 0xb8, 0x8f, 0x9f, 0x98, 0xcd, 0xcc, 0xe5, 0x88, 0x38, 0x89, 0x5d,
  0xb8, 0x53, 0xaf, 0x52, 0x91, 0xd1, 0x35, 0x2c, 0x35, 0x4b, 0x9c, 0xa9,
  0xd9, 0x2e, 0x15, 0xdb, 0xb1, 0x7f, 0x49, 0x37, 0x33, 0xf5, 0x6a, 0xb7,
  0x79, 0x11, 0xaf, 0x70, 0xaf, 0x76, 0xde, 0xee, 0xa2, 0x07, 0x6d, 0xc1,
  0x44, 0xc4, 0x7c, 0x44, 0x4c, 0xd5, 0x6d, 0xbd, 0xa2, 0xa7, 0x31, 0x81,
  0x07, 0xb9, 0xb9, 0x31, 0xbf, 0x16, 0x3d, 0xd9, 0x91, 0xe0, 0x5d, 0x5c,
  0x6e, 0x23, 0xdc, 0x5f, 0xf4, 0xfb, 0x86, 0xc3, 0xf3, 0xa2, 0x4f, 0x34,
  0x22, 0x5b, 0x88, 0x88, 0x17, 0xd8, 0x81, 0xb9, 0x88, 0x58, 0x8a, 0x88,
  0x7e, 0x9d, 0xb0, 0x79, 0x00, 0xc8, 0xcc, 0xb7, 0x78, 0x89, 0x0b, 0x11,
  0xb1, 0xaf, 0x0e, 0xb5, 0x44, 0x9b, 0xeb, 0x4e, 0x70, 0xa4, 0x18, 0x16,
  0x5b, 0xfa, 0x72, 0xae, 0x60, 0x4f, 0xb0, 0xbd, 0x81, 0x0d, 0x70, 0x7b,
  0xa8, 0x0f, 0x4b, 0x64, 0x2b, 0x78, 0x8d, 0x68, 0x21, 0xbd, 0x59, 0x48,
  0x1f, 0x63, 0xa2, 0x66, 0x9f, 0xc7, 0x54, 0x6b, 0x63, 0xe3, 0x4a, 0x71,
  0x3a, 0xdf, 0x42, 0xd8, 0xc3, 0xad, 0x82, 0xaf, 0xe1, 0x06, 0x8e, 0xe3,
  0x20, 0x7a, 0x5d, 0x84, 0x63, 0xaa, 0x5b, 0x5d, 0xc5, 0xd1, 0x8e, 0x19,
  0x3f, 0xa5, 0x1a, 0xc7, 0xac, 0xc9, 0xf5, 0x51, 0xa3, 0x37, 0x53, 0xea,
  0xb2, 0x8a, 0x85, 0x8e, 0xf4, 0x03, 0xd7, 0x0a, 0xd9, 0x0f, 0xd5, 0x54,
  0x6d, 0xe0, 0xd1, 0xdc, 0x87, 0x11, 0x31, 0xa3, 0x1a, 0xc1, 0xbd, 0x78,
  0x83, 0x87, 0x45, 0x27, 0x0e, 0xad, 0xd7, 0x4d, 0xb5, 0x34, 0xce, 0x65,
  0xe6, 0xd2, 0xe6, 0xbb, 0x6e, 0x4f, 0x6d, 0xac, 0xd4, 0x74, 0xa5, 0x91,
  0x5e, 0xe2, 0x13, 0x16, 0xb1, 0xa7, 0xcd, 0x77, 0x28, 0xc2, 0x46, 0xb4,
  0x3d, 0x1c, 0xc6, 0x01, 0xfc, 0xc2, 0x47, 0xd5, 0xa2, 0xfd, 0xdd, 0xe9,
  0xb3, 0xd5, 0xbf, 0x80, 0x3f, 0x33, 0x4b, 0xc1, 0x19, 0xd5, 0x24, 0x08,
  0x3e, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int hr_night_work_Clear_Night_02_20x20_png_len = 541;
