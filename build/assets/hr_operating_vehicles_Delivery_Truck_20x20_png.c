unsigned char hr_operating_vehicles_Delivery_Truck_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x5a, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xe5, 0x94, 0xb1, 0x2f, 0x43, 0x51, 0x14,
  0xc6, 0x7f, 0x9f, 0x34, 0xd2, 0xb7, 0x0b, 0x29, 0x46, 0x8b, 0x58, 0x9a,
  0x18, 0x24, 0x6a, 0xd3, 0x95, 0xad, 0x2b, 0x03, 0xbb, 0x3f, 0x43, 0x07,
  0x8b, 0xc1, 0xc2, 0x64, 0x33, 0x49, 0x13, 0xb1, 0xb0, 0x9b, 0x44, 0x43,
  0x62, 0x20, 0x26, 0x62, 0xea, 0x22, 0x12, 0x4d, 0x45, 0xe5, 0x18, 0xde,
  0x79, 0xaf, 0x7d, 0xcf, 0x53, 0x0d, 0x35, 0xb9, 0xc9, 0xc9, 0x3b, 0xb9,
  0xe7, 0x3b, 0xbf, 0x77, 0x73, 0xee, 0x97, 0x2b, 0x33, 0x63, 0x90, 0x6b,
  0x68, 0xa0, 0xb4, 0xbf, 0x00, 0xe6, 0xa2, 0x44, 0xd2, 0x18, 0xb0, 0x01,
  0x4c, 0xa5, 0x34, 0x2f, 0xc0, 0x91, 0x99, 0x1d, 0xf6, 0x45, 0xf4, 0x19,
  0x06, 0xc0, 0x2d, 0x60, 0x3d, 0x62, 0xcd, 0xcc, 0xf8, 0x2e, 0x22, 0xe0,
  0xa2, 0x37, 0x6d, 0x02, 0x4a, 0x08, 0xa0, 0x00, 0x3c, 0x03, 0x17, 0xc0,
  0xac, 0xc7, 0x34, 0x90, 0xeb, 0x05, 0xac, 0x38, 0xb0, 0x92, 0x29, 0x82,
  0x56, 0xc6, 0x89, 0x1f, 0x81, 0x72, 0x5a, 0x1b, 0xcf, 0xb0, 0x8f, 0xf5,
  0x0e, 0x6c, 0x79, 0x9e, 0x07, 0x56, 0x80, 0x63, 0x49, 0xab, 0x66, 0x76,
  0x90, 0x9e, 0x61, 0x3f, 0x27, 0x6c, 0xa5, 0xf6, 0x66, 0x80, 0x07, 0xff,
  0xd1, 0x7a, 0xb4, 0xff, 0x63, 0xdb, 0x98, 0xd9, 0x35, 0x50, 0x02, 0xee,
  0x80, 0x1d, 0x49, 0x93, 0xf0, 0x4b, 0x1f, 0x9a, 0xd9, 0x3d, 0xe1, 0x45,
  0x0e, 0x13, 0x5e, 0xd6, 0x67, 0xa0, 0xa4, 0x40, 0x52, 0x59, 0x52, 0xf1,
  0x2b, 0x90, 0xa4, 0xa2, 0x6b, 0xf2, 0x84, 0x3e, 0xc5, 0xa1, 0x31, 0xf0,
  0xd5, 0xbf, 0x13, 0xc0, 0x25, 0x70, 0x0a, 0xd4, 0x25, 0xed, 0x66, 0xc0,
  0xf6, 0x80, 0xba, 0x6b, 0xae, 0xbc, 0xa7, 0xc3, 0xf0, 0x01, 0x8f, 0x03,
  0x4d, 0x0f, 0x03, 0xaa, 0xc0, 0xb9, 0xe7, 0x27, 0x84, 0x83, 0x6f, 0x7b,
  0x6e, 0x5e, 0xab, 0x7a, 0xde, 0xf4, 0x53, 0x16, 0x62, 0x1f, 0x3a, 0x74,
  0x19, 0x78, 0x72, 0xd1, 0x1c, 0x50, 0xa3, 0xe3, 0xb9, 0x37, 0x92, 0x1e,
  0xac, 0xb9, 0xc6, 0xbc, 0x67, 0x29, 0x61, 0xec, 0x2e, 0xe8, 0x82, 0x9f,
  0x24, 0x6a, 0xbc, 0x01, 0xf2, 0x5d, 0xf5, 0xc0, 0xf7, 0xa2, 0x7a, 0x1b,
  0x98, 0xef, 0x66, 0x28, 0xfd, 0x1e, 0x4a, 0x2a, 0x11, 0x9a, 0xb6, 0x01,
  0x6c, 0x9b, 0x59, 0x23, 0x55, 0x1f, 0x25, 0x7c, 0x44, 0x46, 0x80, 0x7d,
  0x33, 0x3b, 0x4b, 0xd4, 0xff, 0xdf, 0x03, 0xfb, 0x01, 0x36, 0x05, 0x13,
  0x4f, 0xf6, 0x9a, 0x8a, 0xdf, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e,
  0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_operating_vehicles_Delivery_Truck_20x20_png_len = 461;
