unsigned char Steth<PERSON>cope_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x01, 0xc4, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xed, 0x54, 0xcb,
  0x71, 0xc2, 0x30, 0x10, 0xd5, 0xca, 0x97, 0x4c, 0x4e, 0x02, 0x73, 0xe0,
  0x73, 0x71, 0x3a, 0xa0, 0x84, 0xa4, 0x83, 0x74, 0x90, 0xa4, 0x02, 0xa0,
  0x82, 0x98, 0x0a, 0x20, 0x15, 0x40, 0x2a, 0x48, 0x3a, 0x00, 0x2a, 0x80,
  0x0e, 0xc2, 0x09, 0x38, 0x80, 0xed, 0x13, 0x19, 0x32, 0x41, 0x9b, 0x5d,
  0xd9, 0xe6, 0xeb, 0x98, 0xcf, 0x4c, 0x72, 0xe2, 0xcd, 0x68, 0x2c, 0xad,
  0x9e, 0xf7, 0x3d, 0x7d, 0x56, 0x42, 0x5c, 0x70, 0x0a, 0x94, 0xca, 0x3b,
  0x19, 0xbb, 0xd0, 0xc9, 0xda, 0x45, 0xa4, 0xf6, 0x11, 0xc7, 0xb9, 0x6f,
  0x62, 0xb9, 0x62, 0x9f, 0x39, 0xe2, 0x04, 0xc8, 0xad, 0x81, 0xb4, 0xde,
  0x40, 0xc0, 0x2d, 0xf7, 0x11, 0x71, 0x15, 0x5f, 0xf5, 0x51, 0x94, 0xc1,
  0x82, 0x96, 0x38, 0x01, 0xb0, 0x39, 0x60, 0x97, 0xfc, 0xd5, 0xcb, 0x79,
  0x26, 0x20, 0x6c, 0xce, 0x29, 0xe5, 0x28, 0x69, 0x7d, 0xf9, 0xdc, 0xf7,
  0x66, 0x23, 0x10, 0x47, 0x42, 0x26, 0x05, 0x77, 0x93, 0x87, 0xb1, 0x61,
  0x20, 0xce, 0x80, 0x14, 0x7f, 0x8c, 0x7f, 0x16, 0x40, 0x30, 0xdb, 0xa0,
  0x08, 0xbb, 0x44, 0x3e, 0x83, 0xa4, 0x04, 0xd9, 0x6c, 0xf1, 0x39, 0x6b,
  0x97, 0x7c, 0xd3, 0x72, 0x85, 0x46, 0xaa, 0x00, 0x80, 0x1e, 0x98, 0xa0,
  0xbc, 0xae, 0xee, 0x5b, 0x59, 0x3c, 0x18, 0x0f, 0x02, 0xbb, 0xeb, 0xe4,
  0xa5, 0x06, 0x5d, 0x13, 0x97, 0xa2, 0xca, 0x34, 0x84, 0x2a, 0x09, 0xba,
  0x5b, 0x39, 0xb7, 0x5c, 0xe6, 0xf2, 0xb7, 0x12, 0x65, 0x27, 0x5c, 0x0d,
  0xd6, 0xb4, 0xfe, 0x6c, 0x47, 0x82, 0x95, 0x30, 0x11, 0xdd, 0x30, 0xa1,
  0x9f, 0x82, 0xd9, 0xa4, 0x1d, 0x09, 0xf8, 0x02, 0x50, 0xe9, 0xa5, 0xbe,
  0x31, 0x3c, 0x4b, 0x72, 0xed, 0x04, 0x74, 0xcb, 0x32, 0x89, 0x2b, 0x08,
  0xa6, 0x93, 0x2e, 0x25, 0xaa, 0x47, 0xcb, 0x69, 0x48, 0xeb, 0xda, 0xe7,
  0x16, 0x27, 0x27, 0xfb, 0xf5, 0x38, 0xf9, 0xb1, 0xd8, 0x3b, 0x64, 0x6f,
  0x3a, 0x72, 0xd9, 0x25, 0x6c, 0x6c, 0x05, 0x6f, 0x8b, 0x06, 0x7d, 0xe7,
  0x79, 0x23, 0x77, 0xfb, 0x6f, 0x7c, 0x89, 0x9d, 0x47, 0xee, 0x99, 0xfd,
  0x2a, 0x8e, 0x45, 0xf4, 0x64, 0x60, 0x3a, 0xa7, 0xd4, 0x8c, 0x79, 0xf4,
  0x94, 0xb8, 0xbb, 0xf3, 0x70, 0x48, 0x80, 0xbf, 0x49, 0x95, 0x9d, 0xc4,
  0x4b, 0xaa, 0xf0, 0xd4, 0x3a, 0x58, 0x6f, 0xd3, 0x55, 0xf9, 0x37, 0x8e,
  0x6d, 0x17, 0xee, 0xf9, 0x4b, 0xef, 0xd5, 0x20, 0x69, 0x3e, 0x55, 0x00,
  0x01, 0x7a, 0x86, 0x64, 0xc9, 0x56, 0x72, 0x6d, 0x28, 0x85, 0x02, 0x1a,
  0x21, 0x37, 0x3c, 0x8f, 0x7d, 0x93, 0x29, 0xe0, 0xe2, 0x02, 0xb9, 0xe8,
  0x03, 0x80, 0x43, 0xc3, 0x21, 0xad, 0xa8, 0x36, 0x9b, 0x8d, 0xdf, 0x43,
  0xb1, 0xab, 0x32, 0x0b, 0x53, 0xdc, 0x21, 0xf7, 0x43, 0xdf, 0x1b, 0xdf,
  0x9c, 0x2c, 0x10, 0x8a, 0xe4, 0x1d, 0x90, 0xd0, 0x89, 0x44, 0xf6, 0xc0,
  0xc9, 0x51, 0xe3, 0x5d, 0x10, 0x4c, 0x86, 0x67, 0x09, 0xac, 0x84, 0xec,
  0xfc, 0x23, 0xa0, 0xac, 0x00, 0x08, 0x73, 0x1e, 0x7c, 0x75, 0x49, 0xb4,
  0xa7, 0xbf, 0xe7, 0xcd, 0xb4, 0x0b, 0x70, 0xc1, 0x41, 0xfc, 0x00, 0xed,
  0x55, 0xb9, 0xa2, 0x6d, 0xc3, 0x42, 0x76, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Stethoscope_png_len = 559;
