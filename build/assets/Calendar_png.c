unsigned char Calendar_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x01, 0x16, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xed, 0x94, 0x31,
  0x8e, 0xc2, 0x40, 0x0c, 0x45, 0x6d, 0xcf, 0x4a, 0xac, 0x76, 0x9b, 0x49,
  0xd2, 0x00, 0xdb, 0x64, 0x6f, 0xb0, 0x7b, 0x0c, 0x8e, 0xc3, 0x09, 0x38,
  0x02, 0x47, 0x80, 0x96, 0x8a, 0x23, 0x00, 0x1d, 0x1d, 0xdc, 0x00, 0x2a,
  0x44, 0x03, 0x4c, 0x41, 0x81, 0x90, 0x18, 0x33, 0x13, 0x08, 0x4a, 0x82,
  0x82, 0x86, 0x00, 0x15, 0x79, 0x52, 0xa4, 0xc8, 0x8e, 0xff, 0xf7, 0x8c,
  0x62, 0x03, 0x94, 0x3c, 0x82, 0x1f, 0xd4, 0x67, 0xf6, 0x29, 0x9a, 0xb7,
  0x7c, 0xc0, 0x6d, 0xc2, 0x07, 0xf3, 0x80, 0xd9, 0x80, 0x94, 0xd5, 0x10,
  0x05, 0x76, 0x10, 0xe8, 0x0f, 0x80, 0x25, 0x38, 0x81, 0x8a, 0x41, 0x0f,
  0xf9, 0xc0, 0x4d, 0xa5, 0x96, 0xf3, 0x5c, 0x03, 0x2b, 0x4e, 0x42, 0x4c,
  0xdc, 0x85, 0xaf, 0x50, 0xfa, 0xa0, 0xff, 0xb3, 0x26, 0x17, 0xbc, 0xa0,
  0xde, 0x37, 0x77, 0xca, 0x7e, 0x50, 0xeb, 0x4a, 0x03, 0x38, 0x62, 0x1b,
  0xb3, 0x35, 0xb6, 0xd6, 0x0b, 0x6a, 0x83, 0x64, 0x2e, 0x75, 0x02, 0xdf,
  0xff, 0xd9, 0x00, 0xb2, 0x34, 0x5d, 0xfc, 0xe6, 0x76, 0x91, 0x6b, 0x12,
  0x4a, 0x12, 0xfb, 0x8d, 0x3d, 0xc5, 0x7a, 0xb5, 0xf0, 0xe2, 0x38, 0xa5,
  0xbe, 0xc2, 0xd3, 0xd5, 0xdc, 0x2b, 0x7e, 0xaa, 0x99, 0xab, 0xd8, 0x2b,
  0x19, 0x27, 0x78, 0x31, 0xa5, 0x41, 0x69, 0xf0, 0x6c, 0x03, 0xc6, 0xe8,
  0x5f, 0xbe, 0x67, 0x8a, 0x63, 0xec, 0x34, 0x9f, 0x5f, 0x55, 0xae, 0x01,
  0xa2, 0x9e, 0x46, 0x41, 0xf1, 0xd5, 0x4e, 0x14, 0x38, 0x88, 0x4b, 0x33,
  0xc5, 0xd4, 0x8a, 0x7a, 0x04, 0x1e, 0xa5, 0x34, 0xb3, 0x5d, 0x10, 0x99,
  0x65, 0x87, 0x05, 0x97, 0x1d, 0x9b, 0x65, 0xa7, 0xd3, 0xcb, 0x4e, 0x24,
  0xf3, 0xbb, 0xdd, 0x56, 0x55, 0x2a, 0xdf, 0x3d, 0x20, 0x0c, 0x91, 0xa1,
  0x6a, 0xec, 0x3f, 0xc1, 0x05, 0x23, 0xcc, 0xc8, 0x63, 0xd6, 0xdc, 0x28,
  0xb2, 0x66, 0xde, 0x9c, 0x23, 0xa3, 0x44, 0x60, 0x49, 0x0e, 0x01, 0xb3,
  0x2d, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60,
  0x82
};
unsigned int Calendar_png_len = 385;
