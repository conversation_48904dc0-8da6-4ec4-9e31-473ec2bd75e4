unsigned char Minimize_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x00, 0x4f, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0x63, 0xfc, 0xff, 0xff, 0x3f, 0x03, 0x2d,
  0x01, 0x13, 0x4d, 0x4d, 0x1f, 0xb5, 0x60, 0xd4, 0x82, 0x51, 0x0b, 0x46,
  0x2d, 0x18, 0xb5, 0x60, 0xd4, 0x02, 0x7a, 0x59, 0xc0, 0x82, 0xcc, 0x11,
  0x16, 0x91, 0x3e, 0xca, 0xc0, 0xc0, 0x60, 0x45, 0x91, 0x89, 0x8c, 0x0c,
  0x47, 0xde, 0xbe, 0x7e, 0x6a, 0x0b, 0xe3, 0x32, 0xa1, 0x49, 0xfe, 0xa3,
  0xc8, 0x70, 0x06, 0x06, 0x06, 0x86, 0x7f, 0x0c, 0x28, 0x95, 0x3c, 0xe3,
  0x68, 0xa5, 0x4f, 0x08, 0x00, 0x00, 0x63, 0x4d, 0x0e, 0x27, 0xf5, 0x37,
  0xcd, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42,
  0x60, 0x82
};
unsigned int Minimize_png_len = 194;
