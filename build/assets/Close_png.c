unsigned char Close_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x13, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xd5, 0x95, 0x31, 0x4b, 0x03, 0x41, 0x10,
  0x46, 0xdf, 0x67, 0xa1, 0x76, 0xb1, 0x39, 0xd1, 0x46, 0x62, 0x65, 0x9f,
  0xbf, 0x20, 0xfe, 0x6c, 0x91, 0x54, 0x2a, 0x12, 0x04, 0x45, 0x12, 0x21,
  0x60, 0xc0, 0x4e, 0x10, 0xec, 0x2c, 0x2c, 0x3e, 0x9b, 0x4d, 0x38, 0x8f,
  0xbd, 0xbb, 0xd9, 0xc8, 0x09, 0x0e, 0x4c, 0x71, 0xec, 0xce, 0x7b, 0xdc,
  0xcd, 0x0c, 0x27, 0xdb, 0x0c, 0x19, 0x3b, 0x83, 0xd2, 0xff, 0x5c, 0x20,
  0x69, 0x2a, 0x69, 0x21, 0xe9, 0xa4, 0x14, 0x24, 0xa9, 0x92, 0x34, 0x93,
  0x74, 0x2f, 0x49, 0x59, 0x01, 0xf0, 0x05, 0x9c, 0x01, 0x57, 0x25, 0x12,
  0x49, 0x15, 0x70, 0x09, 0x4c, 0x00, 0xbb, 0xde, 0x58, 0xdb, 0x9b, 0x04,
  0x46, 0xc0, 0x35, 0x60, 0x60, 0x05, 0x9c, 0xd6, 0xcf, 0x73, 0x09, 0x54,
  0xc0, 0x43, 0xaa, 0x99, 0x03, 0xc7, 0x3f, 0xce, 0x33, 0x05, 0x23, 0xe0,
  0x26, 0x22, 0xe9, 0x83, 0x67, 0x05, 0x51, 0x49, 0x04, 0xde, 0x2a, 0xe8,
  0x93, 0x44, 0xe1, 0x9d, 0x82, 0x16, 0xc9, 0xb8, 0x04, 0xde, 0x2b, 0xc8,
  0x48, 0x5e, 0x81, 0x45, 0x14, 0x1e, 0x12, 0xd4, 0x24, 0xb3, 0x04, 0x36,
  0xb0, 0x8c, 0xc0, 0x6d, 0x87, 0x37, 0x79, 0x17, 0xd8, 0xaf, 0x3d, 0xef,
  0xa5, 0xec, 0x8f, 0xc2, 0x39, 0x7f, 0xae, 0xbd, 0xc9, 0x0a, 0x18, 0xff,
  0xea, 0x13, 0xe5, 0x1a, 0x9a, 0x6b, 0xfc, 0x56, 0x82, 0xae, 0x69, 0x29,
  0x91, 0x6c, 0xbf, 0xa1, 0x41, 0x49, 0x04, 0x7e, 0x54, 0xb2, 0x27, 0x9d,
  0x82, 0x12, 0x78, 0x43, 0x72, 0x9b, 0x6a, 0x5e, 0x9a, 0x92, 0xe6, 0xe5,
  0xf5, 0xc5, 0x47, 0xe0, 0x30, 0x32, 0xe7, 0xa9, 0xee, 0x00, 0xb8, 0x4b,
  0xb5, 0x4f, 0x80, 0xda, 0xf6, 0xe0, 0x33, 0x8d, 0xe1, 0xb9, 0xed, 0xb7,
  0x9e, 0x09, 0xdf, 0x84, 0xed, 0x0f, 0xe0, 0x02, 0x98, 0x02, 0xef, 0x4e,
  0x56, 0xd6, 0xa6, 0x21, 0xe3, 0xff, 0xff, 0xf4, 0xbf, 0x01, 0xd7, 0xe6,
  0xd3, 0x20, 0x11, 0xc9, 0xe9, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Close_png_len = 390;
