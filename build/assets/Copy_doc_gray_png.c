unsigned char Copy_doc_gray_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x44, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xed, 0x95, 0x41, 0x4a, 0xc3, 0x40, 0x14,
  0x86, 0xff, 0x37, 0x15, 0x43, 0xd0, 0x0b, 0xa8, 0x0b, 0x17, 0x3d, 0x88,
  0xa2, 0x67, 0x08, 0x88, 0xc7, 0x10, 0x13, 0xba, 0xb0, 0x0c, 0xa1, 0x9b,
  0x12, 0xa4, 0x9b, 0x9c, 0x41, 0x29, 0xe4, 0x0c, 0x15, 0x3d, 0x48, 0x17,
  0x2e, 0xaa, 0x17, 0xa8, 0x1d, 0x1a, 0x6c, 0x9e, 0x0b, 0x93, 0xd2, 0x84,
  0xcc, 0x64, 0x68, 0x5a, 0x57, 0xfe, 0xab, 0x97, 0xe1, 0x4d, 0xbe, 0x61,
  0xbe, 0x19, 0x86, 0x98, 0x19, 0xfb, 0x8c, 0xd8, 0xeb, 0xdf, 0xff, 0x02,
  0x70, 0xd0, 0xd4, 0x10, 0xc7, 0xb1, 0xa3, 0x54, 0x3a, 0x60, 0xa6, 0x5b,
  0x22, 0x9c, 0xe9, 0xfa, 0x98, 0x31, 0x23, 0xe2, 0x67, 0xa5, 0xe6, 0x0f,
  0x52, 0xca, 0xd4, 0x1a, 0xa0, 0x54, 0x3a, 0x00, 0xc8, 0x27, 0x32, 0xf7,
  0xfd, 0xc2, 0xc9, 0x77, 0xdd, 0x63, 0x00, 0x08, 0xac, 0x01, 0x00, 0xdd,
  0x00, 0x40, 0x96, 0x65, 0xd7, 0xbd, 0xde, 0xfd, 0x8b, 0xae, 0x6b, 0x38,
  0x7c, 0xbc, 0x12, 0x42, 0x4c, 0xf2, 0xfe, 0x35, 0xc0, 0xc6, 0xc1, 0x29,
  0x00, 0x2c, 0x97, 0x5f, 0xaf, 0xa6, 0xa6, 0x6e, 0xf7, 0xfc, 0x2d, 0x2f,
  0x4f, 0x36, 0xc7, 0x6d, 0x00, 0x1d, 0x00, 0x90, 0x52, 0x66, 0xa6, 0x26,
  0xcf, 0xf3, 0x56, 0x79, 0x59, 0xda, 0x95, 0xd2, 0x87, 0x49, 0x68, 0x14,
  0x8d, 0xd6, 0x17, 0x46, 0x27, 0xb4, 0x2e, 0x25, 0x40, 0x5b, 0xa1, 0x8d,
  0x80, 0xb6, 0x42, 0xeb, 0x52, 0x75, 0xd0, 0x4a, 0xa8, 0x0d, 0x60, 0x6b,
  0xa1, 0x49, 0x92, 0x74, 0xf2, 0xf2, 0xdb, 0x04, 0xd8, 0x3a, 0xd3, 0xe9,
  0xfb, 0x45, 0x5e, 0x7e, 0x6e, 0x8e, 0x5b, 0x5c, 0x34, 0x73, 0x8a, 0xd3,
  0x25, 0x44, 0xb1, 0x56, 0x1e, 0xef, 0x14, 0x50, 0x84, 0x19, 0x33, 0x21,
  0xf0, 0xb4, 0x58, 0xcc, 0xfb, 0x3b, 0x05, 0x04, 0xc1, 0x9d, 0xf1, 0x50,
  0x57, 0x1d, 0xac, 0x00, 0x20, 0x0c, 0x43, 0xa3, 0x1b, 0x9d, 0x50, 0x1b,
  0xc0, 0x07, 0x00, 0x38, 0xce, 0xd1, 0xa5, 0x69, 0x92, 0x4e, 0x68, 0x5d,
  0x2a, 0x5b, 0xc4, 0x63, 0x80, 0x7c, 0x21, 0xc4, 0x24, 0x8a, 0x46, 0xfa,
  0x55, 0x69, 0x84, 0x36, 0x02, 0x5c, 0xf7, 0xb0, 0xaf, 0x54, 0x0a, 0x9b,
  0xc7, 0xa5, 0x4e, 0x68, 0x5d, 0xe8, 0xff, 0xd1, 0x6f, 0xca, 0x0f, 0xfb,
  0x2b, 0x8d, 0x24, 0xdc, 0x1e, 0x3d, 0x31, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Copy_doc_gray_png_len = 439;
