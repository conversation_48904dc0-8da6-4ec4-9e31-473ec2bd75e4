unsigned char Delete_doc_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x00, 0xe6, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xe5,
  0x95, 0x41, 0x0e, 0xc1, 0x40, 0x18, 0x85, 0x3f, 0x22, 0x6e, 0x40, 0x2c,
  0x74, 0xed, 0x0e, 0xe2, 0x06, 0x2e, 0x56, 0x47, 0x61, 0x63, 0x85, 0x03,
  0x60, 0xa5, 0x2b, 0x16, 0x4e, 0xc0, 0x4a, 0x24, 0x8d, 0x85, 0x9d, 0x5a,
  0xf8, 0xc5, 0x74, 0xd2, 0xe9, 0xcc, 0x98, 0x4a, 0x88, 0x97, 0xfc, 0x99,
  0xcc, 0xeb, 0xdf, 0xf7, 0xde, 0x3f, 0x93, 0xa6, 0xf0, 0x0f, 0x68, 0x02,
  0x23, 0xe0, 0x08, 0x64, 0x52, 0x07, 0x20, 0x96, 0x67, 0xc1, 0x88, 0x15,
  0x61, 0xbd, 0xe2, 0x2a, 0x0c, 0x0e, 0x22, 0xd6, 0x57, 0xb8, 0x01, 0xaf,
  0x49, 0x82, 0xf1, 0x4c, 0xeb, 0xca, 0xe7, 0x50, 0x2f, 0xe0, 0xd6, 0xe4,
  0x8f, 0x41, 0x17, 0x2c, 0xe3, 0x97, 0x2e, 0x06, 0x37, 0x5b, 0xaa, 0x12,
  0x58, 0x27, 0x52, 0x1b, 0x9d, 0x9b, 0xcb, 0xfa, 0x8b, 0x26, 0xa8, 0x14,
  0x5f, 0x63, 0xb0, 0x02, 0x36, 0x40, 0x5b, 0x2a, 0xa1, 0xe0, 0x42, 0x7d,
  0xa0, 0x9f, 0x69, 0x22, 0xfb, 0x9d, 0x54, 0x26, 0x86, 0xa6, 0x7e, 0x6f,
  0x83, 0x16, 0xb0, 0x55, 0xf8, 0x3d, 0xd0, 0x71, 0x31, 0x08, 0xb9, 0x83,
  0x5a, 0xc0, 0xbb, 0xd5, 0x1d, 0x51, 0xc3, 0xd1, 0xf0, 0x2a, 0x26, 0x43,
  0xd9, 0xcf, 0x84, 0x7b, 0x1b, 0x1f, 0xff, 0xd0, 0x2e, 0xb2, 0x76, 0x1d,
  0xc4, 0x23, 0x59, 0x53, 0x8f, 0x40, 0x4c, 0x25, 0xd1, 0xdc, 0x62, 0x12,
  0x01, 0x0b, 0xe9, 0x1d, 0xfb, 0x18, 0xf4, 0x80, 0x33, 0xe6, 0x1f, 0x8d,
  0x5e, 0x27, 0x4b, 0x10, 0x63, 0xba, 0x09, 0x8f, 0xd1, 0x4d, 0xc2, 0xa9,
  0x24, 0xf7, 0x16, 0xff, 0x1d, 0xdc, 0x01, 0x7d, 0x12, 0x67, 0x87, 0x49,
  0x57, 0x74, 0xf9, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae,
  0x42, 0x60, 0x82
};
unsigned int Delete_doc_png_len = 303;
