unsigned char Info_cr_fr_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0b, 0x13, 0x00, 0x00, 0x0b,
  0x13, 0x01, 0x00, 0x9a, 0x9c, 0x18, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52,
  0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 0x00, 0x04, 0x67,
  0x41, 0x4d, 0x41, 0x00, 0x00, 0xb1, 0x8f, 0x0b, 0xfc, 0x61, 0x05, 0x00,
  0x00, 0x02, 0x13, 0x49, 0x44, 0x41, 0x54, 0x78, 0x01, 0xdd, 0x55, 0x4d,
  0x6e, 0xd3, 0x40, 0x14, 0xfe, 0xde, 0x04, 0x09, 0x05, 0x21, 0x64, 0xc6,
  0xa9, 0xd4, 0x24, 0x0b, 0xdc, 0x13, 0x50, 0x4e, 0x40, 0x6e, 0x90, 0xb2,
  0x63, 0x99, 0x9e, 0x80, 0x70, 0x82, 0x90, 0x13, 0xb4, 0x9c, 0xa0, 0xbe,
  0x01, 0xe9, 0x0d, 0x60, 0xc7, 0xd2, 0x4b, 0x76, 0x0d, 0x0b, 0x30, 0x12,
  0xd8, 0xf1, 0xa2, 0x52, 0x00, 0xc9, 0xf3, 0x78, 0x63, 0x3b, 0x51, 0x90,
  0x7f, 0xda, 0x78, 0xd7, 0x7e, 0xd2, 0xc4, 0xb1, 0xdf, 0x37, 0xef, 0xbd,
  0x79, 0x7f, 0x03, 0xdc, 0x75, 0x50, 0x93, 0xd0, 0x11, 0x28, 0xd5, 0x9d,
  0x30, 0xe1, 0xa5, 0x10, 0x8f, 0x85, 0xee, 0x15, 0xbb, 0x02, 0x66, 0x5e,
  0x2a, 0xa8, 0xcb, 0x28, 0xfa, 0xe6, 0xb7, 0x32, 0xe0, 0xba, 0xfd, 0x13,
  0x86, 0xba, 0x00, 0xd8, 0x41, 0x33, 0x96, 0x04, 0x9a, 0xd7, 0x19, 0x52,
  0x55, 0x1f, 0xb5, 0x1e, 0x9e, 0x31, 0xe8, 0x83, 0x55, 0xce, 0xe0, 0x8f,
  0x86, 0xf9, 0xad, 0x49, 0xcd, 0x51, 0x1c, 0x7d, 0x27, 0xbb, 0x4c, 0x4a,
  0x2f, 0x0c, 0xcc, 0xa9, 0x3d, 0x85, 0xd0, 0x3d, 0xe1, 0x5c, 0x68, 0x3d,
  0x98, 0xe1, 0x36, 0xd0, 0xbd, 0xc1, 0x4c, 0xbb, 0x03, 0x96, 0x0d, 0x2b,
  0xdd, 0xeb, 0x4f, 0x6f, 0xe2, 0x3b, 0xba, 0x3f, 0x15, 0x87, 0x56, 0xd9,
  0x9e, 0x5e, 0xff, 0xac, 0x91, 0xec, 0xba, 0xc3, 0xc9, 0x46, 0xb9, 0xe3,
  0x0c, 0x8f, 0x71, 0x4b, 0x58, 0xee, 0xc6, 0x88, 0x0d, 0x6d, 0x2d, 0x51,
  0x08, 0x57, 0x85, 0x27, 0xd3, 0x06, 0xf9, 0x15, 0xea, 0x4e, 0x62, 0xf7,
  0xba, 0xd6, 0x39, 0x67, 0x9b, 0x37, 0xb5, 0xeb, 0x3d, 0x6c, 0x3c, 0x25,
  0xae, 0xf1, 0xaf, 0xf0, 0xbc, 0x4a, 0x89, 0xc8, 0xb2, 0x55, 0x85, 0x24,
  0x0e, 0xcf, 0x49, 0xf2, 0x65, 0x6d, 0xd9, 0xca, 0x2b, 0x19, 0x30, 0xe0,
  0x71, 0xa6, 0x84, 0x78, 0x8e, 0x1a, 0xac, 0xe2, 0xf0, 0xc8, 0xae, 0x3a,
  0x79, 0xca, 0xb8, 0xcc, 0x75, 0x60, 0x54, 0x32, 0x40, 0x4c, 0x5e, 0xce,
  0x4a, 0x03, 0xb4, 0x85, 0xe1, 0x45, 0xa6, 0x0b, 0xf4, 0xbc, 0x24, 0x2b,
  0xe2, 0xc7, 0x4d, 0xfb, 0x9b, 0x72, 0x50, 0xa7, 0xe7, 0x01, 0xf6, 0x83,
  0x87, 0x3d, 0xb1, 0xdb, 0x68, 0x4b, 0xfb, 0xe3, 0x38, 0xfd, 0x67, 0x68,
  0x89, 0x4d, 0x69, 0x4b, 0x21, 0x04, 0x25, 0x03, 0x72, 0xa6, 0xec, 0xa3,
  0x52, 0x78, 0x85, 0x96, 0xe8, 0x74, 0x90, 0xf7, 0x0e, 0xe1, 0x6b, 0xd9,
  0x00, 0xf3, 0xa7, 0xec, 0x49, 0x18, 0xa3, 0x25, 0xa4, 0x88, 0x67, 0xc5,
  0x73, 0x51, 0x32, 0x00, 0xf3, 0xd0, 0x17, 0xed, 0x89, 0x54, 0xc0, 0x48,
  0xeb, 0xea, 0x46, 0xb3, 0x3d, 0x52, 0xcc, 0x9f, 0x12, 0xa4, 0xd1, 0xde,
  0xa0, 0xe8, 0xa3, 0x24, 0xfa, 0xe1, 0xa3, 0x92, 0x24, 0x6d, 0xbe, 0xed,
  0xc6, 0x83, 0x83, 0xfd, 0x46, 0x85, 0x9b, 0x8f, 0x0a, 0xc7, 0x3d, 0x9c,
  0xec, 0xca, 0x3a, 0xbb, 0x2f, 0xbf, 0xd7, 0xd7, 0x5f, 0xba, 0x8f, 0x9e,
  0x3c, 0x95, 0xbf, 0x23, 0xe2, 0xce, 0xeb, 0x6e, 0xf7, 0xf1, 0x9f, 0xf5,
  0xfa, 0xfa, 0x73, 0xa3, 0x72, 0xf1, 0x5c, 0x29, 0xf2, 0xb3, 0xb1, 0xce,
  0x98, 0x4b, 0x23, 0xfe, 0x37, 0x05, 0x2a, 0xef, 0x03, 0x99, 0xa8, 0xef,
  0x84, 0xbc, 0x19, 0xbf, 0xd9, 0xbc, 0x4f, 0xd3, 0xbf, 0x41, 0x92, 0xfc,
  0x0c, 0x72, 0x8f, 0x0f, 0x3d, 0x51, 0x7a, 0x62, 0xf3, 0x65, 0x43, 0x9a,
  0xd3, 0xf8, 0x7d, 0x1c, 0x85, 0xa5, 0xd0, 0xd6, 0x5e, 0x38, 0xf6, 0xa8,
  0xd2, 0xdd, 0x33, 0x22, 0xf2, 0xd0, 0x04, 0x46, 0x62, 0x88, 0x4f, 0x93,
  0x28, 0x5c, 0x54, 0x89, 0x1b, 0xaf, 0xcc, 0xad, 0x21, 0xa8, 0xb1, 0x28,
  0xf2, 0x88, 0x8a, 0x32, 0x84, 0x24, 0x1b, 0x14, 0x64, 0x95, 0x67, 0xd6,
  0x7e, 0x22, 0xc0, 0xbd, 0xc5, 0x3f, 0xaa, 0x93, 0xe4, 0x25, 0xf6, 0x1e,
  0xad, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42,
  0x60, 0x82
};
unsigned int Info_cr_fr_png_len = 638;
