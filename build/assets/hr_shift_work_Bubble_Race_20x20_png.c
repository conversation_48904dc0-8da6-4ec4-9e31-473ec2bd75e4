unsigned char hr_shift_work_Bubble_Race_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x7f, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0xd4, 0x31, 0x6b, 0x54, 0x41, 0x10,
  0xc0, 0xf1, 0xdf, 0x5e, 0x0c, 0xa4, 0x8a, 0x26, 0x82, 0x49, 0x63, 0xa7,
  0x28, 0x89, 0x68, 0x61, 0x21, 0xf9, 0x02, 0x89, 0x8d, 0x42, 0x4c, 0x65,
  0x61, 0x91, 0x5e, 0xfc, 0x00, 0xd6, 0x69, 0x53, 0x68, 0x21, 0xd6, 0x82,
  0x95, 0x60, 0xa1, 0x88, 0x8a, 0xc4, 0x2f, 0x60, 0x17, 0x88, 0x0a, 0x5a,
  0x45, 0x2b, 0x11, 0x02, 0xa7, 0x22, 0x26, 0x92, 0xb1, 0xb8, 0x39, 0x79,
  0xf7, 0xee, 0x05, 0x9f, 0x7a, 0x03, 0xc3, 0xec, 0xce, 0xce, 0xfe, 0x99,
  0xdd, 0xd9, 0x9d, 0x12, 0x11, 0x46, 0x29, 0x9d, 0x91, 0xd2, 0x70, 0xe8,
  0x6f, 0x82, 0x4b, 0x29, 0xb3, 0xb8, 0x84, 0x69, 0xbc, 0xc1, 0xd3, 0x88,
  0xd8, 0x1b, 0x08, 0x8a, 0x88, 0x56, 0x8a, 0x6b, 0xf8, 0x86, 0xa8, 0xe8,
  0x6b, 0x9c, 0x18, 0x88, 0xab, 0x6d, 0xba, 0x88, 0x17, 0xa9, 0x4b, 0x15,
  0xff, 0x05, 0xfc, 0xc4, 0x76, 0x82, 0x17, 0xb0, 0x86, 0xbd, 0x84, 0x8e,
  0x0f, 0x01, 0x71, 0x06, 0xbb, 0xf8, 0x9e, 0xba, 0x8b, 0xf9, 0x5c, 0xbb,
  0x8f, 0x7d, 0x9c, 0xad, 0x25, 0xb0, 0x96, 0x99, 0x5e, 0xee, 0xfb, 0xaa,
  0x45, 0x59, 0xc4, 0x38, 0xae, 0x60, 0x25, 0xc7, 0x8b, 0xb9, 0x76, 0x1a,
  0x1f, 0x23, 0x62, 0xb3, 0x76, 0xad, 0x4f, 0xd2, 0xce, 0xf5, 0x1d, 0xd5,
  0xa2, 0xbc, 0x4f, 0x7b, 0x13, 0x25, 0xc7, 0xef, 0xd2, 0x7e, 0xc6, 0x7c,
  0x29, 0x65, 0x32, 0x22, 0xba, 0x95, 0x3d, 0x27, 0xd3, 0x7e, 0xfa, 0xed,
  0xa9, 0xa4, 0x5f, 0x70, 0xb7, 0x72, 0xe1, 0x77, 0x50, 0x72, 0x6d, 0x35,
  0x7d, 0x0f, 0x31, 0x99, 0xbe, 0x73, 0xf8, 0x80, 0x2f, 0x38, 0xd6, 0x58,
  0x94, 0x0c, 0x9c, 0xc2, 0x54, 0xcd, 0xd7, 0xc1, 0x83, 0x84, 0xfe, 0x48,
  0x50, 0xe8, 0x15, 0xe5, 0xea, 0x81, 0x55, 0xae, 0x41, 0xc6, 0x70, 0x1c,
  0x63, 0x15, 0xe8, 0x2a, 0x9e, 0xe1, 0x15, 0xee, 0xe1, 0xfc, 0xd0, 0xbe,
  0x03, 0x60, 0x37, 0xd0, 0xcd, 0x2c, 0xba, 0xb8, 0xde, 0xfa, 0xbd, 0x36,
  0xc0, 0x56, 0x12, 0xb4, 0x85, 0x75, 0xbd, 0x77, 0x16, 0x58, 0xfe, 0x57,
  0xe0, 0x63, 0x7c, 0xc5, 0x74, 0xce, 0x8f, 0xe6, 0xfc, 0x51, 0x1b, 0x60,
  0x53, 0x73, 0x38, 0x9c, 0x95, 0xdb, 0xc9, 0xf9, 0x4e, 0x02, 0x8f, 0x34,
  0xc4, 0x0e, 0x49, 0x13, 0x70, 0x03, 0xb3, 0xb8, 0x55, 0x4a, 0x59, 0xc0,
  0x6d, 0xcc, 0xe8, 0x7d, 0xc7, 0x3f, 0x4b, 0xc3, 0x91, 0x27, 0xf0, 0xd2,
  0x60, 0x13, 0xd8, 0xc0, 0x44, 0x9b, 0x23, 0xf7, 0x1f, 0xee, 0x80, 0x94,
  0x52, 0x3a, 0x7a, 0x8d, 0xe2, 0x14, 0xde, 0xe2, 0x79, 0x44, 0xec, 0xb7,
  0x49, 0xb0, 0x11, 0xf8, 0x3f, 0x32, 0xf2, 0x8e, 0xfd, 0x0b, 0x9b, 0xd6,
  0xa9, 0x08, 0xcd, 0x93, 0x27, 0xad, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
  0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_shift_work_Bubble_Race_20x20_png_len = 498;
