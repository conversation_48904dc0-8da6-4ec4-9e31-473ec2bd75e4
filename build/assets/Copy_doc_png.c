unsigned char Copy_doc_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x00, 0xac, 0x49, 0x44, 0x41, 0x54, 0x48, 0x89, 0xed,
  0x55, 0x5b, 0x0a, 0x83, 0x30, 0x10, 0x1c, 0x1f, 0xd4, 0x63, 0x59, 0xbc,
  0x9d, 0x39, 0x89, 0xe7, 0x12, 0xd1, 0x43, 0x54, 0xd1, 0x9f, 0x2d, 0x84,
  0x6d, 0xea, 0x74, 0x93, 0x48, 0x7f, 0x1c, 0x08, 0x0b, 0xc9, 0xec, 0x4e,
  0x36, 0x13, 0x12, 0xe0, 0x46, 0x06, 0x34, 0x00, 0x1c, 0x80, 0x09, 0xc0,
  0x7e, 0x32, 0x26, 0xe1, 0x3d, 0xac, 0x02, 0x8e, 0x14, 0xd6, 0xc3, 0x59,
  0x05, 0x46, 0x49, 0xec, 0x08, 0xaf, 0x13, 0xde, 0x68, 0x15, 0x58, 0x25,
  0xb1, 0x24, 0xbc, 0x4a, 0x78, 0x2f, 0xab, 0xc0, 0xbb, 0xf5, 0x28, 0xae,
  0xde, 0x55, 0xc8, 0x50, 0x9d, 0xfc, 0x57, 0x43, 0x69, 0xb7, 0xa9, 0x86,
  0x52, 0x81, 0x54, 0x43, 0xa9, 0x40, 0x8a, 0xa1, 0x41, 0x51, 0xb6, 0x53,
  0x0b, 0x5a, 0x89, 0x8b, 0x3f, 0x59, 0x67, 0x28, 0xac, 0x3b, 0x1e, 0x18,
  0xd9, 0x7a, 0x44, 0xfe, 0xd5, 0xed, 0xa1, 0xae, 0x6e, 0x8e, 0x0e, 0x8a,
  0xb3, 0x45, 0xed, 0xc1, 0xf6, 0x65, 0x5e, 0xa3, 0x92, 0xb8, 0x32, 0x75,
  0x5d, 0x68, 0x96, 0xf8, 0x24, 0x79, 0x41, 0x43, 0x7f, 0xc1, 0xe5, 0x4f,
  0xb3, 0xe5, 0x73, 0xf9, 0x30, 0xf4, 0x46, 0x14, 0x0e, 0xa6, 0x74, 0x6e,
  0xc7, 0x8b, 0xda, 0xd5, 0xe6, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e,
  0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Copy_doc_png_len = 245;
