unsigned char hr_working_position_Line_02_Up_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x54, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xad, 0x94, 0xbd, 0x4b, 0x03, 0x41, 0x10,
  0xc5, 0x7f, 0x23, 0xc1, 0xc2, 0x42, 0x0b, 0xd1, 0x48, 0x40, 0xc4, 0x42,
  0xf0, 0x03, 0x6b, 0xb1, 0x8b, 0x60, 0x6f, 0x2a, 0x2d, 0x04, 0x21, 0xad,
  0x8d, 0x8d, 0x36, 0x56, 0xfe, 0x11, 0x22, 0xa6, 0x12, 0x4b, 0xd1, 0xff,
  0x41, 0xac, 0x04, 0x0b, 0x15, 0xc1, 0x8f, 0xd2, 0x56, 0x09, 0xb1, 0x50,
  0xb1, 0x11, 0x9e, 0xc5, 0xed, 0x9e, 0x9b, 0xc3, 0x24, 0x7b, 0x72, 0x03,
  0xcb, 0x70, 0x6f, 0x6f, 0xe6, 0xde, 0x7b, 0x3b, 0xb7, 0x26, 0x89, 0x22,
  0xa3, 0xaf, 0xd0, 0x6e, 0x61, 0x43, 0x33, 0x1b, 0x33, 0xb3, 0x7a, 0x9e,
  0x62, 0x4b, 0xa2, 0x6e, 0x66, 0xe5, 0x14, 0x94, 0x84, 0x93, 0xbd, 0x0b,
  0x08, 0x98, 0xf2, 0x58, 0xaf, 0x05, 0x2c, 0xbb, 0x9a, 0x4d, 0x8f, 0x85,
  0x92, 0xfb, 0x33, 0x39, 0x26, 0x56, 0x5d, 0x3e, 0xf7, 0xc0, 0xbf, 0x3d,
  0x34, 0xb3, 0x12, 0xb0, 0x02, 0xdc, 0x49, 0x7a, 0xf4, 0x78, 0x29, 0xa2,
  0x70, 0x06, 0xd8, 0x00, 0x3e, 0x80, 0x86, 0xa4, 0xa6, 0xdb, 0xaa, 0x02,
  0xa3, 0xc0, 0x7e, 0x5b, 0x41, 0xe0, 0xc7, 0x1e, 0x89, 0x1f, 0x73, 0x01,
  0x36, 0x0b, 0x7c, 0x3a, 0x5c, 0xc0, 0x33, 0x30, 0xe9, 0xf6, 0x1a, 0x0e,
  0x9b, 0x0e, 0x7d, 0xed, 0xc5, 0x70, 0x0b, 0x18, 0x00, 0x6a, 0x40, 0x19,
  0x38, 0x00, 0x2e, 0xcc, 0xec, 0x08, 0x58, 0x07, 0xae, 0x25, 0x3d, 0x45,
  0x31, 0x74, 0x76, 0xbc, 0x92, 0x78, 0xe4, 0xdf, 0x59, 0x0b, 0x18, 0x37,
  0x81, 0xc5, 0xec, 0xc9, 0x77, 0x3b, 0x94, 0x2a, 0x30, 0x02, 0x9c, 0x06,
  0x1f, 0x3f, 0x01, 0xc6, 0x81, 0x05, 0x60, 0x42, 0xd2, 0x65, 0xb6, 0xa8,
  0x9b, 0x64, 0x3f, 0x12, 0x67, 0x19, 0x45, 0x2d, 0xe0, 0xaa, 0x53, 0xd1,
  0x9f, 0x0c, 0xdd, 0x48, 0xd4, 0xc8, 0x8c, 0x44, 0x4c, 0x74, 0x92, 0x3c,
  0x04, 0x0c, 0x02, 0xc7, 0x79, 0x9a, 0x41, 0xbb, 0xe4, 0x96, 0xcb, 0xc3,
  0x92, 0xee, 0xcd, 0xac, 0x02, 0xbc, 0xe5, 0x6d, 0x68, 0xfe, 0xfa, 0x32,
  0xb3, 0x79, 0xe0, 0x06, 0xb8, 0x05, 0xb6, 0x81, 0xf7, 0xc8, 0x1e, 0x5f,
  0x92, 0x1e, 0xd2, 0xa7, 0xcc, 0xcf, 0xbe, 0x03, 0x7c, 0xf3, 0x3b, 0xc8,
  0xb1, 0xab, 0xee, 0x7b, 0xa4, 0x0c, 0x53, 0xca, 0x09, 0xd3, 0x25, 0xa0,
  0x12, 0xcb, 0x10, 0x38, 0x94, 0xf4, 0xd2, 0x26, 0xb9, 0xa8, 0x28, 0xfc,
  0xc6, 0xfe, 0x01, 0xd0, 0x4a, 0xd5, 0x2d, 0xae, 0x84, 0x6e, 0x61, 0x00,
  0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int hr_working_position_Line_02_Up_20x20_png_len = 455;
