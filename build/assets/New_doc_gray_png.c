unsigned char New_doc_gray_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x02, 0xc4, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xb5, 0x96, 0xc1, 0x4f, 0xd3, 0x70, 0x14,
  0xc7, 0x3f, 0xaf, 0x15, 0x92, 0x0d, 0x97, 0x70, 0x56, 0x34, 0x62, 0x88,
  0x07, 0xe3, 0x09, 0x3d, 0x19, 0xbd, 0x18, 0x34, 0xd1, 0x0b, 0xa0, 0xd9,
  0x5f, 0x80, 0x21, 0x24, 0x5c, 0x64, 0x40, 0xa7, 0xa7, 0xa5, 0x37, 0x6d,
  0xc7, 0x62, 0xa2, 0x09, 0x91, 0xc8, 0x5f, 0x50, 0x35, 0xf3, 0xa2, 0x07,
  0x50, 0x0f, 0xe2, 0xc9, 0xe8, 0xc5, 0xe0, 0x09, 0x18, 0x3a, 0xa2, 0x67,
  0x01, 0xe7, 0xa6, 0x59, 0x9f, 0x87, 0x96, 0x51, 0xd8, 0x06, 0x33, 0x91,
  0x77, 0x69, 0xfb, 0x5e, 0x7f, 0xdf, 0xef, 0xf7, 0x7d, 0xfb, 0xeb, 0x6b,
  0x45, 0x55, 0x39, 0xc8, 0x30, 0x0e, 0x14, 0x1d, 0x38, 0xb4, 0x57, 0xd1,
  0xb6, 0xed, 0xf6, 0x78, 0x3c, 0x31, 0xa8, 0x4a, 0x3f, 0x68, 0x2f, 0x48,
  0x57, 0x50, 0xd1, 0x35, 0x90, 0x8f, 0xaa, 0x92, 0x2f, 0x97, 0xd7, 0xf3,
  0x99, 0x4c, 0xe6, 0x77, 0x33, 0x0c, 0x69, 0x66, 0x91, 0xeb, 0xe6, 0x6e,
  0xa8, 0xe2, 0x00, 0x27, 0xf7, 0x11, 0xb9, 0x0c, 0x6a, 0x59, 0xd6, 0xf8,
  0xb3, 0x96, 0x08, 0x3c, 0xcf, 0x33, 0x0b, 0x85, 0xaf, 0xf7, 0x40, 0xc6,
  0xc3, 0xd4, 0x27, 0x11, 0x9d, 0xf5, 0x7d, 0x63, 0xde, 0x34, 0xab, 0xab,
  0x00, 0xd5, 0xaa, 0x79, 0xc2, 0x30, 0xfc, 0x3e, 0x55, 0xb9, 0x09, 0x9c,
  0x01, 0x10, 0x21, 0x5b, 0x2a, 0x6d, 0xa4, 0x33, 0x99, 0x8c, 0x1f, 0xc5,
  0xab, 0xb3, 0x28, 0x02, 0x5e, 0x51, 0x65, 0xac, 0x5c, 0xde, 0x78, 0xb4,
  0x7b, 0x11, 0xb0, 0x08, 0x2c, 0x7a, 0x9e, 0xf7, 0x70, 0x65, 0xa5, 0x38,
  0x22, 0x42, 0x4e, 0x95, 0x89, 0x58, 0xec, 0xb0, 0x02, 0x56, 0xd3, 0x0e,
  0x42, 0x5b, 0x9e, 0x00, 0x15, 0xe0, 0xaa, 0x65, 0xa5, 0xde, 0xec, 0x63,
  0x0f, 0x00, 0x77, 0xef, 0x66, 0x2f, 0x19, 0x86, 0xf1, 0x12, 0x68, 0x07,
  0x1d, 0xb4, 0xac, 0xf1, 0xfc, 0x56, 0xad, 0xb6, 0x8b, 0x6c, 0xdb, 0x6e,
  0x0f, 0x3d, 0x07, 0xe4, 0x56, 0x23, 0x70, 0xc7, 0xc9, 0xbd, 0x73, 0x9c,
  0xdc, 0xdb, 0xdd, 0xf9, 0xdb, 0xb7, 0x27, 0x5e, 0x6f, 0x5b, 0x2a, 0xae,
  0x6d, 0xdb, 0xed, 0x75, 0x04, 0xf1, 0x78, 0x62, 0x90, 0xe0, 0x81, 0x7e,
  0xfa, 0xf5, 0x6b, 0x7d, 0xa6, 0x89, 0xd8, 0xf3, 0xc0, 0x85, 0x46, 0x85,
  0xee, 0xee, 0xae, 0x69, 0x02, 0xeb, 0x7a, 0xe2, 0xf1, 0x44, 0x7f, 0x1d,
  0x81, 0xaa, 0x0e, 0x84, 0xa7, 0x8f, 0x1b, 0x78, 0xbe, 0x6f, 0x24, 0x93,
  0xc9, 0xaa, 0x88, 0xce, 0x06, 0x58, 0xd4, 0x13, 0x80, 0xf4, 0x02, 0x98,
  0xa6, 0xce, 0xff, 0x2b, 0x78, 0x0d, 0x41, 0x74, 0x2e, 0x94, 0x7b, 0x6e,
  0x2b, 0x17, 0xdd, 0x45, 0x47, 0x00, 0x36, 0x37, 0x63, 0x5f, 0xb7, 0x12,
  0x8e, 0x93, 0x7b, 0x47, 0x60, 0xcb, 0x8e, 0x70, 0x9c, 0x5c, 0x74, 0x6f,
  0x2f, 0x58, 0x56, 0xea, 0x22, 0x80, 0xef, 0xfb, 0x5f, 0x02, 0xcd, 0x72,
  0xb4, 0x41, 0x07, 0x41, 0xc4, 0x62, 0x9b, 0x12, 0xb9, 0x6c, 0xc5, 0xaa,
  0x1a, 0x59, 0x2c, 0x16, 0x13, 0x00, 0xd5, 0xed, 0x75, 0xd1, 0x0e, 0xbe,
  0x01, 0xa7, 0x4c, 0xd3, 0x3c, 0x06, 0x7c, 0x06, 0xd8, 0x52, 0xb6, 0x5b,
  0xb9, 0x65, 0xa5, 0xa2, 0x22, 0x6a, 0x51, 0xa9, 0x54, 0x8e, 0x83, 0x81,
  0x88, 0x7e, 0x6f, 0xd4, 0xc1, 0x07, 0x00, 0xdf, 0xe7, 0x72, 0x0b, 0xaa,
  0x1b, 0x46, 0xb5, 0x6a, 0x5c, 0x01, 0x10, 0x91, 0xf7, 0x75, 0x04, 0xaa,
  0x92, 0x0f, 0x8f, 0x37, 0x3d, 0xcf, 0x33, 0xff, 0x15, 0x3c, 0x5c, 0x33,
  0x14, 0x60, 0xe8, 0xf3, 0x3a, 0x82, 0x72, 0x79, 0x3d, 0x0f, 0x2c, 0x03,
  0x67, 0x56, 0x56, 0x8a, 0x23, 0x4d, 0x70, 0x16, 0x80, 0xba, 0x17, 0x0d,
  0xa0, 0x50, 0x28, 0x8e, 0x8a, 0x70, 0x1a, 0x58, 0xea, 0xec, 0x4c, 0xd4,
  0x08, 0x76, 0x8c, 0x0a, 0xc7, 0x99, 0xba, 0x0e, 0xf2, 0x14, 0xf8, 0x0d,
  0x5c, 0xb3, 0xac, 0xd4, 0xab, 0x56, 0xd4, 0x67, 0xb3, 0xf7, 0xfb, 0x7c,
  0xdf, 0x7f, 0x01, 0xb4, 0xa9, 0xca, 0x40, 0x3a, 0x3d, 0xd6, 0x98, 0x00,
  0xc0, 0x75, 0x73, 0xae, 0x2a, 0x13, 0x01, 0x89, 0x8c, 0x77, 0x77, 0x77,
  0x4d, 0x27, 0x93, 0xc9, 0x6a, 0x23, 0xe0, 0x60, 0xf2, 0x16, 0x47, 0x81,
  0x2c, 0xd0, 0x26, 0x82, 0x33, 0x39, 0x99, 0x4a, 0x47, 0xef, 0xa9, 0x9b,
  0xa6, 0xa5, 0xd2, 0x46, 0x3a, 0x98, 0x8a, 0x32, 0x09, 0xfa, 0xa0, 0x50,
  0x28, 0x8e, 0xb8, 0xee, 0xd4, 0xac, 0x88, 0xce, 0xfd, 0xfc, 0x19, 0x5f,
  0x05, 0xe8, 0xe8, 0x28, 0x9d, 0x08, 0x1f, 0xe8, 0x50, 0x68, 0x0b, 0x22,
  0x38, 0xa5, 0xd2, 0xc6, 0x9d, 0xdd, 0x78, 0x4d, 0x3f, 0x38, 0x8e, 0x33,
  0x35, 0x00, 0xe2, 0x02, 0x3d, 0x7b, 0xb8, 0x03, 0xb0, 0xa4, 0x2a, 0x13,
  0x51, 0x5b, 0x5a, 0x22, 0x00, 0x98, 0x99, 0x99, 0x69, 0xfb, 0xf1, 0x63,
  0x73, 0x40, 0x95, 0x7e, 0x11, 0xce, 0xaa, 0xd2, 0x15, 0xaa, 0x5d, 0x53,
  0xe5, 0x03, 0x68, 0xbe, 0xb3, 0x33, 0xf1, 0x7c, 0x78, 0x78, 0xf8, 0x4f,
  0x33, 0x8c, 0x3d, 0x09, 0xfe, 0x47, 0x1c, 0xf8, 0x5f, 0xc5, 0x5f, 0x3b,
  0x93, 0x33, 0xc8, 0xbc, 0x31, 0x8a, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int New_doc_gray_png_len = 823;
