unsigned char icon_log_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x21,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x57, 0xe4, 0xc2, 0x6f, 0x00, 0x00, 0x00,
  0x04, 0x73, 0x42, 0x49, 0x54, 0x08, 0x08, 0x08, 0x08, 0x7c, 0x08, 0x64,
  0x88, 0x00, 0x00, 0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e,
  0xc4, 0x00, 0x00, 0x0e, 0xc4, 0x01, 0x95, 0x2b, 0x0e, 0x1b, 0x00, 0x00,
  0x00, 0x19, 0x74, 0x45, 0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61,
  0x72, 0x65, 0x00, 0x77, 0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63,
  0x61, 0x70, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00,
  0x00, 0x02, 0x49, 0x49, 0x44, 0x41, 0x54, 0x58, 0x85, 0xdd, 0xd7, 0x3f,
  0x68, 0x14, 0x41, 0x14, 0xc7, 0xf1, 0x4f, 0x62, 0x08, 0x42, 0xfc, 0x03,
  0x46, 0x83, 0x56, 0x9a, 0x32, 0x28, 0x16, 0xf1, 0xbf, 0x82, 0x62, 0x63,
  0x23, 0xda, 0xa8, 0x8d, 0x28, 0xd8, 0x89, 0x90, 0xc6, 0xd2, 0xc2, 0x46,
  0x10, 0x1b, 0xad, 0x2c, 0x05, 0x0b, 0xb1, 0xf2, 0x5f, 0xa7, 0x58, 0x08,
  0x5a, 0x58, 0x08, 0xa2, 0x58, 0x08, 0x6a, 0x61, 0xa1, 0x20, 0xc6, 0x44,
  0x92, 0x60, 0x4c, 0x62, 0x34, 0x24, 0x16, 0xb3, 0x47, 0xc6, 0xcd, 0xdd,
  0xee, 0x9c, 0xb7, 0xa6, 0xf0, 0x07, 0x0f, 0x76, 0xe7, 0xde, 0xbc, 0xf7,
  0xdd, 0xb7, 0xb3, 0x33, 0xef, 0x60, 0xae, 0xc0, 0xfa, 0x34, 0xa7, 0x35,
  0xb8, 0x86, 0xf1, 0x3a, 0xb1, 0xde, 0xe1, 0x0c, 0xda, 0xb0, 0x16, 0xef,
  0x23, 0xab, 0x0c, 0x62, 0x33, 0x86, 0x4a, 0xe2, 0xcd, 0xe1, 0x36, 0xd6,
  0xc7, 0x63, 0x1d, 0x25, 0x81, 0xdb, 0x13, 0x01, 0xd6, 0xe1, 0x91, 0x50,
  0x09, 0x98, 0xc5, 0x2d, 0x3c, 0xc0, 0x20, 0x7a, 0x70, 0x00, 0xc7, 0x71,
  0x14, 0x13, 0xf9, 0x00, 0x45, 0xd4, 0x1b, 0x13, 0x21, 0xee, 0x45, 0x73,
  0xc6, 0xb1, 0xaf, 0x81, 0xdf, 0x36, 0x8c, 0xd4, 0xc9, 0xd3, 0x32, 0x44,
  0x9f, 0xf0, 0xe4, 0xb5, 0x39, 0x27, 0x4a, 0xfc, 0x0f, 0xd5, 0x83, 0x98,
  0xad, 0x33, 0x58, 0xb3, 0x4d, 0x09, 0x10, 0x97, 0x22, 0xff, 0x37, 0xc2,
  0xc2, 0x2b, 0xd3, 0xb3, 0x38, 0x4f, 0x7b, 0x8d, 0xa4, 0x81, 0x52, 0x02,
  0xee, 0x89, 0xae, 0xef, 0x96, 0xc4, 0xab, 0xe9, 0x4e, 0x7c, 0x53, 0x05,
  0x44, 0x5c, 0xad, 0x97, 0x09, 0xfe, 0x0b, 0xfc, 0x52, 0x57, 0x7f, 0x23,
  0xb5, 0x63, 0x65, 0x74, 0x3f, 0x98, 0x38, 0xef, 0x73, 0x3e, 0x48, 0x2b,
  0x95, 0x58, 0xea, 0xcf, 0x07, 0x99, 0x4e, 0x84, 0x98, 0xaa, 0x12, 0x62,
  0x12, 0x33, 0xd1, 0xfd, 0xb2, 0x44, 0x88, 0xe5, 0xcd, 0x40, 0xa4, 0x68,
  0x28, 0xba, 0xde, 0x90, 0x38, 0xa7, 0x37, 0x0f, 0x51, 0xa4, 0x94, 0x85,
  0x19, 0x2f, 0xb2, 0x9d, 0x89, 0x10, 0xbb, 0xf2, 0x03, 0x3f, 0x34, 0xde,
  0x27, 0xfa, 0x13, 0x02, 0x9e, 0x8d, 0xfc, 0x87, 0xd1, 0x55, 0xe2, 0xdf,
  0x89, 0x0f, 0xb9, 0x3c, 0xa6, 0x0a, 0x20, 0xb6, 0x24, 0x40, 0x74, 0xe7,
  0x62, 0x5c, 0x2e, 0xf1, 0x3f, 0x5f, 0x27, 0x4f, 0xcb, 0x10, 0x70, 0x21,
  0x37, 0xef, 0xa2, 0xf0, 0xc4, 0xb1, 0x3a, 0x70, 0x4e, 0xd8, 0xa1, 0xf3,
  0xd5, 0x37, 0x59, 0x00, 0xb1, 0x35, 0x11, 0xa2, 0x13, 0x4f, 0x73, 0x73,
  0xbf, 0xe0, 0x26, 0xae, 0xe0, 0x06, 0x3e, 0x65, 0xe3, 0xb3, 0x18, 0xc8,
  0x43, 0x4c, 0x14, 0x40, 0x6c, 0x4b, 0x84, 0x80, 0x15, 0x78, 0x58, 0x10,
  0x6b, 0x2e, 0xab, 0xc0, 0x69, 0xe1, 0x2b, 0xaa, 0x8d, 0xfd, 0x84, 0xef,
  0x15, 0x41, 0xd4, 0x74, 0x4c, 0xe8, 0x23, 0xbe, 0x65, 0x31, 0x66, 0x84,
  0x85, 0x78, 0xd5, 0xfc, 0xa9, 0xdc, 0x1f, 0xe5, 0x18, 0x2e, 0x83, 0xd8,
  0xfe, 0x17, 0x10, 0xb1, 0xba, 0xb0, 0xa4, 0xce, 0xf8, 0xc9, 0x28, 0xc7,
  0xb3, 0x0e, 0xad, 0x1f, 0x60, 0x45, 0x5a, 0xd0, 0x41, 0x65, 0x3a, 0x1c,
  0x5d, 0x3f, 0x67, 0xbe, 0x6c, 0xf5, 0x2c, 0x75, 0xf3, 0x69, 0x46, 0x3b,
  0xf0, 0x2b, 0xca, 0xb1, 0xbf, 0x4a, 0x88, 0x01, 0xe5, 0x8d, 0xf1, 0x5e,
  0xe1, 0x04, 0xad, 0xc5, 0x7f, 0x85, 0xb6, 0x2a, 0x5f, 0xc7, 0x11, 0x61,
  0xf1, 0xbd, 0x10, 0x9a, 0xde, 0xd7, 0x18, 0x13, 0xd6, 0x45, 0xaf, 0xd0,
  0xe8, 0xee, 0x8f, 0xfc, 0xa7, 0x71, 0xaa, 0x96, 0x7f, 0x4c, 0xe3, 0x4a,
  0xec, 0x6e, 0x02, 0xe2, 0x71, 0x41, 0x9c, 0xbc, 0x7d, 0x95, 0x6b, 0x86,
  0x47, 0x2b, 0x82, 0x38, 0x88, 0xeb, 0x16, 0x9e, 0x0b, 0xb1, 0xbd, 0x15,
  0x76, 0xd7, 0x55, 0xf1, 0xc4, 0xb2, 0xff, 0x1d, 0xcd, 0xbc, 0x8e, 0xfb,
  0x99, 0xc9, 0x92, 0xf4, 0x60, 0xb5, 0xd0, 0xf8, 0x8c, 0xe2, 0xa3, 0x6c,
  0x4f, 0xc8, 0xeb, 0x5f, 0x7d, 0xa2, 0x23, 0x99, 0x25, 0xa9, 0x8a, 0x46,
  0xb7, 0x65, 0xb5, 0x09, 0x8b, 0xa4, 0xbb, 0xc1, 0xef, 0x4f, 0x34, 0x28,
  0xe1, 0x62, 0x42, 0x2c, 0x8a, 0xaa, 0xe8, 0x31, 0xff, 0x0f, 0x88, 0xdf,
  0x0c, 0x17, 0x2c, 0x9b, 0x42, 0xdc, 0x79, 0xb5, 0x00, 0x00, 0x00, 0x00,
  0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int icon_log_png_len = 716;
