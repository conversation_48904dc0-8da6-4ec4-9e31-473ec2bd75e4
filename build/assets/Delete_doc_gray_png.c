unsigned char Delete_doc_gray_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
  0x08, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x77, 0x3d, 0xf8, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0e, 0xc3, 0x00, 0x00, 0x0e,
  0xc3, 0x01, 0xc7, 0x6f, 0xa8, 0x64, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x01, 0x8e, 0x49,
  0x44, 0x41, 0x54, 0x48, 0x89, 0xc5, 0x94, 0xbb, 0x4e, 0x02, 0x41, 0x14,
  0x40, 0xcf, 0x2e, 0x8b, 0x0a, 0x09, 0x1f, 0x20, 0xd1, 0xde, 0x42, 0xa3,
  0x89, 0xb1, 0xd1, 0x10, 0x6b, 0x62, 0xfc, 0x04, 0xbf, 0xc1, 0x64, 0x4d,
  0x66, 0xa9, 0x24, 0x68, 0x37, 0xc5, 0x7c, 0x00, 0xad, 0x9d, 0x76, 0x26,
  0x6a, 0x2c, 0xe8, 0xd0, 0x8a, 0x92, 0x42, 0x6b, 0x8d, 0x9d, 0x85, 0x5a,
  0x6c, 0x22, 0xb0, 0xd7, 0x82, 0x25, 0x21, 0xb0, 0xc0, 0x88, 0x10, 0x6f,
  0xb5, 0x7b, 0x5f, 0x67, 0xee, 0x63, 0xc6, 0x11, 0x11, 0xe6, 0x29, 0xee,
  0x5c, 0xb3, 0x03, 0xde, 0x24, 0x87, 0x4a, 0xa5, 0xb2, 0x90, 0xcd, 0xe6,
  0xce, 0x45, 0x38, 0x02, 0x96, 0x63, 0xf5, 0x1b, 0x70, 0x11, 0x86, 0x5f,
  0xa7, 0xe5, 0x72, 0xf9, 0xfb, 0x4f, 0x80, 0x4c, 0x26, 0x77, 0x26, 0x82,
  0x1a, 0x50, 0xe7, 0x81, 0x20, 0x93, 0xc9, 0x01, 0x94, 0xc6, 0xc5, 0xdb,
  0xb4, 0xe8, 0x08, 0x40, 0x84, 0x3d, 0xa5, 0x7c, 0x47, 0x29, 0xdf, 0x11,
  0x91, 0x42, 0xbf, 0xed, 0xaf, 0x80, 0x3c, 0x40, 0x10, 0xf8, 0x8f, 0x3d,
  0x45, 0x10, 0x9c, 0xd4, 0xfb, 0x6d, 0xe3, 0xc4, 0x19, 0xdc, 0x22, 0xad,
  0xcd, 0x03, 0xb0, 0x6b, 0x01, 0x4e, 0x92, 0xba, 0x52, 0x7e, 0xa1, 0x5f,
  0x91, 0x54, 0x41, 0x34, 0x65, 0x72, 0x80, 0xa1, 0x9d, 0x1f, 0xaa, 0x00,
  0x40, 0x6b, 0x23, 0x00, 0x4a, 0xf9, 0x8e, 0x4d, 0xd6, 0x71, 0xfe, 0x73,
  0xbf, 0x07, 0xff, 0x7f, 0xd1, 0x00, 0xb4, 0x36, 0x75, 0x60, 0xd1, 0x75,
  0xa3, 0x03, 0x00, 0x11, 0xf7, 0x56, 0x84, 0x70, 0x70, 0xa0, 0x53, 0x03,
  0x1c, 0x87, 0x25, 0x11, 0xb6, 0xa3, 0xc8, 0xad, 0xc5, 0xaa, 0x75, 0xa0,
  0x61, 0x13, 0x6b, 0x05, 0x68, 0xb7, 0xd3, 0xc5, 0x54, 0xaa, 0x55, 0x03,
  0x36, 0x62, 0xe0, 0x13, 0x74, 0x0e, 0x6d, 0x62, 0xa7, 0x9e, 0x41, 0xab,
  0x95, 0xb6, 0xda, 0x30, 0x2b, 0x80, 0xe7, 0xb5, 0xee, 0xe8, 0x9e, 0xbe,
  0x09, 0x34, 0x45, 0x58, 0xf3, 0xbc, 0xe8, 0xda, 0x2a, 0xd6, 0xc6, 0x49,
  0x84, 0x10, 0x68, 0xf4, 0x86, 0x1c, 0x45, 0xee, 0x0d, 0x10, 0xce, 0x0c,
  0x90, 0xb0, 0x2d, 0x3b, 0x36, 0x71, 0x30, 0xba, 0x45, 0x5f, 0x00, 0x5a,
  0xeb, 0x95, 0x49, 0x09, 0x8c, 0x31, 0xab, 0xf1, 0xe7, 0xc7, 0x6f, 0x00,
  0xf1, 0x3a, 0xa6, 0xab, 0xe3, 0x20, 0xc6, 0x98, 0xd5, 0x76, 0x9b, 0x6a,
  0xf7, 0x4f, 0xee, 0x93, 0x7c, 0x46, 0xb4, 0x28, 0x55, 0x82, 0xce, 0x3e,
  0x48, 0x11, 0xbc, 0x17, 0xad, 0xcd, 0x28, 0x46, 0x2c, 0xf2, 0x0e, 0x9d,
  0x93, 0x24, 0x4b, 0x62, 0x05, 0x4a, 0x1d, 0x3f, 0x7b, 0x1e, 0x9b, 0xc0,
  0x95, 0x08, 0x9f, 0x23, 0xd3, 0x0a, 0x9f, 0x20, 0x97, 0xd0, 0xd9, 0x52,
  0x4a, 0xbd, 0x26, 0xf9, 0x24, 0xbe, 0xa6, 0xb3, 0x94, 0xb9, 0x3f, 0x76,
  0x3f, 0x6b, 0x83, 0x89, 0x8b, 0x43, 0x96, 0xf2, 0x78, 0x00, 0x00, 0x00,
  0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
unsigned int Delete_doc_gray_png_len = 513;
