unsigned char hr_physical_strain_local_Dumbbell_20x20_png[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x14,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x8d, 0x89, 0x1d, 0x0d, 0x00, 0x00, 0x00,
  0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0c, 0x4d, 0x00, 0x00, 0x0c,
  0x4d, 0x01, 0xd2, 0xce, 0xad, 0x4e, 0x00, 0x00, 0x00, 0x19, 0x74, 0x45,
  0x58, 0x74, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x00, 0x77,
  0x77, 0x77, 0x2e, 0x69, 0x6e, 0x6b, 0x73, 0x63, 0x61, 0x70, 0x65, 0x2e,
  0x6f, 0x72, 0x67, 0x9b, 0xee, 0x3c, 0x1a, 0x00, 0x00, 0x00, 0xb0, 0x49,
  0x44, 0x41, 0x54, 0x38, 0x8d, 0xed, 0x94, 0xc1, 0x0d, 0xc2, 0x30, 0x0c,
  0x45, 0xdf, 0xcf, 0x1c, 0x39, 0x74, 0x0a, 0xe0, 0x00, 0x4b, 0xc1, 0x3a,
  0x88, 0x01, 0xba, 0x06, 0x17, 0x22, 0x71, 0xec, 0x12, 0xbd, 0x74, 0x05,
  0x73, 0xc0, 0x54, 0x51, 0x1b, 0x41, 0x10, 0x3d, 0x70, 0xa8, 0x25, 0xcb,
  0x92, 0xfd, 0xf3, 0xed, 0x6f, 0x4b, 0x91, 0x99, 0xb1, 0xa4, 0x85, 0x45,
  0xd9, 0x56, 0x42, 0x00, 0x24, 0x45, 0x49, 0x1f, 0x1b, 0x49, 0x0a, 0x92,
  0xe2, 0xac, 0x60, 0x66, 0xa3, 0x03, 0x27, 0xc0, 0x80, 0x73, 0x9e, 0x2f,
  0x39, 0x70, 0x71, 0xec, 0x31, 0xcf, 0x4f, 0x27, 0xd9, 0x7a, 0xdc, 0x55,
  0xa8, 0xdb, 0x94, 0xb0, 0x8b, 0xef, 0xf0, 0x35, 0x7e, 0x03, 0x5c, 0x81,
  0x81, 0xa7, 0x8c, 0xde, 0xf3, 0x07, 0x20, 0x01, 0x77, 0xf7, 0x04, 0xec,
  0xbd, 0xd6, 0x3b, 0x76, 0xf0, 0xb7, 0x4d, 0x49, 0xf2, 0xac, 0xdf, 0x37,
  0xb3, 0x8d, 0x13, 0x66, 0x8b, 0x6e, 0xbd, 0xd0, 0x55, 0x1c, 0xa5, 0x73,
  0x6c, 0xfb, 0xee, 0x28, 0x3f, 0xdb, 0x94, 0x30, 0x79, 0xbc, 0x55, 0xbc,
  0x2d, 0x63, 0x0b, 0x52, 0x22, 0x10, 0x2a, 0x24, 0x07, 0x20, 0x4e, 0xf3,
  0x5a, 0xbf, 0xaf, 0xff, 0x23, 0x7c, 0x00, 0x9a, 0xe5, 0xb0, 0x36, 0xd0,
  0x96, 0x70, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae,
  0x42, 0x60, 0x82
};
unsigned int hr_physical_strain_local_Dumbbell_20x20_png_len = 291;
