<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/amcore" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/amdialogs" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/ameliteui" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/amloglite" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/amui" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/amuitable" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/backward-cpp" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/cpp-httplib" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/emsdk" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/googletest" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/imgui" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/json" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/mINI" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/poco" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/sha-2" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/stb" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/dependencies/zlib" vcs="Git" />
  </component>
</project>