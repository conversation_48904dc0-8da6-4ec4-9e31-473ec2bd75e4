//
// Created by <PERSON><PERSON><PERSON> on 18.12.21.
//

#include "MenuView.h"
#include "imgui/imgui.h"
#include "App.h"
#include "amui//AMUIAgenda.h"
#include "ameliteui/AMEliteController.h"
#include "amuitable/AMUITableAcl.h"
#include "generated/tables.h"
//#include "generated/acl_operations.h"

void MenuView::render()
{
    AMEliteUI::AMEliteController* ec = (menu->app->focusedController())
        ? dynamic_cast<AMEliteUI::AMEliteController*>(menu->app->focusedController())
        : nullptr;
    bool opened = false;

    if (ImGui::BeginMainMenuBar())
    {
        if (ImGui::BeginMenu("Agendy"))
        {
            if (AMAcl(SAW_tables::people, AMUITableAclOperation::acl_agenda, App::instance().openedCustomer())) {
                if (ImGui::MenuItem("Osoby", nullptr)) {
                    menu->app->runPeople();
                }
            } else {
                ImGui::MenuItem("Osoby", nullptr, false, false);
            }
            if (AMAcl(SAW_tables::user_degrees, AMUITableAclOperation::acl_agenda, App::instance().openedCustomer())) {
                if (ImGui::MenuItem("Tituly osob", nullptr)) {
                    menu->app->runUserDegrees();
                }
            } else {
                ImGui::MenuItem("Tituly osob", nullptr, false, false);
            }
            if (AMAcl(SAW_tables::categories, AMUITableAclOperation::acl_agenda, App::instance().openedCustomer())) {
                if (ImGui::MenuItem("Kategorie", nullptr)) {
                    menu->app->runCategories();
                }
            } else {
                ImGui::MenuItem("Kategorie", nullptr, false, false);
            }
            ImGui::Separator();
            if(ImGui::MenuItem("Logger", nullptr)) {
                menu->app->runLogger();
            }
#ifdef DEBUG
            ImGui::MenuItem("Demo ImGUI", NULL, &menu->app->showTestWindow);
#endif
            ImGui::EndMenu();
        }
        if (!(ec && ec->onMenuOperation(&opened))) {
            if (!m_lastFocusedControllerOperation) {
                ImGui::BeginMenu("Operace", false);
            } else {
                if (!ec) {
                    m_lastFocusedControllerOperation->onMenuOperation(&opened);
                }
            }
        } else {
            m_lastFocusedControllerOperation = ec;
        }
        if (!opened) {
            m_lastFocusedControllerOperation = nullptr;
        }
        if (ImGui::BeginMenu("Okno"))
        {
            ImGui::MenuItem("Zobraz detail v agendě", NULL, &AMUI::AMUIAgenda::showAgendaDetail);
            if (ImGui::BeginMenu("Panel agend")) {
                if (ImGui::MenuItem("Vpravo", nullptr, App::instance().m_taskBarPlacement == AMEliteUI::AMAppTaskBarPlacement_right)) {
                    App::instance().m_taskBarPlacement = AMEliteUI::AMAppTaskBarPlacement_right;
                }
                if (ImGui::MenuItem("Dole", nullptr, App::instance().m_taskBarPlacement == AMEliteUI::AMAppTaskBarPlacement_bottom)) {
                    App::instance().m_taskBarPlacement = AMEliteUI::AMAppTaskBarPlacement_bottom;
                }
                if (ImGui::MenuItem("Nikde", nullptr, App::instance().m_taskBarPlacement == AMEliteUI::AMAppTaskBarPlacement_none)) {
                    App::instance().m_taskBarPlacement = AMEliteUI::AMAppTaskBarPlacement_none;
                }
                ImGui::EndMenu();
            }
            ImGui::Separator();
            int i=1000;
            for(AMUI::AMUIController* c: menu->app->controllers()) {
                char name[512];
                if (c->subTitle() == "") {
                    snprintf(name, sizeof(name) -1, "%s##%i", c->title().c_str(), i);
                } else {
                    snprintf(name, sizeof(name) -1, "%s - %s##%i", c->title().c_str(), c->subTitle().c_str(), i);
                }
                i++;
                if (ImGui::MenuItem(name, nullptr)) {
                    AMUI::AMUIController* mc = App::instance().maximizedController();
                    if (mc && mc->isMaximized()) {
                        c->bringToForeground();
                        c->setMaximized(true);
                    } else {
                        c->bringToForeground();
                    }
                }
            }

            ImGui::EndMenu();
        }
        if (!(ec && ec->onMenuRecord(&opened))) {
            if (!m_lastFocusedControllerRecord) {
                ImGui::BeginMenu("Záznam", false);
            } else {
                if (!ec) {
                    if (std::find(menu->app->controllers().begin(), menu->app->controllers().end(),m_lastFocusedControllerRecord) != menu->app->controllers().end()) {
                        m_lastFocusedControllerRecord->onMenuRecord(&opened);
                    } else {
                        m_lastFocusedControllerRecord = nullptr;
                    }

                }
            }
        } else {
            m_lastFocusedControllerRecord = ec;
        }
        if (!opened) {
            m_lastFocusedControllerRecord = nullptr;
        }
        if (ImGui::BeginMenu("Účet"))
        {
            if(ImGui::BeginMenu("Odhlásit")) {
                if(ImGui::MenuItem(menu->app->session().domain.c_str(), nullptr/*, menu->app->openedCustomer() == ""*/)) {
                    menu->app->logout(menu->app->session().domain);
                }
                ImGui::Separator();
                for(auto it:menu->app->session().customers) {
                    if(ImGui::MenuItem(it.second.domain.c_str(), nullptr/*, menu->app->openedCustomer() == it.second.domain*/)) {
                        menu->app->logout(it.second.domain);
                    }
                }
                ImGui::EndMenu();
            }
            ImGui::Separator();
            if(ImGui::BeginMenu("Přepnout učet")) {
                if(ImGui::MenuItem(menu->app->session().domain.c_str(), nullptr, menu->app->openedCustomer() == "")) {
                    menu->app->switchOpenedCustomer(menu->app->session().domain);
                }
                ImGui::Separator();
                for(auto it:menu->app->session().customers) {
                    if(ImGui::MenuItem(it.second.domain.c_str(), nullptr, menu->app->openedCustomer() == it.second.domain)) {
                        menu->app->switchOpenedCustomer(it.second.domain);
                    }
                }
                ImGui::EndMenu();
            }
#ifdef DEBUG
            ImGui::Separator();
            if (ImGui::MenuItem("Exit", nullptr)) {
                App::instance().destroy();
            }
#endif
            ImGui::EndMenu();

        }
        ImGui::EndMainMenuBar();
    }

}


MenuView::MenuView()
    : menu(nullptr),
    m_lastFocusedControllerOperation(nullptr),
    m_lastFocusedControllerRecord(nullptr)
{
};

void MenuView::Init(Menu* _menu)
{
    menu = _menu;
}
