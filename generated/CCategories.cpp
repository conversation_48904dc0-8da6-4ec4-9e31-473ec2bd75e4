/* automatically generated file, do not edit. */

#ifndef __EMSCRIPTEN__

#include "CCategories.h"

CCategories::CCategories(int id)
    : AMSvr::AMSvrDbObject<int>(id),
      name(),
      weight(),
      paid(),
      supercategory(),
      note(),
      deletedAt(),
      modifiedBy(),
      createdAt(),
      updatedAt()
    {
    };

CCategories::CCategories()
    : AMSvr::AMSvrDbObject<int>(),
      name(),
      weight(),
      paid(),
      supercategory(),
      note(),
      deletedAt(),
      modifiedBy(),
      createdAt(),
      updatedAt()
    {
    };

AMSvr::AMSvrDbObjectContext<int> *CCategories::g_context = AMSvr::AMSvrDbObjectContext<int>::create<CCategories>(
    "dbo.categories",
    {
        {&CCategories::id, "id", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategories::name, "name", AMCore::AMDataType_string, {}, nullptr},
        {&CCategories::weight, "weight", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategories::paid, "paid", AMCore::AMDataType_bool, {}, nullptr},
        {&CCategories::supercategory, "supercategory", AMCore::AMDataType_integer_nullable, {}, nullptr},
        {&CCategories::note, "note", AMCore::AMDataType_string, {}, nullptr},
        {&CCategories::deletedAt, "deleted_at", AMCore::AMDataType_datetimeus_nullable, {}, nullptr},
        {&CCategories::modifiedBy, "modified_by", AMCore::AMDataType_integer, {}, nullptr},
        {&CCategories::createdAt, "created_at", AMCore::AMDataType_datetimeus, {}, nullptr},
        {&CCategories::updatedAt, "updated_at", AMCore::AMDataType_datetimeus, {}, nullptr},
    }
    );

AMSvr::AMSvrDbObjectContext<int> &CCategories::context()
{
    return *g_context;
}

#endif // __EMSCRIPTEN__
