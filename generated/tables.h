/* automatically generated file, do not edit. */

#ifndef SAW_GENERATED_TABLES_H
#define SAW_GENERATED_TABLES_H
        
enum SAW_tables {
        user_degrees=1,
    employers=2,
    employer_statutories=3,
    users=4,
    people=5,
    people_data=6,
    centres=7,
    connected_accounts=8,
    professions=9,
    employer_osvc=10,
    objects=11,
    workplaces=12,
    profession_timeshots=13,
    categorizations=14,
    profession_precautions=15,
    occupational_hazards=16,
    risk_factors_working_conds=17,
    profession_riskfactor_clarifies=18,
    legal_req_for_med_fitness=19,
    profession_requirement_cs=20,
    profession_education_areas=21,
    providers_wms=22,
    files=23,
    medical_checks=24,
    pdfs=25,
    personal_risks=26,
    healthy_restrictions=27,
    examinations=28,
    med_conclusions=29,
    wms_specifics_2=30,
    working_injuries=31,
    working_injuries_data=32,
    working_injuries_records=33,
    categories=34,
    tables_last=34
};

#endif // SAW_GENERATED_TABLES_H
        
