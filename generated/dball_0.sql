CREATE SCHEMA dbo;

COMMENT ON SCHEMA dbo IS 'Normal objects';

CREATE SCHEMA dbh;

COMMENT ON SCHEMA dbh IS 'History objects';

CREATE SCHEMA dbe;

COMMENT ON SCHEMA dbe IS 'Enumeration objects';

CREATE SCHEMA dbs;

COMMENT ON SCHEMA dbs IS 'System objects';

CREATE SCHEMA dbv;

COMMENT ON SCHEMA dbv IS 'Views';

CREATE SCHEMA dbc;

COMMENT ON SCHEMA dbc IS 'Caches'
CREATE TABLE dbe.tables
(
    id        SERIAL CONSTRAINT dbe_tables_pk PRIMARY KEY,
    title     VARCHAR(128) NOT NULL,
    enum      VARCHAR(32)  NOT NULL
);

INSERT INTO dbe.tables (id, title, enum) VALUES (1, 'Tituly osob', 'user_degrees');
INSERT INTO dbe.tables (id, title, enum) VALUES (2, 'Uživatelé', 'users');
CREATE TABLE dbh.heartbeat
(
    id SERIAL CONSTRAINT dbh_heartbeat_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    table_id INT NOT NULL CONSTRAINT dbh_heartbeat_table_id_dbe_tables_id_fk REFERENCES dbe.tables(id),
    row INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE UNIQUE INDEX dbh_heartbeat_table_id_rowid_uindex ON dbh.heartbeat (table_id, row);
CREATE FUNCTION trigger_update_modified_at_timestamp()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
BEGIN
    NEW.modified_at = now();
    RETURN NEW;
END;
$$;

CREATE FUNCTION trigger_audit_table_foreign_keys(tablename TEXT, newd RECORD)
    RETURNS TEXT
    LANGUAGE PLPGSQL
AS $$
DECLARE
    fkrow RECORD;
    fkid INT := -1;
    gsql TEXT;
    nkeys JSONB;
BEGIN
    FOR fkrow IN
        SELECT
            tc.table_name,
            kcu.column_name,
            ccu.table_schema AS foreign_table_schema,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                     ON tc.constraint_name = kcu.constraint_name
                         AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                     ON ccu.constraint_name = tc.constraint_name
                         AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = tablename AND tc.table_schema = 'dbo' AND ccu.table_schema = 'dbo'
        LOOP
            EXECUTE 'SELECT MAX(id) FROM dbh.'|| fkrow.foreign_table_name ||' WHERE dbh.'|| fkrow.foreign_table_name ||'.record_id = $1.'|| fkrow.column_name USING newd INTO fkid;
            nkeys[fkrow.column_name] = fkid;
        END LOOP;
    gsql = (SELECT string_agg(CASE WHEN nkeys ? column_name THEN nkeys[column_name]::text WHEN column_name = 'modified_at' THEN format('''%s''', now()) ELSE format('$1.%s', column_name) END, ',') q
            FROM information_schema.columns
            WHERE table_schema = 'dbo'
              AND table_name = tablename);
    RETURN gsql;
END;
$$;

CREATE FUNCTION trigger_audit_table_delete()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''D'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, OLD) ||')' USING OLD;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('D', tbl_id, OLD.id)
    ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'D';
    RETURN OLD;
END;
$$;

CREATE FUNCTION trigger_audit_table_insert()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''I'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, NEW) ||')' USING NEW;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('I', tbl_id, NEW.id)
    ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'I';
    RETURN NEW;
END;
$$;

CREATE FUNCTION trigger_audit_table_update()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    tbl TEXT := 'dbh.' || TG_TABLE_NAME;
    tbl_id INT := -1;
BEGIN
    tbl_id = (SELECT id FROM dbe.tables WHERE enum = TG_TABLE_NAME);
    EXECUTE 'INSERT INTO ' || tbl || ' VALUES (DEFAULT, ''U'', '|| trigger_audit_table_foreign_keys(TG_TABLE_NAME, NEW) ||')' USING NEW;
    INSERT INTO dbh.heartbeat (operation, table_id, row) VALUES ('I', tbl_id, NEW.id)
        ON CONFLICT (table_id, row) DO UPDATE SET modified_at = NOW(), operation = 'U';
    RETURN NEW;
END
$$;


CREATE TABLE dbo.user_degrees
(
    id SERIAL CONSTRAINT dbo_user_degrees_pk PRIMARY KEY,
    degree VARCHAR(32) NOT NULL,
    after_name BOOL NOT NULL,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_user_degrees_after_name_index ON dbo.user_degrees (after_name);

CREATE UNIQUE INDEX dbo_user_degrees_degree_uindex ON dbo.user_degrees (degree, after_name);

CREATE TRIGGER dbt_user_degrees_changemodified BEFORE UPDATE
    ON dbo.user_degrees FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.user_degrees
(
    id SERIAL CONSTRAINT dbh_user_degrees_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    record_id INT NOT NULL,
    degree VARCHAR(32) NOT NULL,
    after_name BOOL NOT NULL,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_user_degrees_after_name_index ON dbh.user_degrees (after_name);

CREATE INDEX dbh_user_degrees_record_id_index ON dbh.user_degrees (record_id);

CREATE INDEX dbh_user_degrees_modified_at_index ON dbh.user_degrees (modified_at);

CREATE TRIGGER dbt_user_degrees_delete
    AFTER DELETE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_user_degrees_insert
    AFTER INSERT
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_user_degrees_update
    AFTER UPDATE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();

CREATE FUNCTION trigger_user_degrees_prevent_change_position()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
BEGIN
    IF (NEW.after_name <> OLD.after_name)
    THEN
        RAISE EXCEPTION 'Position of degree cannot be changed';
    END IF;
    RETURN NEW;
END
$$;

CREATE TRIGGER dbt_user_degrees_update_before
    BEFORE UPDATE
    ON dbo.user_degrees FOR EACH ROW
EXECUTE FUNCTION trigger_user_degrees_prevent_change_position();
INSERT INTO dbo.user_degrees (id, degree, after_name, note, modified_by, modified_at)
VALUES  (1, '', false, 'Bez titulu.', 0, '2024-01-01 00:00:00.000000'),
        (2, '', true, 'Bez titulu.', 0, '2024-01-01 00:00:00.000000'),
        (3, 'Bc.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (4, 'BcA.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (5, 'Ing.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (6, 'Ing. arch.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (7, 'MUDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (8, 'MDDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (9, 'MVDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (10, 'MgA.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (11, 'Mgr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (12, 'JUDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (13, 'PhDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (14, 'RNDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (15, 'PharmDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (16, 'ThLic.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (17, 'ThDr.', false, ' ', 0, '2024-01-01 00:00:00.000000'),
        (18, 'Ph.D.', true, ' ', 0, '2024-01-01 00:00:00.000000'),
        (19, 'DSc.', true, ' ', 0, '2024-01-01 00:00:00.000000'),
        (20, 'CSc.', true, ' ', 0, '2024-01-01 00:00:00.000000'),
        (21, 'Dr.', true, ' ', 0, '2024-01-01 00:00:00.000000'),
        (22, 'DrSc.', true, ' ', 0, '2024-01-01 00:00:00.000000'),
        (23, 'Th.D', true, ' ', 0, '2024-01-01 00:00:00.000000');

ALTER SEQUENCE dbo.user_degrees_id_seq RESTART WITH 24;
CREATE TABLE dbe.roles
(
    id    SERIAL CONSTRAINT dbe_roles_pk PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    enum  VARCHAR(32)  NOT NULL
);

INSERT INTO dbe.roles (id, title, enum) VALUES (1, 'Admin', 'r_admin');
INSERT INTO dbe.roles (id, title, enum) VALUES (2, 'Učitel', 'r_teacher');
INSERT INTO dbe.roles (id, title, enum) VALUES (3, 'Student', 'r_student');

CREATE TABLE dbe.citizenships
(
    id                          SERIAL CONSTRAINT dbe_citizenships_pk PRIMARY KEY,
    title                       VARCHAR(128) NOT NULL,
    enum                        VARCHAR(32)  NOT NULL,
    country_code_N3             INTEGER      NOT NULL,
    country_code_A2             VARCHAR(2)   NOT NULL,
    country_code_A3             VARCHAR(3)   NOT NULL,
    name_cz_full                VARCHAR(128) NOT NULL,
    name_eng_full               VARCHAR(128) NOT NULL,
    ctz_country_name_eng_short  VARCHAR(128) NOT NULL
);

INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (1, 'Afghánistán', 'country_AFG', 4, 'AF', 'AFG', 'Afghánská islámská republika', 'the Islamic Republic of Afghanistan', 'Afghanistan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (2, 'Alandy', 'country_ALA', 248, 'AX', 'ALA', 'Provincie Alandy', 'Åland Islands', 'Åland Islands');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (3, 'Albánie', 'country_ALB', 8, 'AL', 'ALB', 'Albánská republika', 'the Republic of Albania', 'Albania');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (4, 'Alžírsko', 'country_DZA', 12, 'DZ', 'DZA', 'Alžírská demokratická a lidová republika', 'the People''s Democratic Republic of Algeria', 'Algeria');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (5, 'Americká Samoa', 'country_ASM', 16, 'AS', 'ASM', 'Území Americká Samoa', 'American Samoa', 'American Samoa');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (6, 'Americké Panenské ostrovy', 'country_VIR', 850, 'VI', 'VIR', 'Americké Panenské ostrovy', 'the Virgin Islands of the United States', 'Virgin Islands (U.S.)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (7, 'Andorra', 'country_AND', 20, 'AD', 'AND', 'Andorrské knížectví', 'the Principality of Andorra', 'Andorra');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (8, 'Angola', 'country_AGO', 24, 'AO', 'AGO', 'Angolská republika', 'the Republic of Angola', 'Angola');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (9, 'Anguilla', 'country_AIA', 660, 'AI', 'AIA', 'Anguilla', 'Anguilla', 'Anguilla');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (10, 'Antarktida', 'country_ATA', 10, 'AQ', 'ATA', 'Antarktida', 'Antarctica', 'Antarctica');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (11, 'Antigua a Barbuda', 'country_ATG', 28, 'AG', 'ATG', 'Antigua a Barbuda', 'Antigua and Barbuda', 'Antigua and Barbuda');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (12, 'Argentina', 'country_ARG', 32, 'AR', 'ARG', 'Argentinská republika', 'the Argentine Republic', 'Argentina');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (13, 'Arménie', 'country_ARM', 51, 'AM', 'ARM', 'Arménská republika', 'the Republic of Armenia', 'Armenia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (14, 'Aruba', 'country_ABW', 533, 'AW', 'ABW', 'Aruba', 'Aruba', 'Aruba');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (15, 'Austrálie', 'country_AUS', 36, 'AU', 'AUS', 'Australské společenství', 'Australia', 'Australia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (16, 'Ázerbájdžán', 'country_AZE', 31, 'AZ', 'AZE', 'Ázerbájdžánská republika', 'the Republic of Azerbaijan', 'Azerbaijan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (17, 'Bahamy', 'country_BHS', 44, 'BS', 'BHS', 'Bahamské společenství', 'the Commonwealth of the Bahamas', 'Bahamas (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (18, 'Bahrajn', 'country_BHR', 48, 'BH', 'BHR', 'Království Bahrajn', 'the Kingdom of Bahrain', 'Bahrain');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (19, 'Bangladéš', 'country_BGD', 50, 'BD', 'BGD', 'Bangladéšská lidová republika', 'the People''s Republic of Bangladesh', 'Bangladesh');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (20, 'Barbados', 'country_BRB', 52, 'BB', 'BRB', 'Barbados', 'Barbados', 'Barbados');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (21, 'Belgie', 'country_BEL', 56, 'BE', 'BEL', 'Belgické království', 'the Kingdom of Belgium', 'Belgium');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (22, 'Belize', 'country_BLZ', 84, 'BZ', 'BLZ', 'Belize', 'Belize', 'Belize');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (23, 'Bělorusko', 'country_BLR', 112, 'BY', 'BLR', 'Běloruská republika', 'the Republic of Belarus', 'Belarus');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (24, 'Benin', 'country_BEN', 204, 'BJ', 'BEN', 'Beninská republika', 'the Republic of Benin', 'Benin');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (25, 'Bermudy', 'country_BMU', 60, 'BM', 'BMU', 'Bermudy', 'Bermuda', 'Bermuda');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (26, 'Bhútán', 'country_BTN', 64, 'BT', 'BTN', 'Bhútánské království', 'the Kingdom of Bhutan', 'Bhutan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (27, 'Bolívie', 'country_BOL', 68, 'BO', 'BOL', 'Mnohonárodní stát Bolívie', 'the Plurinational State of Bolivia', 'Bolivia (Plurinational State of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (28, 'Bonaire, Svatý Eustach a Saba', 'country_BES', 535, 'BQ', 'BES', 'Bonaire, Svatý Eustach a Saba', 'Bonaire, Sint Eustatius and Saba', 'Bonaire, Sint Eustatius and Saba');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (29, 'Bosna a Hercegovina', 'country_BIH', 70, 'BA', 'BIH', 'Bosna a Hercegovina', 'Bosnia and Herzegovina', 'Bosnia and Herzegovina');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (30, 'Botswana', 'country_BWA', 72, 'BW', 'BWA', 'Botswanská republika', 'the Republic of Botswana', 'Botswana');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (31, 'Bouvetův ostrov', 'country_BVT', 74, 'BV', 'BVT', 'Bouvetův ostrov', 'Bouvet Island', 'Bouvet Island');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (32, 'Brazílie', 'country_BRA', 76, 'BR', 'BRA', 'Brazilská federativní republika', 'the Federative Republic of Brazil', 'Brazil');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (33, 'Britské indickooceánské území', 'country_IOT', 86, 'IO', 'IOT', 'Britské území v Indickém oceánu', 'British Indian Ocean Territory (the)', 'British Indian Ocean Territory (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (34, 'Britské Panenské ostrovy', 'country_VGB', 92, 'VG', 'VGB', 'Britské Panenské ostrovy', 'British Virgin Islands (the)', 'Virgin Islands (British)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (35, 'Brunej', 'country_BRN', 96, 'BN', 'BRN', 'Stát Brunej Darussalam', 'Brunei Darussalam', 'Brunei Darussalam');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (36, 'Bulharsko', 'country_BGR', 100, 'BG', 'BGR', 'Bulharská republika', 'the Republic of Bulgaria', 'Bulgaria');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (37, 'Burkina Faso', 'country_BFA', 854, 'BF', 'BFA', 'Burkina Faso', 'Burkina Faso', 'Burkina Faso');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (38, 'Burundi', 'country_BDI', 108, 'BI', 'BDI', 'Burundská republika', 'the Republic of Burundi', 'Burundi');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (39, 'Cookovy ostrovy', 'country_COK', 184, 'CK', 'COK', 'Cookovy ostrovy', 'Cook Islands (the)', 'Cook Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (40, 'Curaçao', 'country_CUW', 531, 'CW', 'CUW', 'Země Curaçao', 'Curaçao', 'Curaçao');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (41, 'Čad', 'country_TCD', 148, 'TD', 'TCD', 'Čadská republika', 'the Republic of Chad', 'Chad');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (42, 'Černá Hora', 'country_MNE', 499, 'ME', 'MNE', 'Černá Hora', 'Montenegro', 'Montenegro');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (43, 'Česko', 'country_CZE', 203, 'CZ', 'CZE', 'Česká republika', 'the Czech Republic', 'Czechia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (44, 'Čína', 'country_CHN', 156, 'CN', 'CHN', 'Čínská lidová republika', 'the People''s Republic of China', 'China');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (45, 'Dánsko', 'country_DNK', 208, 'DK', 'DNK', 'Dánské království', 'the Kingdom of Denmark', 'Denmark');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (46, 'Dominika', 'country_DMA', 212, 'DM', 'DMA', 'Dominické společenství', 'the Commonwealth of Dominica', 'Dominica');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (47, 'Dominikánská republika', 'country_DOM', 214, 'DO', 'DOM', 'Dominikánská republika', 'the Dominican Republic', 'Dominican Republic (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (48, 'Džibutsko', 'country_DJI', 262, 'DJ', 'DJI', 'Džibutská republika', 'the Republic of Djibouti', 'Djibouti');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (49, 'Egypt', 'country_EGY', 818, 'EG', 'EGY', 'Egyptská arabská republika', 'the Arab Republic of Egypt', 'Egypt');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (50, 'Ekvádor', 'country_ECU', 218, 'EC', 'ECU', 'Ekvádorská republika', 'the Republic of Ecuador', 'Ecuador');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (51, 'Eritrea', 'country_ERI', 232, 'ER', 'ERI', 'Stát Eritrea', 'the State of Eritrea', 'Eritrea');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (52, 'Estonsko', 'country_EST', 233, 'EE', 'EST', 'Estonská republika', 'the Republic of Estonia', 'Estonia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (53, 'Etiopie', 'country_ETH', 231, 'ET', 'ETH', 'Etiopská federativní demokratická republika', 'the Federal Democratic Republic of Ethiopia', 'Ethiopia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (54, 'Faerské ostrovy', 'country_FRO', 234, 'FO', 'FRO', 'Faerské ostrovy', 'Faroe Islands (the)', 'Faroe Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (55, 'Falklandy (Malvíny)', 'country_FLK', 238, 'FK', 'FLK', 'Falklandy (Malvíny)', 'Falkland Islands (the) (Malvinas)', 'Falkland Islands (the) (Malvinas)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (56, 'Fidži', 'country_FJI', 242, 'FJ', 'FJI', 'Fidžijská republika', 'the Republic of Fiji', 'Fiji');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (57, 'Filipíny', 'country_PHL', 608, 'PH', 'PHL', 'Filipínská republika', 'the Republic of the Philippines', 'Philippines (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (58, 'Finsko', 'country_FIN', 246, 'FI', 'FIN', 'Finská republika', 'the Republic of Finland', 'Finland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (59, 'Francie', 'country_FRA', 250, 'FR', 'FRA', 'Francouzská republika', 'the French Republic', 'France');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (60, 'Francouzská Guyana', 'country_GUF', 254, 'GF', 'GUF', 'Francouzská Guyana', 'French Guiana', 'French Guiana');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (61, 'Francouzská jižní a antarktická území', 'country_ATF', 260, 'TF', 'ATF', 'Francouzská jižní a antarktická území', 'French Southern Territories (the)', 'French Southern Territories (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (62, 'Francouzská Polynésie', 'country_PYF', 258, 'PF', 'PYF', 'Francouzská Polynésie', 'French Polynesia', 'French Polynesia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (63, 'Gabon', 'country_GAB', 266, 'GA', 'GAB', 'Gabonská republika', 'the Gabonese Republic', 'Gabon');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (64, 'Gambie', 'country_GMB', 270, 'GM', 'GMB', 'Gambijská republika', 'the Republic of the Gambia', 'Gambia (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (65, 'Ghana', 'country_GHA', 288, 'GH', 'GHA', 'Ghanská republika', 'the Republic of Ghana', 'Ghana');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (66, 'Gibraltar', 'country_GIB', 292, 'GI', 'GIB', 'Gibraltar', 'Gibraltar', 'Gibraltar');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (67, 'Grenada', 'country_GRD', 308, 'GD', 'GRD', 'Grenada', 'Grenada', 'Grenada');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (68, 'Grónsko', 'country_GRL', 304, 'GL', 'GRL', 'Grónsko', 'Greenland', 'Greenland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (69, 'Gruzie', 'country_GEO', 268, 'GE', 'GEO', 'Gruzie', 'Georgia', 'Georgia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (70, 'Guadeloupe', 'country_GLP', 312, 'GP', 'GLP', 'Region Guadeloupe', 'Guadeloupe', 'Guadeloupe');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (71, 'Guam', 'country_GUM', 316, 'GU', 'GUM', 'Teritorium Guam', 'Guam', 'Guam');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (72, 'Guatemala', 'country_GTM', 320, 'GT', 'GTM', 'Guatemalská republika', 'the Republic of Guatemala', 'Guatemala');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (73, 'Guernsey', 'country_GGY', 831, 'GG', 'GGY', 'Bailiwick Guernsey', 'Guernsey', 'Guernsey');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (74, 'Guinea', 'country_GIN', 324, 'GN', 'GIN', 'Guinejská republika', 'the Republic of Guinea', 'Guinea');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (75, 'Guinea-Bissau', 'country_GNB', 624, 'GW', 'GNB', 'Republika Guinea-Bissau', 'the Republic of Guinea-Bissau', 'Guinea-Bissau');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (76, 'Guyana', 'country_GUY', 328, 'GY', 'GUY', 'Guyanská kooperativní republika', 'the Co-operative Republic of Guyana', 'Guyana');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (77, 'Haiti', 'country_HTI', 332, 'HT', 'HTI', 'Republika Haiti', 'the Republic of Haiti', 'Haiti');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (78, 'Heardův ostrov a MacDonaldovy ostrovy', 'country_HMD', 334, 'HM', 'HMD', 'Heardův ostrov a MacDonaldovy ostrovy', 'Heard Island and McDonald Islands', 'Heard Island and McDonald Islands');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (79, 'Honduras', 'country_HND', 340, 'HN', 'HND', 'Honduraská republika', 'the Republic of Honduras', 'Honduras');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (80, 'Hongkong', 'country_HKG', 344, 'HK', 'HKG', 'Zvláštní administrativní oblast Čínské lidové republiky Hongkong', 'the Hong Kong Special Administrative Region of China', 'Hong Kong');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (81, 'Chile', 'country_CHL', 152, 'CL', 'CHL', 'Chilská republika', 'the Republic of Chile', 'Chile');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (82, 'Chorvatsko', 'country_HRV', 191, 'HR', 'HRV', 'Chorvatská republika', 'the Republic of Croatia', 'Croatia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (83, 'Indie', 'country_IND', 356, 'IN', 'IND', 'Indická republika', 'the Republic of India', 'India');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (84, 'Indonésie', 'country_IDN', 360, 'ID', 'IDN', 'Indonéská republika', 'the Republic of Indonesia', 'Indonesia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (85, 'Irák', 'country_IRQ', 368, 'IQ', 'IRQ', 'Irácká republika', 'the Republic of Iraq', 'Iraq');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (86, 'Írán', 'country_IRN', 364, 'IR', 'IRN', 'Íránská islámská republika', 'the Islamic Republic of Iran', 'Iran (Islamic Republic of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (87, 'Irsko', 'country_IRL', 372, 'IE', 'IRL', 'Irsko', 'Ireland', 'Ireland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (88, 'Island', 'country_ISL', 352, 'IS', 'ISL', 'Islandská republika', 'Iceland', 'Iceland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (89, 'Itálie', 'country_ITA', 380, 'IT', 'ITA', 'Italská republika', 'the Republic of Italy', 'Italy');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (90, 'Izrael', 'country_ISR', 376, 'IL', 'ISR', 'Stát Izrael', 'the State of Israel', 'Israel');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (91, 'Jamajka', 'country_JAM', 388, 'JM', 'JAM', 'Jamajka', 'Jamaica', 'Jamaica');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (92, 'Japonsko', 'country_JPN', 392, 'JP', 'JPN', 'Japonsko', 'Japan', 'Japan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (93, 'Jemen', 'country_YEM', 887, 'YE', 'YEM', 'Jemenská republika', 'the Republic of Yemen', 'Yemen');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (94, 'Jersey', 'country_JEY', 832, 'JE', 'JEY', 'Bailiwick Jersey', 'Jersey', 'Jersey');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (95, 'Jižní Afrika', 'country_ZAF', 710, 'ZA', 'ZAF', 'Jihoafrická republika', 'the Republic of South Africa', 'South Africa');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (96, 'Jižní Georgie a Jižní Sandwichovy ostrovy', 'country_SGS', 239, 'GS', 'SGS', 'Jižní Georgie a Jižní Sandwichovy ostrovy', 'South Georgia and the South Sandwich Islands', 'South Georgia and the South Sandwich Islands');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (97, 'Jižní Súdán', 'country_SSD', 728, 'SS', 'SSD', 'Jihosúdánská republika', 'the Republic of South Sudan', 'South Sudan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (98, 'Jordánsko', 'country_JOR', 400, 'JO', 'JOR', 'Jordánské hášimovské království', 'the Hashemite Kingdom of Jordan', 'Jordan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (99, 'Kajmanské ostrovy', 'country_CYM', 136, 'KY', 'CYM', 'Kajmanské ostrovy', 'Cayman Islands (the)', 'Cayman Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (100, 'Kambodža', 'country_KHM', 116, 'KH', 'KHM', 'Kambodžské království', 'the Kingdom of Cambodia', 'Cambodia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (101, 'Kamerun', 'country_CMR', 120, 'CM', 'CMR', 'Kamerunská republika', 'the Republic of Cameroon', 'Cameroon');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (102, 'Kanada', 'country_CAN', 124, 'CA', 'CAN', 'Kanada', 'Canada', 'Canada');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (103, 'Kapverdy', 'country_CPV', 132, 'CV', 'CPV', 'Kapverdská republika', 'the Republic of Cabo Verde', 'Cabo Verde');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (104, 'Katar', 'country_QAT', 634, 'QA', 'QAT', 'Stát Katar', 'the State of Qatar', 'Qatar');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (105, 'Kazachstán', 'country_KAZ', 398, 'KZ', 'KAZ', 'Republika Kazachstán', 'the Republic of Kazakhstan', 'Kazakhstan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (106, 'Keňa', 'country_KEN', 404, 'KE', 'KEN', 'Keňská republika', 'the Republic of Kenya', 'Kenya');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (107, 'Kiribati', 'country_KIR', 296, 'KI', 'KIR', 'Republika Kiribati', 'the Republic of Kiribati', 'Kiribati');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (108, 'Kokosové (Keelingovy) ostrovy', 'country_CCK', 166, 'CC', 'CCK', 'Území Kokosové (Keelingovy) ostrovy', 'Cocos (Keeling) Islands (the)', 'Cocos (Keeling) Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (109, 'Kolumbie', 'country_COL', 170, 'CO', 'COL', 'Kolumbijská republika', 'the Republic of Colombia', 'Colombia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (110, 'Komory', 'country_COM', 174, 'KM', 'COM', 'Komorský svaz', 'the Union of the Comoros', 'Comoros (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (111, 'Konžská demokratická republika', 'country_COD', 180, 'CD', 'COD', 'Konžská demokratická republika', 'the Democratic Republic of the Congo', 'Congo (the Democratic Republic of the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (112, 'Konžská republika', 'country_COG', 178, 'CG', 'COG', 'Konžská republika', 'the Republic of the Congo', 'Congo (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (113, 'Korejská lidově demokratická republika', 'country_PRK', 408, 'KP', 'PRK', 'Korejská lidově demokratická republika', 'the Democratic People''s Republic of Korea', 'Korea (the Democratic People''s Republic of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (114, 'Korejská republika', 'country_KOR', 410, 'KR', 'KOR', 'Korejská republika', 'the Republic of Korea', 'Korea (the Republic of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (115, 'Kosovo', 'country_XXK', 95, 'XK', 'XXK', 'Kosovská republika', 'the Republic of Kosovo', 'Kosovo');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (116, 'Kostarika', 'country_CRI', 188, 'CR', 'CRI', 'Kostarická republika', 'the Republic of Costa Rica', 'Costa Rica');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (117, 'Kuba', 'country_CUB', 192, 'CU', 'CUB', 'Kubánská republika', 'the Republic of Cuba', 'Cuba');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (118, 'Kuvajt', 'country_KWT', 414, 'KW', 'KWT', 'Kuvajtský stát', 'the State of Kuwait', 'Kuwait');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (119, 'Kypr', 'country_CYP', 196, 'CY', 'CYP', 'Kyperská republika', 'the Republic of Cyprus', 'Cyprus');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (120, 'Kyrgyzstán', 'country_KGZ', 417, 'KG', 'KGZ', 'Kyrgyzská republika', 'the Kyrgyz Republic', 'Kyrgyzstan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (121, 'Laos', 'country_LAO', 418, 'LA', 'LAO', 'Laoská lidově demokratická republika', 'the Lao People''s Democratic Republic', 'Lao People''s Democratic Republic (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (122, 'Lesotho', 'country_LSO', 426, 'LS', 'LSO', 'Lesothské království', 'the Kingdom of Lesotho', 'Lesotho');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (123, 'Libanon', 'country_LBN', 422, 'LB', 'LBN', 'Libanonská republika', 'the Lebanese Republic', 'Lebanon');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (124, 'Libérie', 'country_LBR', 430, 'LR', 'LBR', 'Liberijská republika', 'the Republic of Liberia', 'Liberia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (125, 'Libye', 'country_LBY', 434, 'LY', 'LBY', 'Libyjský stát', 'the State of Libya', 'Libya');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (126, 'Lichtenštejnsko', 'country_LIE', 438, 'LI', 'LIE', 'Lichtenštejnské knížectví', 'the Principality of Liechtenstein', 'Liechtenstein');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (127, 'Litva', 'country_LTU', 440, 'LT', 'LTU', 'Litevská republika', 'the Republic of Lithuania', 'Lithuania');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (128, 'Lotyšsko', 'country_LVA', 428, 'LV', 'LVA', 'Lotyšská republika', 'the Republic of Latvia', 'Latvia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (129, 'Lucembursko', 'country_LUX', 442, 'LU', 'LUX', 'Lucemburské velkovévodství', 'the Grand Duchy of Luxembourg', 'Luxembourg');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (130, 'Macao', 'country_MAC', 446, 'MO', 'MAC', 'Zvláštní administrativní oblast Čínské lidové republiky Macao', 'Macao Special Administrative Region of China', 'Macao');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (131, 'Madagaskar', 'country_MDG', 450, 'MG', 'MDG', 'Madagaskarská republika', 'the Republic of Madagascar', 'Madagascar');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (132, 'Maďarsko', 'country_HUN', 348, 'HU', 'HUN', 'Maďarsko', 'Hungary', 'Hungary');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (133, 'Malajsie', 'country_MYS', 458, 'MY', 'MYS', 'Malajsie', 'Malaysia', 'Malaysia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (134, 'Malawi', 'country_MWI', 454, 'MW', 'MWI', 'Malawiská republika', 'the Republic of Malawi', 'Malawi');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (135, 'Maledivy', 'country_MDV', 462, 'MV', 'MDV', 'Maledivská republika', 'the Republic of Maldives', 'Maldives');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (136, 'Mali', 'country_MLI', 466, 'ML', 'MLI', 'Republika Mali', 'the Republic of Mali', 'Mali');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (137, 'Malta', 'country_MLT', 470, 'MT', 'MLT', 'Maltská republika', 'the Republic of Malta', 'Malta');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (138, 'Man', 'country_IMN', 833, 'IM', 'IMN', 'Ostrov Man', 'Isle of Man', 'Isle of Man');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (139, 'Maroko', 'country_MAR', 504, 'MA', 'MAR', 'Marocké království', 'the Kingdom of Morocco', 'Morocco');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (140, 'Marshallovy ostrovy', 'country_MHL', 584, 'MH', 'MHL', 'Republika Marshallovy ostrovy', 'the Republic of the Marshall Islands', 'Marshall Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (141, 'Martinik', 'country_MTQ', 474, 'MQ', 'MTQ', 'Martinik', 'Martinique', 'Martinique');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (142, 'Mauricius', 'country_MUS', 480, 'MU', 'MUS', 'Mauricijská republika', 'the Republic of Mauritius', 'Mauritius');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (143, 'Mauritánie', 'country_MRT', 478, 'MR', 'MRT', 'Mauritánská islámská republika', 'the Islamic Republic of Mauritania', 'Mauritania');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (144, 'Mayotte', 'country_MYT', 175, 'YT', 'MYT', 'Departement Mayotte', 'Mayotte', 'Mayotte');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (145, 'Menší odlehlé ostrovy USA', 'country_UMI', 581, 'UM', 'UMI', 'Menší odlehlé ostrovy USA', 'United States Minor Outlying Islands (the)', 'United States Minor Outlying Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (146, 'Mexiko', 'country_MEX', 484, 'MX', 'MEX', 'Spojené státy mexické', 'the United Mexican States', 'Mexico');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (147, 'Mikronésie', 'country_FSM', 583, 'FM', 'FSM', 'Federativní státy Mikronésie', 'the Federated States of Micronesia', 'Micronesia (Federated States of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (148, 'Moldavsko', 'country_MDA', 498, 'MD', 'MDA', 'Moldavská republika', 'the Republic of Moldova', 'Moldova (the Republic of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (149, 'Monako', 'country_MCO', 492, 'MC', 'MCO', 'Monacké knížectví', 'the Principality of Monaco', 'Monaco');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (150, 'Mongolsko', 'country_MNG', 496, 'MN', 'MNG', 'Mongolsko', 'Mongolia', 'Mongolia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (151, 'Montserrat', 'country_MSR', 500, 'MS', 'MSR', 'Montserrat', 'Montserrat', 'Montserrat');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (152, 'Mosambik', 'country_MOZ', 508, 'MZ', 'MOZ', 'Mosambická republika', 'the Republic of Mozambique', 'Mozambique');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (153, 'Myanmar', 'country_MMR', 104, 'MM', 'MMR', 'Republika Myanmarský svaz', 'the Republic of the Union of Myanmar', 'Myanmar');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (154, 'Namibie', 'country_NAM', 516, 'NA', 'NAM', 'Namibijská republika', 'the Republic of Namibia', 'Namibia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (155, 'Nauru', 'country_NRU', 520, 'NR', 'NRU', 'Republika Nauru', 'the Republic of Nauru', 'Nauru');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (156, 'Německo', 'country_DEU', 276, 'DE', 'DEU', 'Spolková republika Německo', 'the Federal Republic of Germany', 'Germany');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (157, 'Nepál', 'country_NPL', 524, 'NP', 'NPL', 'Nepálská federativní demokratická republika', 'Nepal', 'Nepal');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (158, 'Niger', 'country_NER', 562, 'NE', 'NER', 'Nigerská republika', 'the Republic of the Niger', 'Niger (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (159, 'Nigérie', 'country_NGA', 566, 'NG', 'NGA', 'Nigerijská federativní republika', 'the Federal Republic of Nigeria', 'Nigeria');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (160, 'Nikaragua', 'country_NIC', 558, 'NI', 'NIC', 'Nikaragujská republika', 'the Republic of Nicaragua', 'Nicaragua');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (161, 'Niue', 'country_NIU', 570, 'NU', 'NIU', 'Niue', 'Niue', 'Niue');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (162, 'Nizozemsko', 'country_NLD', 528, 'NL', 'NLD', 'Nizozemsko', 'the Kingdom of the Netherlands', 'Netherlands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (163, 'Norfolk', 'country_NFK', 574, 'NF', 'NFK', 'Území Norfolk', 'Norfolk Island', 'Norfolk Island');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (164, 'Norsko', 'country_NOR', 578, 'NO', 'NOR', 'Norské království', 'the Kingdom of Norway', 'Norway');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (165, 'Nová Kaledonie', 'country_NCL', 540, 'NC', 'NCL', 'Nová Kaledonie', 'New Caledonia', 'New Caledonia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (166, 'Nový Zéland', 'country_NZL', 554, 'NZ', 'NZL', 'Nový Zéland', 'New Zealand', 'New Zealand');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (167, 'Omán', 'country_OMN', 512, 'OM', 'OMN', 'Sultanát Omán', 'the Sultanate of Oman', 'Oman');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (168, 'Pákistán', 'country_PAK', 586, 'PK', 'PAK', 'Pákistánská islámská republika', 'the Islamic Republic of Pakistan', 'Pakistan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (169, 'Palau', 'country_PLW', 585, 'PW', 'PLW', 'Republika Palau', 'the Republic of Palau', 'Palau');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (170, 'Palestina', 'country_PSE', 275, 'PS', 'PSE', 'Palestinská autonomní území', 'the State of Palestine', 'Palestine, State of');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (171, 'Panama', 'country_PAN', 591, 'PA', 'PAN', 'Panamská republika', 'the Republic of Panama', 'Panama');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (172, 'Papua Nová Guinea', 'country_PNG', 598, 'PG', 'PNG', 'Nezávislý stát Papua Nová Guinea', 'the Independent State of Papua New Guinea', 'Papua New Guinea');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (173, 'Paraguay', 'country_PRY', 600, 'PY', 'PRY', 'Paraguayská republika', 'the Republic of Paraguay', 'Paraguay');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (174, 'Peru', 'country_PER', 604, 'PE', 'PER', 'Peruánská republika', 'the Republic of Peru', 'Peru');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (175, 'Pitcairn', 'country_PCN', 612, 'PN', 'PCN', 'Pitcairnovy ostrovy', 'Pitcairn', 'Pitcairn');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (176, 'Pobřeží slonoviny', 'country_CIV', 384, 'CI', 'CIV', 'Republika Pobřeží slonoviny', 'the Republic of Côte d''Ivoire', 'Côte d''Ivoire');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (177, 'Polsko', 'country_POL', 616, 'PL', 'POL', 'Polská republika', 'the Republic of Poland', 'Poland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (178, 'Portoriko', 'country_PRI', 630, 'PR', 'PRI', 'Portorické společenství', 'Puerto Rico', 'Puerto Rico');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (179, 'Portugalsko', 'country_PRT', 620, 'PT', 'PRT', 'Portugalská republika', 'the Portuguese Republic', 'Portugal');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (180, 'Rakousko', 'country_AUT', 40, 'AT', 'AUT', 'Rakouská republika', 'the Republic of Austria', 'Austria');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (181, 'Réunion', 'country_REU', 638, 'RE', 'REU', 'Region Réunion', 'Réunion', 'Réunion');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (182, 'Rovníková Guinea', 'country_GNQ', 226, 'GQ', 'GNQ', 'Republika Rovníková Guinea', 'the Republic of Equatorial Guinea', 'Equatorial Guinea');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (183, 'Rumunsko', 'country_ROU', 642, 'RO', 'ROU', 'Rumunsko', 'Romania', 'Romania');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (184, 'Rusko', 'country_RUS', 643, 'RU', 'RUS', 'Ruská federace', 'the Russian Federation', 'Russian Federation (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (185, 'Rwanda', 'country_RWA', 646, 'RW', 'RWA', 'Rwandská republika', 'the Republic of Rwanda', 'Rwanda');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (186, 'Řecko', 'country_GRC', 300, 'GR', 'GRC', 'Řecká republika', 'the Hellenic Republic', 'Greece');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (187, 'Saint Pierre a Miquelon', 'country_SPM', 666, 'PM', 'SPM', 'Územní společenství Saint Pierre a Miquelon', 'Saint Pierre and Miquelon', 'Saint Pierre and Miquelon');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (188, 'Salvador', 'country_SLV', 222, 'SV', 'SLV', 'Salvadorská republika', 'the Republic of El Salvador', 'El Salvador');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (189, 'Samoa', 'country_WSM', 882, 'WS', 'WSM', 'Nezávislý stát Samoa', 'the Independent State of Samoa', 'Samoa');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (190, 'San Marino', 'country_SMR', 674, 'SM', 'SMR', 'Republika San Marino', 'the Republic of San Marino', 'San Marino');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (191, 'Saúdská Arábie', 'country_SAU', 682, 'SA', 'SAU', 'Království Saúdská Arábie', 'the Kingdom of Saudi Arabia', 'Saudi Arabia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (192, 'Senegal', 'country_SEN', 686, 'SN', 'SEN', 'Senegalská republika', 'the Republic of Senegal', 'Senegal');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (193, 'Severní Makedonie', 'country_MKD', 807, 'MK', 'MKD', 'Republika Severní Makedonie', 'the Republic of North Macedonia', 'North Macedonia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (194, 'Severní Mariany', 'country_MNP', 580, 'MP', 'MNP', 'Společenství Severní Mariany', 'the Commonwealth of the Northern Mariana Islands', 'Northern Mariana Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (195, 'Seychely', 'country_SYC', 690, 'SC', 'SYC', 'Seychelská republika', 'the Republic of Seychelles', 'Seychelles');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (196, 'Sierra Leone', 'country_SLE', 694, 'SL', 'SLE', 'Republika Sierra Leone', 'the Republic of Sierra Leone', 'Sierra Leone');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (197, 'Singapur', 'country_SGP', 702, 'SG', 'SGP', 'Singapurská republika', 'the Republic of Singapore', 'Singapore');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (198, 'Slovensko', 'country_SVK', 703, 'SK', 'SVK', 'Slovenská republika', 'the Slovak Republic', 'Slovakia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (199, 'Slovinsko', 'country_SVN', 705, 'SI', 'SVN', 'Slovinská republika', 'the Republic of Slovenia', 'Slovenia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (200, 'Somálsko', 'country_SOM', 706, 'SO', 'SOM', 'Somálská federativní republika', 'the Federal Republic of Somalia', 'Somalia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (201, 'Spojené arabské emiráty', 'country_ARE', 784, 'AE', 'ARE', 'Stát Spojené arabské emiráty', 'the United Arab Emirates', 'United Arab Emirates (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (202, 'Spojené státy', 'country_USA', 840, 'US', 'USA', 'Spojené státy americké', 'the United States of America', 'United States of America (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (203, 'Srbsko', 'country_SRB', 688, 'RS', 'SRB', 'Srbská republika', 'the Republic of Serbia', 'Serbia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (204, 'Středoafrická republika', 'country_CAF', 140, 'CF', 'CAF', 'Středoafrická republika', 'the Central African Republic', 'Central African Republic (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (205, 'Súdán', 'country_SDN', 729, 'SD', 'SDN', 'Súdánská republika', 'the Republic of the Sudan', 'Sudan (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (206, 'Surinam', 'country_SUR', 740, 'SR', 'SUR', 'Surinamská republika', 'the Republic of Suriname', 'Suriname');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (207, 'Svatá Helena', 'country_SHN', 654, 'SH', 'SHN', 'Svatá Helena, Ascension a Tristan da Cunha', 'Saint Helena, Ascension and Tristan da Cunha', 'Saint Helena, Ascension and Tristan da Cunha');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (208, 'Svatá Lucie', 'country_LCA', 662, 'LC', 'LCA', 'Svatá Lucie', 'Saint Lucia', 'Saint Lucia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (209, 'Svatý Bartoloměj', 'country_BLM', 652, 'BL', 'BLM', 'Společenství Svatý Bartoloměj', 'Saint Barthélemy', 'Saint Barthélemy');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (210, 'Svatý Kryštof a Nevis', 'country_KNA', 659, 'KN', 'KNA', 'Federace Svatý Kryštof a Nevis', 'Saint Kitts and Nevis', 'Saint Kitts and Nevis');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (211, 'Svatý Martin (FR)', 'country_MAF', 663, 'MF', 'MAF', 'Společenství Svatý Martin', 'Saint Martin (French part)', 'Saint Martin (French part)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (212, 'Svatý Martin (NL)', 'country_SXM', 534, 'SX', 'SXM', 'Svatý Martin (NL)', 'Sint Maarten (Dutch part)', 'Sint Maarten (Dutch part)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (213, 'Svatý Tomáš a Princův ostrov', 'country_STP', 678, 'ST', 'STP', 'Demokratická republika Svatý Tomáš a Princův ostrov', 'the Democratic Republic of Sao Tome and Principe', 'Sao Tome and Principe');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (214, 'Svatý Vincenc a Grenadiny', 'country_VCT', 670, 'VC', 'VCT', 'Svatý Vincenc a Grenadiny', 'Saint Vincent and the Grenadines', 'Saint Vincent and the Grenadines');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (215, 'Svazijsko', 'country_SWZ', 748, 'SZ', 'SWZ', 'Svazijské království', 'the Kingdom of Eswatini', 'Eswatini');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (216, 'Sýrie', 'country_SYR', 760, 'SY', 'SYR', 'Syrská arabská republika', 'the Syrian Arab Republic', 'Syrian Arab Republic (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (217, 'Šalomounovy ostrovy', 'country_SLB', 90, 'SB', 'SLB', 'Šalomounovy ostrovy', 'Solomon Islands', 'Solomon Islands');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (218, 'Španělsko', 'country_ESP', 724, 'ES', 'ESP', 'Španělské království', 'the Kingdom of Spain', 'Spain');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (219, 'Špicberky a Jan Mayen', 'country_SJM', 744, 'SJ', 'SJM', 'Špicberky a Jan Mayen', 'Svalbard and Jan Mayen', 'Svalbard and Jan Mayen');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (220, 'Šrí Lanka', 'country_LKA', 144, 'LK', 'LKA', 'Šrílanská demokratická socialistická republika', 'the Democratic Socialist Republic of Sri Lanka', 'Sri Lanka');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (221, 'Švédsko', 'country_SWE', 752, 'SE', 'SWE', 'Švédské království', 'the Kingdom of Sweden', 'Sweden');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (222, 'Švýcarsko', 'country_CHE', 756, 'CH', 'CHE', 'Švýcarská konfederace', 'the Swiss Confederation', 'Switzerland');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (223, 'Tádžikistán', 'country_TJK', 762, 'TJ', 'TJK', 'Republika Tádžikistán', 'the Republic of Tajikistan', 'Tajikistan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (224, 'Tanzanie', 'country_TZA', 834, 'TZ', 'TZA', 'Tanzanská sjednocená republika', 'the United Republic of Tanzania', 'Tanzania, the United Republic of');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (225, 'Thajsko', 'country_THA', 764, 'TH', 'THA', 'Thajské království', 'the Kingdom of Thailand', 'Thailand');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (226, 'Tchaj-wan', 'country_TWN', 158, 'TW', 'TWN', 'Tchaj-wan', 'Taiwan', 'Taiwan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (227, 'Togo', 'country_TGO', 768, 'TG', 'TGO', 'Tožská republika', 'the Togolese Republic', 'Togo');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (228, 'Tokelau', 'country_TKL', 772, 'TK', 'TKL', 'Tokelau', 'Tokelau', 'Tokelau');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (229, 'Tonga', 'country_TON', 776, 'TO', 'TON', 'Království Tonga', 'the Kingdom of Tonga', 'Tonga');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (230, 'Trinidad a Tobago', 'country_TTO', 780, 'TT', 'TTO', 'Republika Trinidad a Tobago', 'the Republic of Trinidad and Tobago', 'Trinidad and Tobago');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (231, 'Tunisko', 'country_TUN', 788, 'TN', 'TUN', 'Tuniská republika', 'the Republic of Tunisia', 'Tunisia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (232, 'Turecko', 'country_TUR', 792, 'TR', 'TUR', 'Turecká republika', 'the Republic of Türkiye', 'Türkiye');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (233, 'Turkmenistán', 'country_TKM', 795, 'TM', 'TKM', 'Turkmenistán', 'Turkmenistan', 'Turkmenistan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (234, 'Turks a Caicos', 'country_TCA', 796, 'TC', 'TCA', 'Ostrovy Turks a Caicos', 'Turks and Caicos Islands (the)', 'Turks and Caicos Islands (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (235, 'Tuvalu', 'country_TUV', 798, 'TV', 'TUV', 'Tuvalu', 'Tuvalu', 'Tuvalu');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (236, 'Uganda', 'country_UGA', 800, 'UG', 'UGA', 'Ugandská republika', 'the Republic of Uganda', 'Uganda');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (237, 'Ukrajina', 'country_UKR', 804, 'UA', 'UKR', 'Ukrajina', 'Ukraine', 'Ukraine');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (238, 'Uruguay', 'country_URY', 858, 'UY', 'URY', 'Uruguayská východní republika', 'the Eastern Republic of Uruguay', 'Uruguay');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (239, 'Uzbekistán', 'country_UZB', 860, 'UZ', 'UZB', 'Republika Uzbekistán', 'the Republic of Uzbekistan', 'Uzbekistan');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (240, 'Vánoční ostrov', 'country_CXR', 162, 'CX', 'CXR', 'Území Vánoční ostrov', 'Christmas Island', 'Christmas Island');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (241, 'Vanuatu', 'country_VUT', 548, 'VU', 'VUT', 'Republika Vanuatu', 'the Republic of Vanuatu', 'Vanuatu');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (242, 'Vatikán', 'country_VAT', 336, 'VA', 'VAT', 'Vatikánský městský stát', 'Holy See (the)', 'Holy See (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (243, 'Velká Británie a Severní Irsko', 'country_GBR', 826, 'GB', 'GBR', 'Spojené království Velké Británie a Severního Irska', 'the United Kingdom of Great Britain and Northern Ireland', 'United Kingdom of Great Britain and Northern Ireland (the)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (244, 'Venezuela', 'country_VEN', 862, 'VE', 'VEN', 'Bolívarovská republika Venezuela', 'the Bolivarian Republic of Venezuela', 'Venezuela (Bolivarian Republic of)');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (245, 'Vietnam', 'country_VNM', 704, 'VN', 'VNM', 'Vietnamská socialistická republika', 'the Socialist Republic of Viet Nam', 'Viet Nam');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (246, 'Východní Timor', 'country_TLS', 626, 'TL', 'TLS', 'Demokratická republika Východní Timor', 'the Democratic Republic of Timor-Leste', 'Timor-Leste');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (247, 'Wallis a Futuna', 'country_WLF', 876, 'WF', 'WLF', 'Teritorium Wallisovy ostrovy a Futuna', 'Wallis and Futuna Islands', 'Wallis and Futuna');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (248, 'Zambie', 'country_ZMB', 894, 'ZM', 'ZMB', 'Zambijská republika', 'the Republic of Zambia', 'Zambia');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (249, 'Západní Sahara', 'country_ESH', 732, 'EH', 'ESH', 'Saharská arabská demokratická republika', 'Western Sahara', 'Western Sahara');
INSERT INTO dbe.citizenships (id, title, enum, country_code_N3, country_code_A2, country_code_A3, name_cz_full, name_eng_full, ctz_country_name_eng_short) VALUES (250, 'Zimbabwe', 'country_ZWE', 716, 'ZW', 'ZWE', 'Zimbabwská republika', 'the Republic of Zimbabwe', 'Zimbabwe');

CREATE TABLE dbe.sex
(
    id SERIAL CONSTRAINT dbe_sex_pk PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    enum VARCHAR(32) NOT NULL
);

INSERT INTO dbe.sex (id, title, enum) VALUES
    (1, 'Muž', 'male'),
    (2, 'Žena', 'female');

CREATE TABLE dbo.users
(
    id SERIAL CONSTRAINT dbo_users_pk PRIMARY KEY,
    degree_before INT NOT NULL CONSTRAINT dbo_people_degree_before_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL CONSTRAINT dbo_people_degree_after_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    sex INT NOT NULL CONSTRAINT dbo_people_dbe_sex_id_fk REFERENCES dbe.sex(id),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(32) NOT NULL,
    roles INT[] NOT NULL,
    passhash VARCHAR(64) NOT NULL,
    need_update_password BOOLEAN NOT NULL,
    created_at DATE NOT NULL,
    deleted_at DATE,
    paid_until DATE,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_users_id_partial_index ON dbo.users (id) WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_email_partial_index ON dbo.users (email)  WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_name_partial_index ON dbo.users (name) WHERE deleted_at IS NULL;
CREATE INDEX dbo_users_surname_partial_index ON dbo.users (surname)  WHERE deleted_at IS NULL;
/*ALTER TABLE dbo.users ADD CONSTRAINT users_name_uk UNIQUE (name);*/

CREATE TRIGGER dbt_users_changemodified BEFORE UPDATE
    ON dbo.users FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.users
(
    id SERIAL CONSTRAINT dbh_users_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    degree_before INT NOT NULL,
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL,
    sex INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    roles INT[] NOT NULL,
    passhash VARCHAR(64) NOT NULL,
    need_update_password BOOLEAN NOT NULL,
    created_at DATE NOT NULL,
    deleted_at DATE,
    paid_until DATE,
    note TEXT NOT NULL,
    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_users_record_id_index ON dbh.users (record_id);

CREATE INDEX dbh_users_name_index ON dbh.users (name);

CREATE INDEX dbh_users_modified_at_index ON dbh.users (modified_at);


CREATE TABLE dbs.login_attempts
(
    id INT CONSTRAINT dbs_login_attempts_pk PRIMARY KEY,
    attempts INT NOT NULL
);

CREATE TRIGGER dbt_users_delete
    AFTER DELETE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_users_insert
    AFTER INSERT
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_users_update
    AFTER UPDATE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();


CREATE OR REPLACE FUNCTION trigger_users_foreign_key_check_roles()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
    roleid INT;
BEGIN
    FOREACH roleid IN ARRAY NEW.roles
        LOOP
            IF NOT EXISTS (SELECT FROM dbe.roles WHERE id = roleid) THEN
                RAISE EXCEPTION 'Users:roles:foreign key:fail';
            END IF;
        END LOOP;
    RETURN NEW;
END
$$;

CREATE TRIGGER dbt_users_roles_insert
    BEFORE INSERT
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE TRIGGER dbt_users_roles_update
    BEFORE UPDATE
    ON dbo.users FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE OR REPLACE FUNCTION trigger_roles_foreign_key_check_roles()
    RETURNS TRIGGER
    LANGUAGE PLPGSQL
AS $$
DECLARE
BEGIN
    IF EXISTS (SELECT FROM dbo.users WHERE OLD.id = ANY (roles) ) THEN
        RAISE EXCEPTION 'Roles:users:foreign key:fail';
    END IF;
    RETURN NEW;
END
$$;


CREATE TRIGGER dbt_roles_roles_delete
    BEFORE DELETE
    ON dbe.roles FOR EACH ROW
EXECUTE FUNCTION trigger_roles_foreign_key_check_roles();

CREATE TRIGGER dbt_roles_roles_update
    BEFORE UPDATE
    ON dbe.roles FOR EACH ROW
EXECUTE FUNCTION trigger_roles_foreign_key_check_roles();

CREATE TABLE dbo.people
(
    id SERIAL CONSTRAINT dbo_people_pk PRIMARY KEY,
    degree_before INT NOT NULL CONSTRAINT dbo_people_degree_before_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL CONSTRAINT dbo_people_degree_after_dbo_user_degrees_id_fk REFERENCES dbo.user_degrees(id),
    sex INT NOT NULL CONSTRAINT dbo_people_dbe_sex_id_fk REFERENCES dbe.sex(id),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    note TEXT NOT NULL,

    employer INT NOT NULL CONSTRAINT dbo_people_employer_dbe_employers_id_fk REFERENCES dbo.employers(id),
    centre INT NOT NULL CONSTRAINT dbo_people_centre_dbo_centres_id_fk REFERENCES dbo.centres(id),
    e_relationship INT NOT NULL CONSTRAINT dbo_people_e_r_dbe_e_relationships_id_fk REFERENCES dbe.employer_relationships(id),
    profession INT NOT NULL,-- CONSTRAINT dbo_people_profession_dbo_professions_id_fk REFERENCES dbo.professions(id), at the end
    state INT NOT NULL DEFAULT 2 CONSTRAINT dbo_people_state_dbe_person_states_id_fk REFERENCES dbe.person_states(id),
    leader INT NOT NULL CONSTRAINT dbo_people_leader_dbo_people_id_fk REFERENCES dbo.people(id),

    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbo_people_id_partialindex ON dbo.people (id) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_name_partialindex ON dbo.people (name) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_surname_partialindex ON dbo.people (surname) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_e_relationship_partialindex ON dbo.people (e_relationship) WHERE state = 1 OR state = 2 OR state = 3 OR state = 4 OR state = 5
    OR state = 6 OR state = 7 OR state = 8 OR state = 9 OR state = 10 OR state = 11 OR state = 12 OR state = 13;
CREATE INDEX dbo_people_leader_index ON dbo.people (id);

CREATE TRIGGER dbt_people_changemodified BEFORE UPDATE
    ON dbo.people FOR EACH ROW EXECUTE PROCEDURE
    trigger_update_modified_at_timestamp();

CREATE TABLE dbh.people
(
    id SERIAL CONSTRAINT dbh_people_pk PRIMARY KEY,
    operation CHAR(1) NOT NULL,
    record_id INT NOT NULL,
    degree_before INT NOT NULL,
    name VARCHAR(64) NOT NULL,
    surname VARCHAR(64) NOT NULL,
    degree_after INT NOT NULL,
    sex INT NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(32) NOT NULL,
    note TEXT NOT NULL,

    employer INT NOT NULL,
    centre INT NOT NULL,
    e_relationship INT NOT NULL,
    profession INT NOT NULL,
    state INT NOT NULL DEFAULT 2,
    leader INT NOT NULL,

    modified_by INT NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbh_people_record_id_index ON dbh.people (record_id);
CREATE INDEX dbh_people_name_index ON dbh.people (name);
CREATE INDEX dbh_people_surname_index ON dbh.people (surname);
CREATE INDEX dbh_people_e_relationship_index ON dbo.people (e_relationship);
CREATE INDEX dbh_people_modified_at_index ON dbh.people (modified_at);
CREATE INDEX dbh_people_leader_index ON dbh.people (id);

CREATE TRIGGER dbt_people_delete
    AFTER DELETE
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_delete();

CREATE TRIGGER dbt_people_insert
    AFTER INSERT
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_insert();

CREATE TRIGGER dbt_people_update
    AFTER UPDATE
    ON dbo.people FOR EACH ROW
EXECUTE FUNCTION trigger_audit_table_update();
CREATE TABLE dbe.acl_operations
(
    id SERIAL CONSTRAINT dbe_acl_operations_pk PRIMARY KEY,
    title VARCHAR(128) NOT NULL
);

CREATE TABLE dbe.acl_tables
(
    id SERIAL CONSTRAINT dbe_acl_id_dbe_tables_id_fk REFERENCES dbe.tables(id) PRIMARY KEY,
    query TEXT NOT NULL
);
CREATE VIEW dbv.users AS
    SELECT u.id, u.name, u.roles, u.passhash, u.need_update_password, u.valid, a.attempts,
    EXISTS(SELECT id FROM dbo.people WHERE leader = u.id) OR EXISTS(SELECT id FROM dbo.centres WHERE leader = u.id) OR COALESCE(2 = ANY(u.roles), false) AS is_leader
    FROM dbo.users u
    LEFT JOIN dbs.login_attempts a ON a.id = u.id;
CREATE TABLE dbs.sessions
(
    id VARCHAR(64) CONSTRAINT dbs_sessions_pk PRIMARY KEY,
    remote_employer INT NOT NULL,
    remote_user INT NOT NULL,
    remote_is_leader BOOLEAN NOT NULL,
    roles INT[] NOT NULL,
    remote_domain VARCHAR(128) NOT NULL,
    local_user INT NOT NULL CONSTRAINT dbs_sessions_user_dbo_userd_id REFERENCES dbo.users (id),
    timezoneoffset INT NOT NULL,
    valid_until TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX dbs_sessions_user_index ON dbs.sessions (local_user);

CREATE TRIGGER dbt_sessions_roles_insert
    BEFORE INSERT
    ON dbs.sessions FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();

CREATE TRIGGER dbt_session_roles_update
    BEFORE UPDATE
    ON dbs.sessions FOR EACH ROW
EXECUTE FUNCTION trigger_users_foreign_key_check_roles();
