/* automatically generated file, do not edit. */

#ifndef __EMSCRIPTEN__

#include <map>
#include <string>
#include <vector>
#include <ctime>
#include "amcore/AMNullable.h"
#include "amcore/AMDatetimeus.h"
#include "amcore/AMAddress.h"
#include "amcore/AMAuthorTag.h"
#include "amcore/AMNameBase.h"
#include "svr/AMSvrObject.h"
#include "SAWSettings.h"

class CCategories: public AMSvr::AMSvrDbObject<int>
{
public:
    CCategories();
    CCategories(int id);
    AMSvr::AMSvrDbObjectContext<int> &context() override;

    AMSvr::AMSvrProp<SAWString, int> name;
    AMSvr::AMSvrProp<int, int> weight;
    AMSvr::AMSvrProp<bool, int> paid;
    AMSvr::AMSvrProp<AMCore::AMNullable<int>, int> supercategory;
    AMSvr::AMSvrProp<SAWString, int> note;
    AMSvr::AMSvrProp<AMCore::AMNullable<AMCore::AMDatetimeus>, int> deletedAt;
    AMSvr::AMSvrProp<int, int> modifiedBy;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> createdAt;
    AMSvr::AMSvrProp<AMCore::AMDatetimeus, int> updatedAt;

protected:
    static AMSvr::AMSvrDbObjectContext<int> *g_context;
};

#endif // __EMSCRIPTEN__
