INSERT INTO dbe.tables (id, title, enum)
VALUES  (1, 'Tituly osob', 'user_degrees'),
        (2, '<PERSON><PERSON><PERSON><PERSON>', 'users'),
        (3, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'questions'),
        (4, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t<PERSON>', 'requests')

INSERT INTO dbe.acl_operations (id, title)
VALUES  (1, 'Číst záznam'),
        (2, 'Vytvořit záznam'),
        (3, 'Upravit záznam'),
        (4, 'Smazat záznam'),
        (5, '<PERSON>trován<PERSON> bez restrikce'),
        (6, '<PERSON><PERSON><PERSON><PERSON> s tabulkou'),
        (7, '<PERSON><PERSON><PERSON><PERSON> s agendou'),
        (257, '<PERSON>ed<PERSON><PERSON><PERSON> číst záznam'),
        (258, 'Vedoucí vytvořit záznam'),
        (259, 'Vedou<PERSON><PERSON> upravit záznam'),
        (260, '<PERSON>edou<PERSON><PERSON> smazat záznam'),
        (261, '<PERSON><PERSON><PERSON><PERSON><PERSON> fitrov<PERSON> bez restrikce'),
        (262, '<PERSON><PERSON><PERSON><PERSON><PERSON> práce s tabulkou'),
        (263, '<PERSON><PERSON><PERSON><PERSON><PERSON> práce s agendou');

INSERT INTO dbe.risk_factors_working_conds (id, title, is_headline, color, description, diseases_excluding, diseases_condition, ord)
VALUES  (1, '1. CHEMICKÉ FAKTORY (obecně)', true, 'FF003366', '', '', '', 100),
        (2, '1.1.a Práce s chemickými látkami', false, 'FF003366', 'Poznámka: Jde o obecný postup pro chemické látky. Hodnotí se vždy účinky všech chemických látek a směsí, kterým je zaměstnanec v pracovním prostředí při výkonu práce exponován. U každé práce s chemickými látkami je nezbytné posoudit všechny nebezpečné vlastnosti, které každá jednotlivá látka má, a současně riziko, které při práci posuzované osoby vzniká.', '1. prognosticky závažné nemoci cílových orgánů pro působení daných látek,
2. závažné duševní poruchy a poruchy chování,
3. prokázaná přecitlivělost v anamnéze na látky vyskytující se při posuzované práci.', '1. nemoci cílových orgánů pro působení daných látek,
2. kožní nemoci ekzémového charakteru a závažné dermatózy,
3. závažné poruchy kognitivních funkcí a smyslového vnímání.', 200),
        (3, '1.1.b Práce s chemickými látkami - prokazatelná vysoká expozice hepatotoxickým látkám', false, 'FF003366', 'Poznámka: Jde o obecný postup pro chemické látky. Hodnotí se vždy účinky všech chemických látek a směsí, kterým je zaměstnanec v pracovním prostředí při výkonu práce exponován. U každé práce s chemickými látkami je nezbytné posoudit všechny nebezpečné vlastnosti, které každá jednotlivá látka má, a současně riziko, které při práci posuzované osoby vzniká.', '1. prognosticky závažné nemoci cílových orgánů pro působení daných látek,
2. závažné duševní poruchy a poruchy chování,
3. prokázaná přecitlivělost v anamnéze na látky vyskytující se při posuzované práci.', '1. nemoci cílových orgánů pro působení daných látek,
2. kožní nemoci ekzémového charakteru a závažné dermatózy,
3. závažné poruchy kognitivních funkcí a smyslového vnímání.', 300),
        (4, '1.1.c Práce s chemickými látkami - látky s pozdním účinkem karcinogenním a mutagenním a u látek s fibrogenním účinkem', false, 'FF003366', 'Poznámka: Jde o obecný postup pro chemické látky. Hodnotí se vždy účinky všech chemických látek a směsí, kterým je zaměstnanec v pracovním prostředí při výkonu práce exponován. U každé práce s chemickými látkami je nezbytné posoudit všechny nebezpečné vlastnosti, které každá jednotlivá látka má, a současně riziko, které při práci posuzované osoby vzniká.', '1. prognosticky závažné nemoci cílových orgánů pro působení daných látek,
2. závažné duševní poruchy a poruchy chování,
3. prokázaná přecitlivělost v anamnéze na látky vyskytující se při posuzované práci.', '1. nemoci cílových orgánů pro působení daných látek,
2. kožní nemoci ekzémového charakteru a závažné dermatózy,
3. závažné poruchy kognitivních funkcí a smyslového vnímání.', 400),
        (5, '1.2. Látky s pozdním účinkem karcinogenním a mutagenním - karcinogeny kategorie 1 a 2 nebo kategorie 1A a 1B a mutageny kategorie 1 a 2 nebo kategorie 1A a 1B', false, 'FF003366', '', '1. prekancerózy nebo zhoubné nádory; pokud je riziko dané práce výrazné a prekanceróza nebo prodělaný zhoubný nádor postihl orgán nebo systém, který může být daným karcinogenem též postižen,
2. těžká imunodeficience nebo léčba oslabující výrazně imunitní systém.', '1. chronické kožní nemoci,
2. nemoci mízních uzlin, jater, sleziny,
3. přetrvávající jednoznačně patologické nálezy orgánů nebo systémů, který může být daným karcinogenem též postižen.', 500),
        (6, '1.3. Látky toxické pro reprodukci kategorie 1 a 2 nebo kategorie 1A a 1B', false, 'FF003366', '', 'těhotenství, pokud není k práci zaměstnankyně podle lékařského posudku zdravotně způsobilá, nebo jde o práci zakázanou těhotným.', '1. opakované spontánní aborty,
2. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí,
3. období plánovaného těhotenství.', 600),
        (7, '2. CHEMICKÉ FAKTORY (vybrané)', true, 'FF0F4172', '', '', '', 700),
        (8, '2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R)', false, 'FF0F4172', '', '1. prognosticky závažné anémie, všechny závažné typy porfyrických nemocí,
2. prognosticky závažné nemoci nervového systému,
3. těžké prognosticky závažné nemoci jater a ledvin,', '1. chronické anémie,
2. známky zvýšené expozice olovu,
3. chronické nemoci nervového systému,
4. chronické nemoci jater a ledvin,
5. chronické nemoci gastrointestinálního systému,
6. závažné endokrinní nemoci,
7. opakované spontánní aborty,
8. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí.', 800),
        (9, '2.2. Rtuť a její sloučeniny', true, 'FF1E4F7F', '', '', '', 900),
        (10, '2.2.1. Kovová rtuť a její anorganické sloučeniny (se zvláštním zřetelem na R)', false, 'FF1E4F7F', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost,
4. prognosticky závažné nemoci ledvin.', '1. chronické nemoci nervového systému,
2. závažné duševní nemoci včetně neurastenického syndromu,
3. alkoholová nebo drogová závislost v anamnéze,
4. chronické nemoci ledvin,
5. poruchy funkce štítné žlázy,
6. opakované spontánní aborty u vysoké expozice parám kovové rtuti,
7. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí parám kovové rtuti,
8. závažné stomatitis.', 1000),
        (11, '2.2.2. Organické sloučeniny rtuti', false, 'FF1E4F7F', '', '1. prognosticky závažné nemoci nervového systému u alkylsloučenin rtuti,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost.', '1. chronické nemoci ledvin,
2. chronické nemoci nervového systému a omezení zorného pole u alkylsloučenin rtuti,
3. závažné duševní nemoci,
4. alkoholová nebo drogová závislost v anamnéze,
5. ekzémové nemoci a závažné dermatózy.', 1100),
        (12, '2.3. Arzén a jeho sloučeniny (se zvláštním zřetelem na C a R)', false, 'FF1E4F7F', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné poruchy krvetvorby,
3. prognosticky závažné nemoci dýchacího systému, například prekancerózy a stavy po terapii maligních tumorů,
4. prognosticky závažné nemoci jater a ledvin,
5. prekancerózy a karcinomy kůže,
6. prognosticky závažné duševní poruchy a poruchy chování,
7. prokázaná současná alkoholová nebo drogová závislost.', '1. chronické nemoci nervového systému,
2. poruchy krvetvorby,
3. chronické kožní nemoci,
4. chronické nemoci dýchacího systému,
5. obliterující nemoci cév,
6. chronické nemoci jater a ledvin,
7. chronické nemoci gastrointestinálního systému,
8. opakované spontánní aborty u vysoké expozice těm sloučeninám arzénu, které jsou toxické pro reprodukci,
9. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám arzénu, které jsou toxické pro reprodukci,
10. alkoholová nebo drogová závislost v anamnéze.', 1200),
        (13, '2.4.a Antimon a jeho sloučeniny', false, 'FF1E4F7F', 'Poznámka: Vztahuje se pouze na inhalační expozici.', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci gastrointestinálního systému.', '1. závažné nemoci srdce,
2. chronické nemoci dýchacího systému,
3. chronické kožní nemoci,
4. chronická anémie při práci s hydridem antimonu SbH3,
5. chronické nemoci gastrointestinálního systému.', 1300),
        (14, '2.4.b Antimon a jeho sloučeniny - hydrid antimonu SbH3', false, 'FF1E4F7F', 'Poznámka: Vztahuje se pouze na inhalační expozici.', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci gastrointestinálního systému.', '1. závažné nemoci srdce,
2. chronické nemoci dýchacího systému,
3. chronické kožní nemoci,
4. chronická anémie při práci s hydridem antimonu SbH3,
5. chronické nemoci gastrointestinálního systému.', 1400),
        (15, '2.5. Beryllium a jeho sloučeniny (se zvláštním zřetelem na C)', false, 'FF1E4F7F', 'Poznámka: Vztahuje se pouze na inhalační expozici.', '1. prognosticky závažné nemoci dýchacího systému,
2. prekancerózy a stavy po terapii maligních tumorů v oblasti dýchacího systému.', 'chronické nemoci dýchacího systému,', 1500),
        (16, '2.6. Kadmium a jeho sloučeniny (se zvláštním zřetelem na C, M a R)', false, 'FF1E4F7F', '', '1. prognosticky závažné nemoci ledvin a jater,
2. prognosticky závažné nemoci dýchacího systému, například prekancerózy a stavy po terapii maligních tumorů.', '1. chronické nemoci ledvin a jater,
2. chronické nemoci dýchacího systému,
3. nemoci prostaty,
4. závažné porucha metabolizmu kalcia, fosforu a vitaminu D, osteoporóza,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám kadmia, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám kadmia, které jsou toxické pro reprodukci,
7. anosmie.', 1600),
        (17, '2.7.a Chróm (kromě IV-mocných sloučenin, se zvláštním zřetelem na C a R)', false, 'FF1E4F7F', 'Poznámka: Do této skupiny expozic chrómu nepatří mechanické opracovávání legované oceli.', '1. prognosticky závažné nemoci dýchacího systému, například prekancerózy a stavy po terapii maligních tumorů,
2. těžké chronické kožní nemoci, včetně prekanceróz a karcinomů kůže,
3. perzistující nedostatečně kontrolované astma bronchiale.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci kůže a dýchacího systému,
3. chronické kožní nemoci,
4. opakované spontánní aborty u vysoké expozice těm sloučeninám chrómu, které jsou toxické pro reprodukci,
5. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám chrómu, které jsou toxické pro reprodukci,
6. anosmie.', 1700),
        (18, '2.7.b VI-mocné sloučeniny chrómu (se zvláštním zřetelem na C a R)', false, 'FF1E4F7F', 'Poznámka: Do této skupiny expozic chrómu nepatří mechanické opracovávání legované oceli.', '1. prognosticky závažné nemoci dýchacího systému, například prekancerózy a stavy po terapii maligních tumorů,
2. těžké chronické kožní nemoci, včetně prekanceróz a karcinomů kůže,
3. perzistující nedostatečně kontrolované astma bronchiale.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci kůže a dýchacího systému,
3. chronické kožní nemoci,
4. opakované spontánní aborty u vysoké expozice těm sloučeninám chrómu, které jsou toxické pro reprodukci,
5. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám chrómu, které jsou toxické pro reprodukci,
6. anosmie.', 1800),
        (19, '2.8. Mangan a jeho sloučeniny', false, 'FF1E4F7F', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost,
4. prognosticky závažné nemoci dýchacího systému,
5. prognosticky závažné nemoci jater.', '1. chronické nemoci nervového systému,
2. závažné duševní nemoci,
3. alkoholová nebo drogová závislost v anamnéze,
4. chronické nemoci dýchacího systému,
5. chronické nemoci jater.', 1900),
        (20, '2.9.a Nikl a jeho sloučeniny (kromě sloučenin s karcinogenním účinkem, se zvláštním zřetelem na C a R)', false, 'FF1E4F7F', '', 'prognosticky závažné nemoci dýchacího systému včetně nemocí sliznice nosní a vedlejších dutin nosních, prekancerózy a stavy po terapii nádorových nemocí v těchto lokalizacích.', '1. chronické nemoci dýchacího systému včetně nemocí sliznice nosní a vedlejších dutin nosních,
2. chronické kožní nemoci,
3. opakované spontánní aborty u vysoké expozice těm sloučeninám niklu, které jsou toxické pro reprodukci,
4. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám niklu, které jsou toxické pro reprodukci,
5. alergické nemoci kůže a dýchacích cest.', 2000),
        (21, '2.9.b Sloučeniny niklu s karcinogenním účinkem (se zvláštním zřetelem na C a R)', false, 'FF1E4F7F', '', 'prognosticky závažné nemoci dýchacího systému včetně nemocí sliznice nosní a vedlejších dutin nosních, prekancerózy a stavy po terapii nádorových nemocí v těchto lokalizacích.', '1. chronické nemoci dýchacího systému včetně nemocí sliznice nosní a vedlejších dutin nosních,
2. chronické kožní nemoci,
3. opakované spontánní aborty u vysoké expozice těm sloučeninám niklu, které jsou toxické pro reprodukci,
4. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám niklu, které jsou toxické pro reprodukci,
5. alergické nemoci kůže a dýchacích cest.', 2100),
        (22, '2.10. Fosfor a jeho sloučeniny', true, 'FF2E5D8C', '', '', '', 2200),
        (23, '2.10.1. Fosfor bílý', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci jater a ledvin,
2. prognosticky závažné nemoci kostí (osteomyelitis a osteoporóza),
3. prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci jater a ledvin,
2. závažné nemoci kostí,
3. závažné krvácivé stavy,
4. chronické kožní nemoci,
5. chronické nemoci dýchacího systému.', 2300),
        (24, '2.10.2. Fosfor a jeho anorganické sloučeniny, například (ortho) fosforečnan vápenatý,amonný, sodný, draselný, zinečnatý', false, 'FF2E5D8C', 'Poznámka: Vztahuje se pouze na inhalační expozici.', 'prognosticky závažné nemoci dýchacího systému.', 'chronické nemoci dýchacího systému.', 2400),
        (25, '2.10.3. Organické sloučeniny fosforu (například organofosfáty - OF a trikrezylfosfát-TKP a další se zvláštním zřetelem na R, pokud některá sloučenina je takto klasifikována)', false, 'FF2E5D8C', '', '1. snížení aktivity cholinesterázy nebo acetylcholinesterázy v krvi pod 80 % referenční hodnoty,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné nemoci jater a ledvin,
4. astma bronchiale,
5. prognosticky závažné poruchy a poruchy chování,
6. prokázaná současná alkoholová nebo drogová závislost.', '1. chronické nemoci nervového systému,
2. chronické nemoci jater a ledvin,
3. chronické nemoci dýchacího systému,
4. ekzémová nemoci, chronické nemoci kůže,
5. závažné poruchy a poruchy chování,
6. alkoholová nebo drogová závislost v anamnéze,
7. opakované spontánní aborty u vysoké expozice těm organickým sloučeninám fosforu, které jsou toxické pro reprodukci,
8. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm organickým sloučeninám fosforu, které jsou toxické pro reprodukci,
9. pokles aktivity cholinesterázy nebo acetylcholinesterázy v krvi o 30 % proti bazální hodnotě, a to po dobu nejméně 2 měsíců.', 2500),
        (26, '2.11.a Vanad a jeho sloučeniny', false, 'FF2E5D8C', '', '1. perzistující středně těžké astma bronchiale a závažné nemoci dýchacího systému,
2. závažné zánětlivé oční nemoci,
3. prognosticky závažné nemoci ledvin.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci,
3. chronické kožní nemoci,
4. chronické zánětlivé oční nemoci,
5. chronické nemoci ledvin.', 2600),
        (27, '2.11.b Vanad a jeho sloučeniny - předpokládaná inhalační expozice', false, 'FF2E5D8C', '', '1. perzistující středně těžké astma bronchiale a závažné nemoci dýchacího systému,
2. závažné zánětlivé oční nemoci,
3. prognosticky závažné nemoci ledvin.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci,
3. chronické kožní nemoci,
4. chronické zánětlivé oční nemoci,
5. chronické nemoci ledvin.', 2700),
        (28, '2.11.c Vanad a jeho sloučeniny - významná inhalační expozice', false, 'FF2E5D8C', '', '1. perzistující středně těžké astma bronchiale a závažné nemoci dýchacího systému,
2. závažné zánětlivé oční nemoci,
3. prognosticky závažné nemoci ledvin.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci,
3. chronické kožní nemoci,
4. chronické zánětlivé oční nemoci,
5. chronické nemoci ledvin.', 2800),
        (29, '2.12.a Fluór a jeho anorganické a organické sloučeniny (se zvláštním zřetelem na sloučeniny, které jsou C, M a R)', false, 'FF2E5D8C', 'Poznámka: Nevztahuje se na freony.', '1. prognosticky závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. prognosticky závažné nemoci dýchacího systému.', '1. závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. Morbus Bechtěrev, ankylózy páteře a velkých kloubů, prodělaná tuberkulóza kostí,
3. chronické nemoci dýchacího systému,
4. chronické nemoci ledvin,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám fluoru, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám fluoru, které jsou toxické pro reprodukci,
7. závažné nemoci myokardu.', 2900),
        (30, '2.12.b Fluór a jeho anorganické a organické sloučeniny (se zvláštním zřetelem na sloučeniny, které jsou C, M a R) - předpokládaná inhalační expozice', false, 'FF2E5D8C', 'Poznámka: Nevztahuje se na freony.', '1. prognosticky závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. prognosticky závažné nemoci dýchacího systému.', '1. závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. Morbus Bechtěrev, ankylózy páteře a velkých kloubů, prodělaná tuberkulóza kostí,
3. chronické nemoci dýchacího systému,
4. chronické nemoci ledvin,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám fluoru, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám fluoru, které jsou toxické pro reprodukci,
7. závažné nemoci myokardu.', 3000),
        (31, '2.12.c Fluór a jeho anorganické a organické sloučeniny (se zvláštním zřetelem na sloučeniny, které jsou C, M a R) - významná inhalační expozice', false, 'FF2E5D8C', 'Poznámka: Nevztahuje se na freony.', '1. prognosticky závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. prognosticky závažné nemoci dýchacího systému.', '1. závažné nemoci spojené s poruchou metabolizmu kalcia a fosforu (poruchy osifikace kostí, osteoporóza),
2. Morbus Bechtěrev, ankylózy páteře a velkých kloubů, prodělaná tuberkulóza kostí,
3. chronické nemoci dýchacího systému,
4. chronické nemoci ledvin,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám fluoru, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám fluoru, které jsou toxické pro reprodukci,
7. závažné nemoci myokardu.', 3100),
        (32, '2.13.a Chlór a jeho anorganické sloučeniny (například chlornany, chlorečnany, kyselina chloristá)', false, 'FF2E5D8C', 'Poznámka: Do této skupiny se nezařazuje chlorid sodný, chlorid draselný a další chloridy, které nemají žádnou nebezpečnou vlastnost pro člověka.', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému, včetně alergických,
2. chronické zánětlivé oční nemoci.', 3200),
        (33, '2.13.b Chlór a jeho anorganické sloučeniny (například chlornany, chlorečnany, kyselina chloristá) - předpokládaná inhalační expozice', false, 'FF2E5D8C', 'Poznámka: Do této skupiny se nezařazuje chlorid sodný, chlorid draselný a další chloridy, které nemají žádnou nebezpečnou vlastnost pro člověka.', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému, včetně alergických,
2. chronické zánětlivé oční nemoci.', 3300),
        (34, '2.13.c Chlór a jeho anorganické sloučeniny (například chlornany, chlorečnany, kyselina chloristá) - významná inhalační expozice', false, 'FF2E5D8C', 'Poznámka: Do této skupiny se nezařazuje chlorid sodný, chlorid draselný a další chloridy, které nemají žádnou nebezpečnou vlastnost pro člověka.', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému, včetně alergických,
2. chronické zánětlivé oční nemoci.', 3400),
        (35, '2.14.a Ostatní halogeny a jejich sloučeniny (například bróm, brómované uhlovodíky - monobromomethan, 1,2, dibromethan, bromoform, dibromomethan, jód)', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné nemoci jater a ledvin se známkami aktivity procesu.', '1. chronické nemoci dýchacího systému,
2. závažné alergické nemoci,
3. chronické nemoci kožní,
4. chronické nemoci nervového systému,
5. chronické nemoci jater a ledvin u hepatotoxických nebo nefrotoxických bromovaných uhlovodíků,
6. porucha funkce štítné žlázy u jódu.', 3500),
        (36, '2.14.b Ostatní halogeny a jejich sloučeniny - hepatotoxické nebo nefrotoxické uhlovodíky', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné nemoci jater a ledvin se známkami aktivity procesu.', '1. chronické nemoci dýchacího systému,
2. závažné alergické nemoci,
3. chronické nemoci kožní,
4. chronické nemoci nervového systému,
5. chronické nemoci jater a ledvin u hepatotoxických nebo nefrotoxických bromovaných uhlovodíků,
6. porucha funkce štítné žlázy u jódu.', 3600),
        (37, '2.15. Zinek a jeho sloučeniny, které nemají žádnou nebezpečnou vlastnost pro člověka', false, 'FF2E5D8C', 'Poznámka: Vztahuje se pouze na inhalační expozici. Sloučeniny zinku nebezpečné pro člověka se posuzují například podle položek 2.7, 2.25, 2.47, 2.53 a 7.2.1', 'prognosticky závažné nemoci dýchacího systému.', 'chronické nemoci dýchacího systému.', 3700),
        (38, '2.16. Měď a její sloučeniny', false, 'FF2E5D8C', 'Poznámka: Vztahuje se pouze na inhalační expozici.', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. závažné alergické nemoci,
3. chronické kožní nemoci,
4. závažné nemoci jater.', 3800),
        (39, '2.17. Oxid uhelnatý u prací zařazených do kategorie 4 (se zvláštním zřetelem na R)', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost,
4. prognosticky závažné anémie.', '1. chronické nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. alkoholová nebo drogová závislost v anamnéze,
4. chronická anémie,
5. závažné nemoci kardiovaskulárního systému včetně ischemické choroby srdeční,
6. opakované spontánní aborty,
7. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí,
8. hypertensní nemoc s orgánovým postižením.', 3900),
        (40, '2.18. Oxidy dusíku a ozón', false, 'FF2E5D8C', 'Poznámka: Nevztahuje se na oxid dusný (rajský plyn).', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému, včetně alergických,
2. chronické konjunktivitidy.', 4000),
        (41, '2.19. Oxidy síry', false, 'FF2E5D8C', '', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. chronické konjunktivitidy.', 4100),
        (42, '2.20. Kyanovodík a kyanidy', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová a drogová závislost,
4. prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci nervového systému,
2. závažné duševní poruchy,
3. alkoholová a drogová závislost v anamnéze,
4. chronická anémie,
5. chronické nemoci dýchacího systému.', 4200),
        (43, '2.21. Izokyanáty bez ohledu na kategorii práce', false, 'FF2E5D8C', '', '1. prokázaná přecitlivělost na některý z izokyanátů,
2. prognosticky závažné nemoci dýchacího systému, včetně alergických.', '1. chronické nemoci dýchacího systému,
2. alergické kožní nemoci,
3. závažné kardiální nemoci s levostrannou dekompenzací.', 4300),
        (44, '2.22. Fosgen', false, 'FF2E5D8C', '', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. alergické nemoci dýchacích cest.', 4400),
        (45, '2.23. Borany (hydridy boru - například diboran, pentaboran, dekaboran)', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci jater a ledvin,
2. prognosticky závažné nemoci dýchacího systému,
3. prognosticky závažné nemoci centrálního i periferního nervového systému.', '1. chronické nemoci jater a ledvin,
2. chronické nemoci dýchacího systému,
3. závažné oční nemoci (například nemoci rohovky),
4. závažné nemoci centrálního i periferního nervového systému.', 4500),
        (46, '2.24. Sirouhlík', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci centrálního nebo periferního nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost,
4. prognosticky závažné nemoci kardiovaskulárního systému.', '1. chronické nemoci centrálního nebo periferního nervového systému,
2. závažné duševní poruchy a poruchy chování,
3. alkoholová nebo drogová závislost v anamnéze,
4. závažné nemoci kardiovaskulárního systému,
5. chronické nemoci jater a ledvin.', 4600),
        (47, '2.25.a Sirovodík a sulfidy (sirníky)', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému.', '1. chronické nemoci dýchacího systému,
2. chronické zánětlivé oční nemoci,
3. chronické nemoci nervového systému,
4. závažné kardiovaskulární nemoci, včetně závažných anémií,
5. chronické kožní nemoci,
6. porucha čichu.', 4700),
        (48, '2.25.b Sirovodík a sulfidy (sirníky) - předpokládaná inhalační expozice', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému.', '1. chronické nemoci dýchacího systému,
2. chronické zánětlivé oční nemoci,
3. chronické nemoci nervového systému,
4. závažné kardiovaskulární nemoci, včetně závažných anémií,
5. chronické kožní nemoci,
6. porucha čichu.', 4800),
        (49, '2.25.c Sirovodík a sulfidy (sirníky) - významná inhalační expozice', false, 'FF2E5D8C', '', '1. prognosticky závažné nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému.', '1. chronické nemoci dýchacího systému,
2. chronické zánětlivé oční nemoci,
3. chronické nemoci nervového systému,
4. závažné kardiovaskulární nemoci, včetně závažných anémií,
5. chronické kožní nemoci,
6. porucha čichu.', 4900),
        (50, '2.26. Amoniak a ty hydroxidy, které představují riziko při inhalační expozici', false, 'FF2E5D8C', '', 'prognosticky závažné nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. chronické kožní nemoci.', 5000),
        (51, '2.27. Halogenované uhlovodíky', true, 'FF3D6B99', '', '', '', 5100),
        (52, '2.27.1.a Trichlorethylen a tetrachlorethylen (= perchlorethylen) (nejsou klasifikovány jako karcinogenní)', false, 'FF3D6B99', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování včetně těžšího neurastenického syndromu,
3. prokázaná současná alkoholová nebo drogová závislost,
4. závažné chronické nemoci jater nebo ledvin.', '1. chronické nemoci nervového systému,
2. chronické nemoci jater nebo ledvin,
3. alkoholová nebo drogová závislost v anamnéze,
4. chronické kožní nemoci.', 5200),
        (53, '2.27.1.b Trichlorethylen a tetrachlorethylen (= perchlorethylen) (jsou klasifikovány jako karcinogenní, se zvláštním zřetelem na C)', false, 'FF3D6B99', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování včetně těžšího neurastenického syndromu,
3. prokázaná současná alkoholová nebo drogová závislost,
4. závažné chronické nemoci jater nebo ledvin.', '1. chronické nemoci nervového systému,
2. chronické nemoci jater nebo ledvin,
3. alkoholová nebo drogová závislost v anamnéze,
4. chronické kožní nemoci.', 5300),
        (54, '2.27.2. Methylchlorid (= monochlormethan)', false, 'FF3D6B99', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování včetně výraznějšího neurastenického syndromu,
3. závažné chronické nemoci jater a ledvin,
4. prognosticky závažné chronické nemoci dýchacího systému.', '1. závažné chronické nemoci dýchacího systému,
2. chronické nemoci jater nebo ledvin,
3. alkoholová nebo drogová závislost v anamnéze.', 5400),
        (55, '2.27.3.a Tetrachlormethan, trichlormethan (chloroform), dichlormethan (=methylenchlorid), 1,1-dichlorethan, 1,2-dichlorethan, 1,1,1-trichlorethan, 1,1,2-trichlorethan (nejsou klasifikovány jako karcinogenní)', false, 'FF3D6B99', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování,
4. prokázaná současná alkoholová nebo drogová závislost,
5. prognosticky závažné nemoci kardiovaskulárního systému.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné duševní poruchy a poruchy chování,
4. alkoholová nebo drogová závislost v anamnéze,
5. prognosticky závažné nemoci kardiovaskulárního systému,
6. chronické kožní nemoci.', 5500),
        (56, '2.27.3.b Tetrachlormethan, trichlormethan (chloroform), dichlormethan (=methylenchlorid), 1,1-dichlorethan, 1,2-dichlorethan, 1,1,1-trichlorethan, 1,1,2-trichlorethan (jsou klasifikovány jako karcinogenní, se zvláštním zřetelem na C)', false, 'FF3D6B99', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování,
4. prokázaná současná alkoholová nebo drogová závislost,
5. prognosticky závažné nemoci kardiovaskulárního systému.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné duševní poruchy a poruchy chování,
4. alkoholová nebo drogová závislost v anamnéze,
5. prognosticky závažné nemoci kardiovaskulárního systému,
6. chronické kožní nemoci.', 5600),
        (57, '2.27.4. Vinylchlorid (= chlorethen) (se zvláštním zřetelem na C)', false, 'FF3D6B99', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. závažný Raynaudův fenomén jakékoliv etiologie,
3. závažné nemoci nervového systému,
4. závažné chronické nemoci dýchacího systému.', '1. závažné nemoci jater a ledvin,
2. chronické kožní nemoci,
3. závažné nemoci nervového systému,
4. závažné chronické nemoci dýchacího systému,
5. alkoholová nebo drogová závislost,
6. poruchy krvetvorby.', 5700),
        (58, '2.28. Uhlovodíky', true, 'FF4C79A5', '', '', '', 5800),
        (59, '2.28.1. Alifatické nebo alicyklické uhlovodíky, destiláty ropy (například různé druhy benzínů, solventních naft a petrolejů)', false, 'FF4C79A5', 'Poznámka: Pokud tyto látky obsahují nadlimitní množství benzenu nebo 1,3-butadienu a jsou proto klasifikovány jako karcinogeny nebo mutageny, posuzují se podle položky 1.2 nebo 2.28.2.', '1. prognosticky závažné nemoci centrálního nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování.', '1. závažné nemoci nervového systému,
2. chronické kožní nemoci.', 5900),
        (60, '2.28.2. 1,3-butadien (buta-1,3dien) (se zvláštním zřetelem na C a M)', false, 'FF4C79A5', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. závažné chronické nemoci dýchacího systému.', '1. poruchy krvetvorby,
2. prekancerózy a stavy po terapii maligních tumorů,
3. chronické kožní nemoci,
4. chronické nemoci jater a ledvin.', 6000),
        (61, '2.29. Alkoholy', true, 'FF5C87B2', '', '', '', 6100),
        (62, '2.29.1. Alkoholy (například ethanol, propan-1-ol, propan-2-ol (iso-propanol), butanol,cyklohexanol)', false, 'FF5C87B2', '', '1. prognosticky závažné nemoci centrálního nervového systému,
1. prognosticky závažné duševní poruchy a poruchy chování,
2. prokázaná současná alkoholová a drogová závislost.', '1. závažné nemoci nervového sytému,
2. závažné vestibulární poruchy,
3. chronické kožní nemoci.', 6200),
        (63, '2.29.2. Methanol', false, 'FF5C87B2', '', '1. prognosticky závažné nemoci centrálního i periferního nervového systému, včetně poruchy zrakového nervu,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová nebo drogová závislost.', '1. chronické nemoci jater a ledvin,
2. chronické nemoci dýchacího systému,
3. chronické kožní nemoci,
4. závažné vestibulární poruchy.', 6300),
        (64, '2.30. Glykoly (ethylenglykol, diethylenglykol, hexylenglykol, 1,4 butanediol)', false, 'FF5C87B2', '', '1. prognosticky závažné chronické nemoci ledvin a jater,
2. prognosticky závažné nemoci nervového systému.', '1. závažné chronické nemoci ledvin a jater,
2. závažné nemoci nervového systému,
3. chronické kožní nemoci.', 6400),
        (65, '2.31. Étery', true, 'FF6B95BF', '', '', '', 6500),
        (66, '2.31.1. Étery (kromě halogenovaných a glykoléterů) (například dimethyleter,methylethyleter, diethyleter (ether), diisopropylether', false, 'FF6B95BF', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová a drogová závislost.', '1. závažné chronické nemoci dýchacího systému,
2. chronické nemoci jater a ledvin,
3. závažné nemoci nervového systému,
4. závažné duševní poruchy a poruchy chování,
5. chronické kožní nemoci.', 6600),
        (67, '2.31.2. Glykolethery a glykoletheracetáty (například ethoxyethanol, 2-ethoxyethylacetát, 2-methoxyethanol, 2-methoxyethylacetát, 2-butoxyethanol ;2-butoxyethylacetát)', false, 'FF6B95BF', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné nemoci hematopoetického systému.', '1. závažné nemoci nervového systému,
2. závažné nemoci hematopoetického systému,
3. chronické nemoci ledvin,
4. kožní nemoci,
5. chronické nemoci dýchacího systému.', 6700),
        (68, '2.32. Aldehydy', true, 'FF7BA3CC', '', '', '', 6800),
        (69, '2.32.1. Formaldehyd a jiné alifatické aldehydy (například acetaldehyd, butyraldehyd, paraldehyd (2,4,6-trimethyl-1,3,5-trioxan), glutaraldehyd, metaldehyd, akrylaldehyd (akrolein)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci dýchacího systému.', '1. chronické oční zánětlivé nemoci,
2. ekzémové nemoci a další chronické kožní nemoci,
3. alergické nemoci.', 6900),
        (70, '2.32.2. Aromatické a heterocyklické aldehydy (například 2-furaldehyd (furfural), tetrahydrothiopyran-3-karboxaldehyd, benzaldehyd) (se zvláštním zřetelem na ty, které jsou klasifikovány R)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci dýchacího systému.', '1. chronické kožní nemoci, včetně alergických,
2. závažné nemoci nervového systému,
3. opakované spontánní aborty u vysoké expozice těm aromatickým a heterocyklickým aldehydům, které jsou toxické pro reprodukci,
4. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm aromatickým a heterocyklickým aldehydům, které jsou toxické pro reprodukci,
5. závažné chronické oční zánětlivé nemoci.', 7000),
        (71, '2.33.a Akrylonitril a jiné nitrily (například acetonitril, propannitril, malononitril, adiponitril) (nejsou klasifikovány jako karcinogeny)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci dýchacího systému a dále prekancerózy a stavy po terapii maligních tumorů u akrylonitrilu,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování.', '1. chronické nemoci jater a ledvin,
2. závažné chronické nemoci dýchacího systému,
3. prognosticky závažné nemoci nervového systému,
4. závažné duševní poruchy a poruchy chování,
5. chronické kožní nemoci.', 7100),
        (72, '2.33.b Akrylonitril a jiné nitrily (například acetonitril, propannitril, malononitril, adiponitril) (jsou klasifikovány jako karcinogeny, se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci dýchacího systému a dále prekancerózy a stavy po terapii maligních tumorů u akrylonitrilu,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování.', '1. chronické nemoci jater a ledvin,
2. závažné chronické nemoci dýchacího systému,
3. prognosticky závažné nemoci nervového systému,
4. závažné duševní poruchy a poruchy chování,
5. chronické kožní nemoci.', 7200),
        (73, '2.34. Alifatické nitroderiváty (například nitromethan, trinitromethan, tetranitromethan, nitroethan, nitropropan)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování,
4. prokázaná současná alkoholová nebo jiná drogová závislost.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné duševní poruchy a poruchy chování,
4. alkoholová nebo jiná drogová závislost v anamnéze,
5. chronické nemoci dýchacího systému,
6. závažné kardiovaskulární nemoci,
7. chronické kožní nemoci.', 7300),
        (74, '2.35.a Alifatické amidy (například akrylamid a dimethylformamid) (se zvláštním zřetelem na ty, které jsou klasifikovány jako C, M a R)', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci centrálního i periferního nervového systému,
2. prokázaná současná alkoholová nebo drogová závislost.', '1. závažné nemoci centrálního i periferního nervového systému,
2. alkoholová nebo drogová závislost v anamnéze,
3. závažné chronické nemoci dýchacího systému,
4. chronické oční zánětlivé nemoci,
5. chronické kožní nemoci,
6. opakované spontánní aborty u vysoké expozice těm alifatickým amidům, které jsou toxické pro reprodukci,
7. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm alifatickým amidům, které jsou toxické pro reprodukci.
8. nemoci jater.', 7400),
        (75, '2.35.b Alifatické amidy - akrylamid', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci centrálního i periferního nervového systému,
2. prokázaná současná alkoholová nebo drogová závislost.', '1. závažné nemoci centrálního i periferního nervového systému,
2. alkoholová nebo drogová závislost v anamnéze,
3. závažné chronické nemoci dýchacího systému,
4. chronické oční zánětlivé nemoci,
5. chronické kožní nemoci,
6. opakované spontánní aborty u vysoké expozice těm alifatickým amidům, které jsou toxické pro reprodukci,
7. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm alifatickým amidům, které jsou toxické pro reprodukci.
8. nemoci jater.', 7500),
        (76, '2.36.a Benzen (se zvláštním zřetelem na C a M) - expozice nepřekračující koncentraci 3 mg.m-3 po dobu nejméně 6 měsíců', false, 'FF7BA3CC', '', '1. prognosticky závažné poruchy krvetvorby včetně malignit, prekanceróz a stavů po terapii onkologických nemocí,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné nemoci kardiovaskulárního systému.', '1. závažné poruchy krvetvorby,
2. závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování včetně výraznějšího neurastenického syndromu,
4. závažné nemoci kardiovaskulárního systému,
5. chronické kožní nemoci,
6. chronické nemoci jater a ledvin.', 7600),
        (77, '2.36.b Benzen (se zvláštním zřetelem na C a M) - expozice překračující koncentraci 3 mg.m-3 po dobu nejméně 6 měsíců', false, 'FF7BA3CC', '', '1. prognosticky závažné poruchy krvetvorby včetně malignit, prekanceróz a stavů po terapii onkologických nemocí,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné nemoci kardiovaskulárního systému.', '1. závažné poruchy krvetvorby,
2. závažné nemoci nervového systému,
3. prognosticky závažné duševní poruchy a poruchy chování včetně výraznějšího neurastenického syndromu,
4. závažné nemoci kardiovaskulárního systému,
5. chronické kožní nemoci,
6. chronické nemoci jater a ledvin.', 7700),
        (78, '2.37. Homology benzenu (například toluen, xylen, ethylbenzen, trimethylbenzeny,kumen (isopropylbenzen))', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. prokázaná současná alkoholová a jiná drogová závislost.', '1. závažné nemoci nervového systému,
2. závažné duševní poruchy a poruchy chování,
3. alkoholová a jiná drogová závislost v anamnéze,
4. chronické kožní nemoci,
5. chronické nemoci jater a ledvin,
6. závažné nemoci kardiovaskulárního systému.', 7800),
        (79, '2.38. Naftalen a jeho homology (například methylnaftalen, tetralin, dekalin)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci dýchacího systému,
2. nemoci hematopoetického systému a prokázaná porucha glukózo-6-P-dehydrogenázy.', '1. chronické kožní nemoci,
2. závažné nemoci nervového systému,
3. závažné chronické oční nemoci, včetně katarakty,
4. chronické nemoci jater a ledvin.', 7900),
        (80, '2.39.a Styren (vinylbenzen) a divinylbenzen (se zvláštním zřetelem na C a M, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. u styrenoxidu prognosticky závažné chronické nemoci dýchacího systému a prekancerózy a stavy po terapii maligních tumorů.', '1. závažné nemoci kardiovaskulárního systému,
2. závažné chronické nemoci dýchacího systému,
3. závažné duševní poruchy a poruchy chování,
4. chronické kožní nemoci,
5. chronické nemoci jater a ledvin.', 8000),
        (81, '2.39.b Styrenoxid (se zvláštním zřetelem na C a M, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. u styrenoxidu prognosticky závažné chronické nemoci dýchacího systému a prekancerózy a stavy po terapii maligních tumorů.', '1. závažné nemoci kardiovaskulárního systému,
2. závažné chronické nemoci dýchacího systému,
3. závažné duševní poruchy a poruchy chování,
4. chronické kožní nemoci,
5. chronické nemoci jater a ledvin.', 8100),
        (82, '2.40. Fenol a jeho homology (například krezol, katechol, resorcinol, hydrochinon) nebo halogenované deriváty (pentachlorfenol)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné nemoci nervového systému,
3. prognosticky závažné chronické nemoci dýchacího systému.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné chronické nemoci dýchacího systému,
4. chronické kožní nemoci.', 8200),
        (83, '2.41.a Aromatické nitro sloučeniny (například nitrobenzen, nitrotolueny, dinitrobenzeny, dinitrotolueny, dinitrofenol, trinitrofenol (kyselina pikrová), dinitro-o-krezol, dinitrochlorbenzen) (se zvláštním zřetelem na C, M a R, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné poruchy krvetvorby,
3. prognosticky závažné nemoci nervového systému,
4. prognosticky závažné duševní poruchy a poruchy chování,
5. prognosticky závažné chronické nemoci močového měchýře a močových cest,
6. prokázaná současná alkoholová a jiná drogová závislost.', '1. závažné chronické nemoci jater a ledvin,
2. závažné poruchy kardiovaskulárního systému včetně poruch krvetvorby,
3. závažné nemoci nervového systému,
4. závažné duševní poruchy a poruchy chování,
5. závažné chronické nemoci močového měchýře a močových cest,
6. alkoholová a jiná drogová závislost v anamnéze,
7. prokázaná porucha glukózo-6-P-dehydrogenázy,
8. chronické kožní nemoci,
9. opakované spontánní aborty u vysoké expozice těm aromatickým nitrosloučeninám, které jsou toxické pro reprodukci,
10. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm aromatickým nitrosloučeninám, které jsou toxické pro reprodukci,
11. zákaly oční čočky u trinitrotoluenu.', 8300),
        (84, '2.41.b Aromatické nitro sloučenin - trinitrotoluen', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. prognosticky závažné poruchy krvetvorby,
3. prognosticky závažné nemoci nervového systému,
4. prognosticky závažné duševní poruchy a poruchy chování,
5. prognosticky závažné chronické nemoci močového měchýře a močových cest,
6. prokázaná současná alkoholová a jiná drogová závislost.', '1. závažné chronické nemoci jater a ledvin,
2. závažné poruchy kardiovaskulárního systému včetně poruch krvetvorby,
3. závažné nemoci nervového systému,
4. závažné duševní poruchy a poruchy chování,
5. závažné chronické nemoci močového měchýře a močových cest,
6. alkoholová a jiná drogová závislost v anamnéze,
7. prokázaná porucha glukózo-6-P-dehydrogenázy,
8. chronické kožní nemoci,
9. opakované spontánní aborty u vysoké expozice těm aromatickým nitrosloučeninám, které jsou toxické pro reprodukci,
10. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm aromatickým nitrosloučeninám, které jsou toxické pro reprodukci,
11. zákaly oční čočky u trinitrotoluenu.', 8400),
        (85, '2.42.a Aromatické amino sloučeniny (například anilín, p-toluidin, m-toluidin, benzidin a jeho soli, dichlorbenzidin a jeho soli, 2-naftylamin, 4-aminobifenyl a jeho soli, o-toluidin (se zvláštním zřetelem na C, M a R, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. prekancerózy a stavy po terapii maligních tumorů jater, ledvin a močových cest,
2. prognosticky závažné chronické nemoci jater a ledvin,
3. prognosticky závažné nemoci nervového systému,
4. prognosticky závažné duševní poruchy a poruchy chování,
5. prokázaná současná alkoholová a jiná drogová závislost.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné duševní poruchy a poruchy chování,
4. alkoholová a jiná drogová závislost v anamnéze,
5. chronické nemoci močového měchýře a močových cest,
6. chronické anémie a poruchy hemoglobinu,
7. prokázaná porucha glukózo-6-P-dehydrogenázy,
8. chronické kožní nemoci,
9. opakované spontánní aborty u vysoké expozice těm aromatickým aminosloučeninám, které jsou toxické pro reprodukci,
10. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm aromatickým aminosloučeninám, které jsou toxické pro reprodukci,
11. závažné alergické nemoci.', 8500),
        (86, '2.42.b Aromatické amino sloučeniny (karcinogenní aromatické aminy, se zvláštním zřetelem na C, M a R, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. prekancerózy a stavy po terapii maligních tumorů jater, ledvin a močových cest,
2. prognosticky závažné chronické nemoci jater a ledvin,
3. prognosticky závažné nemoci nervového systému,
4. prognosticky závažné duševní poruchy a poruchy chování,
5. prokázaná současná alkoholová a jiná drogová závislost.', '1. závažné chronické nemoci jater a ledvin,
2. závažné nemoci nervového systému,
3. závažné duševní poruchy a poruchy chování,
4. alkoholová a jiná drogová závislost v anamnéze,
5. chronické nemoci močového měchýře a močových cest,
6. chronické anémie a poruchy hemoglobinu,
7. prokázaná porucha glukózo-6-P-dehydrogenázy,
8. chronické kožní nemoci,
9. opakované spontánní aborty u vysoké expozice těm aromatickým aminosloučeninám, které jsou toxické pro reprodukci,
10. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm aromatickým aminosloučeninám, které jsou toxické pro reprodukci,
11. závažné alergické nemoci.', 8600),
        (87, '2.43.a Polychlorované bifenyly, dibenzodioxiny (kromě 2,3,7,8-TCDD) a dibenzofurany (se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. zhoubná nemoci i po terapii,
2. prognosticky závažné chronické nemoci jater a ledvin.', '1. nekorigované poruchy lipidového metabolismu,
2. prekancerózy,
3. všechny druhy porfyrických nemocí,
4. závažné poruchy imunity,
5. závažné chronické kožní nemoci,
6. závažné nemoci nervového systému,
7. závažné chronické nemoci jater a ledvin,
8. závažné nemoci kardiovaskulárního a dýchacího systému.', 8700),
        (88, '2.43.b Polychlorované bifenyly - 2,3,7,8-TCDD (se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. zhoubná nemoci i po terapii,
2. prognosticky závažné chronické nemoci jater a ledvin.', '1. nekorigované poruchy lipidového metabolismu,
2. prekancerózy,
3. všechny druhy porfyrických nemocí,
4. závažné poruchy imunity,
5. závažné chronické kožní nemoci,
6. závažné nemoci nervového systému,
7. závažné chronické nemoci jater a ledvin,
8. závažné nemoci kardiovaskulárního a dýchacího systému.', 8800),
        (89, '2.44. Polycyklické aromatické uhlovodíky (například benzo(a)pyren, benzo(a)anthracen, benzo(e)acefenanthrylen, benzo(j)fluoranthen, benzo(k)fluoranthen, dibenzo(a,h) anthracen, chrysen, benzo(e)pyren) (se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. závažné onkologické nemoci, prekancerózy a stavy po prodělaných onkologických nemocích,
2. prognosticky závažné chronické nemoci jater, ledvin a plic,
3. prognosticky závažné imunodeficience.', '1. závažné chronické nemoci dýchacího systému,
2. závažné chronické kožní nemoci,
3. chronické nemoci jater a ledvin,
4. závažné imunodeficience.', 8900),
        (90, '2.45. Syntetické pyretroidy (například permethrin, cypermethrin, tetramethrin, deltamethrin)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci dýchacího systému,
2. prognosticky závažné nemoci nervového systému.', '1. závažné chronické nemoci dýchacího systému,
2. závažné chronické kožní nemoci.', 9000),
        (91, '2.46.a Dipyridily (diquat)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. závažné chronické nemoci kůže s poruchou integrity kůže.', '1. chronické nemoci jater a ledvin,
2. chronické nemoci dýchacího systému,
3. chronické kožní nemoci.', 9100),
        (92, '2.46.b Dipyridily (paraquatu)', false, 'FF7BA3CC', '', '1. prognosticky závažné chronické nemoci jater a ledvin,
2. závažné chronické nemoci kůže s poruchou integrity kůže.', '1. chronické nemoci jater a ledvin,
2. chronické nemoci dýchacího systému,
3. chronické kožní nemoci.', 9200),
        (93, '2.47. Karbamátové insekticidy (například inhibitory AChE - aldicarb, carbofuran, methomyl, bendiocarb, carbaryl, pirimicarb) (se zvláštním zřetelem na C, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', '1. snížení aktivity cholinesterázy nebo acetylcholinesterázy v krvi pod 80 % dolní hranice referenčních mezí,
2. prognosticky závažné nemoci centrálního i periferního nervového systému.', '1. závažné nemoci dýchacího systému,
2. chronické nemoci jater a ledvin,
3. chronické nemoci kůže,
4. pokles aktivity cholinesterázy nebo acetylcholinesterázy v krvi o 30% oproti hodnotě zjištěné před expozicí, a to po dobu nejméně 2 měsíců.', 9300),
        (94, '2.48.a Sloučeniny kovů platinové skupiny (platina, osmium)', false, 'FF7BA3CC', '', 'prognosticky závažné alergické nemoci.', '1. alergické a chronické nemoci kůže a dýchacího systému, prokázaná senzibilizace na platinu,
2. významné poruchy krvetvorby,
3. chronické nemoci jater a ledvin.', 9400),
        (95, '2.48.b Sloučeniny kovů platinové skupiny (kyselina osmičelá)', false, 'FF7BA3CC', '', 'prognosticky závažné alergické nemoci.', '1. alergické a chronické nemoci kůže a dýchacího systému, prokázaná senzibilizace na platinu,
2. významné poruchy krvetvorby,
3. chronické nemoci jater a ledvin.', 9500),
        (96, '2.49. Thalium a jeho sloučeniny', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování.', '1. závažné chronické nemoci dýchacího systému,
2. závažné kardiovaskulární nemoci,
3. chronické kožní nemoci,
4. závažné endokrinní nemoci,
5. chronické nemoci jater a ledvin.', 9600),
        (97, '2.50. Baryum a jeho rozpustné sloučeniny', false, 'FF7BA3CC', '', 'závažné chronické nemoci dýchacího systému.', '1. závažné nemoci nervového systému,
2. závažné kardiovaskulární nemoci.', 9700),
        (98, '2.51.a Sloučeniny cínu (se zvláštním zřetelem na R, pokud některá z těchto sloučenin má tuto klasifikaci. Kromě chloridu cínatého, viz položka 2.51.b)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. závažné poruchy krvetvorby (chlorid cínatý),
3. chronické kožní nemoci,
4. závažné nemoci nervového systému u organických sloučenin cínu,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám cínu, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám cínu, které jsou toxické pro reprodukci,
7. chronické nemoci jater a ledvin.', 9800),
        (99, '2.51.b Sloučeniny cínu (chlorid cínatý, se zvláštním zřetelem na R, pokud některá z těchto sloučenin má tuto klasifikaci)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. závažné poruchy krvetvorby (chlorid cínatý),
3. chronické kožní nemoci,
4. závažné nemoci nervového systému u organických sloučenin cínu,
5. opakované spontánní aborty u vysoké expozice těm sloučeninám cínu, které jsou toxické pro reprodukci,
6. poruchy fertility u osob ve fertilním věku, které se pro tento stav léčí, jestliže jde o práce s vysokou expozicí těm sloučeninám cínu, které jsou toxické pro reprodukci,
7. chronické nemoci jater a ledvin.', 9900),
        (100, '2.52. Sloučeniny selenu a teluru', false, 'FF7BA3CC', '', 'Nejsou známy.', '1. závažné chronické nemoci dýchacího systému,
2. chronické kožní nemoci,
3. závažné nemoci nervového systému,
4. chronické nemoci jater a ledvin,
5. anosmie.', 10000),
        (101, '2.53.a Uran a jeho sloučeniny (kromě nerozpustných sloučenin uranu, viz položka 2.53.b)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci jater a ledvin.', '1. závažné chronické nemoci dýchacího systému,
2. poruchy krvetvorby.', 10100),
        (102, '2.53.b Uran a jeho sloučeniny (nerozpustné sloučeniny uranu)', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci jater a ledvin.', '1. závažné chronické nemoci dýchacího systému,
2. poruchy krvetvorby.', 10200),
        (103, '2.54. Estery kyseliny dusičné (například nitroglycerin, dinitroglykol, pentrit, propylenglykoldinitrat,)', false, 'FF7BA3CC', '', '1. prognosticky závažné nemoci kardiovaskulárního systému,
2. dekompenzovaný diabetes mellitus,
3. záchvatovité a kolapsové stavy,
4. prokázaná porucha glukózo-6-P-dehydrogenázy.', '1. těžší poruchy metabolismu lipidů,
2. anémie,
3. nemoci kardiovaskulárního systému.', 10300),
        (104, '2.55. Anorganické a organické kyseliny', false, 'FF7BA3CC', '', 'prognosticky závažné chronické nemoci dýchacího systému.', '1. chronické nemoci dýchacího systému,
2. chronické kožní nemoci, jestliže vedle inhalační expozice je i riziko místního dráždivého nebo leptavého účinku.', 10400),
        (105, '2.56. Ethylenoxid (oxiran) a epichlorhydrin (1-chlor-2,3-epoxypropan; chlórmethyloxiran) (se zvláštním zřetelem na C a M)', false, 'FF7BA3CC', '', '1. zhoubné nemoci i po prodělané léčbě,
2. těžké imunodeficience nebo léčba oslabující imunitní systém.', '1. závažné nemoci nervového systému,
2. prekancerózy,
3. závažné chronické nemoci dýchacího systému,
4. závažné poruchy krvetvorby,
5. chronické nemoci jater a ledvin,
6. závažné chronické kožní nemoci.', 10500),
        (106, '2.57. Halogenované alkylethery a arylethery (například bis(chlormetyl)ether a chlorometylmethylether) (se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. zhoubná nemoci i po prodělané léčbě,
2. těžké imunodeficience nebo léčba oslabující imunitní systém.', '1. závažné chronické nemoci dýchacího systému,
2. chronické kožní nemoci,
3. chronické nemoci jater a ledvin.', 10600),
        (107, '2.58. Cytostatika (se zvláštním zřetelem na C)', false, 'FF7BA3CC', '', '1. zhoubné nemoci v léčbě,
2. těžké imunodeficience nebo léčba oslabující imunitní systém.', '1. závažné alergické nemoci,
2. zhoubné nemoci v anamnéze a prekancerózy,
3. chronické kožní nemoci,
4. chronické nemoci jater a ledvin,
5. závažné nemoci hematopoetického systému,
6. závažné chronické nemoci urogenitálního systému,
7. poruchy fertility,
8. postižení imunitního systému, léčba oslabující imunitní systém.', 10700),
        (108, '3. FYZIKÁLNÍ FAKTORY', true, 'FF99E6FF', '', '', '', 10800),
        (109, '3.1. Ionizující záření', false, 'FF99E6FF', '', '1. zhoubné nádory, pokud ukončení léčby nevede k uspokojivé stabilizaci orgánových funkcí,
2. nositelé odkryté chromozomové odchylky (například typu Nijmegen syndrom), která je spojena se zvýšeným rizikem radiogenních malignit,
3. těžké imunodeficience nebo léčba oslabující imunitní systém,
4. stavy po závažném poškození ionizujícím zářením,
5. závažné poruchy krvetvorby,
6. nemoci spojené se stavy bezvědomí,
7. prognosticky závažné duševní poruchy a poruchy chování,
8. prokázaná současná alkoholová nebo drogová závislost,
9. radiofobické tendence a stavy, které omezují možnost úniku z místa nehody v případě vzniku havárie.', '1. chronické kožní nemoci,
2. chronická anémie,
3. katarakta,
4. prekancerózy nebo paraneoplastické projevy,
5. závažné nemoci s významným orgánovým nebo funkčním postižením.', 10900),
        (110, '3.2. Elektromagnetické záření, včetně laserů', false, 'FF99E6FF', '', 'prognosticky závažné nemoci centrálního nervového systému.', '1. chronické nemoci centrálního nervového systému,
2. prognosticky závažné duševní poruchy a poruchy chování,
3. závažné kardiovaskulární nemoci, implantovaný kardiostimulátor či obdobný přístroj,
4. těhotenství, poruchy fertility a spermiogeneze.', 11000),
        (111, '3.3. Tepelná zátěž', false, 'FF99E6FF', '', '1. prognosticky závažné nemoci oběhové soustavy,
2. závažné chronické nemoci dýchacího systému,
3. závažné poruchy termoregulace.', '1. chronické nemoci oběhové soustavy,
2. chronické nemoci dýchacího systému,
3. endokrinní nemoci, závažná obezita,
4. chronické nemoci ledvin,
5. poruchy termoregulace.', 11100),
        (112, '3.4. Tepelné záření, které může způsobit zákal čočky', false, 'FF99E6FF', '', 'katarakta.', 'chronické zánětlivé nemoci zevního oka.', 11200),
        (113, '3.5. Hluk', false, 'FF99E6FF', '', 'prognosticky závažné poruchy sluchu.', '1. závažné poruchy sluchu,
2. chronické záněty středouší,
3. neurotizující ušní šelesty,
4. chronické neurózy.', 11300),
        (114, '3.6. Atmosférický přetlak', false, 'FF99E6FF', '', '1. poruchy krevního oběhu spojené s pravolevým zkratem,
2. spontánní pneumotorax v anamnéze,
3. nemožnost náležitého vyrovnávání tlaku mezi dutinou nosní a středouším nebo mezi dutinou nosní a vedlejšími dutinami nosními,
4. glaukom,
5. nedokonale sanovaný chrup s dutinami vyplněnými plynem pod plombami,
6. epilepsie u potápěčů a kesonářů,
7. klaustrofobie,
8. těhotenství.', '1. obezita,
2. nízký stupeň fyzické zdatnosti, netrénovanost,
3. závažné poruchy zrakové ostrosti,
4. závažné nemoci kardiovaskulárního systému, CNS, dýchacích cest, jater, ledvin, krve a krvetvorných orgánů, žláz s vnitřní sekrecí, zažívacího ústrojí, pohybového a podpůrného systému,
5. poruchy sluchu omezující komunikační schopnosti,
6. antikoagulační a antitrombotická léčba,
7. chronické infekční nemoci,
8. chronické kožní nemoci významného rozsahu nebo lokalizace,
9. inteligenční kvocient pod 90, alkoholová a jiná drogová závislost v anamnéze.', 11400),
        (115, '3.7. Atmosférický podtlak (hypobarie)', false, 'FF99E6FF', '', '1. poruchy krevního oběhu spojené s pravolevým zkratem,
2. spontánní pneumotorax v anamnéze,
3. nemožnost náležitého vyrovnávání tlaku mezi dutinou nosní a středouším nebo mezi dutinou nosní a vedlejšími dutinami nosními,
4. glaukom,
5. nedokonale sanovaný chrup s dutinami vyplněnými plynem pod plombami,
6. klaustrofobie,
7. těhotenství,
8. plicní hypertenze.', '1. závažné nemoci kardiovaskulárního systému, zvláště hypertonická choroba a poruchy periferní cirkulace,
2. závažné nemoci CNS, dýchacích cest, jater, ledvin, krve a krvetvorných orgánů, žláz s vnitřní sekrecí, zažívacího ústrojí, pohybového a podpůrného systému,
3. závažné poruchy zrakové ostrosti,
4. poruchy sluchu omezující komunikační schopnosti,
5. chronické infekční nemoci,
6. inteligenční kvocient pod 90, závažné duševní nemoci, alkoholová a jiná drogová závislost v anamnéze.', 11500),
        (116, '3.8. Vibrace s přenosem na horní končetiny', false, 'FF99E6FF', '', '1. Raynaudův syndrom,
2. prognosticky nepříznivé nemoci cév a nervů horních končetin,
3. závažné degenerativní a zánětlivé nemoci pohybového systému,
4. uznané a trvající ohrožení nemocí z povolání nebo nemoc z povolání z vibrací nebo z nadměrného a jednostranného přetěžování.', '1. abnormální EMG nález na horních končetinách,
2. diabetes mellitus s farmakologickou terapií,
3. Dupuytrenova kontraktům,
4. chronické zánětlivé nemoci rukou,
5. stavy po těžších omrzlinách rukou,
6. nemoci spojené s poruchou viskozity krve (polyglobulie, makroglobulinémie),
7. závažné kardiovaskulární nemoci.', 11600),
        (117, '3.9. Celkové vibrace a vibrace přenášené zvláštním způsobem', false, 'FF99E6FF', '', 'prognosticky závažné degenerativní a zánětlivé nemoci pohybového a podpůrného systému.', '1. závažné degenerativní a zánětlivá nemoci pohybového a podpůrného systému,
2. chronické nemoci trávicího ústroji,
3. arteriální poruchy prokrvení nohou.', 11700),
        (118, '3.10. Chladová zátěž', false, 'FF99E6FF', '', '1. závažné nemoci oběhové soustavy,
2. chladová alergie,
3. závažné nemoci dýchacího systému,
4. závažné porucha termoregulace.', '1. chronické nemoci oběhové soustavy,
2. závažné endokrinní nemoci,
3. chronické nemoci dýchacího systému,
4. chronické nemoci ledvin a močových cest,
5. závažné degenerativní a chronické zánětlivé nemoci pohybového systému,
6. porucha prokrvení končetin,
7. závažné chronické gynekologické zánětlivé nemoci,
8. porucha termoregulace.', 11800),
        (119, '4. FAKTORY FYZICKÉ ZÁTĚŽE', true, 'FFADCBF8', '', '', '', 11900),
        (120, '4.1. Celková fyzická zátěž', false, 'FFADCBF8', '', '1. prognosticky závažné nemoci kardiovaskulární soustavy,
2. prognosticky závažné nemoci dýchacího systému,
3. prognosticky závažné endokrinní nemoci,
4. prognosticky závažné nemoci pohybového a podpůrného systému,
5. morbidní obezita s indexem tělesné hmotnosti (BMI) nad 40.', '1. chronické nemoci kardiovaskulární soustavy,
2. chronické nemoci dýchacího systému,
3. závažné endokrinní nemoci,
4. závažné nemoci ledvin,
5. chronické nemoci jater,
6. závažné poruchy termoregulace,
7. chronické nemoci pohybového a podpůrného systému.', 12000),
        (121, '4.2. Nepřijatelné pracovní polohy', false, 'FFADCBF8', '', 'prognosticky závažné degenerativní a zánětlivé nemoci pohybového a podpůrného systému.', '1. závažné degenerativní a chronické zánětlivé nemoci pohybového a podpůrného systému,
2. závažné nemoci kardiovaskulárního a dýchacího systému.', 12100),
        (122, '4.3. Lokální svalová zátěž končetin', false, 'FFADCBF8', '', '1. prognosticky nepříznivé nemoci cév a nervů horních končetin,
2. závažné degenerativní a zánětlivé nemoci pohybového systému,
3. uznané a trvající ohrožení nemocí z povolání nebo nemoc z povolání z vibrací nebo z nadměrného a jednostranného přetěžování.', '1. abnormální EMG nález na horních končetinách,
2. diabetes mellitus s farmakologickou terapií,
3. chronické zánětlivé a degenerativní nemoci pohybového systému,
4. závažnější poúrazové a pooperační stavy.', 12200),
        (123, '5. FAKTORY PSYCHOSENZORICKÉ ZÁTĚŽE', true, 'FFCAB9AC', '', '', '', 12300),
        (124, '5.1.a Zraková zátěž (kromě vizuálního zkoušenímetodou NDT, viz položka 5.1.b)', false, 'FFCAB9AC', '', 'prognosticky závažné nekorigovatelné poruchy zraku.', 'závažné poruchy zraku.', 12400),
        (125, '5.1.b Zraková zátěž - vizuální zkoušení metodou NDT (defektoskopie)', false, 'FFCAB9AC', '', 'prognosticky závažné nekorigovatelné poruchy zraku.', 'závažné poruchy zraku.', 12500),
        (126, '5.2. Psychická zátěž', false, 'FFCAB9AC', '', '1. prognosticky závažné duševní poruchy a poruchy chování,
2. prokázaná současná drogová a alkoholová závislost.', '1. závažné duševní poruchy a poruchy chování,
2. závažné psychosomatické nemoci,
3. záchvatovité stavy a opakované kolapsové stavy,
4. chronické kardiovaskulární a respirační nemoci,
5. drogová a alkoholová závislost v anamnéze.', 12600),
        (127, '6. ŠKODLIVINY PŮSOBÍCÍ PROFESIONÁLNÍ NEMOCI DÝCHACÍCH CEST, PLIC, POHRUDNICE A POBŘIŠNICE', true, 'FFD6E4FC', '', '', '', 12700),
        (128, '6.1.a Prach s fibrogenním účinkem nebo možným fibrogenním, bez karcinogenního účinku', false, 'FFD6E4FC', '', '1. rozvinuté pneumokoniózy (i bez funkční poruchy),
2. závažné chronické nemoci dýchacího systému,
3. prodělaná tuberkulóza plic s výjimkou primárního komplexu,
4. chronické nebo recidivující nemoci pohrudnice,
5. těžší deformity hrudníku omezující plicní ventilaci,
6. závažné nemoci oběhové soustavy,
7. závažné chronické nemoci kůže a spojivek,
8. u expozice volnému oxidu křemičitému (SiO2) prekancerózy v oblasti dýchacího systému a stavy po léčení zhoubných tumorů respiračního systému,
9. splnění nejvyšší přípustné expozice fibrogennímu prachu.', '1. chronické nemoci kardiovaskulárního a dýchacího systému,
2. chronické nemoci kůže a spojivek.', 12800),
        (129, '6.1.b Prach s fibrogenním účinkem, možným fibrogenním a karcinogenním účinkem', false, 'FFD6E4FC', '', '1. rozvinuté pneumokoniózy (i bez funkční poruchy),
2. závažné chronické nemoci dýchacího systému,
3. prodělaná tuberkulóza plic s výjimkou primárního komplexu,
4. chronické nebo recidivující nemoci pohrudnice,
5. těžší deformity hrudníku omezující plicní ventilaci,
6. závažné nemoci oběhové soustavy,
7. závažné chronické nemoci kůže a spojivek,
8. u expozice volnému oxidu křemičitému (SiO2) prekancerózy v oblasti dýchacího systému a stavy po léčení zhoubných tumorů respiračního systému,
9. splnění nejvyšší přípustné expozice fibrogennímu prachu.', '1. chronické nemoci kardiovaskulárního a dýchacího systému,
2. chronické nemoci kůže a spojivek.', 12900),
        (130, '6.2. Prach s převážně nespecifickým účinkem', false, 'FFD6E4FC', '', '1. závažné chronické nemoci dýchacího systému,
2. těžší deformity hrudníku omezující plicní ventilaci.', '1. chronické nemoci kardiovaskulárního a dýchacího systému,
2. závažné alergické nemoci dýchacího systému,
3. chronické nemoci kůže a spojivek.', 13000),
        (131, '6.3. Svařování elektrickým obloukem s ohledem na inhalační expozici', false, 'FFD6E4FC', '', '1. rozvinuté pneumokoniózy (i bez funkční poruchy),
2. závažné chronické nemoci dýchacího systému,
3. prodělaná tuberkulóza plic s výjimkou primárního komplexu,
4. těžší deformity hrudníku omezující plicní ventilaci,
5. prekancerózy v oblasti dýchacího systému,
6. stavy po léčení tumorů respiračního systému.', '1. chronické nemoci dýchacího a kardiovaskulárního systému,
2. závažné chronické nemoci kůže a spojivek.', 13100),
        (132, '6.4. Krátkodobé produkty přeměny radonu (s přihlédnutím k vlivu dlouhodobých produktů přeměny uran-radiové řady)', false, 'FFD6E4FC', '', '1. závažné chronické nemoci dýchacího systému,
2. prekancerózy a maligní nemoci v oblasti kůže a dýchacích cest,
3. stavy po léčení tumorů respiračního systému,
4. stavy po závažném poškození ionizujícím zářením,
5. nekorigované závažné poruchy krvetvorby,
6. radiofobické tendence a stavy, které omezují možnost úniku z místa nehody v případě vzniku havárie.', '1. chronické nemoci dýchacího a kardiovaskulárního systému,
2. závažné chronické nemoci kůže a spojivek,
3. stav po operaci bazaliomu a po léčbě zhoubných tumorů kůže,
4. závažné nemoci s významným orgánovým nebo funkčním postižením včetně tumorů.', 13200),
        (133, '6.5. Koksárenské plyny a zplyňování uhlí', false, 'FFD6E4FC', '', '1. závažné chronické nemoci dýchacího systému,
2. prekancerózy a maligní nemoci v oblasti dýchacího systému a stavy po léčení tumorů respiračního systému,
3. závažné chronické nemoci jater a ledvin a močových cest,
4. chronické kožní nemoci,
5. chronické zánětlivé oční nemoci,
6. závažné stavy imunodeficience.', '1. chronické nemoci dýchacího systému i bez funkční poruchy,
2. chronické kožní nemoci,
3. chronické nemoci jater a ledvin a močových cest.', 13300),
        (134, '6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci', false, 'FFD6E4FC', '', '1. závažné chronické nemoci dýchacích cest, zejména prekancerózy a maligní nemoci a stavy po léčení tumorů dýchacího systému,
2. alergické nemoci dýchacích cest.', '1. chronické nemoci dýchacího systému,
2. chronické záněty nosních a paranazálních dutin,
3. chronické nemoci kůže a spojivek.', 13400),
        (135, '6.7. Alergeny a iritancia vyvolávající bronchiální astma a alergickou rinitidu - vysokomolekulární, nízkomolekulární alergeny a látky vyvolávající exogenní alergickou alveolitidu', false, 'FFD6E4FC', '', '1. chronická obstrukce dýchacích cest,
2. prokázaná přecitlivělost na látku z pracovního prostředí,
3. závažné chronické nemoci dýchacího systému, astma bronchiale a jiné alergické nemoci respiračního systému,
4. chronická bronchiální hyperreaktivita,
5. závažné chronické kožní nemoci, zvláště atopická a alergické dermatitida, kopřivka.', 'chronické nemoci dýchacího systému.', 13500),
        (136, '6.8. Prach s převážně dráždivým účinkem - textilní, živočišný, rostlinný, pryskyřic, PVC a další látky obdobného účinku', false, 'FFD6E4FC', '', '1. chronická obstrukce dýchacích cest,
2. závažné chronické nemoci dýchacího systému, astma bronchiale a jiné alergické nemoci respiračního systému,
3. závažné chronické kožní nemoci.', '1. chronické nemoci dýchacího systému,
2. chronické nemoci kůže a spojivek.', 13600),
        (137, '7. FYZIKÁLNÍ, CHEMICKÉ NEBO BIOLOGICKÉ FAKTORY, KTERÉ ZPŮSOBUJÍ KOŽNÍ NEMOCI', true, 'FFCCF2FF', '', '', '', 13700),
        (138, '7.1. Faktory, které způsobují maligní nádory kůže (sluneční záření, umělé UV záření)', false, 'FFCCF2FF', '', '1. prekancerózy, zhoubné nádory kůže i po vyléčení,
2. fotodermatózy,
3. fotosenzitivní kožní choroby,
4. fototyp I (pro expozici slunečnímu záření),
5. rosacea.', '1. závažné kožní nemoci významného rozsahu a lokalizace,
2. další související kožní nemoci řádně zdokumentované,
3. fototyp II (pro expozici slunečnímu záření).', 13800),
        (139, '7.2. Faktory, které způsobují kožní nemoci nenádorové povahy', true, 'FFCCF2FF', '', '', '', 13900),
        (140, '7.2.1. Kontaktní alergeny a látky, které mohou vyvolat senzibilizaci při styku s kůží, kontaktní iritancia které jsou látkami dráždícími kůži', false, 'FFCCF2FF', '', '1. kontaktní alergická dermatitida s prokázanou přecitlivělostí na konkrétní látku z daného pracovního prostředí,
2. prokázaná přecitlivělost na konkrétní látku z daného pracovního prostředí, a to i bez klinických známek nemoci.', '1. prognosticky závažné nemoci autoimunitní povahy,
2. chronický nebo recidivující ekzém jakékoliv etiologie.', 14000),
        (141, '7.2.2. Látky s převážně aknegenním účinkem typu chladících kapalin a řezných olejů s výjimkou látek v položce 2.43', false, 'FFCCF2FF', '', 'prognosticky závažné formy akné s projevy na obličeji a končetinách.', '1. chronické kožní nemoci,
2. těžší formy seborhoické dermatitidy,
3. těžší formy rosacei.', 14100),
        (142, '8. BIOLOGICKÉ FAKTORY', true, 'FFB4E0C6', '', '', '', 14200),
        (143, '8.1. Tuberkulóza', false, 'FFB4E0C6', '', 'imunodeficience specifické buněčné imunity.', '1. chronické nemoci oslabující obranyschopnost organizmu,
2. snížená obranyschopnost v důsledku podávání imunosupresiv, cytostatik, radiační terapie, dlouhodobého systémového podávání kortikosteroidů nebo antibiotik, biologická léčba.', 14300),
        (144, '8.2. Virové hepatitidy', false, 'FFB4E0C6', '', '1. těžká imunodeficience nebo léčba oslabující imunitní systém,
2. chronické nemoci jater.', 'chronické nemoci oslabující obranyschopnost organizmu.', 14400),
        (145, '8.3. Syndrom získané imunodeficience (AIDS)', false, 'FFB4E0C6', '', 'těžká imunodeficience nebo léčba oslabující imunitní systém.', 'chronické nemoci oslabující obranyschopnost organizmu.', 14500),
        (146, '8.4. Ostatní infekční nemoci', false, 'FFB4E0C6', '', 'těžká imunodeficience nebo léčba oslabující imunitní systém.', 'chronické nemoci oslabující obranyschopnost organizmu.', 14600),
        (147, '8.5. Tropické infekční a parazitární nemoci', false, 'FFB4E0C6', '', 'těžká imunodeficience nebo léčba oslabující imunitní systém.', 'chronické nemoci oslabující obranyschopnost organizmu.', 14700);
INSERT INTO dbe.occupational_hazards (id, title, color, description, ord)
VALUES  (1, '1.0', 'FF001ca6', 'Práce ve zdravotnických zařízeních', 100),
        (2, '1.1', 'FF001ca6', 'Práce v zařízeních sociálních služeb a v dalších zařízeních obdobného charakteru, jejíž součástí je obvyklý přímý kontakt s klienty nebo pacienty', 200),
        (3, '1.2', 'FF001ca6', 'Práce zahrnující poskytování sociálních služeb v přirozeném sociálním prostředí osob', 300),
        (4, '2.0', 'FF3891e6', 'Obsluha jeřábů, opraváři jeřábů, vazači jeřábových břemen', 400),
        (5, '2.1', 'FF3891e6', 'Obsluha transportních zařízení', 500),
        (6, '2.2', 'FF3891e6', 'Obsluha regálových zakladačů', 600),
        (7, '2.3', 'FF3891e6', 'Obsluha pracovních plošin', 700),
        (8, '2.4', 'FF3891e6', 'Obsluha důlních těžních strojů', 800),
        (9, '2.5', 'FF3891e6', 'Obsluha stavebních a jim obdobných strojů', 900),
        (10, '2.6', 'FF3891e6', 'Trvalá obsluha nákladních výtahů', 1000),
        (11, '3.0', 'FF4fa86a', 'Obsluha a řízení motorových a elektrických vozíků a obsluha vysokozdvižných vozíků', 1100),
        (12, '4.0', 'FFd8783c', 'Obsluha řídicích center a velínů velkých energetických zdrojů včetně jaderných a chemických provozů, při jejichž havárii by mohlo dojít k ohrožení zaměstnanců či obyvatelstva a k závažným ekologickým následkům', 1200),
        (13, '5.0', 'FFa74e67', 'Nakládání s výbušninami', 1300),
        (14, '5.1', 'FFa74e67', 'Opravy tlakových nádob a kotlů', 1400),
        (15, '5.2', 'FFa74e67', 'Obsluha kotlů s výkonem alespoň jednoho kotle 50 kW a větším a kotelen se součtem jmenovitých tepelných výkonů kotlů větším než 100 kW', 1500),
        (16, '5.3', 'FFa74e67', 'Obsluha tlakových nádob stabilních a tlakových stanic technických plynů', 1600),
        (17, '5.4', 'FFa74e67', 'Obsluha a opravy turbokompresorů, chladicích zařízení nad 40000 kcal (136360 kJ)', 1700),
        (18, '5.5', 'FFa74e67', 'Obsluha a opravy vysokonapěťových elektrických zařízení', 1800),
        (19, '5.6', 'FFa74e67', 'Práce na elektrických zařízeních podle jiných právních předpisů', 1900),
        (20, '6.0', 'FF794da6', 'Práce v hlubinných dolech', 2000),
        (21, '7.0', 'FF0000cc', 'Práce ve výškách a nad volnou hloubkou, pokud je jiným právním přepisem stanoveno použití osobních ochranných prostředků proti pádu', 2100),
        (22, '8.0', 'FF32c2f1', 'Práce záchranářů s výjimkou zdravotnických záchranářů', 2200),
        (23, '9.0', 'FF8e8145', 'Práce v klimaticky a epidemiologicky náročných oblastech zahraničí - do 6 měsíců doby trvání', 2300),
        (24, '9.1', 'FF8e8145', 'Práce v klimaticky a epidemiologicky náročných oblastech zahraničí - nad 6 měsíců doby trvání', 2400),
        (25, '10.0', 'FF000000', 'Noční práce: zaměstnanci pracující v noci podle § 78 odst. 1 písm. k) zákona č. 262/2006 Sb., zákoník práce, ve znění zákona č. 365/2011 Sb.', 2500),
        (26, '11.0', 'FFff0099', 'Další práce nebo činnosti v profesním riziku, které stanoví v rámci prevence rizik, v případech, kdy nelze tato rizika odstranit, zaměstnavatel ve spolupráci s poskytovatelem pracovnělékařských služeb', 2600),
        (27, '11.1', 'FFff0099', 'Práce zařazené do kategorie druhé, jejichž součástí jsou níže uvedené rizikové faktory pracovních podmínek podle části I.', 2700);
INSERT INTO dbe.citizenships (id, title)
VALUES  (1, 'Afghánistán'),
        (2, 'Alandy'),
        (3, 'Albánie'),
        (4, 'Alžírsko'),
        (5, 'Americká Samoa'),
        (6, 'Americké Panenské ostrovy'),
        (7, 'Andorra'),
        (8, 'Angola'),
        (9, 'Anguilla'),
        (10, 'Antarktida'),
        (11, 'Antigua a Barbuda'),
        (12, 'Argentina'),
        (13, 'Arménie'),
        (14, 'Aruba'),
        (15, 'Austrálie'),
        (16, 'Ázerbájdžán'),
        (17, 'Bahamy'),
        (18, 'Bahrajn'),
        (19, 'Bangladéš'),
        (20, 'Barbados'),
        (21, 'Belgie'),
        (22, 'Belize'),
        (23, 'Bělorusko'),
        (24, 'Benin'),
        (25, 'Bermudy'),
        (26, 'Bhútán'),
        (27, 'Bolívie'),
        (28, 'Bonaire, Svatý Eustach a Saba'),
        (29, 'Bosna a Hercegovina'),
        (30, 'Botswana'),
        (31, 'Bouvetův ostrov'),
        (32, 'Brazílie'),
        (33, 'Britské indickooceánské území'),
        (34, 'Britské Panenské ostrovy'),
        (35, 'Brunej'),
        (36, 'Bulharsko'),
        (37, 'Burkina Faso'),
        (38, 'Burundi'),
        (39, 'Cookovy ostrovy'),
        (40, 'Curaçao'),
        (41, 'Čad'),
        (42, 'Černá Hora'),
        (43, 'Česko'),
        (44, 'Čína'),
        (45, 'Dánsko'),
        (46, 'Dominika'),
        (47, 'Dominikánská republika'),
        (48, 'Džibutsko'),
        (49, 'Egypt'),
        (50, 'Ekvádor'),
        (51, 'Eritrea'),
        (52, 'Estonsko'),
        (53, 'Etiopie'),
        (54, 'Faerské ostrovy'),
        (55, 'Falklandy (Malvíny)'),
        (56, 'Fidži'),
        (57, 'Filipíny'),
        (58, 'Finsko'),
        (59, 'Francie'),
        (60, 'Francouzská Guyana'),
        (61, 'Francouzská jižní a antarktická území'),
        (62, 'Francouzská Polynésie'),
        (63, 'Gabon'),
        (64, 'Gambie'),
        (65, 'Ghana'),
        (66, 'Gibraltar'),
        (67, 'Grenada'),
        (68, 'Grónsko'),
        (69, 'Gruzie'),
        (70, 'Guadeloupe'),
        (71, 'Guam'),
        (72, 'Guatemala'),
        (73, 'Guernsey'),
        (74, 'Guinea'),
        (75, 'Guinea-Bissau'),
        (76, 'Guyana'),
        (77, 'Haiti'),
        (78, 'Heardův ostrov a MacDonaldovy ostrovy'),
        (79, 'Honduras'),
        (80, 'Hongkong'),
        (81, 'Chile'),
        (82, 'Chorvatsko'),
        (83, 'Indie'),
        (84, 'Indonésie'),
        (85, 'Irák'),
        (86, 'Írán'),
        (87, 'Irsko'),
        (88, 'Island'),
        (89, 'Itálie'),
        (90, 'Izrael'),
        (91, 'Jamajka'),
        (92, 'Japonsko'),
        (93, 'Jemen'),
        (94, 'Jersey'),
        (95, 'Jižní Afrika'),
        (96, 'Jižní Georgie a Jižní Sandwichovy ostrovy'),
        (97, 'Jižní Súdán'),
        (98, 'Jordánsko'),
        (99, 'Kajmanské ostrovy'),
        (100, 'Kambodža'),
        (101, 'Kamerun'),
        (102, 'Kanada'),
        (103, 'Kapverdy'),
        (104, 'Katar'),
        (105, 'Kazachstán'),
        (106, 'Keňa'),
        (107, 'Kiribati'),
        (108, 'Kokosové (Keelingovy) ostrovy'),
        (109, 'Kolumbie'),
        (110, 'Komory'),
        (111, 'Konžská demokratická republika'),
        (112, 'Konžská republika'),
        (113, 'Korejská lidově demokratická republika'),
        (114, 'Korejská republika'),
        (115, 'Kosovo'),
        (116, 'Kostarika'),
        (117, 'Kuba'),
        (118, 'Kuvajt'),
        (119, 'Kypr'),
        (120, 'Kyrgyzstán'),
        (121, 'Laos'),
        (122, 'Lesotho'),
        (123, 'Libanon'),
        (124, 'Libérie'),
        (125, 'Libye'),
        (126, 'Lichtenštejnsko'),
        (127, 'Litva'),
        (128, 'Lotyšsko'),
        (129, 'Lucembursko'),
        (130, 'Macao'),
        (131, 'Madagaskar'),
        (132, 'Maďarsko'),
        (133, 'Malajsie'),
        (134, 'Malawi'),
        (135, 'Maledivy'),
        (136, 'Mali'),
        (137, 'Malta'),
        (138, 'Man'),
        (139, 'Maroko'),
        (140, 'Marshallovy ostrovy'),
        (141, 'Martinik'),
        (142, 'Mauricius'),
        (143, 'Mauritánie'),
        (144, 'Mayotte'),
        (145, 'Menší odlehlé ostrovy USA'),
        (146, 'Mexiko'),
        (147, 'Mikronésie'),
        (148, 'Moldavsko'),
        (149, 'Monako'),
        (150, 'Mongolsko'),
        (151, 'Montserrat'),
        (152, 'Mosambik'),
        (153, 'Myanmar'),
        (154, 'Namibie'),
        (155, 'Nauru'),
        (156, 'Německo'),
        (157, 'Nepál'),
        (158, 'Niger'),
        (159, 'Nigérie'),
        (160, 'Nikaragua'),
        (161, 'Niue'),
        (162, 'Nizozemsko'),
        (163, 'Norfolk'),
        (164, 'Norsko'),
        (165, 'Nová Kaledonie'),
        (166, 'Nový Zéland'),
        (167, 'Omán'),
        (168, 'Pákistán'),
        (169, 'Palau'),
        (170, 'Palestina'),
        (171, 'Panama'),
        (172, 'Papua Nová Guinea'),
        (173, 'Paraguay'),
        (174, 'Peru'),
        (175, 'Pitcairn'),
        (176, 'Pobřeží slonoviny'),
        (177, 'Polsko'),
        (178, 'Portoriko'),
        (179, 'Portugalsko'),
        (180, 'Rakousko'),
        (181, 'Réunion'),
        (182, 'Rovníková Guinea'),
        (183, 'Rumunsko'),
        (184, 'Rusko'),
        (185, 'Rwanda'),
        (186, 'Řecko'),
        (187, 'Saint Pierre a Miquelon'),
        (188, 'Salvador'),
        (189, 'Samoa'),
        (190, 'San Marino'),
        (191, 'Saúdská Arábie'),
        (192, 'Senegal'),
        (193, 'Severní Makedonie'),
        (194, 'Severní Mariany'),
        (195, 'Seychely'),
        (196, 'Sierra Leone'),
        (197, 'Singapur'),
        (198, 'Slovensko'),
        (199, 'Slovinsko'),
        (200, 'Somálsko'),
        (201, 'Spojené arabské emiráty'),
        (202, 'Spojené státy'),
        (203, 'Srbsko'),
        (204, 'Středoafrická republika'),
        (205, 'Súdán'),
        (206, 'Surinam'),
        (207, 'Svatá Helena'),
        (208, 'Svatá Lucie'),
        (209, 'Svatý Bartoloměj'),
        (210, 'Svatý Kryštof a Nevis'),
        (211, 'Svatý Martin (FR)'),
        (212, 'Svatý Martin (NL)'),
        (213, 'Svatý Tomáš a Princův ostrov'),
        (214, 'Svatý Vincenc a Grenadiny'),
        (215, 'Svazijsko'),
        (216, 'Sýrie'),
        (217, 'Šalomounovy ostrovy'),
        (218, 'Španělsko'),
        (219, 'Špicberky a Jan Mayen'),
        (220, 'Šrí Lanka'),
        (221, 'Švédsko'),
        (222, 'Švýcarsko'),
        (223, 'Tádžikistán'),
        (224, 'Tanzanie'),
        (225, 'Thajsko'),
        (226, 'Tchaj-wan'),
        (227, 'Togo'),
        (228, 'Tokelau'),
        (229, 'Tonga'),
        (230, 'Trinidad a Tobago'),
        (231, 'Tunisko'),
        (232, 'Turecko'),
        (233, 'Turkmenistán'),
        (234, 'Turks a Caicos'),
        (235, 'Tuvalu'),
        (236, 'Uganda'),
        (237, 'Ukrajina'),
        (238, 'Uruguay'),
        (239, 'Uzbekistán'),
        (240, 'Vánoční ostrov'),
        (241, 'Vanuatu'),
        (242, 'Vatikán'),
        (243, 'Velká Británie a Severní Irsko'),
        (244, 'Venezuela'),
        (245, 'Vietnam'),
        (246, 'Východní Timor'),
        (247, 'Wallis a Futuna'),
        (248, 'Zambie'),
        (249, 'Západní Sahara'),
        (250, 'Zimbabwe');
INSERT INTO dbe.accident_insurance_corps (id, title) VALUES
    (1, 'Kooperativa pojišťovna a.s.'),
    (2, 'Generali Česká pojišťovna a.s.');
INSERT INTO dbe.roles (id, title)
VALUES  (1, 'Admin'),
        (2, 'Vedoucí zaměstnanec'),
        (3, 'Zaměstnanec'),
        (4, 'Odborně způsobilá osoba v BOZP'),
        (5, 'Odborně způsobilá osoba v PO'),
        (6, 'Personalista'),
        (7, 'Poskytovatel pracovnělékařských služeb'),
        (8, 'Vedoucí údržby'),
        (9, 'Údržbář'),
        (10, 'Revizní technik elektrických zařízení'),
        (11, 'Revizní technik tlakových zařízení'),
        (12, 'Revizní technik plynových zařízení'),
        (13, 'Revizní technik zdvihacích zařízení'),
        (14, 'Firemní ekolog');

INSERT INTO dbe.sex (id, title) VALUES
    (1, 'Muž'),
    (2, 'Žena')
INSERT INTO dbe.employer_relationships (id, title, full_name)
VALUES  (1, 'Zaměstnanec', 'Zaměstnanec (na základě pracovní smlouvy)'),
        (2, 'DPP', 'DPP (zaměstnanec na základě dohody o provedení práce)'),
        (3, 'DPČ', 'DPČ (zaměstnanec na základě dohody o pracovní činnosti)'),
        (4, 'Mimo pracovněprávní', 'Mimo pracovněprávní (osoba vykonávající činnosti nebo poskytující služby mimo pracovněprávní vztahy podle § 12, zákona č. 309/20'),
        (5, 'Žák', 'Žák (SŠ)'),
        (6, 'Student', 'Student (VOŠ/VŠ)'),
        (7, 'Dodavatel', 'Dodavatel'),
        (8, 'Zákazník', 'Zákazník'),
        (9, 'Státní zaměstnanec', 'Státní zaměstnanec'),
        (10, 'Jiná osoba', 'Jiná osoba');
INSERT INTO dbe.driving_licence_groups (id, title)
VALUES  (1, 'AM'),
        (2, 'A1'),
        (3, 'A2'),
        (4, 'A'),
        (5, 'B1'),
        (6, 'B'),
        (7, 'C1'),
        (8, 'C'),
        (9, 'D1'),
        (10, 'D'),
        (11, 'B+E'),
        (12, 'C1+E'),
        (13, 'C+E'),
        (14, 'D1+E'),
        (15, 'D+E'),
        (16, 'T');
INSERT INTO dbe.person_states (id, title)
VALUES  (1, 'Aktivní'),
        (2, 'Dočasně práce neschopný'),
        (3, 'Dlouhodobě práce neschopný'),
        (4, 'Dlouhodobě práce neschopný pro pracovní úraz'),
        (5, 'Dočasně práce neschopný pro pracovní úraz'),
        (6, 'Mateřská / rodičovská dovolená'),
        (7, 'Dovolená'),
        (8, 'Neplacené volno'),
        (9, 'Přeložený'),
        (10, 'Dočasně přidělený'),
        (11, 'Převedený na jinou práci'),
        (12, 'Uvolněný'),
        (13, 'Výpovědní lhůta'),
        (14, 'Propuštěný');

INSERT INTO dbe.healthy_insurance_corps (id, title)
VALUES  (1, 'Neuvedeno'),
        (2, '111 - Všeobecná zdravotní pojišťovna České republiky'),
        (3, '201 - Vojenská zdravotní pojišťovna České republiky'),
        (4, '205 - Česká průmyslová zdravotní pojišťovna'),
        (5, '207 - Oborová zdravotní pojišťovna zaměstnanců bank, pojišťoven a stavebnictví'),
        (6, '209 - Zaměstnanecká pojišťovna Škoda'),
        (7, '211 - Zdravotní pojišťovna ministerstva vnitra České republiky'),
        (8, '213 - RBP, zdravotní pojišťovna');

INSERT INTO dbe.working_modes (id, title, enum) VALUES
    (1, 'Jednosměnný', 'single_shift'),
    (2, 'Dvousměnný', 'double_shift'),
    (3, 'Třísměnný', 'triple_shift'),
    (4, 'Nepravidelný', 'irregular_shift'),
    (5, 'Pružná pracovní doba', 'flexible_shift')
INSERT INTO dbe.job_categorization_factors (id, title, measurement)
VALUES  (1, 'Prach', true),
        (2, 'Chemické látky a směsi', true),
        (3, 'Hluk', true),
        (4, 'Vibrace', true),
        (5, 'Neionizující záření', true),
        (6, 'Fyzická zátěž', true),
        (7, 'Pracovní poloha', true),
        (8, 'Zátěž teplem', true),
        (9, 'Zátěž chladem', true),
        (10, 'Psychická zátěž', false),
        (11, 'Zraková zátěž', false),
        (12, 'Práce s biologickými činiteli', false),
        (13, 'Práce ve zvýšeném tlaku vzduchu', false);
INSERT INTO dbe.job_category (id, title)
VALUES  (1, '1'),
        (2, '2'),
        (3, '2R'),
        (4, '3'),
        (5, '4');
INSERT INTO dbe.legal_req_for_med_fitness (id, title, color, description, ord)
VALUES  (1, '1.0', 'FF999999', 'Řidiči vozidel
(dle § 87, zákona č. 361/2000 Sb., úz)', 100),
        (2, '2.0', 'FFA74809', 'Při provozování dráhy a drážní dopravy přímo zabezpečuje obsluhu dráhy, zabezpečuje nebo organizuje drážní dopravu a řídí se při tom tvarovými, světelnými a zvukovými znaky a návěstmi nebo je dává
(k písm. a), odst. (3), § 2, vyhlášky č. 260/2023 Sb., úz)', 200),
        (3, '2.1', 'FFA74809', 'Vstupuje bez dozoru na provozovanou dopravní cestu a nepodílí se přímo na zabezpečení obsluhy dráhy, ani na zabezpečení nebo organizování drážní dopravy
(k písm. b), osdt. (3), § 2, vyhlášky č. 260/2023 Sb., úz)', 300),
        (4, '2.2', 'FFA74809', 'Provádí revize, prohlídky a zkoušky určených technických zařízení
(k písm. c), odst. (3), § 2, vyhlášky č. 260/2023 Sb., úz)', 400),
        (5, '2.3', 'FFA74809', 'Osoba řídící drážní vozidlo - průkaz způsobilosti
(k § 5, vyhlášky č. 260/2023 Sb., úz)', 500),
        (6, '2.4', 'FFA74809', 'Strojvedoucí - licence
(k odst. (5), § 7, vyhlášky č. 260/2023 Sb., úz)', 600),
        (7, '3.0', 'FFC0C0C0', 'Strážník městské policie
(k § 3, vyhlášky č. 444/2008 Sb., úz)', 700),
        (8, '4.0', 'FFA9A9A9', 'Držitelé zbrojních průkazů skupiny D nebo F
(k § 2, vyhlášky č. 493/2002 Sb., úz)', 800),
        (9, '4.1', 'FF808080', 'Držitelé zbrojních průkazů skupiny E
(k § 2, vyhlášky č. 493/2002 Sb., úz)', 900),
        (10, '5.0', 'FF669933', 'Radiační pracovník kategorie A
(k § 80, zákona č. 263/2016 Sb., úz)', 1000),
        (11, '6.0', 'FF336699', 'Odborně způsobilá osoba pro provádění, organizování, řízení a vyhodnocování zkoušek těsnosti
(k § 6a odst.2 písm.a), vyhlášky č. 450/2005 Sb., úz)', 1100),
        (12, '7.0', 'FF4D0000', 'Zaměstnanec jednotky hasičského záchranného sboru podniku - kategorie I
(k nařízení vlády č. 352/2003 Sb., úz)', 1200),
        (13, '7.1', 'FF660000', 'Zaměstnanec jednotky hasičského záchranného sboru podniku - kategorie II
(k nařízení vlády č. 352/2003 Sb., úz)', 1300),
        (14, '7.2', 'FF800000', 'Zaměstnanec jednotky hasičského záchranného sboru podniku - kategorie III
(k nařízení vlády č. 352/2003 Sb., úz)', 1400),
        (15, '7.3', 'FF990000', 'Zaměstnanec jednotky hasičského záchranného sboru podniku - nositel dýchací techniky (kategorie II, III)
(k § 2 odst.2 nařízení vlády č. 352/2003 Sb., úz)', 1500),
        (16, '7.4', 'FFB30000', 'Člen jednotky sboru dobrovolných hasičů obce (kategorie IV)
(k nařízení vlády č. 352/2003 Sb., úz)', 1600),
        (17, '7.5', 'FFCC0000', 'Člen jednotky sboru dobrovolných hasičů podniku (kategorie IV)
(k nařízení vlády č. 352/2003 Sb., úz)', 1700),
        (18, '7.6', 'FFE60000', 'Člen jednotky sboru dobrovolných hasičů obce (kategorie IV) - nositel dýchací techniky
(k § 2 odst. 4 nařízení vlády č. 352/2003 Sb., úz)', 1800),
        (19, '7.7', 'FFFF1A1A', 'Člen jednotky sboru dobrovolných hasičů podniku (kategorie IV) - nositel dýchací techniky
(k § 2 odst. 4 nařízení vlády č. 352/2003 Sb., úz)', 1900),
        (20, '8.0', 'FFFF9900', 'Zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4
(k § 10, vyhlášky č. 284/2016 Sb., úz)', 2000),
        (21, '9.0', 'FF001830', 'Kapitáni plavidel
(dle vyhlášky. č. 112/2015 Sb., úz)', 2100),
        (22, '9.1', 'FF002851', 'Palubní důstojník
(dle vyhlášky. č. 112/2015 Sb., úz)', 2200),
        (23, '9.2', 'FF003972', 'Palubní posádka
(dle vyhlášky. č. 112/2015 Sb., úz)', 2300),
        (24, '9.3', 'FF004B8E', 'Osoba zařazená k výkonu navigační strážní služby
(dle vyhlášky. č. 112/2015 Sb., úz)', 2400),
        (25, '9.4', 'FF005FA1', 'Osoba zařazená k výkonu hlídkové služby
(dle vyhlášky. č. 112/2015 Sb., úz)', 2500),
        (26, '9.5', 'FF0073B3', 'Strojní strážní služba
(dle vyhlášky. č. 112/2015 Sb., úz)', 2600),
        (27, '9.6', 'FF0086C7', 'Strojní posádka
(dle vyhlášky. č. 112/2015 Sb., úz)', 2700);

INSERT INTO dbe.medical_check_types (id, title, full_name)
VALUES  (1, 'Vstupní', 'Vstupní pracovnělékařská prohlídka'),
        (2, 'Periodická', 'Periodická pracovnělékařská prohlídka'),
        (3, 'Mimořádná', 'Mimořádná pracovnělékařská prohlídka'),
        (4, 'Výstupní', 'Výstupní pracovnělékařská prohlídka'),
        (5, 'Následná', 'Následná pracovnělékařská prohlídka');
SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.centres (
    id,
    code,
    name,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    '9999',
    'SAWAPP',
    'Toto je vzorové středisko. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
);

INSERT INTO dbh.centres (
    id,
    operation,
    record_id,
    code,
    name,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
     DEFAULT,
     'I',
     1,
     '9999',
     'SAWAPP',
     'Toto je vzorové středisko. Upravte dle potřeby.',
     1,
     '',
     0,
     '2024-01-01 00:00:00.000000'
 );

SET SESSION_REPLICATION_ROLE TO DEFAULT;

INSERT INTO dbo.objects (
    id,
    name,
    address,
    description,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    'Budova SAWAPP',
    'Bezpečná 22, Velká Přilba, 77709',
    'Toto je vzorový objekt. Upravte dle potřeby.',
    '',
    0,
    '2024-01-01 00:00:00.000000'
    );

SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.workplaces (
    id,
    object,
    name,
    floorlevel,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    1,
    'Pracoviště SAWAPP',
    1,
    'Toto je vzorové pracovitě. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
    );

INSERT INTO dbh.workplaces (
    id,
    operation,
    record_id,
    object,
    name,
    floorlevel,
    description,
    leader,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    'I',
    1,
    1,
    'Pracoviště SAWAPP',
    1,
    'Toto je vzorové pracovitě. Upravte dle potřeby.',
    1,
    '',
    0,
    '2024-01-01 00:00:00.000000'
);

SET SESSION_REPLICATION_ROLE TO DEFAULT;

INSERT INTO dbo.professions (
    id,
    name,
    type_of_work,
    cz_isco,
    workplace,
    working_time,
    interrupt_mode,
    working_mode,
    workload,
    add_info,
    note,
    modified_by,
    modified_at
) VALUES (
    DEFAULT,
    'SAWAPP',
    'Toto je vzorová profese. Upravte dle potřeby.',
    0,
    1,
    '6:00 - 14:30',
    '1 x 30 min nejdéle po 6 odpracovaných hodinách ( 11:00 - 11:30)',
    1,--jednosměnný
    '',
    '',
    '',
    0,
    '2024-01-01 00:00:00.000000'
);
--SET SESSION_REPLICATION_ROLE TO REPLICA;

INSERT INTO dbo.users (
    id,
    name,
    roles,
    passhash,
    need_update_password,
    valid,
    note,
    modified_by,
    modified_at
) VALUES (
    0,
    'agentsaw',
    '{1}',
    '',
    false,
    true,
    'Technický uživatel. Needitovat!',
    0,--sám se zapsal
    '2024-01-01 00:00:00.000000'
);
/*
INSERT INTO dbh.people (
    id,
    operation,
    record_id,
    degree_before,
    name,
    surname,
    degree_after,
    sex,
    e_relationship,
    leader,
    note,
    modified_by,
    modified_at,
    email,
    employer,
    centre,
    profession,
    state,
    phone
) VALUES (
    0,
    'I',
    0,
    1,--bez titulu
    'Agent',
    'SAW',
    2,--bez titulu
    1,--muž
    10,--jiná osoba
    0,--sám sobě leaderem
    'Technický uživatel. Needitovat!',
    0,--sám se zapsal
    '2024-01-01 00:00:00.000000',
    '<EMAIL>',
    1,
    0,--středisko agenta saw
    1,
    1,--aktivni
    ''
);

SET SESSION_REPLICATION_ROLE TO DEFAULT;*/
INSERT INTO dbe.examination_types (id, title, tooltip)
VALUES  (1, 'Alfa1 mikroglobulin nebo N-acetylglukosaminidáza v moči', ''),
        (2, 'Alfa1 mikroglobuliny', ''),
        (3, 'Alfa1 mikroglobuliny v moči', ''),
        (4, 'ALP', ''),
        (5, 'ALT', ''),
        (6, 'Amyláza v krvi', ''),
        (7, 'Anti HBc total', ''),
        (8, 'Anti HCV', ''),
        (9, 'AST', ''),
        (10, 'Audiometrické vyšetření', ''),
        (11, 'Bilirubin', ''),
        (12, 'CRP', ''),
        (13, 'Další odborná vyšetření (strážník městské policie)', 'Další nezbytná odborná vyšetření podle vyžádání posuzujícího lékaře'),
        (14, 'Další odborná vyšetření (uchazeč, čekatel, strážník městské policie)', 'Další odborná vyšetření nezbytná pro posouzení zdravotní způsobilosti
uchazeče / čekatele / strážníka'),
        (15, 'Další odborná vyšetření podle charakteru a míry příslušného rizika', ''),
        (16, 'Další odborná vyšetření (práce s chemickými látkami)', 'Další odborná vyšetření podle míry expozice a povahy toxického,
senzibilizujícího nebo dráždivého účinku, popřípadě dalších účinků
chemických látek nebo směsí podle jiného právního předpisu'),
        (17, 'Další odborná vyšetření s přihlédnutím k BET', 'Další odborná vyšetření podle míry expozice, povahy toxického, senzibilizujícího
nebo dráždivého účinku, popřípadě dalších účinků chemických látek
nebo směsí podle jiného právního předpisu s přihlédnutím k výsledkům
biologických expozičních testů (BET)'),
        (18, 'Další odborná vyšetření (držitel zbrojního průkazu D, E, F)', 'Další potřebné odborné vyšetření v případě, že u žadatele je nezbytné vyloučit
podezření na nemoc, která omezuje nebo vylučuje zdravotní způsobilost žadatele'),
        (19, 'Diff', ''),
        (20, 'Dopravně psychologické vyšetření', ''),
        (21, 'EKG', ''),
        (22, 'EKG zátěžové', ''),
        (23, 'EMG v rozsahu stanovení distální motorické latence středových nervů', ''),
        (24, 'FW', ''),
        (25, 'FW nebo CRP', ''),
        (26, 'GMT', ''),
        (27, 'GMT u prokazatelné vysoké expozice hepatotoxickým látkám', ''),
        (28, 'Hodnocení ztráty sluchu podle Fowlera', ''),
        (29, 'Chemické vyšetření moče a močového sedimentu', ''),
        (30, 'Chemické vyšetření moči - orientační', ''),
        (31, 'Chladový test', ''),
        (32, 'Cholesterol', ''),
        (33, 'KO', ''),
        (34, 'Komplexní fyzikální vyšetření', ''),
        (35, 'Komplexní fyzikální vyšetření interního charakteru', ''),
        (36, 'Kontrola povinného očkování proti VHB', ''),
        (37, 'Krční vyšetření', ''),
        (38, 'Kreatinin', ''),
        (39, 'Kreatinin u hepatotoxických nebo nefrotoxických uhlovodíků', ''),
        (40, 'Kreatinin v krvi', ''),
        (41, 'Kreatinin v krvi u expozice nefrotoxickým látkám', ''),
        (42, 'Laboratorní vyšetření glykémie', ''),
        (43, 'Laboratorní vyšetření krevního obrazu', ''),
        (44, 'Močový sediment', ''),
        (45, 'Neprovádí se', ''),
        (46, 'Neurologické vyšetření - odborné', ''),
        (47, 'Neurologické vyšetření - orientační', ''),
        (48, 'Neurologické vyšetření, včetně elektroencefalografického', ''),
        (49, 'Nosní vyšetření', ''),
        (50, 'Oční vyšetření', ''),
        (51, 'Oční vyšetření - odborné', ''),
        (52, 'Oční vyšetření - orientační', ''),
        (53, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb.,', ''),
        (54, 'Orientační test na přítomnost psychoaktivních látek', ''),
        (55, 'ORL vyšetření', ''),
        (56, 'ORL vyšetření - odborné', ''),
        (57, 'ORL vyšetření - orientační', ''),
        (58, 'Otorinolaryngologické vyšetření', ''),
        (59, 'Otorinolaryngologické vyšetření - orientační', ''),
        (60, 'Otoskopické vyšetření', ''),
        (61, 'Plumbémie', ''),
        (62, 'Posouzení kognitivních funkcí včetně komunikace', 'Posouzení kognitivních funkcí, zejména pozornosti a koncentrace, paměti,
vnímání, logického myšlení a komunikace'),
        (63, 'Posouzení kognitivních funkcí, včetně vnímání a usuzování', 'Posouzení kognitivních funkcí, zejména pozornosti, koncentrace, paměti,
vnímání a usuzování'),
        (64, 'Posouzení komunikačních dovedností', ''),
        (65, 'Posouzení osobnostních vlastností', 'Posouzení osobnostních vlastností, zejména spolehlivost, samostatnost,
svědomitost a ovládání vlastních emocí'),
        (66, 'Posouzení psychomotorických funkcí včetně koordinace rukou', 'Posouzení psychomotorických funkcí, zejména rychlost reakce
a koordinace rukou'),
        (67, 'Posouzení psychomotorických funkcí včetně pohybové koordinace', 'Posouzení psychomotorických funkcí, zejména rychlost reakce
a pohybová koordinace'),
        (68, 'Prahová tónová audiometrie', ''),
        (69, 'Prahová tónová audiometrie nebo screeningová audiometrie', ''),
        (70, 'Prstová pletyzmografie', ''),
        (71, 'Ostatní laboratorní vyšetření podle anamnézy', 'Případná ostatní laboratorní vyšetření podle anamnézy a fyzikálního vyšetření'),
        (72, 'PSA', ''),
        (73, 'Psychiatrické a psychologické vyšetření', ''),
        (74, 'Psychiatrické vyšetření', ''),
        (75, 'Quantiferon nebo Mantoux II', ''),
        (76, 'Retikulocyty', ''),
        (77, 'Rinoskopie', ''),
        (78, 'Rinoskopie rinoskopem', ''),
        (79, 'Rinoskopie zrcátkem', ''),
        (80, 'Rombergův test', ''),
        (81, 'Rozbor údajů o dosavadním vývoji zdravotního stavu', 'Rozbor údajů o dosavadním vývoji zdravotního stavu a dosud prodělaných
nemocech, stavech nebo vadách (dále jen „nemoc“) s cíleným zaměřením
zejména na výskyt nemocí, které mohou omezit nebo vyloučit zdravotní
způsobilost k zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4.'),
        (82, 'RTG hrudníku', ''),
        (83, 'RTG plic', ''),
        (84, 'Screeningová audiometrie', ''),
        (85, 'Sérologie AIDS (HIV)', ''),
        (86, 'Sérologie virových hepatitid-HBsAg', ''),
        (87, 'Spirometrie', ''),
        (88, 'Spirometrie pouze při předpokládané inhalační expozici', ''),
        (89, 'Spirometrie u prací s významnou inhalační expozicí', ''),
        (90, 'Spirometrie u prašné expozice sloučeninám manganu', ''),
        (91, 'Stanovení aktivity cholinesterázy nebo acetylcholinesterázy v krvi', ''),
        (92, 'Stanoví lékař', ''),
        (93, 'Stomatologické vyšetření', ''),
        (94, 'Test kožní alkalirezistence', ''),
        (95, 'Triglyceridy', ''),
        (96, 'Ultrasonografie břicha', ''),
        (97, 'Ušní vyšetření', ''),
        (98, 'Vodní chladový test', ''),
        (99, 'Vyšetření barvocitu - orientační', ''),
        (100, 'Vyšetření elektrokardiografické', ''),
        (101, 'Vyšetření glykémie', ''),
        (102, 'Vyšetření kůže - orientační', ''),
        (103, 'Vyšetření moči', ''),
        (104, 'Vyšetření obou očí pro zjištění efektivity vidění', 'Vyšetření obou očí pro zjištění efektivity vidění, zejména zrakové ostrosti
a barvocitu'),
        (105, 'Vyšetření očí pro zjištění efektivity vidění', 'Vyšetření očí pro zjištění efektivity vidění, zejména zrakové ostrosti a barvocitu'),
        (106, 'Vyšetření očního pozadí', ''),
        (107, 'Vyšetření ortopedické - orientační', ''),
        (108, 'Vyšetření podpůrného a pohybového aparátu - orientační', ''),
        (109, 'Vyšetření prostorového vidění', ''),
        (110, 'Vyšetření rovnováhy - orientační', ''),
        (111, 'Vyšetření rovnováhy (dráha celostátní nebo regionální)', 'Vyšetření rovnováhy, a to s cíleným zaměřením na zjištění příznaků nemocí,
které vylučují nebo podmiňují zdravotní způsobilost k řízení drážního vozidla
na dráze celostátní nebo regionální'),
        (112, 'Vyšetření rovnováhy (dráha místní, tramvajová, speciální, lanová a vlečka)', 'Vyšetření rovnováhy, a to s cíleným zaměřením na zjištění příznaků nemocí,
které vylučují nebo podmiňují zdravotní způsobilost k řízení drážního vozidla
na dráze místní, tramvajové, speciální nebo lanové anebo na vlečce'),
        (113, 'Vyšetření rovnováhy (pracovní činnosti na dráze)', 'Vyšetření rovnováhy, a to s cíleným zaměřením na zjištění příznaků nemocí,
které vylučují nebo podmiňují zdravotní způsobilost k výkonu dané
pracovní činnosti'),
        (114, 'Vyšetření rovnováhy (řízení, celostátní a regionální dráha)', 'Vyšetření rovnováhy, a to s cíleným zaměřením na zjištění příznaků nemocí,
které vylučují nebo podmiňují zdravotní způsobilost k výkonu práce spočívající
v řízení drážního vozidla na dráze celostátní nebo regionální'),
        (115, 'Vyšetření sluchu - orientační', ''),
        (116, 'Vyšetření šerosleposti', ''),
        (117, 'Vyšetření vnímání barev', ''),
        (118, 'Vyšetření vnímání barev pomocí testu Ishihara nebo jiného uznaného testu', ''),
        (119, 'Vyšetření vnímání barev pomocí testu Ishihara (rozpoznání barev)', 'Vyšetření vnímání barev pomocí testu Ishihara nebo jiného uznaného testu;
při rozpoznávání barevných odstínů test musí být založen na rozpoznávání
jednotlivých barev, nikoliv na relativních rozdílech'),
        (120, 'Vyšetření zorného pole - orientační', ''),
        (121, 'Vyšetření zrakové ostrosti - orientační', ''),
        (122, 'Vyšetření zraku - orientační', ''),
        (123, 'Vyšetření zraku - orientační (ostrost, barvocit a prostorové vidění)', ''),
        (124, 'Základní chemické vyšetření moče', 'Základní chemické vyšetření moče ke zjištění přítomnosti bílkoviny,
glukózy, ketonů, urobilinogenu, krve a pH moče'),
        (125, 'Základní spirometrické vyšetření', ''),
        (126, 'Základní vyšetření', ''),
        (127, 'Základní vyšetření: pracovní anamnéza', 'Základní vyšetření, které zahrnuje: pracovní anamnézu;
zejména se sleduje odezva organizmu na výskyt rizikových faktorů'),
        (128, 'Základní vyšetření: rozbor vývoje zdravotního stavu', 'Základní vyšetření, které zahrnuje: rozbor údajů o dosavadním vývoji
zdravotního stavu a dosud prodělaných nemocech s cíleným zaměřením
zejména na výskyt nemocí, které mohou omezit nebo vyloučit zdravotní
způsobilost, nebo nemocí, které by se pravděpodobně zhoršily v důsledku
služby na moři'),
        (129, 'Zaměření na možnost rozvoje parkinsonského syndromu', ''),
        (130, 'Zjištění anamnestických údajů o zdravotním stavu žadatele', 'Zjištění anamnestických údajů o zdravotním stavu žadatele s cíleným zaměřením na vyloučení nemocí, které zdravotní způsobilost vylučují nebo omezují'),
        (131, 'Zjištění o zdravotním stavu posuzované osoby (řidiči)', 'Zjištění o zdravotním stavu posuzované osoby s cíleným zaměřením na nemoci, které zdravotní způsobilost k řízení motorových vozidel vylučují nebo podmiňují'),
        (132, 'Zjištění průchodnosti Eustachovy trubice', ''),
        (133, 'Zjištění zdravotního stavu (práce na drahách)', 'Zjištění zdravotního stavu a tělesné způsobilosti osoby s cíleným zaměřením
na nemoci, které vylučují nebo podmiňují zdravotní způsobilost k výkonu dané
pracovní činnosti'),
        (134, 'Zjištění zdravotního stavu (řízení, celostátní a regionální dráha)', 'Zjištění zdravotního stavu a tělesné způsobilosti s cíleným zaměřením na
nemoci, které vylučují nebo podmiňují zdravotní způsobilost k řízení drážního
vozidla na dráze celostátní nebo regionální'),
        (135, 'Zjištění zdravotního stavu (dráha místní, tramvajová, speciální, lanová a vlečka)', 'Zjištění zdravotního stavu a tělesné způsobilosti s cíleným zaměřením na
nemoci, které vylučují nebo podmiňují zdravotní způsobilost k řízení drážního
vozidla na dráze místní, tramvajové, speciální nebo lanové anebo na vlečce'),
        (136, 'Zjištění zdravotního stavu (řízení a práce, celostátní a regionální dráha)', 'Zjištění zdravotního stavu a tělesné způsobilosti s cíleným zaměřením na
nemoci, které vylučují nebo podmiňují zdravotní způsobilost k výkonu práce
spočívající v řízení drážního vozidla na dráze celostátní nebo regionální'),
        (137, 'Zkouška rovnováhy', ''),
        (138, 'Další odborná vyšetření indikována lékařem', '');
INSERT INTO dbe.pdf_classes (id, title) VALUES (1, 'Prohlídka');
INSERT INTO dbe.pdf_types (id, title, class ,version, commandline) VALUES (1, 'Prohlídka', 1, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class, version, commandline) VALUES (2, 'Prohlídka (barevně)', 1, '1.0', '');
INSERT INTO dbe.healthy_restriction_types (id, title)
VALUES  (1, 'Prach'),
        (2, 'Chemické látky'),
        (3, 'Hluk'),
        (4, 'Vibrace'),
        (5, 'Záření'),
        (6, 'Fyzická zátěž - celková '),
        (7, 'Fyzická zátěž - lokální'),
        (8, 'Pracovní poloha'),
        (9, 'Teplo'),
        (10, 'Chlad'),
        (11, 'Psychická zátěž'),
        (12, 'Zrak'),
        (13, 'Biologické činitele'),
        (14, 'Noční práce'),
        (15, 'Směnnost'),
        (16, 'Pracovní doba a přestávky'),
        (17, 'Řízení dopravních prostředků'),
        (18, 'Práce ve výškách'),
        (19, 'Kompenzační pomůcky'),
        (20, 'Jiné');
INSERT INTO dbe.examinations (id, occup_hazard, r_f_working_cond, legal_req, check_type, examination, period_under_50_ktg1, period_under_50_ktg2, period_under_50_ktg2r, period_under_50_ktg3, period_under_50_ktg4, period_over_50_ktg1, period_over_50_ktg2, period_over_50_ktg2r, period_over_50_ktg3, period_over_50_ktg4, examination_delay, examination_period_under50, examination_period_over50, valid_over_50_only, medical_info, exception_fn)
VALUES  (1, NULL, 2, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2, NULL, 2, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3, NULL, 2, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (4, NULL, 2, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (5, NULL, 2, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (6, NULL, 2, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (7, NULL, 2, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (8, NULL, 2, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (9, NULL, 2, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (10, NULL, 2, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (11, NULL, 2, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (12, NULL, 2, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (13, NULL, 2, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (14, NULL, 2, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (15, NULL, 2, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (16, NULL, 2, NULL, 4, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (17, NULL, 2, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (18, NULL, 2, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (19, NULL, 2, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (20, NULL, 2, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (21, NULL, 2, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (22, NULL, 3, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (23, NULL, 3, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (24, NULL, 3, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (25, NULL, 3, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (26, NULL, 3, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (27, NULL, 3, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (28, NULL, 3, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (29, NULL, 3, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (30, NULL, 3, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (31, NULL, 3, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (32, NULL, 3, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (33, NULL, 3, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (34, NULL, 3, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (35, NULL, 3, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (36, NULL, 3, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (37, NULL, 3, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (38, NULL, 3, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (39, NULL, 3, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (40, NULL, 3, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (41, NULL, 3, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (42, NULL, 3, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (43, NULL, 3, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (44, NULL, 4, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (45, NULL, 4, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (46, NULL, 4, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (47, NULL, 4, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (48, NULL, 4, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (49, NULL, 4, NULL, 2, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (50, NULL, 4, NULL, 2, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (51, NULL, 4, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (52, NULL, 4, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (53, NULL, 4, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (54, NULL, 4, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (55, NULL, 4, NULL, 1, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (56, NULL, 4, NULL, 1, 16, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (57, NULL, 4, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (58, NULL, 4, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (59, NULL, 4, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (60, NULL, 4, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (61, NULL, 4, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (62, NULL, 4, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (63, NULL, 4, NULL, 4, 41, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (64, NULL, 4, NULL, 4, 17, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (65, NULL, 4, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 1.1.c Práce s chemickými látkami - látky s pozdním účinkem karcinogenním a mutagenním a u látek s fibrogenním účinkem, se následná prohlídka provádí na základě rozhodnutí orgánu ochrany veřejného zdraví.', 0),
        (66, NULL, 4, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (67, NULL, 4, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (68, NULL, 4, NULL, 5, 3, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (69, NULL, 4, NULL, 5, 41, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (70, NULL, 4, NULL, 5, 17, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (71, NULL, 5, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (72, NULL, 5, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (73, NULL, 5, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (74, NULL, 5, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (75, NULL, 5, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (76, NULL, 5, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (77, NULL, 5, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (78, NULL, 5, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (79, NULL, 5, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (80, NULL, 5, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (81, NULL, 5, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (82, NULL, 5, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (83, NULL, 5, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (84, NULL, 5, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (85, NULL, 5, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (86, NULL, 5, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (87, NULL, 5, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (88, NULL, 5, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (89, NULL, 5, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (90, NULL, 5, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (91, NULL, 5, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (92, NULL, 5, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (93, NULL, 5, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 1.2. Látky s pozdním účinkem karcinogenním a mutagenním - karcinogeny kategorie 1 a 2 nebo kategorie 1A a 1B a mutageny kategorie 1 a 2 nebo kategorie 1A a 1B, se následná prohlídka provádí na základě rozhodnutí orgánu ochrany veřejného zdraví.', 0),
        (94, NULL, 5, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (95, NULL, 5, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (96, NULL, 5, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (97, NULL, 5, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (98, NULL, 5, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (99, NULL, 5, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (100, NULL, 6, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (101, NULL, 6, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (102, NULL, 6, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (103, NULL, 6, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (104, NULL, 6, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (105, NULL, 8, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (106, NULL, 8, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (107, NULL, 8, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (108, NULL, 8, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (109, NULL, 8, NULL, 2, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (110, NULL, 8, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (111, NULL, 8, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (112, NULL, 8, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (113, NULL, 8, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (114, NULL, 8, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (115, NULL, 8, NULL, 3, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (116, NULL, 8, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (117, NULL, 8, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (118, NULL, 8, NULL, 3, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (119, NULL, 8, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (120, NULL, 8, NULL, 3, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (121, NULL, 8, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (122, NULL, 8, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (123, NULL, 8, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (124, NULL, 8, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (125, NULL, 8, NULL, 4, 61, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 2.1. Olovo a jeho sloučeniny (se zvláštním zřetelem na C a R) se při vyšetření plumbémie postupuje se podle § 13 a 14 nařízení vlády č. 361/2007 Sb., kterým se stanoví podmínky ochrany zdraví při práci.', 0),
        (126, NULL, 8, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (127, NULL, 9, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (128, NULL, 9, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (129, NULL, 9, NULL, 2, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (130, NULL, 9, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (131, NULL, 9, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (132, NULL, 9, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (133, NULL, 9, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (134, NULL, 9, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (135, NULL, 9, NULL, 4, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (136, NULL, 9, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (137, NULL, 11, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (138, NULL, 11, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (139, NULL, 11, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (140, NULL, 11, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (141, NULL, 11, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (142, NULL, 11, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (143, NULL, 11, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (144, NULL, 11, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (145, NULL, 12, NULL, 2, 126, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (146, NULL, 12, NULL, 2, 25, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (147, NULL, 12, NULL, 2, 33, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (148, NULL, 12, NULL, 2, 19, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (149, NULL, 12, NULL, 2, 5, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (150, NULL, 12, NULL, 2, 26, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 0, 0, 0, false, '', 0),
        (151, NULL, 12, NULL, 2, 82, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 120, 24, 24, false, 'Pro rizikový faktor: 2.3. Arzén a jeho sloučeniny (se zvláštním zřetelem na C a R) se RTG hrudníku a ultrasonografie břicha provedou po 10 letech expozice arzénu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (152, NULL, 12, NULL, 2, 96, -1, -1, 0, 12, 6, -1, -1, 0, 12, 6, 120, 24, 24, false, '', 0),
        (153, NULL, 12, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (154, NULL, 12, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (155, NULL, 12, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (156, NULL, 12, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (157, NULL, 12, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (158, NULL, 12, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (159, NULL, 12, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (160, NULL, 12, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (161, NULL, 12, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (162, NULL, 12, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (163, NULL, 12, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (164, NULL, 12, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (165, NULL, 12, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (166, NULL, 12, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (167, NULL, 12, NULL, 4, 96, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (168, NULL, 12, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (169, NULL, 12, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (170, NULL, 12, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (171, NULL, 12, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (172, NULL, 12, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (173, NULL, 12, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (174, NULL, 12, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (175, NULL, 12, NULL, 5, 96, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (176, NULL, 13, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (177, NULL, 13, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (178, NULL, 13, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (179, NULL, 13, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (180, NULL, 13, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (181, NULL, 13, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (182, NULL, 13, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (183, NULL, 13, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (184, NULL, 13, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (185, NULL, 13, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (186, NULL, 13, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (187, NULL, 13, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (188, NULL, 13, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (189, NULL, 13, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (190, NULL, 14, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (191, NULL, 14, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (192, NULL, 14, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (193, NULL, 14, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (194, NULL, 14, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (195, NULL, 14, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 72, 48, 48, false, 'RTG hrudníku se provede po 6 letech expozice hydridu antimonu SbH3 a dále se provádí 1 x 4 roky.', 0),
        (196, NULL, 14, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (197, NULL, 14, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (198, NULL, 14, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (199, NULL, 14, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (200, NULL, 14, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (201, NULL, 14, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (202, NULL, 14, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (203, NULL, 14, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (204, NULL, 14, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (205, NULL, 14, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (206, NULL, 14, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (207, NULL, 14, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (208, NULL, 15, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (209, NULL, 15, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (210, NULL, 15, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (211, NULL, 15, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (212, NULL, 15, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (213, NULL, 15, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (214, NULL, 15, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (215, NULL, 15, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (216, NULL, 15, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 72, 24, 24, false, 'RTG hrudníku se provede po 6 letech expozice berylliu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (217, NULL, 15, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (218, NULL, 15, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (219, NULL, 15, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (220, NULL, 15, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (221, NULL, 15, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (222, NULL, 15, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (223, NULL, 15, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (224, NULL, 15, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (225, NULL, 15, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (226, NULL, 15, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (227, NULL, 15, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (228, NULL, 15, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (229, NULL, 15, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (230, NULL, 15, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (231, NULL, 15, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (232, NULL, 15, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (233, NULL, 15, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (234, NULL, 15, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (235, NULL, 15, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (236, NULL, 15, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (237, NULL, 15, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (238, NULL, 15, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (239, NULL, 15, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (240, NULL, 15, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (241, NULL, 15, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (242, NULL, 15, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (243, NULL, 15, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (244, NULL, 15, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (245, NULL, 16, NULL, 2, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (246, NULL, 16, NULL, 2, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (247, NULL, 16, NULL, 2, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (248, NULL, 16, NULL, 2, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (249, NULL, 16, NULL, 2, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (250, NULL, 16, NULL, 2, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (251, NULL, 16, NULL, 2, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (252, NULL, 16, NULL, 2, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (253, NULL, 16, NULL, 2, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (254, NULL, 16, NULL, 2, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 120, 24, 24, false, 'RTG hrudníku a PSA u mužů se provede po 10 letech expozice kadminu nebo jeho sloučeninám a dále se provádí 1 x 2 roky.', 0),
        (255, NULL, 16, NULL, 2, 72, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 120, 24, 24, false, '', 0),
        (256, NULL, 16, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (257, NULL, 16, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (258, NULL, 16, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (259, NULL, 16, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (260, NULL, 16, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (261, NULL, 16, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (262, NULL, 16, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (263, NULL, 16, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (264, NULL, 16, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (265, NULL, 16, NULL, 1, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (266, NULL, 16, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (267, NULL, 16, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (268, NULL, 16, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (269, NULL, 16, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (270, NULL, 16, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (271, NULL, 16, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (272, NULL, 16, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (273, NULL, 16, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (274, NULL, 16, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (275, NULL, 16, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (276, NULL, 16, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (277, NULL, 16, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (278, NULL, 16, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (279, NULL, 16, NULL, 4, 72, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (280, NULL, 16, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (281, NULL, 16, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (282, NULL, 16, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (283, NULL, 16, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (284, NULL, 16, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (285, NULL, 16, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (286, NULL, 16, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (287, NULL, 16, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (288, NULL, 16, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (289, NULL, 16, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (290, NULL, 16, NULL, 5, 72, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (291, NULL, 17, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (292, NULL, 17, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (293, NULL, 17, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (294, NULL, 17, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (295, NULL, 17, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (296, NULL, 18, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (297, NULL, 18, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (298, NULL, 18, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (299, NULL, 18, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (300, NULL, 18, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (301, NULL, 18, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (302, NULL, 18, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (303, NULL, 18, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (304, NULL, 18, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (305, NULL, 18, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (306, NULL, 18, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (307, NULL, 18, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 24, 24, false, 'RTG hrudníku se provede po 10 letech expozice VI-mocným sloučeninám chrómu a dále se provádí 1 x 2 roky.', 0),
        (308, NULL, 18, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (309, NULL, 18, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (310, NULL, 18, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (311, NULL, 18, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (312, NULL, 18, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (313, NULL, 18, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (314, NULL, 18, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (315, NULL, 18, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (316, NULL, 18, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (317, NULL, 18, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (318, NULL, 18, NULL, 1, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (319, NULL, 18, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (320, NULL, 18, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (321, NULL, 18, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (322, NULL, 18, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (323, NULL, 18, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (324, NULL, 18, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (325, NULL, 18, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (326, NULL, 18, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (327, NULL, 18, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (328, NULL, 18, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (329, NULL, 18, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (330, NULL, 18, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (331, NULL, 18, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (332, NULL, 18, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (333, NULL, 18, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (334, NULL, 18, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (335, NULL, 18, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (336, NULL, 18, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (337, NULL, 18, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (338, NULL, 18, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (339, NULL, 18, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (340, NULL, 18, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (341, NULL, 18, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (342, NULL, 18, NULL, 5, 50, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (343, NULL, 18, NULL, 5, 77, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (344, NULL, 18, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (345, NULL, 19, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (346, NULL, 19, NULL, 2, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (347, NULL, 19, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (348, NULL, 19, NULL, 1, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (349, NULL, 19, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (350, NULL, 19, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (351, NULL, 19, NULL, 4, 90, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (352, NULL, 19, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (353, NULL, 19, NULL, 5, 90, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (354, NULL, 19, NULL, 5, 129, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 48, 0, 0, false, '', 0),
        (355, NULL, 20, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (356, NULL, 20, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (357, NULL, 20, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (358, NULL, 20, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (359, NULL, 20, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (360, NULL, 20, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (361, NULL, 20, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (362, NULL, 20, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (363, NULL, 20, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (364, NULL, 21, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (365, NULL, 21, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (366, NULL, 21, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (367, NULL, 21, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (368, NULL, 21, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (369, NULL, 21, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (370, NULL, 21, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (371, NULL, 21, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (372, NULL, 21, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku, ORL vyšetření a rinoskopie se provedou po 10 letech expozice sloučeninám niklu s karcinogenním účinkem.', 0),
        (373, NULL, 21, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (374, NULL, 21, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (375, NULL, 21, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (376, NULL, 21, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (377, NULL, 21, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (378, NULL, 21, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (379, NULL, 21, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (380, NULL, 21, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (381, NULL, 21, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (382, NULL, 21, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (383, NULL, 21, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (384, NULL, 21, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (385, NULL, 21, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (386, NULL, 21, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (387, NULL, 21, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (388, NULL, 21, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (389, NULL, 21, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (390, NULL, 21, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (391, NULL, 21, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (392, NULL, 21, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (393, NULL, 21, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (394, NULL, 21, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (395, NULL, 21, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (396, NULL, 21, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (397, NULL, 21, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (398, NULL, 21, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (399, NULL, 21, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (400, NULL, 21, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (401, NULL, 21, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (402, NULL, 21, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (403, NULL, 21, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (404, NULL, 21, NULL, 5, 55, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (405, NULL, 21, NULL, 5, 77, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (406, NULL, 21, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (407, NULL, 23, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (408, NULL, 23, NULL, 2, 44, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (409, NULL, 23, NULL, 2, 25, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (410, NULL, 23, NULL, 2, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (411, NULL, 23, NULL, 2, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (412, NULL, 23, NULL, 2, 9, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (413, NULL, 23, NULL, 2, 5, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (414, NULL, 23, NULL, 2, 26, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (415, NULL, 23, NULL, 2, 38, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (416, NULL, 23, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (417, NULL, 23, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (418, NULL, 23, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (419, NULL, 23, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (420, NULL, 23, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (421, NULL, 23, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (422, NULL, 23, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (423, NULL, 23, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (424, NULL, 23, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (425, NULL, 23, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (426, NULL, 23, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (427, NULL, 23, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (428, NULL, 23, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (429, NULL, 23, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (430, NULL, 23, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (431, NULL, 23, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (432, NULL, 23, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (433, NULL, 23, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (434, NULL, 23, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (435, NULL, 23, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (436, NULL, 24, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (437, NULL, 24, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (438, NULL, 24, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (439, NULL, 24, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (440, NULL, 24, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (441, NULL, 24, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (442, NULL, 24, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (443, NULL, 24, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (444, NULL, 25, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (445, NULL, 25, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (446, NULL, 25, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (447, NULL, 25, NULL, 2, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 4, 4, false, 'S ohledem na rizikový faktor: 2.10.3. Organické sloučeniny fosforu (například organofosfáty - OF a trikrezylfosfát-TKP a další se zvláštním zřetelem na R, pokud některá sloučenina je takto klasifikována) se odborná vyšetření provádějí se zaměřením na periferní nervový systém (opožděný efekt) a stanovení aktivity cholinesterázy nebo acetylcholinesterázy v krvi provede podle závažnosti expozice 1x až 3x za rok, nejméně však 1x během práce.', 0),
        (448, NULL, 25, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (449, NULL, 25, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (450, NULL, 25, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (451, NULL, 25, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (452, NULL, 25, NULL, 1, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (453, NULL, 25, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (454, NULL, 25, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (455, NULL, 25, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (456, NULL, 25, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (457, NULL, 25, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (458, NULL, 25, NULL, 4, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (459, NULL, 25, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (460, NULL, 25, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (461, NULL, 26, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (462, NULL, 26, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (463, NULL, 26, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (464, NULL, 26, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (465, NULL, 26, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (466, NULL, 27, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (467, NULL, 27, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (468, NULL, 27, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (469, NULL, 27, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (470, NULL, 27, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (471, NULL, 27, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (472, NULL, 28, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (473, NULL, 28, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (474, NULL, 28, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (475, NULL, 28, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (476, NULL, 28, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (477, NULL, 28, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (478, NULL, 28, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (479, NULL, 28, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (480, NULL, 29, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (481, NULL, 29, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (482, NULL, 29, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (483, NULL, 29, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (484, NULL, 29, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (485, NULL, 30, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (486, NULL, 30, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (487, NULL, 30, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (488, NULL, 30, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (489, NULL, 30, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (490, NULL, 30, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (491, NULL, 31, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (492, NULL, 31, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (493, NULL, 31, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (494, NULL, 31, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (495, NULL, 31, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (496, NULL, 31, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (497, NULL, 31, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (498, NULL, 31, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (499, NULL, 32, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (500, NULL, 32, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (501, NULL, 32, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (502, NULL, 32, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (503, NULL, 32, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (504, NULL, 33, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (505, NULL, 33, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (506, NULL, 33, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (507, NULL, 33, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (508, NULL, 33, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (509, NULL, 33, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (510, NULL, 34, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (511, NULL, 34, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (512, NULL, 34, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (513, NULL, 34, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (514, NULL, 34, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (515, NULL, 34, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (516, NULL, 34, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (517, NULL, 34, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (518, NULL, 35, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (519, NULL, 35, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (520, NULL, 35, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (521, NULL, 35, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (522, NULL, 35, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (523, NULL, 35, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (524, NULL, 35, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (525, NULL, 35, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (526, NULL, 36, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (527, NULL, 36, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (528, NULL, 36, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (529, NULL, 36, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (530, NULL, 36, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (531, NULL, 36, NULL, 2, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (532, NULL, 36, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (533, NULL, 36, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (534, NULL, 36, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (535, NULL, 36, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (536, NULL, 36, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (537, NULL, 36, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (538, NULL, 36, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (539, NULL, 36, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (540, NULL, 36, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (541, NULL, 36, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (542, NULL, 36, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (543, NULL, 36, NULL, 4, 2, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (544, NULL, 36, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (545, NULL, 37, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (546, NULL, 37, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (547, NULL, 37, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (548, NULL, 37, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (549, NULL, 37, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (550, NULL, 38, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (551, NULL, 38, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (552, NULL, 38, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (553, NULL, 38, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (554, NULL, 38, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (555, NULL, 38, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (556, NULL, 38, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (557, NULL, 38, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (558, NULL, 39, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (559, NULL, 39, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (560, NULL, 39, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (561, NULL, 39, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (562, NULL, 39, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (563, NULL, 39, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (564, NULL, 39, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (565, NULL, 39, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (566, NULL, 39, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (567, NULL, 39, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (568, NULL, 39, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (569, NULL, 39, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (570, NULL, 39, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (571, NULL, 39, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (572, NULL, 40, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (573, NULL, 40, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (574, NULL, 40, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (575, NULL, 40, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (576, NULL, 40, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (577, NULL, 40, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (578, NULL, 40, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (579, NULL, 40, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (580, NULL, 41, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (581, NULL, 41, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (582, NULL, 41, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (583, NULL, 41, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (584, NULL, 41, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (585, NULL, 41, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (586, NULL, 41, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (587, NULL, 41, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (588, NULL, 42, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (589, NULL, 42, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (590, NULL, 42, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (591, NULL, 42, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (592, NULL, 42, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (593, NULL, 43, NULL, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 3, 12, 12, false, '', 0),
        (594, NULL, 43, NULL, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 3, 12, 12, false, '', 0),
        (595, NULL, 43, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (596, NULL, 43, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (597, NULL, 43, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (598, NULL, 43, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (599, NULL, 43, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (600, NULL, 43, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (601, NULL, 44, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (602, NULL, 44, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (603, NULL, 44, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (604, NULL, 44, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (605, NULL, 44, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (606, NULL, 44, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (607, NULL, 44, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (608, NULL, 44, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (609, NULL, 45, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (610, NULL, 45, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (611, NULL, 45, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (612, NULL, 45, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (613, NULL, 45, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (614, NULL, 45, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (615, NULL, 45, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (616, NULL, 45, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (617, NULL, 45, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (618, NULL, 45, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (619, NULL, 45, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (620, NULL, 45, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (621, NULL, 45, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (622, NULL, 45, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (623, NULL, 46, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (624, NULL, 46, NULL, 2, 21, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (625, NULL, 46, NULL, 2, 50, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 6, 12, 12, false, '', 0),
        (626, NULL, 46, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (627, NULL, 46, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (628, NULL, 46, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (629, NULL, 46, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (630, NULL, 46, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (631, NULL, 46, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (632, NULL, 46, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (633, NULL, 46, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (634, NULL, 47, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (635, NULL, 47, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (636, NULL, 47, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (637, NULL, 47, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (638, NULL, 47, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (639, NULL, 47, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (640, NULL, 47, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (641, NULL, 47, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (642, NULL, 48, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (643, NULL, 48, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (644, NULL, 48, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (645, NULL, 48, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (646, NULL, 48, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (647, NULL, 48, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (648, NULL, 48, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (649, NULL, 48, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (650, NULL, 48, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (651, NULL, 49, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (652, NULL, 49, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (653, NULL, 49, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (654, NULL, 49, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (655, NULL, 49, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (656, NULL, 49, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (657, NULL, 49, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (658, NULL, 49, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (659, NULL, 49, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (660, NULL, 49, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (661, NULL, 49, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (662, NULL, 50, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (663, NULL, 50, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (664, NULL, 50, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (665, NULL, 50, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (666, NULL, 50, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (667, NULL, 50, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (668, NULL, 50, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (669, NULL, 50, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (670, NULL, 52, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (671, NULL, 52, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (672, NULL, 52, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (673, NULL, 52, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (674, NULL, 52, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (675, NULL, 52, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (676, NULL, 52, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (677, NULL, 52, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (678, NULL, 52, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (679, NULL, 52, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (680, NULL, 52, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (681, NULL, 52, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (682, NULL, 52, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (683, NULL, 52, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (684, NULL, 53, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (685, NULL, 53, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (686, NULL, 53, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (687, NULL, 53, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (688, NULL, 53, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (689, NULL, 53, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (690, NULL, 53, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (691, NULL, 53, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (692, NULL, 53, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (693, NULL, 53, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (694, NULL, 53, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (695, NULL, 53, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (696, NULL, 53, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (697, NULL, 53, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (698, NULL, 53, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (699, NULL, 53, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (700, NULL, 53, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (701, NULL, 54, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, 'První periodická prohlídka s ohledem na rizikový faktor: 2.27.2. Methylchlorid (= monochlormethan), se provede za 3 - 9 měsíců.', 0),
        (702, NULL, 54, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (703, NULL, 54, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (704, NULL, 54, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (705, NULL, 54, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (706, NULL, 54, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (707, NULL, 54, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, '', 0),
        (708, NULL, 54, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (709, NULL, 54, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (710, NULL, 54, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (711, NULL, 54, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (712, NULL, 54, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (713, NULL, 54, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (714, NULL, 54, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (715, NULL, 54, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (716, NULL, 54, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (717, NULL, 54, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (718, NULL, 54, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (719, NULL, 54, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (720, NULL, 54, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (721, NULL, 54, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (722, NULL, 54, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (723, NULL, 54, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (724, NULL, 55, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (725, NULL, 55, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (726, NULL, 55, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (727, NULL, 55, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (728, NULL, 55, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (729, NULL, 55, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (730, NULL, 55, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (731, NULL, 55, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (732, NULL, 55, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (733, NULL, 55, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (734, NULL, 55, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (735, NULL, 55, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (736, NULL, 55, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (737, NULL, 55, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (738, NULL, 55, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (739, NULL, 55, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (740, NULL, 55, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (741, NULL, 56, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (742, NULL, 56, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (743, NULL, 56, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (744, NULL, 56, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (745, NULL, 56, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (746, NULL, 56, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (747, NULL, 56, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (748, NULL, 56, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (749, NULL, 56, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (750, NULL, 56, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (751, NULL, 56, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (752, NULL, 56, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (753, NULL, 56, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (754, NULL, 56, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (755, NULL, 56, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (756, NULL, 56, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (757, NULL, 56, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (758, NULL, 56, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (759, NULL, 56, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (760, NULL, 56, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (761, NULL, 56, NULL, 5, 21, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (762, NULL, 57, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (763, NULL, 57, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (764, NULL, 57, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (765, NULL, 57, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (766, NULL, 57, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (767, NULL, 57, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (768, NULL, 57, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (769, NULL, 57, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (770, NULL, 57, NULL, 2, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Vodní chladový test a prstová pletyzmografie se provádějí po více než 10 leté práci v riziku vinylchloridu.', 0),
        (771, NULL, 57, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (772, NULL, 57, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (773, NULL, 57, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (774, NULL, 57, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (775, NULL, 57, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (776, NULL, 57, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (777, NULL, 57, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (778, NULL, 57, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (779, NULL, 57, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (780, NULL, 57, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (781, NULL, 57, NULL, 3, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'S ohledem na rizikový faktor: 2.27.4. Vinylchlorid (= chlorethen) (se zvláštním zřetelem na C), se vyšetření pro mimořádnou pracovnělékařskou prohlídku provádějí po vyšší expozici vinylchloridu.', 0),
        (782, NULL, 57, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (783, NULL, 57, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (784, NULL, 57, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (785, NULL, 57, NULL, 3, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (786, NULL, 57, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (787, NULL, 57, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (788, NULL, 57, NULL, 3, 11, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (789, NULL, 57, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (790, NULL, 57, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (791, NULL, 57, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (792, NULL, 57, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (793, NULL, 57, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (794, NULL, 57, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (795, NULL, 57, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (796, NULL, 57, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (797, NULL, 57, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'GMT se provede po více než 10 leté práci v riziku vinylchloridu.', 0),
        (798, NULL, 57, NULL, 4, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (799, NULL, 57, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (800, NULL, 57, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (801, NULL, 57, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (802, NULL, 57, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (803, NULL, 57, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (804, NULL, 57, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (805, NULL, 57, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (806, NULL, 57, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (807, NULL, 59, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (808, NULL, 59, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (809, NULL, 59, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (810, NULL, 59, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (811, NULL, 59, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (812, NULL, 60, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (813, NULL, 60, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (814, NULL, 60, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (815, NULL, 60, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (816, NULL, 60, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (817, NULL, 60, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (818, NULL, 60, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (819, NULL, 60, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (820, NULL, 60, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (821, NULL, 60, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (822, NULL, 60, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (823, NULL, 60, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (824, NULL, 60, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (825, NULL, 60, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (826, NULL, 60, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (827, NULL, 60, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (828, NULL, 60, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (829, NULL, 60, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (830, NULL, 60, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (831, NULL, 60, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (832, NULL, 60, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (833, NULL, 60, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (834, NULL, 60, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (835, NULL, 60, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (836, NULL, 60, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (837, NULL, 60, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (838, NULL, 60, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (839, NULL, 60, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (840, NULL, 60, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (841, NULL, 60, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (842, NULL, 60, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (843, NULL, 60, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (844, NULL, 60, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (845, NULL, 62, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (846, NULL, 62, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (847, NULL, 62, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (848, NULL, 62, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (849, NULL, 62, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (850, NULL, 63, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (851, NULL, 63, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (852, NULL, 63, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (853, NULL, 63, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (854, NULL, 63, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (855, NULL, 63, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (856, NULL, 63, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (857, NULL, 63, NULL, 1, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (858, NULL, 63, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (859, NULL, 63, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (860, NULL, 63, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (861, NULL, 63, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (862, NULL, 63, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (863, NULL, 63, NULL, 4, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (864, NULL, 63, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (865, NULL, 64, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (866, NULL, 64, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (867, NULL, 64, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (868, NULL, 64, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (869, NULL, 64, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (870, NULL, 64, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (871, NULL, 64, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (872, NULL, 64, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (873, NULL, 64, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (874, NULL, 64, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (875, NULL, 64, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (876, NULL, 64, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (877, NULL, 64, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (878, NULL, 64, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (879, NULL, 64, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (880, NULL, 64, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (881, NULL, 64, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (882, NULL, 66, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (883, NULL, 66, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (884, NULL, 66, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (885, NULL, 66, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (886, NULL, 66, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (887, NULL, 66, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (888, NULL, 66, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (889, NULL, 66, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (890, NULL, 66, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (891, NULL, 66, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (892, NULL, 66, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (893, NULL, 66, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (894, NULL, 66, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (895, NULL, 66, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (896, NULL, 67, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (897, NULL, 67, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (898, NULL, 67, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (899, NULL, 67, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (900, NULL, 67, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (901, NULL, 67, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (902, NULL, 67, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (903, NULL, 67, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (904, NULL, 67, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (905, NULL, 67, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (906, NULL, 67, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (907, NULL, 67, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (908, NULL, 67, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (909, NULL, 67, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (910, NULL, 69, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (911, NULL, 69, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Rinoskopie a ORL vyšetření se provádějí po nejméně desetileté expozici formaldehydu a jiným alifatickým aldehydům.', 0),
        (912, NULL, 69, NULL, 2, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (913, NULL, 69, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (914, NULL, 69, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (915, NULL, 69, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (916, NULL, 69, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'ORL vyšetření se provádí po nejméně desetileté expozici formaldehydu a jiným alifatickým aldehydům.', 0),
        (917, NULL, 69, NULL, 4, 77, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (918, NULL, 69, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (919, NULL, 70, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (920, NULL, 70, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (921, NULL, 70, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (922, NULL, 70, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (923, NULL, 70, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (924, NULL, 70, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (925, NULL, 70, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (926, NULL, 70, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (927, NULL, 71, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (928, NULL, 71, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (929, NULL, 71, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (930, NULL, 71, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (931, NULL, 71, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (932, NULL, 71, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (933, NULL, 71, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (934, NULL, 71, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (935, NULL, 71, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (936, NULL, 71, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (937, NULL, 71, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (938, NULL, 71, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (939, NULL, 71, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (940, NULL, 71, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (941, NULL, 71, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (942, NULL, 71, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (943, NULL, 71, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (944, NULL, 72, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (945, NULL, 72, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (946, NULL, 72, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (947, NULL, 72, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (948, NULL, 72, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (949, NULL, 72, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (950, NULL, 72, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (951, NULL, 72, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (952, NULL, 72, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (953, NULL, 72, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (954, NULL, 72, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (955, NULL, 72, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (956, NULL, 72, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (957, NULL, 72, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (958, NULL, 72, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (959, NULL, 72, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (960, NULL, 72, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (961, NULL, 72, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (962, NULL, 72, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (963, NULL, 72, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (964, NULL, 72, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (965, NULL, 73, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (966, NULL, 73, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (967, NULL, 73, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (968, NULL, 73, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (969, NULL, 73, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (970, NULL, 73, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (971, NULL, 73, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (972, NULL, 73, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (973, NULL, 73, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (974, NULL, 73, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (975, NULL, 73, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (976, NULL, 73, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (977, NULL, 73, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (978, NULL, 73, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (979, NULL, 73, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (980, NULL, 73, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (981, NULL, 73, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (982, NULL, 73, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (983, NULL, 73, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (984, NULL, 73, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (985, NULL, 73, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (986, NULL, 73, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (987, NULL, 73, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (988, NULL, 73, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (989, NULL, 73, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (990, NULL, 73, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (991, NULL, 73, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (992, NULL, 73, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (993, NULL, 73, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (994, NULL, 73, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (995, NULL, 74, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (996, NULL, 74, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (997, NULL, 74, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (998, NULL, 74, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (999, NULL, 74, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1000, NULL, 74, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1001, NULL, 74, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1002, NULL, 74, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1003, NULL, 74, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1004, NULL, 74, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1005, NULL, 74, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1006, NULL, 74, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1007, NULL, 74, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1008, NULL, 74, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1009, NULL, 75, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1010, NULL, 75, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1011, NULL, 75, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1012, NULL, 75, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1013, NULL, 75, NULL, 2, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1014, NULL, 75, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1015, NULL, 75, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1016, NULL, 75, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1017, NULL, 75, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1018, NULL, 75, NULL, 1, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1019, NULL, 75, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1020, NULL, 75, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1021, NULL, 75, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1022, NULL, 75, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1023, NULL, 75, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1024, NULL, 75, NULL, 4, 6, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1025, NULL, 75, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1026, NULL, 76, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1027, NULL, 76, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1028, NULL, 76, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1029, NULL, 76, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1030, NULL, 76, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1031, NULL, 76, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1032, NULL, 76, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1033, NULL, 76, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1034, NULL, 76, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1035, NULL, 76, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1036, NULL, 76, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1037, NULL, 76, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1038, NULL, 76, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1039, NULL, 76, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1040, NULL, 77, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1041, NULL, 77, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1042, NULL, 77, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1043, NULL, 77, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1044, NULL, 77, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1045, NULL, 77, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1046, NULL, 77, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1047, NULL, 77, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1048, NULL, 77, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1049, NULL, 77, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1050, NULL, 77, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1051, NULL, 77, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1052, NULL, 77, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1053, NULL, 77, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1054, NULL, 77, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1055, NULL, 77, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1056, NULL, 77, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1057, NULL, 78, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1058, NULL, 78, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1059, NULL, 78, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1060, NULL, 78, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1061, NULL, 78, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1062, NULL, 79, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1063, NULL, 79, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1064, NULL, 79, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1065, NULL, 79, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1066, NULL, 79, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1067, NULL, 79, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1068, NULL, 79, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1069, NULL, 79, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1070, NULL, 79, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1071, NULL, 79, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1072, NULL, 79, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1073, NULL, 79, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1074, NULL, 79, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1075, NULL, 79, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1076, NULL, 80, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1077, NULL, 80, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1078, NULL, 80, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1079, NULL, 80, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1080, NULL, 80, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1081, NULL, 81, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1082, NULL, 81, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1083, NULL, 81, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1084, NULL, 81, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1085, NULL, 81, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1086, NULL, 81, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1087, NULL, 81, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1088, NULL, 81, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1089, NULL, 82, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1090, NULL, 82, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1091, NULL, 82, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1092, NULL, 82, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1093, NULL, 82, NULL, 2, 89, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1094, NULL, 82, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1095, NULL, 82, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1096, NULL, 82, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1097, NULL, 82, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1098, NULL, 82, NULL, 1, 88, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1099, NULL, 82, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1100, NULL, 82, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1101, NULL, 82, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1102, NULL, 82, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1103, NULL, 82, NULL, 4, 89, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1104, NULL, 82, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1105, NULL, 83, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1106, NULL, 83, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1107, NULL, 83, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1108, NULL, 83, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1109, NULL, 83, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1110, NULL, 83, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1111, NULL, 83, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1112, NULL, 83, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1113, NULL, 83, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1114, NULL, 83, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1115, NULL, 83, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1116, NULL, 83, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1117, NULL, 83, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1118, NULL, 83, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1119, NULL, 83, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1120, NULL, 83, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1121, NULL, 83, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1122, NULL, 83, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1123, NULL, 83, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1124, NULL, 83, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1125, NULL, 84, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1126, NULL, 84, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1127, NULL, 84, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1128, NULL, 84, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1129, NULL, 84, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1130, NULL, 84, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1131, NULL, 84, NULL, 2, 50, -1, -1, 36, 36, 36, -1, -1, 36, 36, 36, 0, 36, 36, false, 'Oční vyšetření při práci s s trinitrotoluenem se provádí 1x 3 roky.', 0),
        (1132, NULL, 84, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1133, NULL, 84, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1134, NULL, 84, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1135, NULL, 84, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1136, NULL, 84, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1137, NULL, 84, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1138, NULL, 84, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1139, NULL, 84, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1140, NULL, 84, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1141, NULL, 84, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1142, NULL, 84, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1143, NULL, 84, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1144, NULL, 84, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1145, NULL, 84, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1146, NULL, 84, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1147, NULL, 84, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1148, NULL, 85, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1149, NULL, 85, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1150, NULL, 85, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1151, NULL, 85, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1152, NULL, 85, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1153, NULL, 85, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1154, NULL, 85, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1155, NULL, 85, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1156, NULL, 85, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1157, NULL, 85, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1158, NULL, 85, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1159, NULL, 85, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1160, NULL, 85, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1161, NULL, 85, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1162, NULL, 85, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1163, NULL, 85, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1164, NULL, 85, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1165, NULL, 85, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1166, NULL, 85, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1167, NULL, 85, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1168, NULL, 85, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1169, NULL, 85, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1170, NULL, 85, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1171, NULL, 86, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1172, NULL, 86, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1173, NULL, 86, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1174, NULL, 86, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1175, NULL, 86, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1176, NULL, 86, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1177, NULL, 86, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1178, NULL, 86, NULL, 2, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 6, 6, false, 'Při expozici aromatickým amino sloučeninám se provádí chemické vyšetření moče a močového sedimentu 2x za rok.', 0),
        (1179, NULL, 86, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1180, NULL, 86, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1181, NULL, 86, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1182, NULL, 86, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1183, NULL, 86, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1184, NULL, 86, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1185, NULL, 86, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1186, NULL, 86, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1187, NULL, 86, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1188, NULL, 86, NULL, 3, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1189, NULL, 86, NULL, 3, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1190, NULL, 86, NULL, 3, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1191, NULL, 86, NULL, 3, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1192, NULL, 86, NULL, 3, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1193, NULL, 86, NULL, 3, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1194, NULL, 86, NULL, 3, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1195, NULL, 86, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1196, NULL, 86, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1197, NULL, 86, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1198, NULL, 86, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1199, NULL, 86, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1200, NULL, 86, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1201, NULL, 86, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1202, NULL, 86, NULL, 4, 29, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1203, NULL, 86, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1204, NULL, 86, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1205, NULL, 86, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1206, NULL, 86, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1207, NULL, 86, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1208, NULL, 86, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1209, NULL, 86, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1210, NULL, 86, NULL, 5, 29, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1211, NULL, 87, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1212, NULL, 87, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1213, NULL, 87, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1214, NULL, 87, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1215, NULL, 87, NULL, 2, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1216, NULL, 87, NULL, 2, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1217, NULL, 87, NULL, 2, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1218, NULL, 87, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1219, NULL, 87, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1220, NULL, 87, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1221, NULL, 87, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1222, NULL, 87, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1223, NULL, 87, NULL, 1, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1224, NULL, 87, NULL, 1, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1225, NULL, 87, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1226, NULL, 87, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1227, NULL, 87, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1228, NULL, 87, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1229, NULL, 87, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1230, NULL, 87, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1231, NULL, 87, NULL, 4, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1232, NULL, 87, NULL, 4, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1233, NULL, 87, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1234, NULL, 88, NULL, 2, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1235, NULL, 88, NULL, 2, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1236, NULL, 88, NULL, 2, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1237, NULL, 88, NULL, 2, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1238, NULL, 88, NULL, 2, 101, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1239, NULL, 88, NULL, 2, 32, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1240, NULL, 88, NULL, 2, 95, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1241, NULL, 88, NULL, 2, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1242, NULL, 88, NULL, 2, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1243, NULL, 88, NULL, 2, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1244, NULL, 88, NULL, 2, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1245, NULL, 88, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1246, NULL, 88, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1247, NULL, 88, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1248, NULL, 88, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1249, NULL, 88, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1250, NULL, 88, NULL, 1, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1251, NULL, 88, NULL, 1, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1252, NULL, 88, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1253, NULL, 88, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1254, NULL, 88, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1255, NULL, 88, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1256, NULL, 88, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1257, NULL, 88, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1258, NULL, 88, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1259, NULL, 88, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1260, NULL, 88, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1261, NULL, 88, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1262, NULL, 88, NULL, 4, 32, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1263, NULL, 88, NULL, 4, 95, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1264, NULL, 88, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1265, NULL, 88, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1266, NULL, 88, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1267, NULL, 88, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1268, NULL, 88, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1269, NULL, 88, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1270, NULL, 88, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1271, NULL, 88, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1272, NULL, 88, NULL, 5, 101, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1273, NULL, 88, NULL, 5, 32, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1274, NULL, 88, NULL, 5, 95, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1275, NULL, 88, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1276, NULL, 88, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1277, NULL, 88, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1278, NULL, 88, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1279, NULL, 89, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1280, NULL, 89, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1281, NULL, 89, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1282, NULL, 89, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1283, NULL, 89, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1284, NULL, 89, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1285, NULL, 89, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1286, NULL, 89, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1287, NULL, 89, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 24, 24, false, 'RTG hrudníku se provede po 10 letech expozice polycyklickým aromatickým uhlovodíkům a dále se provádí 1x 2 roky.', 0),
        (1288, NULL, 89, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1289, NULL, 89, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1290, NULL, 89, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1291, NULL, 89, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1292, NULL, 89, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1293, NULL, 89, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1294, NULL, 89, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1295, NULL, 89, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1296, NULL, 89, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1297, NULL, 89, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1298, NULL, 89, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1299, NULL, 89, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1300, NULL, 89, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1301, NULL, 89, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1302, NULL, 89, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1303, NULL, 89, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1304, NULL, 89, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1305, NULL, 89, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1306, NULL, 89, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1307, NULL, 89, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1308, NULL, 89, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1309, NULL, 89, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1310, NULL, 89, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1311, NULL, 89, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1312, NULL, 89, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1313, NULL, 89, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1314, NULL, 89, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1315, NULL, 89, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1316, NULL, 90, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1317, NULL, 90, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1318, NULL, 90, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1319, NULL, 90, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1320, NULL, 90, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1321, NULL, 91, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1322, NULL, 91, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1323, NULL, 91, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1324, NULL, 91, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1325, NULL, 91, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1326, NULL, 91, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1327, NULL, 91, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1328, NULL, 91, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1329, NULL, 91, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1330, NULL, 91, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1331, NULL, 91, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1332, NULL, 91, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1333, NULL, 91, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1334, NULL, 91, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1335, NULL, 91, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1336, NULL, 91, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1337, NULL, 91, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1338, NULL, 91, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1339, NULL, 91, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1340, NULL, 91, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1341, NULL, 92, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1342, NULL, 92, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1343, NULL, 92, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1344, NULL, 92, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1345, NULL, 92, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1346, NULL, 92, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1347, NULL, 92, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1348, NULL, 92, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1349, NULL, 92, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1350, NULL, 92, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1351, NULL, 92, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1352, NULL, 92, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1353, NULL, 92, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1354, NULL, 92, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1355, NULL, 92, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1356, NULL, 92, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1357, NULL, 92, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1358, NULL, 92, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1359, NULL, 92, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1360, NULL, 92, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1361, NULL, 92, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1362, NULL, 92, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1363, NULL, 92, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1364, NULL, 92, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1365, NULL, 92, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1366, NULL, 92, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1367, NULL, 93, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1368, NULL, 93, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1369, NULL, 93, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1370, NULL, 93, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1371, NULL, 93, NULL, 2, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1372, NULL, 93, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1373, NULL, 93, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1374, NULL, 93, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1375, NULL, 93, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1376, NULL, 93, NULL, 1, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1377, NULL, 93, NULL, 3, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'EMG v rozsahu stanovení distální motorické latence středových nervů se provádí pro rizikový faktor: 2.47. Karbamátové insekticidy (například inhibitory AChE - aldicarb, carbofuran, methomyl, bendiocarb, carbaryl, pirimicarb) (se zvláštním zřetelem na C, pokud některá z těchto sloučenin má tuto klasifikaci) před zahájením sezónní expozice, v průběhu expozice a po jejím ukončení.', 0),
        (1378, NULL, 93, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1379, NULL, 93, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1380, NULL, 93, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1381, NULL, 93, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1382, NULL, 93, NULL, 4, 91, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1383, NULL, 93, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1384, NULL, 94, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1385, NULL, 94, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1386, NULL, 94, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1387, NULL, 94, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1388, NULL, 94, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1389, NULL, 94, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1390, NULL, 94, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1391, NULL, 94, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1392, NULL, 95, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1393, NULL, 95, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1394, NULL, 95, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1395, NULL, 95, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1396, NULL, 95, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1397, NULL, 95, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1398, NULL, 95, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1399, NULL, 95, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1400, NULL, 95, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1401, NULL, 95, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1402, NULL, 95, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1403, NULL, 95, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1404, NULL, 95, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1405, NULL, 95, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1406, NULL, 95, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1407, NULL, 95, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1408, NULL, 95, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1409, NULL, 96, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1410, NULL, 96, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1411, NULL, 96, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1412, NULL, 96, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1413, NULL, 96, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1414, NULL, 97, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1415, NULL, 97, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1416, NULL, 97, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1417, NULL, 97, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1418, NULL, 97, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1419, NULL, 98, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1420, NULL, 98, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1421, NULL, 98, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1422, NULL, 98, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1423, NULL, 98, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1424, NULL, 99, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1425, NULL, 99, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1426, NULL, 99, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1427, NULL, 99, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1428, NULL, 99, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1429, NULL, 99, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1430, NULL, 99, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1431, NULL, 99, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1432, NULL, 100, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1433, NULL, 100, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1434, NULL, 100, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1435, NULL, 100, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1436, NULL, 100, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1437, NULL, 101, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1438, NULL, 101, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1439, NULL, 101, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1440, NULL, 101, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1441, NULL, 101, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1442, NULL, 101, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1443, NULL, 101, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1444, NULL, 101, NULL, 1, 1, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1445, NULL, 101, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1446, NULL, 101, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1447, NULL, 101, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1448, NULL, 101, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1449, NULL, 101, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1450, NULL, 101, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1451, NULL, 102, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1452, NULL, 102, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1453, NULL, 102, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1454, NULL, 102, NULL, 2, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1455, NULL, 102, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku se provede po 10 a více letech od začátku expozice uranu a jeho sloučeninám.', 0),
        (1456, NULL, 102, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1457, NULL, 102, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1458, NULL, 102, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1459, NULL, 102, NULL, 1, 1, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1460, NULL, 102, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1461, NULL, 102, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1462, NULL, 102, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1463, NULL, 102, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1464, NULL, 102, NULL, 4, 3, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1465, NULL, 102, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1466, NULL, 102, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1467, NULL, 102, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1468, NULL, 102, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1469, NULL, 102, NULL, 5, 3, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1470, NULL, 102, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 2.53.b Uran a jeho sloučeniny (nerozpustné sloučeniny uranu), individuálně zvážit RTG hrudníku.', 0),
        (1471, NULL, 103, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1472, NULL, 103, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1473, NULL, 103, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1474, NULL, 103, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1475, NULL, 103, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1476, NULL, 103, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1477, NULL, 103, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1478, NULL, 103, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1479, NULL, 103, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1480, NULL, 103, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1481, NULL, 103, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, true, '', 0),
        (1482, NULL, 104, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1483, NULL, 104, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1484, NULL, 104, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1485, NULL, 104, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1486, NULL, 104, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1487, NULL, 105, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1488, NULL, 105, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1489, NULL, 105, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1490, NULL, 105, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1491, NULL, 105, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1492, NULL, 105, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1493, NULL, 105, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1494, NULL, 105, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1495, NULL, 105, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1496, NULL, 105, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1497, NULL, 105, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1498, NULL, 105, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1499, NULL, 105, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1500, NULL, 105, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1501, NULL, 105, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1502, NULL, 105, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1503, NULL, 105, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1504, NULL, 105, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1505, NULL, 105, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1506, NULL, 105, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1507, NULL, 105, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1508, NULL, 105, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1509, NULL, 105, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1510, NULL, 105, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1511, NULL, 105, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1512, NULL, 105, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1513, NULL, 105, NULL, 5, 38, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1514, NULL, 105, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1515, NULL, 105, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1516, NULL, 105, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1517, NULL, 105, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1518, NULL, 105, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1519, NULL, 105, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1520, NULL, 106, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1521, NULL, 106, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1522, NULL, 106, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1523, NULL, 106, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1524, NULL, 106, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1525, NULL, 106, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1526, NULL, 106, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1527, NULL, 106, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1528, NULL, 106, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'RTG hrudníku se provede po 10 a více letech od začátku expozice halogenovým alkyletherům a aryletherům.', 0),
        (1529, NULL, 106, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1530, NULL, 106, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1531, NULL, 106, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1532, NULL, 106, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1533, NULL, 106, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1534, NULL, 106, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1535, NULL, 106, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1536, NULL, 106, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1537, NULL, 106, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1538, NULL, 106, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1539, NULL, 106, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1540, NULL, 106, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1541, NULL, 106, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1542, NULL, 106, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1543, NULL, 106, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1544, NULL, 106, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1545, NULL, 106, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1546, NULL, 106, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1547, NULL, 106, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1548, NULL, 106, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1549, NULL, 106, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1550, NULL, 106, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1551, NULL, 106, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1552, NULL, 106, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1553, NULL, 106, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1554, NULL, 106, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1555, NULL, 106, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1556, NULL, 106, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1557, NULL, 107, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1558, NULL, 107, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1559, NULL, 107, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1560, NULL, 107, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1561, NULL, 107, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1562, NULL, 107, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1563, NULL, 107, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1564, NULL, 107, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1565, NULL, 107, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1566, NULL, 107, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1567, NULL, 107, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1568, NULL, 107, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1569, NULL, 107, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1570, NULL, 107, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1571, NULL, 107, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1572, NULL, 107, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1573, NULL, 107, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1574, NULL, 107, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1575, NULL, 107, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1576, NULL, 107, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1577, NULL, 107, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1578, NULL, 107, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1579, NULL, 107, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1580, NULL, 107, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1581, NULL, 107, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1582, NULL, 107, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1583, NULL, 107, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1584, NULL, 107, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1585, NULL, 107, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1586, NULL, 107, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1587, NULL, 107, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1588, NULL, 107, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1589, NULL, 107, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1590, NULL, 107, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1591, NULL, 109, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1592, NULL, 109, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1593, NULL, 109, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1594, NULL, 109, NULL, 2, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1595, NULL, 109, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1596, NULL, 109, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1597, NULL, 109, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1598, NULL, 109, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1599, NULL, 109, NULL, 1, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1600, NULL, 109, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1601, NULL, 109, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1602, NULL, 109, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1603, NULL, 109, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1604, NULL, 109, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1605, NULL, 109, NULL, 4, 76, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1606, NULL, 109, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1607, NULL, 109, NULL, 5, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, 'Pro rizikový faktor: 3.1. Ionizující záření, u pracovníků, u nichž byly zjištěny kožní změny nebo oční zákaly, nebo u nichž bylo během práce zjištěno významné překročení přípustných dávek na oční čočku nebo na kůži, popřípadě na kostní dřeň, provádět prohlídky zaměřené na možná poškození uvedených orgánů 1x za 1 - 2 roky od skončení expozice v rozsahu výstupní prohlídky.', 0),
        (1608, NULL, 109, NULL, 5, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1609, NULL, 109, NULL, 5, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1610, NULL, 109, NULL, 5, 76, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1611, NULL, 109, NULL, 5, 25, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1612, NULL, 110, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1613, NULL, 110, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1614, NULL, 110, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1615, NULL, 110, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1616, NULL, 110, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1617, NULL, 111, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1618, NULL, 111, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1619, NULL, 111, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1620, NULL, 111, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1621, NULL, 111, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1622, NULL, 111, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1623, NULL, 111, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1624, NULL, 111, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1625, NULL, 111, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1626, NULL, 111, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1627, NULL, 111, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1628, NULL, 111, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1629, NULL, 111, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1630, NULL, 111, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1631, NULL, 112, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1632, NULL, 112, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1633, NULL, 112, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1634, NULL, 112, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1635, NULL, 112, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1636, NULL, 112, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1637, NULL, 112, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1638, NULL, 112, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1639, NULL, 113, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1640, NULL, 113, NULL, 2, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1641, NULL, 113, NULL, 2, 84, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1642, NULL, 113, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'Hodnocení ztráty sluchu podle Fowlera, ORL vyšetření a prahová tónová audiometrie se provedou při expozici hluku u rizikových prací po 10 letech od začátku expozice nebo při ztrátách sluchu vyšších jak 20 % podle Fowlera zjištěných screeningovou audiometrií nebo v případě významné progrese ztráty sluchu.', 1),
        (1643, NULL, 113, NULL, 2, 68, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 1),
        (1644, NULL, 113, NULL, 2, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 1),
        (1645, NULL, 113, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1646, NULL, 113, NULL, 1, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1647, NULL, 113, NULL, 1, 69, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1648, NULL, 113, NULL, 1, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1649, NULL, 113, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1650, NULL, 113, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1651, NULL, 113, NULL, 4, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1652, NULL, 113, NULL, 4, 69, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1653, NULL, 113, NULL, 4, 28, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1654, NULL, 113, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1655, NULL, 114, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1656, NULL, 114, NULL, 2, 24, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1657, NULL, 114, NULL, 2, 33, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1658, NULL, 114, NULL, 2, 19, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1659, NULL, 114, NULL, 2, 5, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1660, NULL, 114, NULL, 2, 9, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1661, NULL, 114, NULL, 2, 26, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1662, NULL, 114, NULL, 2, 38, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1663, NULL, 114, NULL, 2, 101, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1664, NULL, 114, NULL, 2, 44, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1665, NULL, 114, NULL, 2, 60, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1666, NULL, 114, NULL, 2, 132, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1667, NULL, 114, NULL, 2, 87, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1668, NULL, 114, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1669, NULL, 114, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1670, NULL, 114, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1671, NULL, 114, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1672, NULL, 114, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1673, NULL, 114, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1674, NULL, 114, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1675, NULL, 114, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1676, NULL, 114, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1677, NULL, 114, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1678, NULL, 114, NULL, 1, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1679, NULL, 114, NULL, 1, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1680, NULL, 114, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1681, NULL, 114, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1682, NULL, 114, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1683, NULL, 114, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1684, NULL, 114, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1685, NULL, 114, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1686, NULL, 114, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1687, NULL, 114, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1688, NULL, 114, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1689, NULL, 114, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1690, NULL, 114, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1691, NULL, 114, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1692, NULL, 114, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1693, NULL, 114, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1694, NULL, 114, NULL, 4, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1695, NULL, 114, NULL, 4, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1696, NULL, 114, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1697, NULL, 114, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1698, NULL, 115, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1699, NULL, 115, NULL, 2, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1700, NULL, 115, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1701, NULL, 115, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1702, NULL, 115, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1703, NULL, 115, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1704, NULL, 115, NULL, 2, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1705, NULL, 115, NULL, 2, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1706, NULL, 115, NULL, 2, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1707, NULL, 115, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1708, NULL, 115, NULL, 2, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1709, NULL, 115, NULL, 2, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1710, NULL, 115, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1711, NULL, 115, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1712, NULL, 115, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1713, NULL, 115, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1714, NULL, 115, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1715, NULL, 115, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1716, NULL, 115, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1717, NULL, 115, NULL, 1, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1718, NULL, 115, NULL, 1, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1719, NULL, 115, NULL, 1, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1720, NULL, 115, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1721, NULL, 115, NULL, 1, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1722, NULL, 115, NULL, 1, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1723, NULL, 115, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1724, NULL, 115, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1725, NULL, 115, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1726, NULL, 115, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1727, NULL, 115, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1728, NULL, 115, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1729, NULL, 115, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1730, NULL, 115, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1731, NULL, 115, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1732, NULL, 115, NULL, 4, 27, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1733, NULL, 115, NULL, 4, 38, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1734, NULL, 115, NULL, 4, 101, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1735, NULL, 115, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1736, NULL, 115, NULL, 4, 60, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1737, NULL, 115, NULL, 4, 132, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1738, NULL, 115, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1739, NULL, 115, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1740, NULL, 116, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1741, NULL, 116, NULL, 2, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1742, NULL, 116, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1743, NULL, 116, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1744, NULL, 116, NULL, 1, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1745, NULL, 116, NULL, 1, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1746, NULL, 116, NULL, 1, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1747, NULL, 116, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1748, NULL, 116, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1749, NULL, 116, NULL, 4, 98, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1750, NULL, 116, NULL, 4, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1751, NULL, 116, NULL, 4, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1752, NULL, 116, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1753, NULL, 117, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1754, NULL, 117, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1755, NULL, 117, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1756, NULL, 117, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1757, NULL, 117, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1758, NULL, 118, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1759, NULL, 118, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1760, NULL, 118, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1761, NULL, 118, NULL, 2, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1762, NULL, 118, NULL, 2, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1763, NULL, 118, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1764, NULL, 118, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1765, NULL, 118, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1766, NULL, 118, NULL, 1, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1767, NULL, 118, NULL, 1, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1768, NULL, 118, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1769, NULL, 118, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1770, NULL, 118, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1771, NULL, 118, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1772, NULL, 118, NULL, 4, 31, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1773, NULL, 118, NULL, 4, 70, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1774, NULL, 118, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1775, NULL, 120, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1776, NULL, 120, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1777, NULL, 120, NULL, 2, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1778, NULL, 120, NULL, 2, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1779, NULL, 120, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1780, NULL, 120, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1781, NULL, 120, NULL, 1, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1782, NULL, 120, NULL, 1, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1783, NULL, 120, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1784, NULL, 120, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1785, NULL, 120, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1786, NULL, 120, NULL, 4, 21, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, true, '', 0),
        (1787, NULL, 120, NULL, 4, 22, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1788, NULL, 120, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1789, NULL, 121, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1790, NULL, 121, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1791, NULL, 121, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1792, NULL, 121, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1793, NULL, 121, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1794, NULL, 122, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1795, NULL, 122, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1796, NULL, 122, NULL, 1, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1797, NULL, 122, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1798, NULL, 122, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1799, NULL, 122, NULL, 4, 23, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1800, NULL, 122, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1801, NULL, 124, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1802, NULL, 124, NULL, 2, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1803, NULL, 124, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1804, NULL, 124, NULL, 1, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1805, NULL, 124, NULL, 3, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1806, NULL, 124, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1807, NULL, 124, NULL, 4, 50, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1808, NULL, 124, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1809, NULL, 125, NULL, 2, 126, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1810, NULL, 125, NULL, 2, 52, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1811, NULL, 125, NULL, 2, 99, -1, -1, 12, 12, 12, -1, -1, 12, 12, 12, 0, 0, 0, false, '', 0),
        (1812, NULL, 125, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1813, NULL, 125, NULL, 1, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1814, NULL, 125, NULL, 1, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1815, NULL, 125, NULL, 3, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1816, NULL, 125, NULL, 3, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1817, NULL, 125, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1818, NULL, 125, NULL, 4, 52, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1819, NULL, 125, NULL, 4, 99, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1820, NULL, 125, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1821, NULL, 126, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1822, NULL, 126, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1823, NULL, 126, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1824, NULL, 126, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1825, NULL, 126, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1826, NULL, 128, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1827, NULL, 128, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1828, NULL, 128, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1829, NULL, 128, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1830, NULL, 128, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1831, NULL, 128, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1832, NULL, 128, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1833, NULL, 128, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1834, NULL, 128, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1835, NULL, 128, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1836, NULL, 129, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1837, NULL, 129, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1838, NULL, 129, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice prachům s fibrogenním účinkem nebo možným fibrogenním a karcinogenním účinkem a dále se provádí 1x 2 roky.', 0),
        (1839, NULL, 129, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1840, NULL, 129, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1841, NULL, 129, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice prachům s fibrogenním účinkem nebo možným fibrogenním a karcinogenním účinkem a dále se provádí 1x 2 roky.', 0),
        (1842, NULL, 129, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1843, NULL, 129, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1844, NULL, 129, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1845, NULL, 129, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1846, NULL, 129, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1847, NULL, 129, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1848, NULL, 129, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1849, NULL, 129, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1850, NULL, 129, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1851, NULL, 129, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 0, 0, 0, false, '', 0),
        (1852, NULL, 130, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1853, NULL, 130, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1854, NULL, 130, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1855, NULL, 130, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1856, NULL, 130, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1857, NULL, 130, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1858, NULL, 130, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1859, NULL, 130, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1860, NULL, 131, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1861, NULL, 131, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1862, NULL, 131, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 48, 48, false, 'RTG hrudníku se provede poprvé po 4 letech expozice svářečských dýmů u svařování elektrickým obloukem a dále se provádí 1x 4 roky.', 0),
        (1863, NULL, 131, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1864, NULL, 131, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1865, NULL, 131, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1866, NULL, 131, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1867, NULL, 131, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1868, NULL, 131, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1869, NULL, 131, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1870, NULL, 131, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1871, NULL, 132, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1872, NULL, 132, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1873, NULL, 132, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1874, NULL, 132, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1875, NULL, 132, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1876, NULL, 132, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech expozice krátkodobým produktům přeměny radonu a dále se provádí 1x 2 roky.', 0),
        (1877, NULL, 132, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1878, NULL, 132, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1879, NULL, 132, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1880, NULL, 132, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1881, NULL, 132, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1882, NULL, 132, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1883, NULL, 132, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1884, NULL, 132, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1885, NULL, 132, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1886, NULL, 132, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1887, NULL, 132, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1888, NULL, 132, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1889, NULL, 132, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1890, NULL, 132, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1891, NULL, 132, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1892, NULL, 132, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1893, NULL, 132, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1894, NULL, 132, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1895, NULL, 132, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1896, NULL, 133, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1897, NULL, 133, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1898, NULL, 133, NULL, 2, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1899, NULL, 133, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1900, NULL, 133, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1901, NULL, 133, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1902, NULL, 133, NULL, 2, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1903, NULL, 133, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1904, NULL, 133, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1905, NULL, 133, NULL, 2, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 96, 0, 0, false, 'RTG hrudníku se provede poprvé po 8 letech expozice koksárenským plynům a zplyňování uhlí.', 0),
        (1906, NULL, 133, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1907, NULL, 133, NULL, 1, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1908, NULL, 133, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1909, NULL, 133, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1910, NULL, 133, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1911, NULL, 133, NULL, 1, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1912, NULL, 133, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1913, NULL, 133, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1914, NULL, 133, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1915, NULL, 133, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1916, NULL, 133, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1917, NULL, 133, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1918, NULL, 133, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1919, NULL, 133, NULL, 4, 44, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1920, NULL, 133, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1921, NULL, 133, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1922, NULL, 133, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1923, NULL, 133, NULL, 4, 9, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1924, NULL, 133, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1925, NULL, 133, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1926, NULL, 133, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1927, NULL, 133, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1928, NULL, 133, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1929, NULL, 133, NULL, 5, 44, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1930, NULL, 133, NULL, 5, 25, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1931, NULL, 133, NULL, 5, 33, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1932, NULL, 133, NULL, 5, 19, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1933, NULL, 133, NULL, 5, 9, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1934, NULL, 133, NULL, 5, 5, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1935, NULL, 133, NULL, 5, 26, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1936, NULL, 133, NULL, 5, 82, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1937, NULL, 134, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1938, NULL, 134, NULL, 2, 57, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1939, NULL, 134, NULL, 2, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1940, NULL, 134, NULL, 2, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, 'ORL vyšetření, rinoskopie rinoskopem a spirometrie se provedou po 10 letech od začátku expozice prachům tvrdých dřev.', 0),
        (1941, NULL, 134, NULL, 2, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (1942, NULL, 134, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 120, 0, 0, false, '', 0),
        (1943, NULL, 134, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1944, NULL, 134, NULL, 1, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1945, NULL, 134, NULL, 1, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zrcátkem provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie rinoskopem.', 2),
        (1946, NULL, 134, NULL, 1, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie rinoskopem provádí u zaměstnanců, kteří dovršili 45 let věku. Do 45 let věku se provádí rinoskopie zrcátkem.', 3),
        (1947, NULL, 134, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1948, NULL, 134, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1949, NULL, 134, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1950, NULL, 134, NULL, 4, 55, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se ORL vyšetření provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie zrcátkem.', 2),
        (1951, NULL, 134, NULL, 4, 79, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zcátkem provádí po dovršení 45 let věku zaměstnance. Do 45 let se provádí ORL vyšetření.', 3),
        (1952, NULL, 134, NULL, 4, 78, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1953, NULL, 134, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1954, NULL, 134, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1955, NULL, 134, NULL, 5, 55, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se ORL vyšetření se provádí do 45 let věku zaměstnance. Nad 45 let věku se provádí rinoskopie zrcátkem.', 2),
        (1956, NULL, 134, NULL, 5, 79, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, 'Pro rizikový faktor: 6.6. Prachy tvrdých dřev (břízy, buku, bílého ořechu, dubu, habru, jasanu, javoru, jilmu, kaštanu, lípy, olše, ořešáku vlašského, platanu, švestky, topolu, třešně a dalších dřev uvedených v části A tabulky č. 4 vysvětlivce písm. b) přílohy č. 3 k nařízení vlády o ochraně zdraví zaměstnanců při práci se rinoskopie zrcátkem provádí po dovršení 45 let věku zaměstnance. Do 45 let se provádí ORL vyšetření.', 3),
        (1957, NULL, 134, NULL, 5, 78, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1958, NULL, 134, NULL, 5, 87, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1959, NULL, 135, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1960, NULL, 135, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1961, NULL, 135, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1962, NULL, 135, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1963, NULL, 135, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1964, NULL, 135, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1965, NULL, 135, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1966, NULL, 135, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1967, NULL, 136, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1968, NULL, 136, NULL, 2, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1969, NULL, 136, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1970, NULL, 136, NULL, 1, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1971, NULL, 136, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1972, NULL, 136, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1973, NULL, 136, NULL, 4, 87, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1974, NULL, 136, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1975, NULL, 138, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1976, NULL, 138, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1977, NULL, 138, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1978, NULL, 138, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1979, NULL, 138, NULL, 5, 126, -1, -1, 24, 24, 24, -1, -1, 24, 24, 24, 60, 0, 0, false, '', 0),
        (1980, NULL, 140, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1981, NULL, 140, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1982, NULL, 140, NULL, 1, 94, -1, -1, -1, -1, 0, -1, -1, -1, -1, 0, 0, 0, 0, false, '', 0),
        (1983, NULL, 140, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1984, NULL, 140, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1985, NULL, 140, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1986, NULL, 141, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1987, NULL, 141, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1988, NULL, 141, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1989, NULL, 141, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1990, NULL, 141, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (1991, NULL, 143, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1992, NULL, 143, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1993, NULL, 143, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1994, NULL, 143, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1995, NULL, 143, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1996, NULL, 143, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1997, NULL, 143, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1998, NULL, 143, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (1999, NULL, 143, NULL, 1, 75, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2000, NULL, 143, NULL, 1, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2001, NULL, 143, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2002, NULL, 143, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2003, NULL, 143, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2004, NULL, 143, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2005, NULL, 143, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2006, NULL, 143, NULL, 4, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2007, NULL, 143, NULL, 5, 82, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 6, 0, 0, false, 'Pro rizikový faktor: 8.1. Tuberkulóza se za 6-12 měsíců po ukončení rizikové práce provede RTG hrudníku.', 0),
        (2008, NULL, 144, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2009, NULL, 144, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2010, NULL, 144, NULL, 2, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2011, NULL, 144, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2012, NULL, 144, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2013, NULL, 144, NULL, 1, 86, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2014, NULL, 144, NULL, 1, 7, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2015, NULL, 144, NULL, 1, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2016, NULL, 144, NULL, 1, 36, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2017, NULL, 144, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2018, NULL, 144, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2019, NULL, 144, NULL, 4, 7, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2020, NULL, 144, NULL, 4, 8, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2021, NULL, 144, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2022, NULL, 145, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2023, NULL, 145, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2024, NULL, 145, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2025, NULL, 145, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2026, NULL, 145, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2027, NULL, 145, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2028, NULL, 145, NULL, 1, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2029, NULL, 145, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2030, NULL, 145, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2031, NULL, 145, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2032, NULL, 145, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2033, NULL, 145, NULL, 4, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2034, NULL, 145, NULL, 5, 85, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 3, 0, 0, false, 'Pro rizikový faktor: 8.3. Syndrom získané imunodeficience (AIDS) je za 3 měsíce po ukončení rizikové práce, nutné individuálně zvážit provedení sérologie AIDS (HIV).', 0),
        (2035, NULL, 146, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2036, NULL, 146, NULL, 2, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2037, NULL, 146, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2038, NULL, 146, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2039, NULL, 146, NULL, 2, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2040, NULL, 146, NULL, 2, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2041, NULL, 146, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2042, NULL, 146, NULL, 1, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2043, NULL, 146, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2044, NULL, 146, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2045, NULL, 146, NULL, 1, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2046, NULL, 146, NULL, 1, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2047, NULL, 146, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2048, NULL, 146, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2049, NULL, 146, NULL, 4, 24, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2050, NULL, 146, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2051, NULL, 146, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2052, NULL, 146, NULL, 4, 5, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2053, NULL, 146, NULL, 4, 26, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2054, NULL, 146, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2055, NULL, 147, NULL, 2, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2056, NULL, 147, NULL, 2, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2057, NULL, 147, NULL, 2, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2058, NULL, 147, NULL, 2, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2059, NULL, 147, NULL, 1, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2060, NULL, 147, NULL, 1, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2061, NULL, 147, NULL, 1, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2062, NULL, 147, NULL, 1, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2063, NULL, 147, NULL, 3, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2064, NULL, 147, NULL, 4, 126, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2065, NULL, 147, NULL, 4, 25, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2066, NULL, 147, NULL, 4, 33, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2067, NULL, 147, NULL, 4, 19, -1, -1, 0, 0, 0, -1, -1, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2068, NULL, 147, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2069, 20, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2070, 20, NULL, NULL, 2, 21, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 24, 0, 0, false, 'EKG po 2 letech v profesním riziku: Práce v hlubinných dolech', 0),
        (2071, 20, NULL, NULL, 2, 82, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 48, 24, 24, false, 'RTG hrudníku se provede poprvé po 4 letech práce v profesním riziku: Práce v hlubinných dolech, následně vždy po 2 letech.', 0),
        (2072, 22, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2073, 22, NULL, NULL, 2, 22, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 24, 12, false, '', 0),
        (2074, 22, NULL, NULL, 2, 87, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2075, 1, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2076, 2, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2077, 3, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2078, 4, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2079, 5, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2080, 6, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2081, 7, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2082, 8, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2083, 9, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2084, 10, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2085, 11, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2086, 11, NULL, NULL, 2, 123, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2087, 12, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2088, 13, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2089, 14, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2090, 15, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2091, 16, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2092, 17, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2093, 18, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2094, 19, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2095, 21, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2096, 21, NULL, NULL, 2, 137, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2097, 21, NULL, NULL, 2, 80, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2098, 23, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2099, 24, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2100, 25, NULL, NULL, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2101, 26, NULL, NULL, 2, 126, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2102, 26, NULL, NULL, 2, 15, 48, 48, 48, 48, 48, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2103, 27, NULL, NULL, 2, 126, -1, 48, 48, 48, 48, -1, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2104, 27, NULL, NULL, 2, 15, -1, 48, 48, 48, 48, -1, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2105, 20, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2106, 20, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2107, 20, NULL, NULL, 1, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2108, 22, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2109, 22, NULL, NULL, 1, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2110, 22, NULL, NULL, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2111, 1, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2112, 2, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2113, 3, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2114, 4, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2115, 5, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2116, 6, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2117, 7, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2118, 8, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2119, 9, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2120, 10, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2121, 11, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2122, 11, NULL, NULL, 1, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2123, 12, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2124, 13, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2125, 14, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2126, 15, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2127, 16, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2128, 17, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2129, 18, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2130, 19, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2131, 21, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2132, 21, NULL, NULL, 1, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2133, 21, NULL, NULL, 1, 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2134, 23, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2135, 23, NULL, NULL, 1, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2136, 23, NULL, NULL, 1, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2137, 23, NULL, NULL, 1, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2138, 23, NULL, NULL, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2139, 23, NULL, NULL, 1, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2140, 23, NULL, NULL, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2141, 23, NULL, NULL, 1, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2142, 23, NULL, NULL, 1, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2143, 23, NULL, NULL, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2144, 23, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2145, 24, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2146, 24, NULL, NULL, 1, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2147, 24, NULL, NULL, 1, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2148, 24, NULL, NULL, 1, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2149, 24, NULL, NULL, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2150, 24, NULL, NULL, 1, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2151, 24, NULL, NULL, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2152, 24, NULL, NULL, 1, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2153, 24, NULL, NULL, 1, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2154, 24, NULL, NULL, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2155, 24, NULL, NULL, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2156, 24, NULL, NULL, 1, 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2157, 25, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2158, 26, NULL, NULL, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2159, 26, NULL, NULL, 1, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2160, 27, NULL, NULL, 1, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2161, 27, NULL, NULL, 1, 15, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2162, 20, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2163, 22, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2164, 1, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2165, 2, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2166, 3, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2167, 4, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2168, 5, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2169, 6, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2170, 7, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2171, 8, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2172, 9, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2173, 10, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2174, 11, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2175, 12, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2176, 13, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2177, 14, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2178, 15, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2179, 16, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2180, 17, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2181, 18, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2182, 19, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2183, 21, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2184, 23, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2185, 24, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2186, 25, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2187, 26, NULL, NULL, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2188, 27, NULL, NULL, 3, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2189, 20, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2190, 20, NULL, NULL, 4, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2191, 20, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2192, 22, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2193, 22, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2194, 1, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2195, 1, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2196, 2, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2197, 2, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2198, 3, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2199, 3, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2200, 4, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2201, 4, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2202, 5, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2203, 5, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2204, 6, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2205, 6, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2206, 7, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2207, 7, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2208, 8, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2209, 8, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2210, 9, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2211, 9, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2212, 10, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2213, 10, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2214, 11, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2215, 11, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2216, 12, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2217, 12, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2218, 13, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2219, 13, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2220, 14, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2221, 14, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2222, 15, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2223, 15, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2224, 16, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2225, 16, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2226, 17, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2227, 17, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2228, 18, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2229, 18, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2230, 19, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2231, 19, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2232, 21, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2233, 21, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2234, 23, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2235, 23, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2236, 24, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2237, 24, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2238, 25, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2239, 25, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2240, 26, NULL, NULL, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2241, 26, NULL, NULL, 4, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2242, 26, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2243, 27, NULL, NULL, 4, 126, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2244, 27, NULL, NULL, 4, 15, -1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2245, 27, NULL, NULL, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2246, NULL, NULL, 7, 1, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2247, NULL, NULL, 7, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2248, NULL, NULL, 7, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2249, NULL, NULL, 7, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2250, NULL, NULL, 7, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2251, NULL, NULL, 7, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2252, NULL, NULL, 7, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2253, NULL, NULL, 7, 1, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2254, NULL, NULL, 7, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2255, NULL, NULL, 7, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2256, NULL, NULL, 7, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2257, NULL, NULL, 7, 1, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2258, NULL, NULL, 7, 2, 35, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2259, NULL, NULL, 7, 2, 122, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2260, NULL, NULL, 7, 2, 115, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2261, NULL, NULL, 7, 2, 102, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2262, NULL, NULL, 7, 2, 108, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2263, NULL, NULL, 7, 2, 47, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2264, NULL, NULL, 7, 2, 30, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2265, NULL, NULL, 7, 2, 14, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2266, NULL, NULL, 7, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2267, NULL, NULL, 7, 4, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2268, NULL, NULL, 7, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2269, NULL, NULL, 7, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2270, NULL, NULL, 7, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2271, NULL, NULL, 7, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2272, NULL, NULL, 7, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2273, NULL, NULL, 7, 4, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2274, NULL, NULL, 7, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2275, NULL, NULL, 2, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2276, NULL, NULL, 2, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2277, NULL, NULL, 2, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2278, NULL, NULL, 2, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2279, NULL, NULL, 2, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2280, NULL, NULL, 2, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2281, NULL, NULL, 2, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2282, NULL, NULL, 2, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2283, NULL, NULL, 2, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2284, NULL, NULL, 2, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2285, NULL, NULL, 2, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2286, NULL, NULL, 2, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2287, NULL, NULL, 2, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2288, NULL, NULL, 2, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2289, NULL, NULL, 2, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2290, NULL, NULL, 2, 1, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2291, NULL, NULL, 2, 1, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2292, NULL, NULL, 2, 1, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2293, NULL, NULL, 2, 1, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2294, NULL, NULL, 2, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2295, NULL, NULL, 2, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2296, NULL, NULL, 2, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2297, NULL, NULL, 2, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2298, NULL, NULL, 2, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2299, NULL, NULL, 2, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2300, NULL, NULL, 2, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2301, NULL, NULL, 2, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2302, NULL, NULL, 2, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2303, NULL, NULL, 2, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2304, NULL, NULL, 2, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2305, NULL, NULL, 2, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2306, NULL, NULL, 2, 2, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2307, NULL, NULL, 2, 2, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2308, NULL, NULL, 2, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2309, NULL, NULL, 2, 2, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2310, NULL, NULL, 2, 2, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2311, NULL, NULL, 2, 2, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2312, NULL, NULL, 2, 2, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (2313, NULL, NULL, 2, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2314, NULL, NULL, 2, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2315, NULL, NULL, 2, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2316, NULL, NULL, 2, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2317, NULL, NULL, 2, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2318, NULL, NULL, 2, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2319, NULL, NULL, 2, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2320, NULL, NULL, 2, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2321, NULL, NULL, 2, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2322, NULL, NULL, 2, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2323, NULL, NULL, 2, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2324, NULL, NULL, 2, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2325, NULL, NULL, 2, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2326, NULL, NULL, 2, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2327, NULL, NULL, 2, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2328, NULL, NULL, 2, 3, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2329, NULL, NULL, 2, 3, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2330, NULL, NULL, 2, 3, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2331, NULL, NULL, 2, 3, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2332, NULL, NULL, 2, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2333, NULL, NULL, 2, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2334, NULL, NULL, 3, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2335, NULL, NULL, 3, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2336, NULL, NULL, 3, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2337, NULL, NULL, 3, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2338, NULL, NULL, 3, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2339, NULL, NULL, 3, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2340, NULL, NULL, 3, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2341, NULL, NULL, 3, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2342, NULL, NULL, 3, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2343, NULL, NULL, 3, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2344, NULL, NULL, 3, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2345, NULL, NULL, 3, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2346, NULL, NULL, 3, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2347, NULL, NULL, 3, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2348, NULL, NULL, 3, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2349, NULL, NULL, 3, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2350, NULL, NULL, 3, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2351, NULL, NULL, 3, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2352, NULL, NULL, 3, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2353, NULL, NULL, 3, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2354, NULL, NULL, 3, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2355, NULL, NULL, 3, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2356, NULL, NULL, 3, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2357, NULL, NULL, 3, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (2358, NULL, NULL, 3, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2359, NULL, NULL, 3, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2360, NULL, NULL, 3, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2361, NULL, NULL, 3, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2362, NULL, NULL, 3, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2363, NULL, NULL, 3, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2364, NULL, NULL, 3, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2365, NULL, NULL, 3, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2366, NULL, NULL, 3, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2367, NULL, NULL, 3, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2368, NULL, NULL, 3, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2369, NULL, NULL, 3, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2370, NULL, NULL, 3, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2371, NULL, NULL, 3, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2372, NULL, NULL, 4, 1, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2373, NULL, NULL, 4, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2374, NULL, NULL, 4, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2375, NULL, NULL, 4, 1, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2376, NULL, NULL, 4, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2377, NULL, NULL, 4, 1, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2378, NULL, NULL, 4, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2379, NULL, NULL, 4, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2380, NULL, NULL, 4, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2381, NULL, NULL, 4, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2382, NULL, NULL, 4, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2383, NULL, NULL, 4, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2384, NULL, NULL, 4, 2, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2385, NULL, NULL, 4, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2386, NULL, NULL, 4, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2387, NULL, NULL, 4, 2, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2388, NULL, NULL, 4, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2389, NULL, NULL, 4, 2, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2390, NULL, NULL, 4, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2391, NULL, NULL, 4, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2392, NULL, NULL, 4, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2393, NULL, NULL, 4, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2394, NULL, NULL, 4, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2395, NULL, NULL, 4, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (2396, NULL, NULL, 4, 3, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2397, NULL, NULL, 4, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2398, NULL, NULL, 4, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2399, NULL, NULL, 4, 3, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2400, NULL, NULL, 4, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2401, NULL, NULL, 4, 3, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2402, NULL, NULL, 4, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2403, NULL, NULL, 4, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2404, NULL, NULL, 4, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2405, NULL, NULL, 4, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2406, NULL, NULL, 4, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2407, NULL, NULL, 4, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2408, NULL, NULL, 4, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2409, NULL, NULL, 4, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2410, NULL, NULL, 5, 1, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2411, NULL, NULL, 5, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2412, NULL, NULL, 5, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2413, NULL, NULL, 5, 1, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2414, NULL, NULL, 5, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2415, NULL, NULL, 5, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2416, NULL, NULL, 5, 1, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2417, NULL, NULL, 5, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2418, NULL, NULL, 5, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2419, NULL, NULL, 5, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2420, NULL, NULL, 5, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2421, NULL, NULL, 5, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2422, NULL, NULL, 5, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2423, NULL, NULL, 5, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2424, NULL, NULL, 5, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2425, NULL, NULL, 5, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2426, NULL, NULL, 5, 1, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2427, NULL, NULL, 5, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2428, NULL, NULL, 5, 1, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2429, NULL, NULL, 5, 1, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2430, NULL, NULL, 5, 1, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2431, NULL, NULL, 5, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2432, NULL, NULL, 5, 1, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2433, NULL, NULL, 5, 1, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2434, NULL, NULL, 5, 1, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2435, NULL, NULL, 5, 2, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2436, NULL, NULL, 5, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2437, NULL, NULL, 5, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2438, NULL, NULL, 5, 2, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2439, NULL, NULL, 5, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2440, NULL, NULL, 5, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2441, NULL, NULL, 5, 2, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2442, NULL, NULL, 5, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2443, NULL, NULL, 5, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2444, NULL, NULL, 5, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2445, NULL, NULL, 5, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2446, NULL, NULL, 5, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2447, NULL, NULL, 5, 2, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2448, NULL, NULL, 5, 2, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2449, NULL, NULL, 5, 2, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2450, NULL, NULL, 5, 2, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2451, NULL, NULL, 5, 2, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2452, NULL, NULL, 5, 2, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2453, NULL, NULL, 5, 2, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2454, NULL, NULL, 5, 2, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2455, NULL, NULL, 5, 2, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2456, NULL, NULL, 5, 2, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2457, NULL, NULL, 5, 2, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2458, NULL, NULL, 5, 2, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2459, NULL, NULL, 5, 2, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2460, NULL, NULL, 5, 3, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2461, NULL, NULL, 5, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2462, NULL, NULL, 5, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2463, NULL, NULL, 5, 3, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2464, NULL, NULL, 5, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2465, NULL, NULL, 5, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2466, NULL, NULL, 5, 3, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2467, NULL, NULL, 5, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2468, NULL, NULL, 5, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2469, NULL, NULL, 5, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2470, NULL, NULL, 5, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2471, NULL, NULL, 5, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2472, NULL, NULL, 5, 3, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2473, NULL, NULL, 5, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2474, NULL, NULL, 5, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2475, NULL, NULL, 5, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2476, NULL, NULL, 5, 3, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2477, NULL, NULL, 5, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2478, NULL, NULL, 5, 3, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2479, NULL, NULL, 5, 3, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2480, NULL, NULL, 5, 3, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2481, NULL, NULL, 5, 3, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2482, NULL, NULL, 5, 3, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2483, NULL, NULL, 5, 3, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2484, NULL, NULL, 5, 3, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2485, NULL, NULL, 5, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2486, NULL, NULL, 5, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2487, NULL, NULL, 6, 1, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2488, NULL, NULL, 6, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2489, NULL, NULL, 6, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2490, NULL, NULL, 6, 1, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2491, NULL, NULL, 6, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2492, NULL, NULL, 6, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2493, NULL, NULL, 6, 1, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2494, NULL, NULL, 6, 1, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2495, NULL, NULL, 6, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2496, NULL, NULL, 6, 1, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2497, NULL, NULL, 6, 1, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2498, NULL, NULL, 6, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2499, NULL, NULL, 6, 1, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2500, NULL, NULL, 6, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2501, NULL, NULL, 6, 1, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2502, NULL, NULL, 6, 1, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2503, NULL, NULL, 6, 1, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2504, NULL, NULL, 6, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2505, NULL, NULL, 6, 1, 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2506, NULL, NULL, 6, 1, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2507, NULL, NULL, 6, 1, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2508, NULL, NULL, 6, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2509, NULL, NULL, 6, 1, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2510, NULL, NULL, 6, 1, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2511, NULL, NULL, 6, 1, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2512, NULL, NULL, 6, 2, 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2513, NULL, NULL, 6, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2514, NULL, NULL, 6, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2515, NULL, NULL, 6, 2, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2516, NULL, NULL, 6, 2, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2517, NULL, NULL, 6, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2518, NULL, NULL, 6, 2, 114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2519, NULL, NULL, 6, 2, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2520, NULL, NULL, 6, 2, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2521, NULL, NULL, 6, 2, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2522, NULL, NULL, 6, 2, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (2523, NULL, NULL, 6, 2, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření elektrokardiografické se pro: Strojvedoucí - licence
(k odst. (5), § 7, vyhlášky č. 260/2023 Sb., úz) provede pouze u osob starších 40 let.', 7),
        (2524, NULL, NULL, 6, 3, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2525, NULL, NULL, 6, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2526, NULL, NULL, 6, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2527, NULL, NULL, 6, 3, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2528, NULL, NULL, 6, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2529, NULL, NULL, 6, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2530, NULL, NULL, 6, 3, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2531, NULL, NULL, 6, 3, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2532, NULL, NULL, 6, 3, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2533, NULL, NULL, 6, 3, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2534, NULL, NULL, 6, 3, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2535, NULL, NULL, 6, 3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2536, NULL, NULL, 6, 3, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2537, NULL, NULL, 6, 3, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2538, NULL, NULL, 6, 3, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2539, NULL, NULL, 6, 3, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2540, NULL, NULL, 6, 3, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2541, NULL, NULL, 6, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2542, NULL, NULL, 6, 3, 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2543, NULL, NULL, 6, 3, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2544, NULL, NULL, 6, 3, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2545, NULL, NULL, 6, 3, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2546, NULL, NULL, 6, 3, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2547, NULL, NULL, 6, 3, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2548, NULL, NULL, 6, 3, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Jsou-li splněny podmínky pro provedení mimořádné lékařské prohlídky držitele licence strojvedoucího, a považuje-li to posuzující lékař za vhodné vzhledem k důvodu provádění mimořádné lékařské prohlídky nebo vzhledem ke skutečnostem zjištěným při mimořádné lékařské prohlídce, provede se u držitele licence strojvedoucího rovněž dopravně psychologické vyšetření podle § 4, vyhlášky č. 260/2023 Sb.', 0),
        (2549, NULL, NULL, 6, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2550, NULL, NULL, 6, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2551, NULL, NULL, 1, 1, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2552, NULL, NULL, 1, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2553, NULL, NULL, 1, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2554, NULL, NULL, 1, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2555, NULL, NULL, 1, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2556, NULL, NULL, 1, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2557, NULL, NULL, 1, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2558, NULL, NULL, 1, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2559, NULL, NULL, 1, 1, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Dopravně psychologickému vyšetření je povinen se podrobovat:
a) držitel řidičského oprávnění pro skupinu C1+E, C nebo C+E, pokud řídí nákladní automobil o největší povolené hmotnosti převyšující 7 500 kg nebo speciální automobil o největší povolené hmotnosti převyšující 7 500 kg nebo jízdní soupravu, která je složena z nákladního automobilu a přípojného vozidla nebo ze speciálního automobilu a přípojného vozidla a jejíž největší povolená hmotnost převyšuje 7 500 kg,
b) držitel řidičského oprávnění pro skupinu D1+E, D nebo D+E, pokud řídí motorové vozidlo zařazené do některé z těchto skupin vozidel.

Povinnost podrobit se dopravně psychologickému vyšetření před zahájením výkonu činnosti nevzniká, podrobil-li se držitel řidičského oprávnění uvedený v odstavci 1 dopravně psychologickému vyšetření před získáním tohoto řidičského oprávnění, a ode dne provedení vyšetření neuplynulo ke dni zahájení výkonu činnosti více než 6 měsíců.', 0),
        (2560, NULL, NULL, 1, 2, 131, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2561, NULL, NULL, 1, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2562, NULL, NULL, 1, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2563, NULL, NULL, 1, 2, 121, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2564, NULL, NULL, 1, 2, 99, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2565, NULL, NULL, 1, 2, 120, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2566, NULL, NULL, 1, 2, 110, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2567, NULL, NULL, 1, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2568, NULL, NULL, 1, 2, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, true, 'Dopravně psychologickému vyšetření je povinen se podrobovat:
a) držitel řidičského oprávnění pro skupinu C1+E, C nebo C+E, pokud řídí nákladní automobil o největší povolené hmotnosti převyšující 7 500 kg nebo speciální automobil o největší povolené hmotnosti převyšující 7 500 kg nebo jízdní soupravu, která je složena z nákladního automobilu a přípojného vozidla nebo ze speciálního automobilu a přípojného vozidla a jejíž největší povolená hmotnost převyšuje 7 500 kg,
b) držitel řidičského oprávnění pro skupinu D1+E, D nebo D+E, pokud řídí motorové vozidlo zařazené do některé z těchto skupin vozidel.

Kromě dopravně psychologickému vyšetření při zahájení činnosti řidiče, je nutné se podrobit i dalšímu dopravně psychologickému vyšetření nejdříve šest měsíců před dovršením 50 let a nejpozději v den dovršení 50 let a dále pak každých pět let. ', 0),
        (2569, NULL, NULL, 1, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2570, NULL, NULL, 1, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, 'Vyšetření zaměstnanců - řidičů, se provádí s cíleným zaměřením na zjištění příznaků nemoci uvedené v příloze č. 3, vyhlášky č. 277/2004 Sb.', 0),
        (2571, NULL, NULL, 1, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2572, NULL, NULL, 8, 1, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2573, NULL, NULL, 8, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2574, NULL, NULL, 8, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2575, NULL, NULL, 8, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2576, NULL, NULL, 8, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2577, NULL, NULL, 8, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2578, NULL, NULL, 8, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2579, NULL, NULL, 8, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2580, NULL, NULL, 8, 1, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2581, NULL, NULL, 8, 1, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2582, NULL, NULL, 8, 1, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2583, NULL, NULL, 8, 2, 130, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2584, NULL, NULL, 8, 2, 34, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2585, NULL, NULL, 8, 2, 115, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2586, NULL, NULL, 8, 2, 121, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2587, NULL, NULL, 8, 2, 99, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2588, NULL, NULL, 8, 2, 120, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2589, NULL, NULL, 8, 2, 110, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2590, NULL, NULL, 8, 2, 47, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2591, NULL, NULL, 8, 2, 107, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2592, NULL, NULL, 8, 2, 18, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (2593, NULL, NULL, 8, 2, 53, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2594, NULL, NULL, 8, 3, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2595, NULL, NULL, 8, 3, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2596, NULL, NULL, 8, 3, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2597, NULL, NULL, 8, 3, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2598, NULL, NULL, 8, 3, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2599, NULL, NULL, 8, 3, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2600, NULL, NULL, 8, 3, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2601, NULL, NULL, 8, 3, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2602, NULL, NULL, 8, 3, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2603, NULL, NULL, 8, 3, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2604, NULL, NULL, 8, 3, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2605, NULL, NULL, 8, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2606, NULL, NULL, 8, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2607, NULL, NULL, 9, 1, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2608, NULL, NULL, 9, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2609, NULL, NULL, 9, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2610, NULL, NULL, 9, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2611, NULL, NULL, 9, 1, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2612, NULL, NULL, 9, 1, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2613, NULL, NULL, 9, 1, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2614, NULL, NULL, 9, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2615, NULL, NULL, 9, 1, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2616, NULL, NULL, 9, 1, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2617, NULL, NULL, 9, 1, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Odborné vyšetření, dle § 2 odst. 3 vyhlášky 493/2002 Sb., se u držitelů zbrojních průkazů skupiny D nebo F provádí, pokud je žadatel v soustavné péči jiného lékaře nebo klinického psychologa pro nemoc, která omezuje zdravotní způsobilost k řízení motorových vozidel.', 0),
        (2618, NULL, NULL, 9, 2, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2619, NULL, NULL, 9, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2620, NULL, NULL, 9, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2621, NULL, NULL, 10, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2622, NULL, NULL, 10, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2623, NULL, NULL, 10, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2624, NULL, NULL, 10, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'V případě radiačních pracovníků kategorie A, s ohledem na kategorii práce, se provedou odborná vyšetření stanovená pro rizikový faktor ionizujícího záření, dle bodu 3.1, přílohy k vyhlášce č. 79/2013 Sb.', 0),
        (2625, NULL, NULL, 10, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2626, NULL, NULL, 11, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2627, NULL, NULL, 11, 1, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2628, NULL, NULL, 11, 2, 126, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2629, NULL, NULL, 11, 2, 50, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (2630, NULL, NULL, 11, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2631, NULL, NULL, 11, 3, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2632, NULL, NULL, 11, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2633, NULL, NULL, 11, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2634, NULL, NULL, 12, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2635, NULL, NULL, 12, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2636, NULL, NULL, 12, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2637, NULL, NULL, 12, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2638, NULL, NULL, 12, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2639, NULL, NULL, 12, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2640, NULL, NULL, 12, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2641, NULL, NULL, 12, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2642, NULL, NULL, 12, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2643, NULL, NULL, 12, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2644, NULL, NULL, 12, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2645, NULL, NULL, 12, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2646, NULL, NULL, 12, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2647, NULL, NULL, 12, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2648, NULL, NULL, 12, 1, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2649, NULL, NULL, 12, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2650, NULL, NULL, 12, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2651, NULL, NULL, 12, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2652, NULL, NULL, 12, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2653, NULL, NULL, 12, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2654, NULL, NULL, 12, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2655, NULL, NULL, 12, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2656, NULL, NULL, 12, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2657, NULL, NULL, 12, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2658, NULL, NULL, 12, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2659, NULL, NULL, 12, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie I příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2660, NULL, NULL, 12, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2661, NULL, NULL, 12, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2662, NULL, NULL, 12, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2663, NULL, NULL, 12, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2664, NULL, NULL, 12, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2665, NULL, NULL, 12, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2666, NULL, NULL, 12, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2667, NULL, NULL, 13, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2668, NULL, NULL, 13, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2669, NULL, NULL, 13, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2670, NULL, NULL, 13, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2671, NULL, NULL, 13, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2672, NULL, NULL, 13, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2673, NULL, NULL, 13, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2674, NULL, NULL, 13, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2675, NULL, NULL, 13, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2676, NULL, NULL, 13, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2677, NULL, NULL, 13, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2678, NULL, NULL, 13, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2679, NULL, NULL, 13, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2680, NULL, NULL, 13, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2681, NULL, NULL, 13, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2682, NULL, NULL, 13, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2683, NULL, NULL, 13, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2684, NULL, NULL, 13, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2685, NULL, NULL, 13, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2686, NULL, NULL, 13, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2687, NULL, NULL, 13, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2688, NULL, NULL, 13, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2689, NULL, NULL, 13, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2690, NULL, NULL, 13, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie II příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2691, NULL, NULL, 13, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2692, NULL, NULL, 13, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2693, NULL, NULL, 13, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2694, NULL, NULL, 13, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2695, NULL, NULL, 13, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2696, NULL, NULL, 13, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2697, NULL, NULL, 13, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2698, NULL, NULL, 14, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2699, NULL, NULL, 14, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2700, NULL, NULL, 14, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2701, NULL, NULL, 14, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2702, NULL, NULL, 14, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2703, NULL, NULL, 14, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2704, NULL, NULL, 14, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2705, NULL, NULL, 14, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2706, NULL, NULL, 14, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2707, NULL, NULL, 14, 2, 34, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2708, NULL, NULL, 14, 2, 122, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2709, NULL, NULL, 14, 2, 115, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2710, NULL, NULL, 14, 2, 102, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2711, NULL, NULL, 14, 2, 108, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2712, NULL, NULL, 14, 2, 47, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2713, NULL, NULL, 14, 2, 30, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (2714, NULL, NULL, 14, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie III příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2715, NULL, NULL, 14, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2716, NULL, NULL, 14, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2717, NULL, NULL, 14, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2718, NULL, NULL, 14, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2719, NULL, NULL, 14, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2720, NULL, NULL, 14, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2721, NULL, NULL, 14, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2722, NULL, NULL, 15, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2723, NULL, NULL, 15, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2724, NULL, NULL, 15, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2725, NULL, NULL, 15, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2726, NULL, NULL, 15, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2727, NULL, NULL, 15, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2728, NULL, NULL, 15, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2729, NULL, NULL, 15, 1, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2730, NULL, NULL, 15, 1, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2731, NULL, NULL, 15, 1, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2732, NULL, NULL, 15, 1, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2733, NULL, NULL, 15, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2734, NULL, NULL, 15, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2735, NULL, NULL, 15, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2736, NULL, NULL, 15, 1, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2737, NULL, NULL, 15, 2, 34, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2738, NULL, NULL, 15, 2, 122, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2739, NULL, NULL, 15, 2, 115, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2740, NULL, NULL, 15, 2, 102, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2741, NULL, NULL, 15, 2, 108, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2742, NULL, NULL, 15, 2, 30, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2743, NULL, NULL, 15, 2, 100, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2744, NULL, NULL, 15, 2, 87, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2745, NULL, NULL, 15, 2, 42, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2746, NULL, NULL, 15, 2, 46, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (2747, NULL, NULL, 15, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu zaměstnance jednotky hasičského záchranného sboru podniku - kategorie II a III - nositel dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2748, NULL, NULL, 15, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2749, NULL, NULL, 15, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2750, NULL, NULL, 15, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2751, NULL, NULL, 15, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2752, NULL, NULL, 15, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2753, NULL, NULL, 15, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2754, NULL, NULL, 15, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2755, NULL, NULL, 16, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2756, NULL, NULL, 16, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2757, NULL, NULL, 16, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2758, NULL, NULL, 16, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2759, NULL, NULL, 16, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2760, NULL, NULL, 16, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2761, NULL, NULL, 16, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2762, NULL, NULL, 16, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2763, NULL, NULL, 16, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2764, NULL, NULL, 16, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2765, NULL, NULL, 16, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2766, NULL, NULL, 16, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2767, NULL, NULL, 16, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2768, NULL, NULL, 16, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2769, NULL, NULL, 16, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2770, NULL, NULL, 16, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2771, NULL, NULL, 16, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2772, NULL, NULL, 16, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2773, NULL, NULL, 16, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2774, NULL, NULL, 16, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2775, NULL, NULL, 16, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2776, NULL, NULL, 16, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2777, NULL, NULL, 16, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2778, NULL, NULL, 16, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2779, NULL, NULL, 16, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2780, NULL, NULL, 16, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2781, NULL, NULL, 16, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2782, NULL, NULL, 16, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2783, NULL, NULL, 16, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena sboru dobrovolných hasičů obce (kategorie IV), příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2784, NULL, NULL, 16, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovlných hasičů obce (kategorie IV) jako své zaměstnání.', 0),
        (2785, NULL, NULL, 16, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2786, NULL, NULL, 16, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2787, NULL, NULL, 16, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2788, NULL, NULL, 16, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2789, NULL, NULL, 16, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2790, NULL, NULL, 16, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2791, NULL, NULL, 18, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2792, NULL, NULL, 18, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2793, NULL, NULL, 18, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2794, NULL, NULL, 18, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2795, NULL, NULL, 18, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2796, NULL, NULL, 18, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2797, NULL, NULL, 18, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2798, NULL, NULL, 18, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2799, NULL, NULL, 18, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2800, NULL, NULL, 18, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2801, NULL, NULL, 18, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2802, NULL, NULL, 18, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2803, NULL, NULL, 18, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2804, NULL, NULL, 18, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2805, NULL, NULL, 18, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2806, NULL, NULL, 18, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2807, NULL, NULL, 18, 2, 87, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2808, NULL, NULL, 18, 2, 100, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2809, NULL, NULL, 18, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena sboru dobrovolných hasičů obce (kategorie IV) - nositele dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2810, NULL, NULL, 18, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovolných hasičů obce (kategorie IV)  - nositele dýchací techniky, jako své zaměstnání.', 0),
        (2811, NULL, NULL, 18, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2812, NULL, NULL, 18, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2813, NULL, NULL, 18, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2814, NULL, NULL, 18, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2815, NULL, NULL, 18, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2816, NULL, NULL, 18, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2817, NULL, NULL, 19, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2818, NULL, NULL, 19, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2819, NULL, NULL, 19, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2820, NULL, NULL, 19, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2821, NULL, NULL, 19, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2822, NULL, NULL, 19, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2823, NULL, NULL, 19, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2824, NULL, NULL, 19, 1, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2825, NULL, NULL, 19, 1, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2826, NULL, NULL, 19, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2827, NULL, NULL, 19, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2828, NULL, NULL, 19, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2829, NULL, NULL, 19, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2830, NULL, NULL, 19, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2831, NULL, NULL, 19, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2832, NULL, NULL, 19, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2833, NULL, NULL, 19, 2, 87, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2834, NULL, NULL, 19, 2, 100, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (2835, NULL, NULL, 19, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Rozsah a náplň mimořádné zdravotní prohlídky stanoví podle aktuálního zdravotního stavu člena jednotky sboru dobrovolných hasičů podniku (kategorie IV) - nositele dýchací techniky, příslušný lékař uvedený v § 7, vyhlášky č. 352/2003 Sb.', 0),
        (2836, NULL, NULL, 19, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Následující vyšetření: Komplexní fyzikální vyšetření; Vyšetření zraku - orientační; Vyšetření sluchu - orientační; Vyšetření kůže - orientační; Vyšetření podpůrného a pohybového aparátu - orientační; Neurologické vyšetření - orientační, se provádějí pouze u osob, které vykonávají činnost člena jednotky sboru dobrovlných hasičů podniku (kategorie IV) - nositel dýchací techniky, jako své zaměstnání.', 0),
        (2837, NULL, NULL, 19, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2838, NULL, NULL, 19, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2839, NULL, NULL, 19, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2840, NULL, NULL, 19, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2841, NULL, NULL, 19, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2842, NULL, NULL, 19, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2843, NULL, NULL, 20, 1, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2844, NULL, NULL, 20, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření u osob zacházejících s pyrotechnickými výrobky kategorie P2, T2 nebo F4, se provádějí s důrazem na zjištění nemocí, které vylučují nebo omezují zdravotní způsobilost k zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4.', 0),
        (2845, NULL, NULL, 20, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2846, NULL, NULL, 20, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2847, NULL, NULL, 20, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2848, NULL, NULL, 20, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2849, NULL, NULL, 20, 2, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2850, NULL, NULL, 20, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Vyšetření u osob zacházejících s pyrotechnickými výrobky kategorie P2, T2 nebo F4, se provádějí s důrazem na zjištění nemocí, které vylučují nebo omezují zdravotní způsobilost k zacházení s pyrotechnickými výrobky kategorie P2, T2 nebo F4.', 0),
        (2851, NULL, NULL, 20, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2852, NULL, NULL, 20, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2853, NULL, NULL, 20, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2854, NULL, NULL, 20, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2855, NULL, NULL, 20, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2856, NULL, NULL, 20, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2857, NULL, NULL, 20, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2858, NULL, NULL, 21, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2859, NULL, NULL, 21, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2860, NULL, NULL, 21, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci plavidel se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2861, NULL, NULL, 21, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2862, NULL, NULL, 21, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2863, NULL, NULL, 21, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2864, NULL, NULL, 21, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2865, NULL, NULL, 21, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2866, NULL, NULL, 21, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2867, NULL, NULL, 21, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2868, NULL, NULL, 21, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2869, NULL, NULL, 21, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2870, NULL, NULL, 21, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2871, NULL, NULL, 21, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2872, NULL, NULL, 21, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2873, NULL, NULL, 21, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2874, NULL, NULL, 21, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci kapitánů plavidel se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2875, NULL, NULL, 21, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2876, NULL, NULL, 21, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2877, NULL, NULL, 21, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2878, NULL, NULL, 21, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2879, NULL, NULL, 21, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2880, NULL, NULL, 21, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2881, NULL, NULL, 21, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2882, NULL, NULL, 21, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2883, NULL, NULL, 21, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2884, NULL, NULL, 21, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2885, NULL, NULL, 21, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2886, NULL, NULL, 22, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2887, NULL, NULL, 22, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2888, NULL, NULL, 22, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci palubních důstojníků se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2889, NULL, NULL, 22, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2890, NULL, NULL, 22, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2891, NULL, NULL, 22, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2892, NULL, NULL, 22, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2893, NULL, NULL, 22, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2894, NULL, NULL, 22, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2895, NULL, NULL, 22, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2896, NULL, NULL, 22, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2897, NULL, NULL, 22, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2898, NULL, NULL, 22, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2899, NULL, NULL, 22, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2900, NULL, NULL, 22, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2901, NULL, NULL, 22, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2902, NULL, NULL, 22, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření u zaměstnanců ve funkci palubních důstojníků se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2903, NULL, NULL, 22, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2904, NULL, NULL, 22, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2905, NULL, NULL, 22, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2906, NULL, NULL, 22, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2907, NULL, NULL, 22, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2908, NULL, NULL, 22, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2909, NULL, NULL, 22, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2910, NULL, NULL, 22, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2911, NULL, NULL, 22, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2912, NULL, NULL, 22, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2913, NULL, NULL, 22, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2914, NULL, NULL, 23, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2915, NULL, NULL, 23, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2916, NULL, NULL, 23, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci palubní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2917, NULL, NULL, 23, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2918, NULL, NULL, 23, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2919, NULL, NULL, 23, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2920, NULL, NULL, 23, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2921, NULL, NULL, 23, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2922, NULL, NULL, 23, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2923, NULL, NULL, 23, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2924, NULL, NULL, 23, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2925, NULL, NULL, 23, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2926, NULL, NULL, 23, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2927, NULL, NULL, 23, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2928, NULL, NULL, 23, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2929, NULL, NULL, 23, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2930, NULL, NULL, 23, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci palubní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2931, NULL, NULL, 23, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2932, NULL, NULL, 23, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2933, NULL, NULL, 23, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2934, NULL, NULL, 23, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2935, NULL, NULL, 23, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2936, NULL, NULL, 23, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2937, NULL, NULL, 23, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2938, NULL, NULL, 23, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2939, NULL, NULL, 23, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2940, NULL, NULL, 23, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2941, NULL, NULL, 23, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2942, NULL, NULL, 24, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2943, NULL, NULL, 24, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2944, NULL, NULL, 24, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu navigační strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2945, NULL, NULL, 24, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2946, NULL, NULL, 24, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2947, NULL, NULL, 24, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2948, NULL, NULL, 24, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2949, NULL, NULL, 24, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2950, NULL, NULL, 24, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2951, NULL, NULL, 24, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2952, NULL, NULL, 24, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2953, NULL, NULL, 24, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2954, NULL, NULL, 24, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2955, NULL, NULL, 24, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2956, NULL, NULL, 24, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2957, NULL, NULL, 24, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2958, NULL, NULL, 24, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu navigační strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2959, NULL, NULL, 24, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2960, NULL, NULL, 24, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2961, NULL, NULL, 24, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2962, NULL, NULL, 24, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2963, NULL, NULL, 24, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2964, NULL, NULL, 24, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2965, NULL, NULL, 24, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2966, NULL, NULL, 24, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2967, NULL, NULL, 24, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2968, NULL, NULL, 24, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2969, NULL, NULL, 24, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2970, NULL, NULL, 25, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2971, NULL, NULL, 25, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2972, NULL, NULL, 25, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu hlídkové služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2973, NULL, NULL, 25, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2974, NULL, NULL, 25, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2975, NULL, NULL, 25, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2976, NULL, NULL, 25, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2977, NULL, NULL, 25, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2978, NULL, NULL, 25, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2979, NULL, NULL, 25, 1, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2980, NULL, NULL, 25, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2981, NULL, NULL, 25, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2982, NULL, NULL, 25, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2983, NULL, NULL, 25, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2984, NULL, NULL, 25, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2985, NULL, NULL, 25, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2986, NULL, NULL, 25, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu hlídkové služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (2987, NULL, NULL, 25, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2988, NULL, NULL, 25, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2989, NULL, NULL, 25, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2990, NULL, NULL, 25, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2991, NULL, NULL, 25, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2992, NULL, NULL, 25, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2993, NULL, NULL, 25, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2994, NULL, NULL, 25, 2, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2995, NULL, NULL, 25, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2996, NULL, NULL, 25, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2997, NULL, NULL, 25, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (2998, NULL, NULL, 26, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (2999, NULL, NULL, 26, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3000, NULL, NULL, 26, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3001, NULL, NULL, 26, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3002, NULL, NULL, 26, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3003, NULL, NULL, 26, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3004, NULL, NULL, 26, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3005, NULL, NULL, 26, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3006, NULL, NULL, 26, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3007, NULL, NULL, 26, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní strážní služby.', 0),
        (3008, NULL, NULL, 26, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3009, NULL, NULL, 26, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3010, NULL, NULL, 26, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3011, NULL, NULL, 26, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3012, NULL, NULL, 26, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3013, NULL, NULL, 26, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní strážní služby se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3014, NULL, NULL, 26, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3015, NULL, NULL, 26, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3016, NULL, NULL, 26, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3017, NULL, NULL, 26, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3018, NULL, NULL, 26, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3019, NULL, NULL, 26, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3020, NULL, NULL, 26, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3021, NULL, NULL, 26, 2, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní strážní služby.', 0),
        (3022, NULL, NULL, 26, 2, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3023, NULL, NULL, 26, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3024, NULL, NULL, 26, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3025, NULL, NULL, 26, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3026, NULL, NULL, 27, 1, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3027, NULL, NULL, 27, 1, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3028, NULL, NULL, 27, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3029, NULL, NULL, 27, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3030, NULL, NULL, 27, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3031, NULL, NULL, 27, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3032, NULL, NULL, 27, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3033, NULL, NULL, 27, 1, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3034, NULL, NULL, 27, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3035, NULL, NULL, 27, 1, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní posádky.', 0),
        (3036, NULL, NULL, 27, 1, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3037, NULL, NULL, 27, 1, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3038, NULL, NULL, 27, 1, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3039, NULL, NULL, 27, 2, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3040, NULL, NULL, 27, 2, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3041, NULL, NULL, 27, 2, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, 'Lékařská vyšetření zaměstnanců ve funkci osoby zařazené k výkonu strojní posádky se provádějí s důrazem na posouzení stavu a funkce orgánů a systémů, které budou zatěžovány při výkonu práce.', 0),
        (3042, NULL, NULL, 27, 2, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3043, NULL, NULL, 27, 2, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3044, NULL, NULL, 27, 2, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3045, NULL, NULL, 27, 2, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3046, NULL, NULL, 27, 2, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3047, NULL, NULL, 27, 2, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3048, NULL, NULL, 27, 2, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3049, NULL, NULL, 27, 2, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, 'Otorinolaryngologické vyšetření, včetně prahové tónové audiometrie, po 10 letech od začátku expozice hluku u strojní posádky.', 0),
        (3050, NULL, NULL, 27, 2, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, false, '', 0),
        (3051, NULL, NULL, 27, 3, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3052, NULL, NULL, 27, 4, 126, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3053, NULL, NULL, 27, 5, 45, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 0, 0, false, '', 0),
        (3054, NULL, NULL, 1, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3055, NULL, NULL, 1, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3056, NULL, NULL, 2, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3057, NULL, NULL, 2, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 4),
        (3058, NULL, NULL, 2, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3059, NULL, NULL, 3, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3060, NULL, NULL, 3, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 5),
        (3061, NULL, NULL, 3, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3062, NULL, NULL, 4, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3063, NULL, NULL, 4, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 6),
        (3064, NULL, NULL, 4, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3065, NULL, NULL, 5, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3066, NULL, NULL, 5, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (3067, NULL, NULL, 5, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3068, NULL, NULL, 6, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3069, NULL, NULL, 6, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 7),
        (3070, NULL, NULL, 6, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3071, NULL, NULL, 7, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3072, NULL, NULL, 7, 2, 126, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 0, 0, 0, false, '', 0),
        (3073, NULL, NULL, 7, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3074, NULL, NULL, 8, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3075, NULL, NULL, 8, 2, 126, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 0, 0, 0, false, '', 0),
        (3076, NULL, NULL, 8, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3077, NULL, NULL, 9, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3078, NULL, NULL, 12, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3079, NULL, NULL, 12, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3080, NULL, NULL, 12, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3081, NULL, NULL, 12, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3082, NULL, NULL, 13, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3083, NULL, NULL, 13, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3084, NULL, NULL, 13, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3085, NULL, NULL, 13, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3086, NULL, NULL, 14, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3087, NULL, NULL, 14, 2, 126, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 0, 0, 0, false, '', 0),
        (3088, NULL, NULL, 14, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3089, NULL, NULL, 14, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3090, NULL, NULL, 15, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3091, NULL, NULL, 15, 2, 126, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 0, 0, 0, false, '', 0),
        (3092, NULL, NULL, 15, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3093, NULL, NULL, 15, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3094, NULL, NULL, 16, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3095, NULL, NULL, 16, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3096, NULL, NULL, 16, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3097, NULL, NULL, 16, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3098, NULL, NULL, 17, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3099, NULL, NULL, 17, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3100, NULL, NULL, 17, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3101, NULL, NULL, 17, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3102, NULL, NULL, 17, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3103, NULL, NULL, 17, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3104, NULL, NULL, 17, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3105, NULL, NULL, 17, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3106, NULL, NULL, 17, 1, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3107, NULL, NULL, 17, 1, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3108, NULL, NULL, 17, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3109, NULL, NULL, 17, 1, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3110, NULL, NULL, 17, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3111, NULL, NULL, 17, 1, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3112, NULL, NULL, 17, 1, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3113, NULL, NULL, 17, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3114, NULL, NULL, 17, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3115, NULL, NULL, 17, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3116, NULL, NULL, 17, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3117, NULL, NULL, 17, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3118, NULL, NULL, 17, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3119, NULL, NULL, 17, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3120, NULL, NULL, 17, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3121, NULL, NULL, 17, 2, 34, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3122, NULL, NULL, 17, 2, 122, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3123, NULL, NULL, 17, 2, 115, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3124, NULL, NULL, 17, 2, 102, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3125, NULL, NULL, 17, 2, 108, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3126, NULL, NULL, 17, 2, 47, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3127, NULL, NULL, 17, 2, 30, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3128, NULL, NULL, 17, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3129, NULL, NULL, 17, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3130, NULL, NULL, 17, 4, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3131, NULL, NULL, 17, 4, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3132, NULL, NULL, 17, 4, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3133, NULL, NULL, 17, 4, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3134, NULL, NULL, 17, 4, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3135, NULL, NULL, 17, 4, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3136, NULL, NULL, 18, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3137, NULL, NULL, 18, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3138, NULL, NULL, 18, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3139, NULL, NULL, 18, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3140, NULL, NULL, 19, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3141, NULL, NULL, 19, 2, 126, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 0, 0, 0, false, '', 0),
        (3142, NULL, NULL, 19, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3143, NULL, NULL, 19, 4, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3144, NULL, NULL, 20, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3145, NULL, NULL, 20, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3146, NULL, NULL, 20, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3147, NULL, NULL, 21, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3148, NULL, NULL, 21, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3149, NULL, NULL, 21, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3150, NULL, NULL, 22, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3151, NULL, NULL, 22, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3152, NULL, NULL, 22, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3153, NULL, NULL, 23, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3154, NULL, NULL, 23, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3155, NULL, NULL, 23, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3156, NULL, NULL, 24, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3157, NULL, NULL, 24, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3158, NULL, NULL, 24, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3159, NULL, NULL, 25, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3160, NULL, NULL, 25, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3161, NULL, NULL, 25, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3162, NULL, NULL, 26, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3163, NULL, NULL, 26, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3164, NULL, NULL, 26, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3165, NULL, NULL, 27, 1, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3166, NULL, NULL, 27, 2, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0),
        (3167, NULL, NULL, 27, 3, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, '', 0);
INSERT INTO dbe.med_conclusions (id, title)
VALUES  (1, 'Zdravotně způsobilý(á)'),
        (2, 'Zdravotně způsobilý(á) s podmínkou'),
        (3, 'Zdravotně nezpůsobilý(á)'),
        (4, 'Dlouhodobě pozbyl(a) zdravotní způsobilost');
INSERT INTO dbe.med_concl_completeness (id, title)
VALUES  (0, 'Dokončeno'),
        (1, 'Žádá o přezkum'),
        (2, 'Čeká na výsledek');

INSERT INTO dbe.med_check_states (id, title)
VALUES  (1, 'Dokončena'),
        (2, 'Naplánovaná'),
        (3, 'Blíží se'),
        (4, 'Odeslán'),
        (5, 'Po termínu'),
        (6, 'Přezkum'),
        (7, 'Čeká na výsledek');
INSERT INTO dbe.people_check_states (id, title)
VALUES
    (1, 'Naplánovaná'),
    (2, 'Blíží se'),
    (3, 'Po termínu'),
    (4, 'Není naplánovaná'),
    (5, 'Chybí prohlídka'),
    (6, 'Pozastavená'),
    (7, 'Není povinná'),
    (8, 'Nevyžaduje se');
INSERT INTO dbe.injury_statuses (id, title, enum, order_) VALUES
    (1, 'Uzavřeno', 'wi_status_done', 100),
    (2, 'V řešení', 'wi_status_in_progress', 200),
    (3, 'Šetří státní orgány', 'wi_status_investigating', 300),
    (4, 'Probíhá soud', 'wi_status_trial', 400),
    (5, 'Přerušeno', 'wi_status_interrupted', 500),
    (6, 'Odloženo', 'wi_status_postponed', 600),
    (7, 'Zrušeno', 'wi_status_cancelled', 700);


INSERT INTO dbe.injury_type_stats (id, title, enum, order_) VALUES
    (1, 'Smrtelný pracovní úraz', 'wi_type_stat_death', 100),
    (2, 'Úraz s hospitalizací > 5 dnů', 'wi_type_stat_hospital', 200),
    (3, 'Úraz s pracovní neschopností > 3 kalendářní dny', 'wi_type_stat_long', 300),
    (4, 'Ostatní', 'wi_type_stat_rest', 400);


INSERT INTO dbe.injury_types (id, code, title, enum, heading, order_) VALUES
    (1, 0,	'Neznámý nebo neurčený druh zranění', 'wi_unknown_or_unspecified_injury', true, 100),
    (2, 10, 'Rány a povrchová zranění', 'wi_cuts_and_surface_injuries', true, 200),
    (3, 11, 'Povrchové zranění', 'wi_surface_injuries', false, 300),
    (4, 12, 'Otevřené rány', 'wi_open_wounds', false, 400),
    (5, 19, 'Jiné typy ran a povrchových zranění', 'wi_other_cuts_and_surface_injuries', false, 500),
    (6, 20, 'Zlomeniny kostí', 'wi_bone_fractures', true, 600),
    (7, 21, 'Zavřené zlomeniny', 'wi_closed_fractures', false, 700),
    (8, 22, 'Otevřené zlomeniny', 'wi_open_fractures', false, 800),
    (9, 29, 'Jiné typy zlomenin kostí', 'wi_other_bone_fractures', false, 900),
    (10, 30, 'Vykloubení, vyvrtnutí, natažení', 'wi_dislocations_sprains_strains', true, 1000),
    (11, 31, 'Vykloubení nebo neúplné vykloubení', 'wi_dislocations_or_subluxations', false, 1100),
    (12, 32, 'Vyvrtnutí nebo natažení', 'wi_sprains_or_strains', false, 1200),
    (13, 39, 'Jiné typy vykloubení, vyvrtnutí, natažení', 'wi_other_dislocations_sprains_strains', false, 1300),
    (14, 40, 'Traumatická amputace (ztráta části těla)', 'wi_traumatic_amputation', true, 1400),
    (15, 50, 'Otřes mozku a vnitřní zranění', 'wi_concussions_and_internal_injuries', true, 1500),
    (16, 51, 'Otřes mozku a vnitrolebeční zranění', 'wi_concussions_and_intracranial_injuries', false, 1600),
    (17, 52, 'Vnitřní zranění', 'wi_internal_injuries', false, 1700),
    (18, 59, 'Jiné typy otřesů mozku a vnitřních zranění', 'wi_other_concussions_and_internal_injuries', false, 1800),
    (19, 60, 'Popáleniny, opařeniny a omrzliny', 'wi_burns_scalds_and_frostbites', true, 1900),
    (20, 61, 'Popáleniny a opařeniny (tepelné)', 'wi_burns_and_scalds_thermal', false, 2000),
    (21, 62, 'Chemické popáleniny (poleptání)', 'wi_chemical_burns', false, 2100),
    (22, 63, 'Omrzliny', 'wi_frostbites', false, 2200),
    (23, 69, 'Jiné typy popálenin, opařenin a omrzlin', 'wi_other_burns_scalds_and_frostbites', false, 2300),
    (24, 70, 'Otravy a infekce', 'wi_poisonings_and_infections', true, 2400),
    (25, 71, 'Akutní otravy', 'wi_acute_poisonings', false, 2500),
    (26, 72, 'Akutní infekce', 'wi_acute_infections', false, 2600),
    (27, 79, 'Jiné typy otrav a infekcí', 'wi_other_poisonings_and_infections', false, 2700),
    (28, 80, 'Tonutí a dušení', 'wi_drowning_and_asphyxiation', true, 2800),
    (29, 81, 'Dušení', 'wi_asphyxiation', false, 2900),
    (30, 82, 'Tonutí bez smrtelných následků', 'wi_non_fatal_drowning', false, 3000),
    (31, 89, 'Jiné typy tonutí a dušení', 'wi_other_drowning_and_asphyxiation', false, 3100),
    (32, 90, 'Účinky zvuku, vibrací a tlaku', 'wi_effects_of_sound_vibration_and_pressure', true, 3200),
    (33, 91, 'Akutní ztráta sluchu', 'wi_acute_hearing_loss', false, 3300),
    (34, 92, 'Působení tlaku (barotrauma)', 'wi_pressure_effects_barotrauma', false, 3400),
    (35, 99, 'Jiné účinky zvuku, vibrací a tlaku', 'wi_other_effects_of_sound_vibration_and_pressure', false, 3500),
    (36, 100, 'Účinky extrémních teplot, světla a ozáření', 'wi_effects_of_extreme_temperature_light_and_radiation', true, 3600),
    (37, 101, 'Úpal z tepla a slunečního záření', 'wi_heat_stroke_and_solar_radiation', false, 3700),
    (38, 102, 'Účinky ozáření (netepelné)', 'wi_radiation_effects_non_thermal', false, 3800),
    (39, 103, 'Účinky snížené teploty', 'wi_effects_of_cold', false, 3900),
    (40, 109, 'Jiné účinky extrémních teplot, světla a ozáření', 'wi_other_effects_of_extreme_temperature_light_radiation', false, 4000),
    (41, 110, 'Šok', 'wi_shock', true, 4100),
    (42, 111, 'Šoky po agresích a hrozbách', 'wi_shocks_from_assaults_and_threats', false, 4200),
    (43, 112, 'Traumatické šoky', 'wi_traumatic_shock', false, 4300),
    (44, 119, 'Jiné typy šoků', 'wi_other_shocks', false, 4400),
    (45, 120, 'Vícenásobné zranění', 'wi_multiple_injuries', true, 4500),
    (46, 999, 'Jiná specifická zranění nezahrnutá do jiných kategorií', 'wi_other_specific_injuries_not_elsewhere_classified', true, 4600);


INSERT INTO dbe.injury_parts (id, title, code, enum, heading, tooltip, valid, order_) VALUES
        (1, 'Zraněná část těla nespecifikována', 0, 'wi_unspecified_injury_area', true, '', true, 100),
        (2, 'Hlava bez podrobnějšího rozlišení, dále nespecifikována', 10, 'wi_head_unspecified', true, '', true, 200),
        (3, 'Hlava, mozek, lebeční nervy a cévy', 11, 'wi_head_brain_skull_nerves_vessels', false, '', true, 300),
        (4, 'Tvář', 12, 'wi_face', false, '', true, 400),
        (5, 'Oko', 13, 'wi_eye', false, '', true, 500),
        (6, 'Ucho', 14, 'wi_ear', false, '', true, 600),
        (7, 'Zuby', 15, 'wi_teeth', false, '', true, 700),
        (8, 'Hlava - více postižených oblastí', 18, 'wi_head_multiple_areas', false, '', true, 800),
        (9, 'Hlava - jiné části výše neuvedené', 19, 'wi_head_other', false, '', true, 900),
        (10, 'Krk včetně páteře a krčních obratlů', 20, 'wi_neck_including_spine_cervical_vertebrae_heading', true, '', true, 1000),
        (11, 'Krk včetně páteře a krčních obratlů', 21, 'wi_neck_including_spine_cervical_vertebrae', false, '', true, 1100),
        (12, 'Krk - jiné části dosud neuvedené', 29, 'wi_neck_other', false, '', true, 1200),
        (13, 'Záda včetně páteře a zádových obratlů', 30, 'wi_back_including_spine_back_vertebrae_heading', true, '', true, 1300),
        (14, 'Záda včetně páteře a zádových obratlů', 31, 'wi_back_including_spine_back_vertebrae', false, '', true, 1400),
        (15, 'Záda - jiné části výše neuvedené', 39, 'wi_back_other', false, '', true, 1500),
        (16, 'Trup a orgány bez podrobnějšího rozlišení', 40, 'wi_trunk_organs_unspecified', true, '', true, 1600),
        (17, 'Hrudní koš, žebra včetně kloubů a lopatek', 41, 'wi_chest_ribs_joints_scapula', false, '', true, 1700),
        (18, 'Oblast hrudníku včetně orgánů', 42, 'wi_chest_organs', false, '', true, 1800),
        (19, 'Pánevní a břišní oblast včetně orgánů', 43, 'wi_pelvic_abdominal_organs', false, '', true, 1900),
        (20, 'Trup - více postižených oblastí', 48, 'wi_trunk_multiple_areas', false, '', true, 2000),
        (21, 'Trup - jiné části výše neuvedené', 49, 'wi_trunk_other', false, '', true, 2100),
        (22, 'Horní končetiny bez podrobnějšího rozlišení', 50, 'wi_upper_limb_unspecified', true, '', true, 2200),
        (23, 'Rameno a ramenní klouby', 51, 'wi_shoulder_joints', false, '', true, 2300),
        (24, 'Ruka včetně lokte', 52, 'wi_arm_including_elbow', false, '', true, 2400),
        (25, 'Ruka od zápěstí dolů', 53, 'wi_hand_from_wrist_down', false, '', true, 2500),
        (26, 'Prst', 54, 'wi_finger', false, '', true, 2600),
        (27, 'Zápěstí', 55, 'wi_wrist', false, '', true, 2700),
        (28, 'Horní končetiny - více postižených oblastí', 58, 'wi_upper_limb_multiple_areas', false, '', true, 2800),
        (29, 'Horní končetiny - jiné části výše neuvedené', 59, 'wi_upper_limb_other', false, '', true, 2900),
        (30, 'Dolní končetiny bez podrobnějšího rozlišení', 60, 'wi_lower_limb_unspecified', true, '', true, 3000),
        (31, 'Bedra, bederní klouby', 61, 'wi_hip_hip_joints', false, '', true, 3100),
        (32, 'Noha včetně kolena', 62, 'wi_leg_including_knee', false, '', true, 3200),
        (33, 'Kotník', 63, 'wi_ankle', false, '', true, 3300),
        (34, 'Noha od kotníku dolů', 64, 'wi_foot_from_ankle_down', false, '', true, 3400),
        (35, 'Prst na noze', 65, 'wi_toe', false, '', true, 3500),
        (36, 'Dolní končetiny - více postižených oblastí', 68, 'wi_lower_limb_multiple_areas', false, '', true, 3600),
        (37, 'Dolní končetiny - jiné části výše neuvedené', 69, 'wi_lower_limb_other', false, '', true, 3700),
        (38, 'Celé tělo a více oblastí bez podrobnějšího rozlišení', 70, 'wi_whole_body_unspecified', true, '', true, 3800),
        (39, 'Celé tělo (systémové účinky)', 71, 'wi_whole_body_systemic_effects', false, '', true, 3900),
        (40, 'Tělo - více postižených oblastí', 72, 'wi_body_multiple_areas', false, '', true, 4000),
        (41, 'Tělo - jiná zraněná část výše neuvedená', 79, 'wi_body_other_unspecified_injury', false, '', true, 4100);

INSERT INTO dbe.injury_sources (id, title, enum, order_) VALUES
    (1, 'Dopravní prostředek', 'wi_transport_vehicle', 100),
    (2, 'Stroje a zařízení přenosná nebo mobilní', 'wi_portable_mobile_machinery', 200),
    (3, 'Materiál, břemena, předměty (pád, přiražení, odlétnutí, náraz, zavalení)', 'wi_materials_objects', 300),
    (4, 'Pád na rovině, z výšky, do hloubky, propadnutí', 'wi_fall_levels_heights_depths', 400),
    (5, 'Nástroj, přístroj, nářadí', 'wi_tools_instruments', 500),
    (6, 'Průmyslové škodliviny, chemické látky, biologické činitele', 'wi_industrial_hazards', 600),
    (7, 'Horké látky a předměty, oheň a výbušniny', 'wi_hot_materials_fire_explosives', 700),
    (8, 'Stroje a zařízení stabilní', 'wi_fixed_machinery', 800),
    (9, 'Lidé, zvířata nebo přírodní živly', 'wi_human_animal_natural_forces', 900),
    (10, 'Elektrická energie', 'wi_electrical_energy', 1000),
    (11, 'Jiný blíže nespecifikovaný zdroj', 'wi_other_unspecified_source', 1100);


INSERT INTO dbe.injury_causes (id, title, enum, order_) VALUES
    (1, 'Pro poruchu nebo vadný stav některého ze zdrojů úrazu', 'wi_source_fault_or_defect', 100),
    (2, 'Pro špatné nebo nedostatečné vyhodnocení rizika zaměstnavatelem', 'wi_employer_risk_evaluation_failure', 200),
    (3, 'Pro závady na pracovišti', 'wi_workplace_faults', 300),
    (4, 'Pro nedostatečné osobní zajištění zaměstnance včetně osobních ochranných pracovních prostředků', 'wi_employee_personal_security_lack', 400),
    (5, 'Pro porušení předpisů vztahujících se k práci nebo pokynů zaměstnavatele úrazem postiženého zaměstnance', 'wi_regulations_or_instructions_violation', 500),
    (6, 'Pro nepředvídatelné riziko práce nebo selhání lidského činitele', 'wi_unforeseeable_work_risk_or_human_error', 600),
    (7, 'Pro jiný, blíže nespecifikovaný důvod', 'wi_other_unspecified_reason', 700);


INSERT INTO dbe.injury_doc_types (id, title, enum) VALUES
    (1, 'Evideční list knihy úrazů', 'wi_registration_file'),
    (2, 'Záznam o úrazu', 'wi_injury_record'),
    (3, 'Záznam o úrazu - hlášení změn', 'wi_injury_record_change'),
    (4, 'Doklad o předání záznamu', 'wi_injury_record_handover');

INSERT INTO dbe.pdf_classes (id, title) VALUES (2, 'Záznam o úrazu');
INSERT INTO dbe.pdf_types (id, title, class ,version, commandline) VALUES (3, 'Evideční list knihy úrazů', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class, version, commandline) VALUES (4, 'Záznam o úrazu', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class ,version, commandline) VALUES (5, 'Záznam o úrazu - hlášení změn', 2, '1.0', '');
INSERT INTO dbe.pdf_types (id, title, class, version, commandline) VALUES (6, 'Doklad o předání záznamu', 2, '1.0', '');
