//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTBOOLTRISTATE_H
#define SAW_ALL_AMUIFORMELEMENTBOOLTRISTATE_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElementBool.h"
#include "amui/AMUIIView.h"

namespace AMDialogs {

    extern char boolUnselectedText[32];

    template<typename TStringType>
    class AMUIFormElementBoolTristate : public AMUIFormElementBool<TStringType> {
    public:
        AMUIFormElementBoolTristate(const char *key);

        ~AMUIFormElementBoolTristate();

        void setValue(int value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return std::to_string(this->m_value);};
        void setValueString(std::string value) override {int newVal = -1;if (!value.empty()) {newVal = std::stoi(value);};setValue(newVal);};
    };

}

#include "src/AMUIFormElementBoolTristate.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTBOOL_H
