//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTAUTHORTAG_H
#define SAW_ALL_AMUIFORMELEMENTAUTHORTAG_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElement.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"


namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAuthorTag;
    class AMUIDialog;

    template<typename TStringType>
    class AMUIFormElementAuthorTag : public AMUIFormElement {
    public:
        AMUIFormElementAuthorTag(const char *key);

        ~AMUIFormElementAuthorTag();

        void init();

        void reset() override;

        void updateState() override;

        [[nodiscard]] int length() const override { return m_length + 1; }

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_author_tag; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        void setValue(const char *value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return "";};
        std::string valueAsString(std::map<int, std::string> *keys) override {return "";};
        void setValueString(std::string value) override {setValue(value.c_str());};

        void onDialogChange(AMUIDialog*);
        void onDialogClose(AMUIDialog*);

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}

        void loadDefault() override;

    protected:
        char *m_buffer;
        GLuint m_icon;
        AMUIDialogAuthorTag<TStringType> *m_dialog;
        char m_wildcard;
        int m_preventRefreshDialog;
    };

}

#include "src/AMUIFormElementAuthorTag.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTSTRING_H
