//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGENUMLINE_H
#define SAW_AMUIDIALOGENUMLINE_H

#include "amdialogs/AMUIDialog.h"
#include "amdialogs/AMUIFormElementCheckBox.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    struct _AMUIDialogEnumLineItem
    {
        AMUIFormElementCheckBox<TStringType> element;
        TEnumType value;
        _AMUIDialogEnumLineItem()
            : element(""),
            value()
        {};
    };


    template<typename TEnumType, typename TStringType>
    class AMUIDialogEnumLine:public  AMUIDialog {
    public:
        AMUIDialogEnumLine();
        void init(
            std::string name,
            std::function<void(AMUIDialog *)>,
            std::function<void(AMUIDialog *)>,
            int enumNo,
            std::string strVoidParam,
            std::map<TEnumType, int> *selected,
            std::string flashbackTimepoint
            );
        std::vector<AMUIFormElement *> &elements() override;
        std::vector<_AMUIDialogEnumLineItem<TEnumType, TStringType> > &elementsStor();

        const AMCore::AMTwoWayTable<TEnumType, TStringType> *table();
        std::map<TEnumType, int> *selected();
        //void onChange() override;
        void update();

        std::string hintText() {return m_hintText;}
        std::string setHintText(std::string text) {std::string rv = m_hintText; m_hintText = text; return rv;}
    protected:
        int m_enumNo;
        std::map<TEnumType, int> *m_selected;
        const AMCore::AMTwoWayTable<TEnumType, TStringType> *m_table;
        std::chrono::time_point<std::chrono::steady_clock> m_timePoint;
        std::vector<AMUIFormElement *> m_elements;
        std::vector<_AMUIDialogEnumLineItem<TEnumType, TStringType> > m_elementsStor;
        std::string m_strVoidParam;
        std::string m_hintText;
        std::string m_flashbackTimepoint;
    };

} // AMDialogs

#include "src/AMUIDialogEnumLine.hpp"

#endif //SAW_AMUIDIALOGENUMLINE_H
