//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTADDRESSNULLABLE_H
#define SAW_ALL_AMUIFORMELEMENTADDRESSNULLABLE_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElement.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "AMUIFormElementAddress.h"


namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAddress;
    class AMUIDialog;

    template<typename TStringType>
    class AMUIFormElementAddressNullable : public AMUIFormElementAddress<TStringType> {
    public:
        AMUIFormElementAddressNullable(const char *key);

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_address_nullable; };

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return m_notNull ? AMUI::url_encode_string(TStringType(this->m_buffer)) : "";};
        void setValueString(std::string value) override {if (value.empty()) {setValue("");m_notNull = false;} else {setValue(value.c_str());m_notNull = false;};};

        void setValue(const char *value) override;

        void setValue();

        bool isNull() override;

    protected:
        bool m_notNull;
    };

}

#include "src/AMUIFormElementAddressNullable.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTADDRESSNULLABLE_H
