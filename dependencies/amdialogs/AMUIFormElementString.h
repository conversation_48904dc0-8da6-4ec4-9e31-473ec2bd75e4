//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTSTRING_H
#define SAW_ALL_AMUIFORMELEMENTSTRING_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElement.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"


namespace AMDialogs {

    class AMUIFormElementString : public AMUIFormElement {
    public:
        AMUIFormElementString(const char *key, bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementString();

        void init();

        void reset() override;

        void updateState() override;

        int length() const override { return m_length + 1; }

        AMCore::AMDataType type() const override { return m_nullable ? AMCore::AMDataType_string_nullable : AMCore::AMDataType_string; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        virtual void setValue(const char *value);
        virtual void setValue();

        void render(bool formGrayed, int element_id) override;



        std::string valueAsString() override;
        void setValueString(std::string value) override;

        bool isNull() override;

        //void setWildcard(char wildcard);

        //char wildcard() {return m_wildcard;}

        void loadDefault() override;

    protected:
        char *m_buffer;
        //bool  m_notNull;
        //char m_wildcard;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTSTRING_H
