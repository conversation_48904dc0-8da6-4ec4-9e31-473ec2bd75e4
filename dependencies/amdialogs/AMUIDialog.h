//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOG_H
#define SAW_AMUIDIALOG_H

//#include "AMUIForm.h"
#include <functional>
#include "amui/AMUIIView.h"
#include "amdialogs/AMUIFormElement.h"

namespace AMDialogs {

    class AMUIDialogView;

    class AMUIDialog {

    public:
        AMUIDialog();
        virtual ~AMUIDialog();
        void init(std::string name, std::function<void(AMUIDialog *)> on_close, std::function<void(AMUIDialog *)> on_change = std::function<void(AMUIDialog *)>());
        AMUIDialogView *view();
        int id() const;
        std::string name();
        virtual std::vector<AMUIFormElement *> &elements();
        virtual void perform();
        virtual void onChange();
        virtual void onClose();
        virtual void closeRequest(bool byCloseButton);
        AMUIWidgetState state();
        bool isClosedByCloseButton();
        std::string closeButtontext();
        bool enterHitClosesDialog();
        void setEnterHitClosesDialog(bool value);
    protected:
        AMUIDialogView *m_view;
        std::string m_dlgName;
        int m_id;
        static int g_id;
        std::function<void(AMUIDialog *)> m_onClose;
        std::function<void(AMUIDialog *)> m_onChange;
        bool m_closeRequest;
        bool m_closedByCloseButton;
        std::string m_closeButtonText;
        bool m_enterHitClosesDialog;
    };

} // AMDialogs

#endif //SAW_AMUIDIALOG_H
