//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGENUMLINENULLABLEVIEW_H
#define SAW_AMUIDIALOGENUMLINENULLABLEVIEW_H

#include "AMUIDialogEnumLineView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogEnumLineNullableView: public AMDialogs::AMUIDialogEnumLineView<TEnumType, TStringType>
    {
    public:
        AMUIDialogEnumLineNullableView();
        bool renderNullable() override;

    protected:

    };

} // AMDialogs

#include "src/AMUIDialogEnumLineNullableView.hpp"

#endif //SAW_AMUIDIALOGENUMLINENULLABLEVIEW_H
