//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTENUMMAPIMAGE_H
#define SAW_ALL_AMUIFORMELEMENTENUMMAPIMAGE_H

#include "AMUIFormElementEnumMap.h"
#include <cassert>
#include <ctime>
#include <functional>
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIController.h"
#include "amui/AMUIConfig.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIFormElementEnumMapImage : public AMUIFormElementEnumMap<TEnumType, TStringType> {
    public:
        explicit AMUIFormElementEnumMapImage(const std::string key = "", bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementEnumMapImage() override;

        void init(int enumId, const std::string &strVoidParam) override;

    protected:
        void drawIcon() override;
        bool drawIconTest() override;

    protected:

        std::vector<GLuint> *m_icons;
        bool m_iconsForeign;
    };




}

#include "src/AMUIFormElementEnumMapImage.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTENUMMAPIMAGE_H
