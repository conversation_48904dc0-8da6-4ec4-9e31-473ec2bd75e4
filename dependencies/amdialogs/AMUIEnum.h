//
// Created by <PERSON><PERSON><PERSON> on 29.1.23.
//

#ifndef AMUITABLE_AMUIENUM_H
#define AMUITABLE_AMUIENUM_H

#include <chrono>
#include <string>
#include <vector>
#include "amcore/AMTwoWayTable.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMCore::AMTwoWayTable<TEnumType, TStringType> *twoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint = "", std::chrono::time_point<std::chrono::steady_clock> *clock = nullptr);

    template<typename TEnumType, typename TStringType>
    void prepareTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint = "");

    template<typename TEnumType, typename TStringType>
    void refreshTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint = "");

    template<typename TEnumType, typename TStringType>
    void destroyTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint = "");

    template<typename TEnumType, typename TStringType>
    void lockTwoWayTable(int index);

    template<typename TEnumType, typename TStringType>
    void unlockTwoWayTable(int index);

    template<typename TEnumType, typename TStringType>
    void twoWayTableWasUpdated(int index, const std::string &strVoidParam, std::string flashTimepoint = "");

    int twoWayTableTableID(int index);
    int twoWayTableTableIndex(int id);

    template<typename TEnumType, typename TStringType>
    class AMUIFormTwoWayTableLock
    {
    public:
        AMUIFormTwoWayTableLock(int column)
            : m_column(column)
        {
            lockTwoWayTable<TEnumType, TStringType>(column);
        }

        ~AMUIFormTwoWayTableLock()
        {
            unlockTwoWayTable<TEnumType, TStringType>(m_column);
        }
    protected:
        int m_column;
    };

#define IMAGE_IN_IMAGETABLE_SIZE 20.0f

    std::vector<unsigned int> *imageTable(int index);

    void prepareImageTable(int index);

    void destroyImageTable(int index);

}

#endif //AMUITABLE_AMUIENUM_H
