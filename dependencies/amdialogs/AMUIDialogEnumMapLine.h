////
//// Created by <PERSON><PERSON><PERSON> on 9.2.23.
////
//
//#ifndef SAW_AMUIDIALOGENUMMAPLINE_H
//#define SAW_AMUIDIALOGENUMMAPLINE_H
//
//#include "amdialogs/AMUIDialog.h"
//#include "amdialogs/AMUIFormElementCheckBox.h"
//#include "amdialogs/AMUIFormElementButton.h"
//#include "amdialogs/AMUIFormElementEnumMap.h"
//
//namespace AMDialogs {
//
//
//    template<typename TEnumType, typename TStringType>
//    struct _AMUIDialogEnumMapLineItem
//    {
//        AMUIFormElementCheckBox<TStringType> element;
//        TEnumType value;
//        _AMUIDialogEnumMapLineItem()
//            : element(""),
//            value()
//        {};
//    };
//
//    template<typename TEnumType, typename TStringType>
//    struct _AMUIDialogEnumMapLineItemEx
//    {
//        AMUIFormElementButton elementRemoveButton;
//        AMUIFormElementEnumMap<TEnumType, TStringType> elementEnumMap;
//        std::map<TEnumType, int> value;
//        _AMUIDialogEnumMapLineItemEx()
//            : elementRemoveButton(""),
//              elementEnumMap(""),
//              value()
//        {};
//    };
//
//
//    template<typename TEnumType, typename TStringType>
//    class AMUIDialogEnumMapLine:public  AMUIDialog {
//    public:
//        AMUIDialogEnumMapLine();
//        void init(
//            std::string name,
//            std::function<void(AMUIDialog *)>,
//            std::function<void(AMUIDialog *)>,
//            int enumNo,
//            std::string strVoidParam,
//            std::map<TEnumType, int> *selectedCommon,
//            std::map<int, std::map<TEnumType, int> > *selectedExtended
//            );
//        std::vector<_AMUIDialogEnumMapLineItem<TEnumType, TStringType> > &elementsStorCommon() {return m_elementsStorCommon;};
//        std::vector<_AMUIDialogEnumMapLineItemEx<TEnumType, TStringType> > &elementsStorExtended() {return  m_elementsStorExtended;};
//        std::vector<AMUIFormElement *> &elements() override {return m_elements;};
//        AMUIFormElementCheckBox<TStringType> &elementAny() {return  m_elementAny;}
//        void setAny(bool any);
//        bool any() {return m_valueAny;}
//        const AMCore::AMTwoWayTable<TEnumType, TStringType> *table();
//        std::map<TEnumType, int> *selectedCommon();
//
//
//
//        void onDeleteButton(int ndx);
//        void update();
//    protected:
//        int m_enumNo;
//        std::map<TEnumType, int> *m_selectedCommon;
//        std::map<int, std::map<TEnumType, int> > *m_selectedExtended;
//        const AMCore::AMTwoWayTable<TEnumType, TStringType> *m_table;
//        bool m_valueAny;
//        AMUIFormElementCheckBox<TStringType> m_elementAny;
//        std::chrono::time_point<std::chrono::steady_clock> m_timePoint;
//        std::vector<AMUIFormElement *> m_elements;
//        std::vector<_AMUIDialogEnumMapLineItem<TEnumType, TStringType> > m_elementsStorCommon;
//        std::vector<_AMUIDialogEnumMapLineItemEx<TEnumType, TStringType> > m_elementsStorExtended;
//        std::string m_strVoidParam;
//    };
//
//} // AMDialogs
//
//#include "src/AMUIDialogEnumMapLine.hpp"
//
//#endif //SAW_AMUIDIALOGENUMMAPLINE_H
