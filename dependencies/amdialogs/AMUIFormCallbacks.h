//
// Created by <PERSON><PERSON><PERSON> on 15.9.22.
//

#ifndef SAW_ALL_AMUIFORMPARSERS_H
#define SAW_ALL_AMUIFORMPARSERS_H

//#include <memory>
//#include <sstream>
//#include <string>
//#include <langinfo.h>
//#include <ctime>
#include "imgui/imgui.h"
//#include "AMUIFormElements.h"
//#include "AMUIParsers.h"
//#include "../vendor/include/AMCore/AMInteger.h"


namespace AMDialogs {

    int AMUIInputCallbackDatetime(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackDatetimeLostFocus(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackString(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackStringNullable(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackInteger(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackFloat(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackNatural(::ImGuiInputTextCallbackData *data);

    int AMUIInputCallbackEmpty(::ImGuiInputTextCallbackData *data);

    template<typename TStringType>
    int AMUIInputCallbackAuthorTag(::ImGuiInputTextCallbackData *data);

    template<typename TEnumType, typename TStringType>
    int AMUIInputCallbackName(::ImGuiInputTextCallbackData *data);

    template<typename TStringType>
    int AMUIInputCallbackAddress(::ImGuiInputTextCallbackData *data);

    template<typename TEnumType, typename TStringType>
    int AMUIInputCallbackEnumMap(::ImGuiInputTextCallbackData *data);
}

#include "AMUIFormElementAuthorTag.h"
#include "AMUIFormElementAddress.h"
#include "AMUIFormElementName.h"
#include "AMUIFormElementEnumMap.h"
#include "amuitable/AMUIFormElementEnumBig.h"

namespace AMDialogs {
    template<typename TStringType>
    int AMUIInputCallbackAuthorTag(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementAuthorTag<TStringType> *el = dynamic_cast<AMUIFormElementAuthorTag<TStringType> *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }

    template<typename TEnumType, typename TStringType>
    int AMUIInputCallbackName(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementName<TEnumType, TStringType> *el
            = dynamic_cast<AMUIFormElementName<TEnumType, TStringType> *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }

    template<typename TStringType>
    int AMUIInputCallbackAddress(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementAddress<TStringType> *el = dynamic_cast<AMUIFormElementAddress<TStringType> *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }

    template<typename TEnumType, typename TStringType>
    int AMUIInputCallbackEnumMap(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementEnumMap<TEnumType, TStringType> *el = dynamic_cast<AMUIFormElementEnumMap<TEnumType, TStringType> *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }
}

#endif //SAW_ALL_AMUIFORMPARSERS_H
