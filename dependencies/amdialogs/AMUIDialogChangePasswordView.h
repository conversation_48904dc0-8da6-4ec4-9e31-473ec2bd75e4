//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGCHANGEPASSWORDVIEW_H
#define SAW_AMUIDIALOGCHANGEPASSWORDVIEW_H
#define IMGUI_DEFINE_MATH_OPERATORS
#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogChangePasswordView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogChangePasswordView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogChangePasswordView.hpp"

#endif //SAW_AMUIDIALOGCHANGEPASSWORDVIEW_H
