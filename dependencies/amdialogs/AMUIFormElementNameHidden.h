//
// Created by <PERSON><PERSON><PERSON> on 10.6.23.
//

#ifndef SAW_AMUIFORMELEMENTNAMEHIDDEN_H
#define SAW_AMUIFORMELEMENTNAMEHIDDEN_H

#include "AMUIFormElementName.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIForm<PERSON>lementNameHidden : public AMUIFormElementName<TEnumType, TStringType> {
    public:
        AMUIFormElementNameHidden(const char *key, bool mandatory = false, bool nullable = false, bool hidden = false)
            : AMUIFormElementName<TEnumType, TStringType>(key, mandatory, nullable, hidden)
        {};
        void render(bool formGrayed, int element_id) override
        {
        }
    };
}


#endif //SAW_AMUIFORMELEMENTNAMEHIDDEN_H
