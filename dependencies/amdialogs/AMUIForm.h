//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORM_H
#define SAW_ALL_AMUIFORM_H

#include <vector>
#include <set>
#include "amui/AMFuture.h"
//#include "nlohmann/json.hpp"
#include "amcore/AMDataType.h"
#include "AMUIFormElements.h"
#include "amui/AMUIGarbageManager.h"


namespace AMUI {
    class AMUIController;
}

namespace AMDialogs {

    struct _AMUIFormRequest;

    enum ERowUpdateMode{
        UPDATE,
        INSERT,
        DELETE,
        INVALID_OPERATION
    };

class AMUIForm: public AMUI::AMUIGarbageItem {
    public:
        //Init destroy
        AMUIForm();

        virtual ~AMUIForm();

        void init(AMUI::AMUIController *controller, std::string endpoint);

        //compulsory for inherited
        virtual int sections() = 0;

        virtual std::vector<AMUIFormElement *> &elementsForSection(int n) = 0;
        AMUIFormElement *findElement(std::string key);



        //function for inherited

        std::string saveToPost(bool all);

        virtual void clearModified();

        virtual bool grayed();

        virtual void reset();

        virtual void loadDefaults();

        virtual void perform();

        //callbacks for controller
        virtual void onInsert();

        virtual void onSave(std::function<void()> onDone = std::function<void()>());

        virtual void onDelete(std::function<void()> onDone = std::function<void()>());

        virtual void onCopy();

        virtual void onLoad(int id, std::function<void()> onDone = std::function<void()>());

        virtual void onRefresh(int id, std::function<void()> onDone = std::function<void()>());

        virtual bool canInsert() const;

        virtual bool canSave() const;

        virtual bool canDelete() const;

        virtual bool canCopy() const;

        virtual bool modified() const;

        virtual bool canSendKey(std::string key, bool all);

        //callback for inherited

        virtual std::string insertRecordUrl(bool &important, std::string &post);

        virtual std::string updateRecordUrl(bool &important, std::string &post);

        virtual std::string loadRecordUrl(int id, bool &important, std::string &post);

        virtual std::string deleteRecordUrl(bool &important, std::string &post);

        virtual bool acceptInserted(std::tuple<std::unique_ptr<char>, int>);

        virtual bool acceptUpdated(std::tuple<std::unique_ptr<char>, int>);

        virtual bool acceptLoaded(std::tuple<std::unique_ptr<char>, int>);

        virtual bool acceptDeleted(std::tuple<std::unique_ptr<char>, int>);

        virtual std::string recordTitle();

        virtual std::tuple<std::unique_ptr<char>, int> getData(void *mem);

        virtual void *putData(std::string url, bool important, std::string post, std::string method);

        virtual bool isDataAvail(void *);

        virtual int loadingId();

        virtual void onChange();
        virtual void onUpdateState();

        virtual void setGrayed(bool  grayed);
        virtual void setForceGrayed(bool grayed);
        virtual void setFlashbackTimepoint(std::string flashbackTimepoint);

        virtual std::map<int, std::string> *additionalKeys(std::string key);

        bool isNew();



        bool canDestroyFinish() override;

        void destroy();

        int recordId();

        void setIsNew(bool isNew);

        void setCaller(AMUIForm* caller);

        virtual void onRenderStart();
        virtual void onRenderEnd();

        void printInfoLine(const char* msg, GLuint icon);
protected:
        void acceptRecords();


        AMUI::AMUIController *m_controller;
        std::string m_endpoint;
        AMUIFormElementInteger *m_idElement;
        bool m_isNew;
        bool m_modified;
        std::set<_AMUIFormRequest> m_requests;
        int m_timeout;
        int m_loadingId;
        bool m_grayed;
        bool m_destroyed;
        AMUIForm *m_caller;
        bool m_forceGrayed;
        bool m_forceRefreshAfterLoad;
    };


    struct _AMUIFormRequest {
        std::chrono::time_point<std::chrono::steady_clock> deadline;
        mutable AMUI::AMFuture <std::tuple<std::unique_ptr<char>, int>> future;

        //void (AMUIForm::*callback)(std::tuple<std::unique_ptr<char>, int>);
        std::function<bool(std::tuple<std::unique_ptr<char>, int>)> callback;

        ERowUpdateMode mode;

        std::function<void()> customCallback;

        _AMUIFormRequest(
            std::chrono::time_point<std::chrono::steady_clock> deadline,
            AMUI::AMFuture <std::tuple<std::unique_ptr<char>, int>> &&future,
            //void (AMUIForm::*callback)(std::tuple<std::unique_ptr<char>, int>),
            std::function<bool(std::tuple<std::unique_ptr<char>, int>)> callback,
            ERowUpdateMode mode,
            std::function<void()> customCallback
        );

        _AMUIFormRequest(_AMUIFormRequest &&right);

        bool operator<(const _AMUIFormRequest &right) const;

        ~_AMUIFormRequest();
    };

}
#endif //SAW_ALL_AMUIFORM_H
