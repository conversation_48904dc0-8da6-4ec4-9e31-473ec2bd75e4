cmake_minimum_required(VERSION 3.12)

set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "build")

# set the project name and version
project(AMDialogs VERSION 201.0)
file(GLOB SOURCES "src/*.cpp")

# specify the C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

include(../3rdparty/DownloadProject/DownloadProject.cmake)
download_project(
        PROJ                googletest
        GIT_REPOSITORY      https://github.com/google/googletest.git
        GIT_TAG             main
        UPDATE_DISCONNECTED 1
)

add_subdirectory(${googletest_SOURCE_DIR} ${googletest_BINARY_DIR} EXCLUDE_FROM_ALL)

message(STATUS "foo googletest_SOURCE_DIR dir: ${googletest_SOURCE_DIR}")
message(STATUS "foo googletest_BINARY_DIR dir: ${googletest_BINARY_DIR}")
message(STATUS "foo CMAKE_CURRENT_BINARY_DIR dir: ${CMAKE_CURRENT_BINARY_DIR}")
#include the googlemock/include dir
include_directories(${googletest_SOURCE_DIR}/googlemock/include)

include(FindPkgConfig)
find_package(OpenGL REQUIRED)
pkg_search_module(SDL2 REQUIRED sdl2)
pkg_search_module(SDL2IMAGE REQUIRED SDL2_image>=2.0.0)
include_directories(${OPENGL_INCLUDE_DIRS} ${SDL2_INCLUDE_DIRS})
if(DEBUG MATCHES yes)
    set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -DDEBUG ")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DDEBUG ")
endif()

include_directories(../ ../3rdparty/ ../../3rdparty/)
#set(CMAKE_CXX_FLAGS --coverage)
#set(CMAKE_CXX_FLAGS -fexceptions)s

add_library(AMDialogs SHARED
        src/AMUIForm.cpp
        src/AMUIFormElement.cpp
        src/AMUIFormElementBool.hpp
        src/AMUIFormElementDatetime.cpp
        src/AMUIFormElementInteger.cpp
        src/AMUIFormElementString.cpp
        src/AMUIFormElementStringNullable.cpp
        src/AMUIFormCallbacks.cpp
        src/AMUIFormConfig.cpp
        src/AMUIFormView.cpp
        src/AMUIParsers.cpp
        )
set_target_properties(AMDialogs
    PROPERTIES
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)


add_executable(TEST_AMParsers test/Parsers/test_AMParsers.cpp src/AMUIParsers.cpp ../amcore/src/AMNullable.cpp ../amcore/src/AMTextUtils.cpp ../amcore/src/AMNameBase.cpp test/Parsers/test_twowaytable.hpp)
target_link_libraries(TEST_AMParsers gtest pthread)
##target_link_libraries(TEST_AMParsers GTest::gtest GTest::gtest_main pthread)
add_custom_target(Tests_10 ALL COMMAND TEST_AMParsers)

# first we can indicate the documentation build as an option and set it to ON by default
option(BUILD_DOC "Build documentation" OFF)
# check if Doxygen is installed
find_package(Doxygen)
if (DOXYGEN_FOUND)
    # set input and output files
    set(DOXYGEN_IN  ${CMAKE_CURRENT_SOURCE_DIR}/docs/doxyfile)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/html/doxyfile)

    # request to configure the file
    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
    message("Doxygen build started")

    # note the option ALL which allows to build the docs together with the application
    add_custom_target( doc_doxygen ALL
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/..
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM )
else (DOXYGEN_FOUND)
  message("Doxygen need to be installed to generate the doxygen documentation")
endif (DOXYGEN_FOUND)
