//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTNAME_H
#define SAW_ALL_AMUIFORMELEMENTNAME_H

#include <cassert>
#include <string>
#include "AMUIFormElement.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIController.h"


namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogName;
    class AMUIDialog;

    template<typename TEnumType, typename TStringType>
    class AMUIFormElementName : public AMUIFormElement {
    public:
        AMUIFormElementName(const char *key, bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementName();

        void init(const std::string &strVoidParam, AMUI::AMUIController *controller = nullptr);

        void reset() override;

        void updateState() override;

        [[nodiscard]] int length() const override { return m_length + 1; }

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_name; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        void setValue(const char *value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return "";};
        std::string valueAsString(std::map<int, std::string> *keys) override;
        void setValueString(std::string value) override {setValue(value.c_str());};

        void onDialogChange(AMUIDialog*);
        void onDialogClose(AMUIDialog*);

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}

        void setFlashbackTimepoint(std::string flashbackTimepoint) override;

        std::string flashbackTimepoint() {return m_flashbackTimepoint;};

        void loadDefault() override;

    protected:
        char *m_buffer;
        GLuint m_icon;
        AMUIDialogName<TEnumType, TStringType> *m_dialog;
        char m_wildcard;
        int m_preventRefreshDialog;
        AMUI::AMUIController *m_controller;
        std::string m_strVoidParam;
        std::string m_flashbackTimepoint;
    };

}

#include "src/AMUIFormElementName.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTNAME_H
