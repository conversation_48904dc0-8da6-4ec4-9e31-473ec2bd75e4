//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGVIEW_H
#define SAW_AMUIDIALOGVIEW_H

#include "amui/AMUIIView.h"
#include "imgui/imgui.h"


namespace AMDialogs {

    class AMUIDialog;

    class AMUIDialogView: public AMUI::AMUIIView {
    public:
        AMUIDialogView();

        bool renderBegin();
        void render() override;
        void renderEnd(bool beginSuccessful);

        void init(AMUIDialog *dialog);

        void setOrigin(ImVec2 &origin);
        void setSize(ImVec2 &size);
        bool focused();

    protected:
        AMUIDialog *m_dialog;
        ImVec2 m_origin;
        ImVec2 m_size;
        ImVec2 m_originLast;
        //ImVec2 m_sizeLast;
        char m_name[168];
        bool m_focused;
        bool m_windowIsOpened;
        bool m_starting;
    };

} // AMDialogs

#endif //SAW_AMUIDIALOGVIEW_H
