//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGEXPRESSIONVIEW_H
#define SAW_AMUIDIALOGEXPRESSIONVIEW_H

#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogExpressionView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogExpressionView();
        void render() override;
        virtual bool renderNullable();
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogExpressionView.hpp"

#endif //SAW_AMUIDIALOGEXPRESSIONVIEW_H
