//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGLOGINVIEW_H
#define SAW_AMUIDIALOGLOGINVIEW_H
#define IMGUI_DEFINE_MATH_OPERATORS
#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogLoginView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogLoginView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogLoginView.hpp"

#endif //SAW_AMUIDIALOGLOGINVIEW_H
