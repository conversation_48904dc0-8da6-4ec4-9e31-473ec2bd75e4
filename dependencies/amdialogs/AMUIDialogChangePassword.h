//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGCHANGEPASSWORD_H
#define SAW_AMUIDIALOGCHANGEPASSWORD_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementString.h"
#include "AMUIFormElementPassword.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIRect.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogChangePassword:public  AMUIDialog {
    public:
        AMUIDialogChangePassword();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, bool forceChange);
        std::vector<AMUIFormElement *> &elements() override;
       // AMUIFormElementString &userName() {return m_userName;};
        AMUIFormElementPassword &userPassword() {return  m_password;};
        AMUIFormElementPassword &userNewPassword1() {return  m_newPassword1;};
        AMUIFormElementPassword &userNewPassword2() {return  m_newPassword2;};
        //GLuint icon() {return m_icon;};
        //AMUI::AMUIRect<float> iconRect() {return m_iconRect;}
        //void setIcon(GLuint icon, AMUI::AMUIRect<float> iconRect);
        //void setErrorText(std::string text);
        std::string text() {return m_text;};

        void onChange() override;

        void onClose() override;

        bool forceChange() {return m_forceChange;};

    protected:
      //  AMUIFormElementString m_userName;
        AMUIFormElementPassword m_password;
        AMUIFormElementPassword m_newPassword1;
        AMUIFormElementPassword m_newPassword2;

        std::vector<AMUIFormElement *> m_elements;

        //GLuint m_icon;
        //AMUI::AMUIRect<float> m_iconRect;
        std::string m_text;
        bool m_forceChange;
    };

} // AMDialogs

#include "src/AMUIDialogChangePassword.hpp"

#endif //SAW_AMUIDIALOGCHANGEPASSWORD_H
