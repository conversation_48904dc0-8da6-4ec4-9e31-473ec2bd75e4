//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGAUTHORTAG_H
#define SAW_AMUIDIALOGAUTHORTAG_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementString.h"
#include "AMUIFormElementDatetime.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAuthorTag:public  AMUIDialog {
    public:
        AMUIDialogAuthorTag();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, char wildcard);
        std::vector<AMUIFormElement *> &elements() override;
        AMUIFormElementDatetime &datetime();
        AMUIFormElementString &author();

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}
    protected:
        AMUIFormElementDatetime m_datetime;
        AMUIFormElementString m_author;
        std::vector<AMUIFormElement *> m_elements;
        char m_wildcard;
    };

} // AMDialogs

#include "src/AMUIDialogAuthorTag.hpp"

#endif //SAW_AMUIDIALOGAUTHORTAG_H
