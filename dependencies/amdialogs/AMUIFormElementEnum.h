//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTENUM_H
#define SAW_ALL_AMUIFORMELEMENTENUM_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include <ctime>
#include <functional>
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIController.h"
#include "amui/AMUIConfig.h"

namespace AMDialogs {

    //class AMUIDialogDatetime;
    //class AMUIDialog;

    template<typename TEnumType, typename TStringType>
    class AMUIFormElementEnum : public AMUIFormElement {
    public:
        explicit AMUIFormElementEnum(const std::string key = "", bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementEnum() override;

        virtual void init(int enumId, const std::string &strVoidParam);

        void reset() override;

        void updateState() override;

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_enum; };

        inline TEnumType value() const;

        virtual void setValue(TEnumType value);
        virtual void setValue();

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override;
        void setValueString(std::string value) override;
        TEnumType valueStored() {return m_valueValue;}

        virtual void onControllerClose(AMUI::AMUIController *controller);

        void setController(std::function<AMUI::AMUIController *()> controllerFactory, AMUI::AMUIController *parentController);

        void load();

        bool focused();

        bool needUpdateState() override;

        void setState(AMUIWidgetState state) override;

        void setFlashbackTimepoint(std::string flashbackTimepoint) override;

        virtual void setValue(TEnumType value, TStringType svalue) {};

        bool isNull() override;

        void setNull(bool _null) override;

        void loadDefault() override;



    protected:
        virtual void drawIcon(int n) {};
        virtual bool drawIconFE(int n, unsigned int color) {return false;};
        int m_valueIndex;
        TEnumType m_valueValue;
        int m_enumId;
        GLuint m_icon;
        bool m_focused;
        bool m_updateFromDialog;
        std::vector<std::string> m_titles;
        std::vector<TEnumType> m_ids;
        std::vector<std::string> m_tooltips;
        std::chrono::time_point<std::chrono::steady_clock> m_tableTime;
        std::function<AMUI::AMUIController *()> m_controllerFactory;
        AMUI::AMUIController *m_controller;
        AMUI::AMUIController *m_parentController;
        bool m_parentMaximized;
        std::string m_strVoidParam;
        std::string m_flashbackTimepoint;
    };



}

#include "src/AMUIFormElementEnum.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTENUM_H
