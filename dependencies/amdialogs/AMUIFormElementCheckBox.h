//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTCHECKBOX_H
#define SAW_ALL_AMUIFORMELEMENTCHECKBOX_H

#include <cassert>
#include <string>
#include "AMUIFormElement.h"
#include "amui/AMUIIView.h"

namespace AMDialogs {


    template<typename TStringType>
    class AMUIFormElementCheckBox: public AMUIFormElement {
    public:
        AMUIFormElementCheckBox();
        AMUIFormElementCheckBox(const char *key);

        ~AMUIFormElementCheckBox();

        void init(TStringType label);

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_bool; };

        inline bool *valuePtr() { return &m_value; }

        inline bool value() const { return m_value; }

        inline TStringType label() const { return m_label; }

        void setValue(bool value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return m_value ? "true" : "false";};
        void setValueString(std::string value) override {setValue((bool)std::stoi(value));};

        void loadDefault() override;
    protected:
        bool m_value;
        TStringType m_labelOnRight;
    };

}

#include "src/AMUIFormElementCheckBox.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTCHECKBOX_H
