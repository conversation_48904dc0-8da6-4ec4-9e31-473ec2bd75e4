//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTPASSWORD_H
#define SAW_ALL_AMUIFORMELEMENTPASSWORD_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElementString.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"


namespace AMDialogs {

    class AMUIFormElementPassword : public AMUIFormElementString {
    public:
        AMUIFormElementPassword(const char *key);

        ~AMUIFormElementPassword();

        void render(bool formGrayed, int element_id) override;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTPASSWORD_H
