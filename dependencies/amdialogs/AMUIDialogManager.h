//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGMANAGER_H
#define SAW_AMUIDIALOGMANAGER_H

#include <queue>
#include "amdialogs/AMUIDialog.h"

namespace AMDialogs {

    class AMUIDialogManager {
    public:
        static AMUIDialogManager &instance();
        void deleteDialog(AMUIDialog *dlg);
        void perform();
    protected:
        AMUIDialogManager();
        std::queue<AMUIDialog *> m_queue;
        static AMUIDialogManager *m_instance;
    };

} // AMDialogs

#endif //SAW_AMUIDIALOGMANAGER_H
