//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTENUMIMAGE_H
#define SAW_ALL_AMUIFORMELEMENTENUMIMAGE_H

#include "AMUIFormElementEnum.h"
#include <cassert>
#include <ctime>
#include <functional>
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIController.h"
#include "amui/AMUIConfig.h"

namespace AMDialogs {

    //class AMUIDialogDatetime;
    //class AMUIDialog;

    template<typename TEnumType, typename TStringType>
    class AMUIFormElementEnumImage : public AMUIFormElementEnum<TEnumType, TStringType> {
    public:
        explicit AMUIFormElementEnumImage(const std::string key = "", bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementEnumImage() override;

        void init(int enumId, const std::string &strVoidParam) override;

/*
        struct AMUIFormElementEnumImageDef
        {
            const unsigned char *data;
            const int len;
            AMEliteUI::AMEliteImageManagerImage *img;
            AMUIFormElementEnumImageDef(const unsigned char *_data, const int _len, AMEliteUI::AMEliteImageManagerImage *_img)
            : data(_data),
              len(_len),
              img(_img)
            {};
        };
        void initImages(std::vector<AMUIFormElementEnumImageDef>);
        void setImages(std::vector<GLuint>*);*/

    protected:
        void drawIcon(int n) override;
        bool drawIconFE(int n, unsigned int color) override;



/*

        void reset() override;

        void updateState() override;

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_enum; };

        inline TEnumType value() const;

        virtual void setValue(TEnumType value);
        virtual void setValue();
*/
       // void render(bool formGrayed, int element_id) override;
/*
        std::string valueAsString() override;
        void setValueString(std::string value) override;
        TEnumType valueStored() {return m_valueValue;}

        virtual void onControllerClose(AMUI::AMUIController *controller);

        void setController(std::function<AMUI::AMUIController *()> controllerFactory, AMUI::AMUIController *parentController);

        void load();

        bool focused();

        bool needUpdateState() override;

        void setState(AMUIWidgetState state) override;

        void setFlashbackTimepoint(std::string flashbackTimepoint) override;

        virtual void setValue(TEnumType value, TStringType svalue) {};

        bool isNull() override;

        void setNull(bool _null) override;

        void loadDefault() override;*/

    protected:

        std::vector<GLuint> *m_icons;
        //bool m_iconsForeign;
    };




}

#include "src/AMUIFormElementEnumImage.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTENUMIMAGE_H
