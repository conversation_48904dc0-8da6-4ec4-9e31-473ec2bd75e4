//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTENUMMAP_H
#define SAW_ALL_AMUIFORMELEMENTENUMMAP_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include <ctime>
#include <functional>
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIController.h"
#include "amui/AMUIConfig.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogEnumLine;

    template<typename TEnumType, typename TStringType>
    class AMUIFormElementEnumMap : public AMUIFormElement {
    public:
        explicit AMUIFormElementEnumMap(const std::string key = "", bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementEnumMap() override;

        virtual void init(int enumId, const std::string &strVoidParam);

        void reset() override;

        void updateState() override;

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_enum_map; };

        inline std::map<TEnumType, int> &value() const;

        void setValue(std::map<TEnumType, int> &value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override;
        void setValueString(std::string value) override;

        //void onControllerClose(AMUI::AMUIController *controller);

        //void setController(std::function<AMUI::AMUIController *()> controllerFactory, AMUI::AMUIController *parentController);

        void load();

        bool focused();

        bool needUpdateState() override;

        void setState(AMUIWidgetState state) override;

        void onDialogChange(AMUIDialog *dlg);
        void onDialogClose(AMUIDialog *dlg);

        void updateValue();

        void setLines(int lines);

        void setFlashbackTimepoint(std::string flashbackTimepoint) override;

        void loadDefault() override;

    protected:

        virtual void drawIcon() {};
        virtual bool drawIconTest() {return false;};

        std::map<TEnumType, int> m_valueInt;
        char *m_buffer;
        int m_lines;
        std::map<TEnumType, int> m_valueValue;
        int m_enumId;
        GLuint m_icon;
        bool m_focused;
        bool m_updateFromDialog;
        //std::vector<std::string> m_titles;
        //std::vector<TEnumType> m_ids;
        std::chrono::time_point<std::chrono::steady_clock> m_tableTime;
        //std::function<AMUI::AMUIController *()> m_controllerFactory;
        //AMUI::AMUIController *m_controller;
        //AMUI::AMUIController *m_parentController;
        //bool m_parentMaximized;
        std::string m_strVoidParam;
        AMUIDialogEnumLine<TEnumType, TStringType> *m_dialog;
        int m_preventRefreshDialog;
        std::string m_flashbackTimepoint;
    };

}

#include "src/AMUIFormElementEnumMap.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTENUM_H
