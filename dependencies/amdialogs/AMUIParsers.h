//
// Created by <PERSON><PERSON><PERSON> on 11.10.22.
//

#ifndef AMCORE_AMUIPARSERS_H
#define AMCORE_AMUIPARSERS_H


#include <memory>
#include <sstream>
#include <string>
#include <ctime>
#include <map>
#include <vector>
#include <memory>
#include "amcore/AMInteger.h"
#include "amcore/AMNullable.h"
#include "amcore/AMDataType.h"
#include "amcore/AMTextUtils.h"
#include "amdialogs/AMUINameFlashback.h"
#include "amcore/AMAddress.h"
#include "amcore/AMAuthorTag.h"
#include "amcore/AMTwoWayTable.h"

namespace AMDialogs {

    extern const char andText[32];
    extern const char orText[32];
    extern const char datetimeFormatText[32];
    extern const char datetimeusFormatText[32];
    extern const char dateFormatText[32];
    extern const char timeFormatText[32];

    enum AMUIParseResult {
        FAIL = 0,
        PARTIAL = 1,
        SUCCESS = 2
    };

    template<typename TReturnType>
    AMUIParseResult AMUIParseNatural(
        std::istream &is,
        TReturnType *rMin,
        TReturnType *rMax = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '\0',
        int *digitsRead = nullptr
            );

    template<typename TReturnType>
    AMUIParseResult AMUIParseNatural(
        std::istream &is,
        AMCore::AMNullable<TReturnType> *rMin,
        AMCore::AMNullable<TReturnType> *rMax = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '\0',
        int *digitsRead = nullptr
    );

    extern template AMUIParseResult AMUIParseNatural(std::istream &, int *, int *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<int> *, AMCore::AMNullable<int> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned int *, unsigned int *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned int> *, AMCore::AMNullable<unsigned int> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, short *, short *rMax, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<short> *, AMCore::AMNullable<short> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned short *, unsigned short *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned short> *, AMCore::AMNullable<unsigned short> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, long *, long *rMax, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<long> *, AMCore::AMNullable<long> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long *, unsigned long *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned long> *, AMCore::AMNullable<unsigned long> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, long long *, long long *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<long long> *, AMCore::AMNullable<long long> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long long *, unsigned long long *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned long long> *, AMCore::AMNullable<unsigned long long> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, char *, char *rMax, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<char> *, AMCore::AMNullable<char> *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned char *, unsigned char *, std::ostream *, char, int *);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned char> *, AMCore::AMNullable<unsigned char> *, std::ostream *, char, int *);

    /*
    extern template AMUIParseResult AMUIParseNatural(std::istream &, int *, int * rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<int> *rMin, AMCore::AMNullable<int> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned int *, unsigned int *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned int> *rMin, AMCore::AMNullable<unsigned int> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, short *, short *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<short> *rMin, AMCore::AMNullable<short> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned short *, unsigned short *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned short> *rMin, AMCore::AMNullable<unsigned short> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, long *, long *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<long> *rMin, AMCore::AMNullable<long> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long *, unsigned long *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned long> *rMin, AMCore::AMNullable<unsigned long> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, long long *, long long *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<long long> *rMin, AMCore::AMNullable<long long> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long long *, unsigned long long *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned long long> *rMin, AMCore::AMNullable<unsigned long long> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, char *, char*rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<char> *rMin, AMCore::AMNullable<char> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &, unsigned char *, unsigned char *rMax = nullptr, std::ostream * os =  = nullptr, char wildcard = '\0', int * digitsRead = nullptr);
    extern template AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<unsigned char> *rMin, AMCore::AMNullable<unsigned char> *rMax = nullptr, std::ostream *os = nullptr, char wildcard = '\0', int *digitsRead = nullptr);
*/

    template<typename TReturnType>
    AMUIParseResult AMUIParseInteger(
        std::istream &is,
        TReturnType *rMin,
        TReturnType *rMax = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '\0'
            );

    template<typename TReturnType>
    AMUIParseResult AMUIParseInteger(
        std::istream &is,
        AMCore::AMNullable<TReturnType> *rMin,
        AMCore::AMNullable<TReturnType> *rMax = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '\0'
    );

    extern template AMUIParseResult AMUIParseInteger(std::istream &, int *, int *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<int> *, AMCore::AMNullable<int> *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, short *, short *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<short> *, AMCore::AMNullable<short> *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, long *, long *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<long> *, AMCore::AMNullable<long> *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, long long *, long long *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<long long> *, AMCore::AMNullable<long long> *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, char *, char *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<char> *, AMCore::AMNullable<char> *, std::ostream *, char);

    template<typename TReturnType>
    AMUIParseResult AMUIParseFloat(
        std::istream &is,
        TReturnType *rMin,
        TReturnType *rMax,
        std::ostream *os = nullptr,
        char wildcard = '\0'
    );

    template<typename TReturnType>
    AMUIParseResult AMUIParseFloat(
        std::istream &is,
        AMCore::AMNullable<TReturnType> *rMin,
        AMCore::AMNullable<TReturnType> *rMax,
        std::ostream *os = nullptr,
        char wildcard = '\0'
    );

    extern template AMUIParseResult AMUIParseFloat(std::istream &, float *, float *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseFloat(std::istream &, AMCore::AMNullable<float> *, AMCore::AMNullable<float> *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseFloat(std::istream &, double *, double *, std::ostream *, char);
    extern template AMUIParseResult AMUIParseFloat(std::istream &, AMCore::AMNullable<double> *, AMCore::AMNullable<double> *, std::ostream *, char);

    AMUIParseResult AMUIParseDatetime(
        std::istream &is,
        std::tm *rMin,
        std::tm *rMax = nullptr,
        std::ostream *os = nullptr,
        std::string format = std::string(datetimeFormatText),
        char wildcard = '\0'
        );

    AMUIParseResult AMUIParseDatetime(
        std::istream &is,
        AMCore::AMNullable<std::tm> *rMin,
        AMCore::AMNullable<std::tm> *rMax = nullptr,
        std::ostream *os = nullptr,
        std::string format = std::string(datetimeFormatText),
        char wildcard = '\0'
        );

    AMUIParseResult AMUIParseDatetime(
        std::istream &is,
        AMCore::AMDatetimeus *rMin,
        AMCore::AMDatetimeus *rMax,
        std::ostream *os,
        std::string format,
        char wildcard = '\0',
        bool dateShift = false
        );

    AMUIParseResult AMUIParseDatetime(
        std::istream &is,
        AMCore::AMNullable<AMCore::AMDatetimeus> *rMin,
        AMCore::AMNullable<AMCore::AMDatetimeus> *rMax,
        std::ostream *os,
        std::string format,
        char wildcard = '\0'
    );

    AMUIParseResult AMUIParseBool(
        std::istream &is,
        bool *r,
        std::string trueString,
        std::string falseString,
        std::ostream *os = nullptr
        );

    AMUIParseResult AMUIParseBool(
        std::istream &is,
        AMCore::AMNullable<bool> *r,
        std::string trueString,
        std::string falseString,
        std::ostream *os = nullptr
    );

    template<typename TStringType>
    AMUIParseResult AMUIParseString(
        std::istream &is,
        TStringType *r,
        bool *likePrPr = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '_',
        char wildcardMulti = '%',
        bool wildcardEnabled = true
        );

    template<typename TStringType>
    AMUIParseResult AMUIParseString(
        std::istream &is,
        AMCore::AMNullable<TStringType> *r,
        bool *likePrPr = nullptr,
        std::ostream *os = nullptr,
        char wildcard = '_',
        char wildcardMulti = '%',
        bool wildcardEnabled = true
    );



    AMUIParseResult AMUIParseNull(
        std::istream &is,
        bool *r,
        std::ostream *os = nullptr
    );

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnum(
            std::istream &is,
            TReturnType *r,
            const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
            std::ostream *os = nullptr
    );

    extern template AMUIParseResult AMUIParseEnum(std::istream &, int *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, std::ostream *);

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumStartsWith(
        TStringType &is,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        bool reverse = false,
        TStringType *os = nullptr
    );

    extern template AMUIParseResult AMUIParseEnumStartsWith(AMCore::AMStringCZ &, int *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, bool, AMCore::AMStringCZ*);


    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnum(
        std::istream &is,
        AMCore::AMNullable<TReturnType> *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        std::ostream *os = nullptr
    );

    extern template AMUIParseResult AMUIParseEnum(std::istream &, AMCore::AMNullable<int> *,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, std::ostream *);

    template<typename TReturnType, typename TStringType, typename TStringCompareType = AMCore::AMStringStripped>
    AMUIParseResult AMUIParseEnum(
        TStringType &input,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    );
    extern template AMUIParseResult AMUIParseEnum(AMCore::AMStringCZ &, int *,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);

    template<typename TReturnType, typename TStringType, typename TStringCompareType = AMCore::AMStringStripped>
    AMUIParseResult AMUIParseEnum(
        TStringType &input,
        AMCore::AMNullable<TReturnType> *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    );
    extern template AMUIParseResult AMUIParseEnum(AMCore::AMStringCZ &, AMCore::AMNullable<int> *,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);

    template<typename TReturnType, typename TStringType = std::string>
    AMUIParseResult AMUIParseEnumLine(
        TStringType &input,
        std::map<TReturnType, int>& r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        TStringType separator = ";"
    );
    extern template AMUIParseResult AMUIParseEnumLine(AMCore::AMStringCZ &, std::map<int, int> &,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, AMCore::AMStringCZ separator);

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumMapLine(
        TStringType &input,
        std::map<TReturnType, int>& rs,
        std::map<int, std::map<TReturnType, int> >& rv,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        TStringType separatorAnd = ";",
        TStringType separatorOr = ","
    );

    extern template AMUIParseResult AMUIParseEnumMapLine(AMCore::AMStringCZ &, std::map<int, int> &, std::map<int, std::map<int, int> >&, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table,
        AMCore::AMStringCZ, AMCore::AMStringCZ);

    template<typename TReturnType, typename TStringType = std::string>
    AMUIParseResult AMUIParseEnumLine(
        TStringType &input,
        std::map<TReturnType, int>& r,
        bool& null,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    );
    extern template AMUIParseResult AMUIParseEnumLine(AMCore::AMStringCZ &, std::map<int, int> &, bool &,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);


    template<typename TStringType>
    struct AMUIParseExpressionElement
    {

        enum class ComboId
        {
            EVEN,
            EVENNULL,
            EVENSTRING,
            EVENSTRINGNULL,
            ODD,
            ODDNULL,
            ODDSTRING,
            ODDSTRINGNULL,
            INVALID,
        };
        enum class Operations
        {
            NOTHING,
            BRACEOPEN,
            BRACECLOSED,
            EQUAL,
            NOTEQUAL,
            LIKE,
            NOTLIKE,
            LESS,
            MORE,
            LESSEQUAL,
            MOREEQUAL,
            ISNULL,
            NOTNULL,
            AND,
            OR,
            INVALID
        };
        static const ComboId comboIdsTab[2][(int)AMCore::AMDataType_void + 1];
        static const size_t inputSize = 32;

        int indent;
        ComboId comboId;
        Operations operation;
        TStringType inputValue;

        static bool isEven(ComboId combo)
        {
            return combo >= ComboId::EVEN && combo <= ComboId::EVENSTRINGNULL;
        }

        static bool isValue(Operations op)
        {
            return op >= Operations::EQUAL && op <= Operations::MOREEQUAL;
        }

        static ComboId getOdd(ComboId combo)
        {
            return (combo >= ComboId::EVEN && combo <= ComboId::EVENSTRINGNULL) ? (ComboId)((int)combo + (int)ComboId::ODD) : combo;
        }

        static ComboId getEven(ComboId combo)
        {
            return (combo >= ComboId::ODD && combo <= ComboId::ODDSTRINGNULL) ? (ComboId)((int)combo - (int)ComboId::ODD) : combo;
        }

        AMUIParseExpressionElement(int _indent, ComboId _comboId, Operations _state, TStringType _inputBuffer = ""):
            indent(_indent),
            comboId(_comboId),
            operation(_state),
            inputValue(_inputBuffer)
        {};

        inline bool operator<(const AMUIParseExpressionElement &right) const
        {
            if (indent < right.indent) {
                return true;
            } else if (indent == right.indent) {
                if (comboId < right.comboId) {
                    return true;
                } else if (comboId == right.comboId) {
                    if (operation < right.operation) {
                        return true;
                    } else if (operation == right.operation) {
                        if (inputValue < right.inputValue) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        inline bool operator==(const AMUIParseExpressionElement &right) const
        {
            if (indent == right.indent) {
                if (comboId == right.comboId) {
                    if (operation == right.operation) {
                        if (inputValue == right.inputValue) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    };

    template<typename TStringType>
    bool isExpressionType(AMCore::AMDataType type);


    template<typename TStringType>
    AMUIParseResult AMUIParseExpression(std::istream &_input, std::vector<AMUIParseExpressionElement<TStringType> > &_result, AMCore::AMDataType _type, int* errorFrom = nullptr, int* errorTo = nullptr, char wildcard = '_');
    template<typename TStringType>
    void AMUIUpdateExpression(std::vector<AMUIParseExpressionElement<TStringType> > &table, AMCore::AMDataType _type, int row, typename AMUIParseExpressionElement<TStringType>::Operations newState, const char* newValue = "");
    template<typename TStringType>
    void AMUIInsertExpression(std::vector<AMUIParseExpressionElement<TStringType> > &table, AMCore::AMDataType _type, int row);
    template<typename TStringType>
    void AMUIRepairExpression(std::vector<AMUIParseExpressionElement<TStringType> > &table, AMCore::AMDataType type);
    template<typename TStringType>
    bool AMUIIsValidExpression(std::vector<AMUIParseExpressionElement<TStringType> > &table);
    template<typename TStringType>
    TStringType AMUIParseDefaultValueForType(AMCore::AMDataType type);

    //AMUIParseResult AMUIParseName(std::string& input, AMCore::AMNameBase* r, char wildcard = '\0');
    template<typename TEnumType, typename TStringType, typename TReturnType>
    AMUIParseResult AMUIParseName(const TStringType& input, TReturnType/*AMCore::AMNameBase<TEnumType, TStringType>*/* r, char wildcard = '\0');

    template<typename TStringType>
    AMUIParseResult AMUIParseAuthorTag(TStringType& input, AMCore::AMAuthorTag<TStringType>* r, char wildcard = '\0');
    template<typename TStringType>
    AMUIParseResult AMUIParseAddress(TStringType& input, AMCore::AMAddress<TStringType>* r, char wildcard = '\0');

    void AMMktimeNoShift(std::tm *t);
    void AMMktimeWithShift(std::tm *t);

    std::set<int> si_from_string(std::string);
    template<typename TEnumType>
    std::map<TEnumType, int> em_from_string(std::string);

    template<typename TEnumType, typename TStringType>
    TStringType AMUISplitStringIntoEnumvalAndId(TStringType input, TEnumType *enumResult);
    //template<typename TEnumType, typename TStringType>
    //TStringType AMUISplitStringIntoEnumvalAndIdStripWildcardMulti(TStringType input, TEnumType *enumResult, char wildcardMulti = '%');

}

    bool operator==(const std::tm& left, const std::tm& right);
    bool operator<(const std::tm& left, const std::tm& right);
    bool operator==(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right);
    int yearsTo(const std::tm& left, const std::tm& right);
    std::tm subractTime(const std::tm& left, const std::tm& right);
    bool isDateZero(const std::tm& t);
#include "amdialogs/src/AMUIParsers.hpp"

#endif //AMCORE_AMUIPARSERS_H
