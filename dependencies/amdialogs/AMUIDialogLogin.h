//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGLOGIN_H
#define SAW_AMUIDIALOGLOGIN_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementString.h"
#include "AMUIFormElementPassword.h"
#include "amui/AMUIGLWrappers.h"
#include "amui/AMUIRect.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogLogin:public  AMUIDialog {
    public:
        AMUIDialogLogin();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, int unused);
        std::vector<AMUIFormElement *> &elements() override;
        AMUIFormElementString &userName() {return m_userName;};
        AMUIFormElementPassword &userPassword() {return  m_password;};
        GLuint icon() {return m_icon;};
        AMUI::AMUIRect<float> iconRect() {return m_iconRect;}
        void setIcon(GLuint icon, AMUI::AMUIRect<float> iconRect);
        void setErrorText(std::string text);
        std::string errorText();
    protected:
        AMUIFormElementString m_userName;
        AMUIFormElementPassword m_password;

        std::vector<AMUIFormElement *> m_elements;

        GLuint m_icon;
        AMUI::AMUIRect<float> m_iconRect;
        std::string m_errorText;
    };

} // AMDialogs

#include "src/AMUIDialogLogin.hpp"

#endif //SAW_AMUIDIALOGLOGIN_H
