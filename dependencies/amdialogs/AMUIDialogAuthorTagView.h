//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGAUTHORTAGVIEW_H
#define SAW_AMUIDIALOGAUTHORTAGVIEW_H

#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAuthorTagView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogAuthorTagView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogAuthorTagView.hpp"

#endif //SAW_AMUIDIALOGAUTHORTAGVIEW_H
