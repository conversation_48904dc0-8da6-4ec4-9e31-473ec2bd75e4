//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENT_H
#define SAW_ALL_AMUIFORMELEMENT_H

#include "amcore/AMDataType.h"
#include "AMUIWidgetState.h"

struct ImGuiInputTextCallbackData;

namespace AMDialogs {

    class AMUIFormElement {
    public:
        enum EAlign{
            LEFT,
            RIGHT,
            CENTER
        };

        enum ELastEnterBackspaceEvent {
            NONE,
            ENTER,
            BACKSPACE,
            DELETE,
            ARROW_UP,
            ARROW_DOWN
        };

        AMUIFormElement(const std::string key, bool mandatory = false, bool nullable = false, bool hidden = false);

        virtual ~AMUIFormElement() {};

        virtual void initBase(int length, const std::string label);

        virtual void reset() = 0;

        virtual AMCore::AMDataType type() const = 0;

        virtual void updateState();

        virtual bool modified() const {return m_modified; };

        virtual void clearModified() {
            m_modified = false;
            m_needUpdateState = true;
        };

        void wasModified();

        void wasHitEnter();

        void lostFocus();

        AMUIWidgetState state();

        virtual void setState(AMUIWidgetState state) { m_state = state; };

        inline std::string key() const { return m_key; };

        virtual int length() const { return m_length; };

        inline std::string label() const { return m_label; };
        inline void setLabel(std::string label) {m_label = label;}

        virtual bool needUpdateState()
        {
            if (m_enterCallback && m_lastKeyEvent != NONE) {
                m_enterCallback(m_enterCallbackParam, this, m_lastKeyEvent);
                m_lastKeyEvent = NONE;
            }
            return m_needUpdateState;
        };

        typedef int (*callbackType)(ImGuiInputTextCallbackData *data);

        inline callbackType callback() const { return m_callback; };

        virtual void render(bool formGrayed, int element_id) {};

        virtual std::string valueAsString() {return "";};
        virtual std::string valueAsString(std::map<int, std::string> *keys) {return (keys && keys->size() >= 1) ? keys->at(0) + std::string("=") + valueAsString() : "";};
        virtual void setValueString(std::string value) {};

        virtual void setFormGrayed(bool grayed) {};
        virtual void setFlashbackTimepoint(std::string flashbackTimepoint) {};

        EAlign align() {return m_align;};

        void setAlign(EAlign align) {m_align = align;};

        float sizeX() {return m_sizeX;};

        void setSizeX(float sizeX) {m_sizeX = sizeX;};

        bool changeRequestByEnter() {return m_changeRequestByEnter;};

        void setChangeRequestByEnter(bool changeRequestByEnter) {m_changeRequestByEnter = changeRequestByEnter;};

        void setEnterCallback(std::function<void(int, AMUIFormElement*, ELastEnterBackspaceEvent event)> callback, int param);

        int cursorPos() const {return m_cursorPos;}

        void setCursorPos(int cursorPos) {m_cursorPos = cursorPos;}

        void wasHitSpecialKey(ELastEnterBackspaceEvent ev);

        void activate();

        virtual void loadDefault();

        bool nullable() {return m_nullable;}
        void setNullable(bool nullable) {m_nullable = nullable;}
        virtual bool isNull() {return false;}
        virtual void setNull(bool _null) {}
        bool mandatory() {return m_mandatory;}
        void setMandatory(bool mandatory) {m_mandatory = mandatory;}
        bool hidden() {return m_hidden;}
        void setHidden(bool hidden) {m_hidden = hidden;}

        virtual void refresh() {}

    protected:
        bool m_changeRequestByEnter;
        bool m_modified;
        bool m_silentModified;
        AMUIWidgetState m_state;
        int m_length;
        std::string m_key;
        std::string m_label;
        callbackType m_callback;
        bool m_needUpdateState;
        //bool m_needUpdateStateLostFocus;
        float m_sizeX;
        EAlign m_align;
        std::function<void(int, AMUIFormElement*, ELastEnterBackspaceEvent event)> m_enterCallback;
        int m_enterCallbackParam;
        int m_cursorPos;
        ELastEnterBackspaceEvent m_lastKeyEvent;
        bool m_activateRequest;
        bool m_nullable;
        //bool m_null;
        bool m_mandatory;
        bool m_hidden;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENT_H
