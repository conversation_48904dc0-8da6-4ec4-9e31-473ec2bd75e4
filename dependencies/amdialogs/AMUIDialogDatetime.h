//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGDATETIME_H
#define SAW_AMUIDIALOGDATETIME_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementSelectBox.h"
#include "AMUIFormElementButton.h"
#include "AMUIFormElementInteger.h"
#include "AMUIFormElementRadios.h"

namespace AMDialogs {

    struct _AMUIDialogDatetimeBtn
    {
        int day;
        std::string label;
        AMDialogs::AMUIWidgetState state;
        _AMUIDialogDatetimeBtn()
            : day(0),
            label(),
            state(AMDialogs::AMUIWidgetState::INVALID)
        {
        }
    };

    class AMUIDialogDatetime:public  AMUIDialog {
    public:
        AMUIDialogDatetime();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, char wildcard, AMCore::AMDataType type);
        //std::vector<AMUIFormElement *> &elements() override;
        std::string datetime();
        void setDatetime(std::string datetime);

        void onButtonMonth(int id);

        AMUIFormElementInteger &year() {return m_year;};
        AMUIFormElementSelectBox &month() {return m_month;};
        AMUIFormElementButton &monthPlus() {return m_monthPlus;};
        AMUIFormElementButton &monthMinus() {return m_monthMinus;};
        std::vector<std::string> &wDayLabels() {return m_wdayLabels;}
        std::vector<_AMUIDialogDatetimeBtn> &days() {return m_days;}
        AMUIFormElementSelectBox &hours() {return m_hours;}
        AMUIFormElementSelectBox &mins() {return m_mins;}
        AMUIFormElementSelectBox &secs() {return m_secs;}
        AMUIFormElementInteger &usec() {return m_usec;};
        AMUIFormElementRadios &precision() {return m_precision;};
        int firstWeekday() {return m_firstWeekday;}
        int monthDays(int year, int month);
        bool showDate() {return m_showDate;};
        bool showTime() {return m_showTime;};
        bool showUsecs() {return m_showUsec;};
        //void setShowDate(bool showdate) {m_showDate = showdate;fillElements();};
        //void setShowTime(bool showTime) {m_showTime = showTime;fillElements();};
        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;fillElements();};
        std::vector<AMUIFormElement *> &elements() override {return m_elements;};
        std::string format() {return m_format;};
        void setFormat(std::string format) {m_format = format;};
        void onChange() override;
        void onDayButton(int newDay);
    protected:
        bool m_showDate;
        bool m_showTime;
        bool m_showUsec;
        char m_wildcard;
        int  m_firstWeekday;
        std::string m_format;

        AMUIFormElementInteger m_year;
        std::vector<std::string> m_monthsLabels;
        AMUIFormElementSelectBox m_month;
        AMUIFormElementButton m_monthMinus;
        AMUIFormElementButton m_monthPlus;
        std::vector<std::string> m_wdayLabels;
        std::vector<_AMUIDialogDatetimeBtn> m_days;
        int m_day;
        static const std::vector<std::string> c_hoursLabels;
        AMUIFormElementSelectBox m_hours;
        static const std::vector<std::string> c_minSecLabels;
        AMUIFormElementSelectBox m_mins;
        AMUIFormElementSelectBox m_secs;
        AMUIFormElementInteger m_usec;
        static const std::vector<std::string> c_precisionsLabels;
        AMUIFormElementRadios m_precision;
        std::vector<AMUIFormElement *> m_elements;

        void fillDays(AMCore::AMDatetimeus &);
        void recomputeDays();
        void fillElements();
    };

} // AMDialogs


#endif //SAW_AMUIDIALOGDATETIME_H
