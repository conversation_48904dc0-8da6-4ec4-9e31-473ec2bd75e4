//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTSELECTBOX_H
#define SAW_ALL_AMUIFORMELEMENTSELECTBOX_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include "amcore/AMDataType.h"

namespace AMDialogs {

    class AMUIFormElementSelectBox : public AMUIFormElement {
    public:
        AMUIFormElementSelectBox(const char *key);

        ~AMUIFormElementSelectBox();

        void init(const std::vector<std::string> &items);

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_integer; };

        inline const char *buffer() {
            return m_items->at(m_value).c_str();
        }

        inline int value() const { return m_value; }

        void setValue(int value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return std::to_string(m_value);};
        void setValueString(std::string value) override {setValue(std::stoi(value));};

        void loadDefault() override;

    protected:
        int m_value;
        const std::vector<std::string> *m_items;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTSELECTBOX_H
