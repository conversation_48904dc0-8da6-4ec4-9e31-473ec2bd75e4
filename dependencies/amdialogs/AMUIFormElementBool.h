//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTBOOL_H
#define SAW_ALL_AMUIFORMELEMENTBOOL_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElement.h"
#include "amui/AMUIIView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIFormElementBool : public AMUIFormElement {
    public:
        AMUIFormElementBool(const char *key);

        ~AMUIFormElementBool();

        void init(TStringType falseLabel, TStringType trueLabel);

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_bool; };

        inline int *valuePtr() { return &m_value; }

        inline int value() const { return m_value; }

        inline TStringType trueLabel() const { return m_trueLabel; }

        inline TStringType falseLabel() const { return m_falseLabel; }

        void setValue(bool value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return m_value ? "true" : "false";};
        void setValueString(std::string value) override {setValue(value == "true" ? true : false);};

        float sizeX() {return m_sizeX;};

        void setSizeX(float sizeX) {m_sizeX = sizeX;};

        void setState(AMUIWidgetState state) override;

        void loadDefault() override;
    protected:
        int m_value;
        TStringType m_trueLabel;
        TStringType m_falseLabel;
    };

    /*
    template<typename TStringType>
    class AMUIFormElementBoolView: public AMUI::AMUIIView
    {
    public:
        void init(AMUIFormElementBool<TStringType>* elementBool, AMUIWidgetState state, int element_no);
        void render() override;
    protected:
        AMUIFormElementBool<TStringType>* m_element;
        AMUIWidgetState m_state;
        int m_element_no;
    };*/

}

#include "src/AMUIFormElementBool.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTBOOL_H
