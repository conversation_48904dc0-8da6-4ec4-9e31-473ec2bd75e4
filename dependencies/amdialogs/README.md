# AMCore

Essentially library for writing good appliactions.

## Compiling

```bash

mkdir build

cd build

cmake ../

make
```

## Output Library

```bash
/build/bin/libAMCore.so
```

## Examples

```bash
; go to root of project
cd examples
make
```

## Single test (not necesary)

```bash
; go to root of project
cd build
mkdir tests
cd tests
mkdir SmartProperties
cd SmartProperties
cmake ../../../tests/SmartProperties
make
```
