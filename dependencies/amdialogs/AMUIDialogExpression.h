//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGEXPRESSION_H
#define SAW_AMUIDIALOGEXPRESSION_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElement.h"

namespace AMDialogs {

    class AMUIFormElementSelectBox;

    template<typename TStringType>
    class AMUIDialogExpression:public  AMUIDialog {
    public:
        AMUIDialogExpression();
        void init(
            std::string name,
            std::function<void(AMUIDialog *)>,
            std::function<void(AMUIDialog *)>,
            std::vector<AMUIParseExpressionElement<TStringType> > *expression,
            AMCore::AMDataType type
            );
        std::vector<AMUIFormElement *> &elements() override;
        std::vector<int> &tabs() {return m_tabs;};
        std::vector<bool> &isValues() {return m_isValues;};
        std::vector<const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations *> &backtable() {return m_elementsBacktable;};
        AMCore::AMDataType type() {return m_type;};

        virtual void update(std::vector<AMUIParseExpressionElement<TStringType> > *expression);
        void updateInt(int max, std::vector<AMUIParseExpressionElement<TStringType> > *expression);
        void updateSelectBox(AMUIFormElementSelectBox *sel, int index,std::vector<AMUIParseExpressionElement<TStringType> > *expression);

        void onButtonClicked(int buttonId);

        bool causedByButton() {return m_causedByButton;}

        typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations causedOperation() {return m_causedOperation;}

        const char *causedString() {return m_causedString;}

        int causedLine() {return m_causedByLine;};

        AMUIWidgetState causedState();

        void setCausedByButton(bool causedByButton) {m_causedByButton = causedByButton;};

        void setCausedOperation(typename AMUIParseExpressionElement<TStringType>::Operations causedOperation) {m_causedOperation = causedOperation;};

        void setCausedString(const char *causedString) {m_causedString = causedString;};

        void setCausedByLine(int line) { m_causedByLine = line;}

    protected:
        //std::vector<AMUIParseExpressionElement<TStringType> > *m_expression;
        AMCore::AMDataType m_type;
        std::vector<AMUIFormElement *> m_elements;
        std::vector<int> m_tabs;
        std::vector<bool> m_isValues;
        std::vector<const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations *> m_elementsBacktable;
        bool m_causedByButton;
        typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations m_causedOperation;
        const char *m_causedString;
        int m_causedByLine;
        //AMUIWidgetState m_causedState;
    };

} // AMDialogs

#include "src/AMUIDialogExpression.hpp"

#endif //SAW_AMUIDIALOGEXPRESSION_H
