//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGEXPRESSIONNULLABLE_H
#define SAW_AMUIDIALOGEXPRESSIONNULLABLE_H

#include "amdialogs/AMUIDialogExpression.h"
#include "amdialogs/AMUIFormElementCheckBox.h"

namespace AMDialogs {



    template<typename TStringType>
    class AMUIDialogExpressionNullable:public  AMUIDialogExpression<TStringType> {
    public:
        AMUIDialogExpressionNullable();
        void init(
            std::string name,
            std::function<void(AMUIDialog *)>,
            std::function<void(AMUIDialog *)>,
            std::vector<AMUIParseExpressionElement<TStringType> > *expression,
            AMCore::AMDataType type,
            bool *nullable
        );
        bool isNull() {return *m_nullable;}
        void setNull(bool newNull) {if (m_nullable) *m_nullable = newNull;};
        AMUIFormElement<PERSON>heckBox<TStringType> &nullableCheckBox() {return m_nullableCheckBox;};

        //void onChange() override;
        void update(std::vector<AMUIParseExpressionElement<TStringType>> *expression) override;

    protected:
        bool *m_nullable;
        AMUIFormElementCheckBox<TStringType> m_nullableCheckBox;
    };

} // AMDialogs

#include "src/AMUIDialogExpressionNullable.hpp"

#endif //SAW_AMUIDIALOGEXPRESSIONNULLABLE_H
