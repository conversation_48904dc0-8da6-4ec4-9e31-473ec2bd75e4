//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGBOOLTRISTATEVIEW_H
#define SAW_AMUIDIALOGBOOLTRISTATEVIEW_H

#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogBoolTristateView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogBoolTristateView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogBoolTristateView.hpp"

#endif //SAW_AMUIDIALOGAUTHORTAGVIEW_H
