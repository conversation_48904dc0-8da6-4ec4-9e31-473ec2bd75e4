//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGNAMEVIEW_H
#define SAW_AMUIDIALOGNAMEVIEW_H

#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogNameView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogNameView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogNameView.hpp"

#endif //SAW_AMUIDIALOGNAMEVIEW_H
