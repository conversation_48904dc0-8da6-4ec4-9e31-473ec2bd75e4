//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTSTRINGNULLABLE_H
#define SAW_ALL_AMUIFORMELEMENTSTRINGNULLABLE_H


#include "AMUIFormElementString.h"

#include "amcore/AMAssert.h"
#include <string>

namespace AMDialogs {

    class AMUIFormElementStringNullable : public AMUIFormElementString {
    public:
        AMUIFormElementStringNullable(const char *key);

        ~AMUIFormElementStringNullable();

        void reset() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_string_nullable; };

        inline bool *notNullValuePtr() { return &m_notNull; }

        void setValue() override;
        void setValue(const char *value) override;

        std::string valueAsString() override;

        void setValueString(std::string value) override;

        bool isNull() override;

        void render(bool formGrayed, int element_id) override;

    protected:
        bool m_notNull;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTSTRINGNULLABLE_H
