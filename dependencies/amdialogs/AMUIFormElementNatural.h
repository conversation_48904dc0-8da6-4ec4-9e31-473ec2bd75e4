//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTNATURAL_H
#define SAW_ALL_AMUIFORMELEMENTNATURAL_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include "amcore/AMDataType.h"

namespace AMDialogs {

    class AMUIFormElementNatural : public AMUIFormElement {
    public:
        AMUIFormElementNatural(const char *key);

        ~AMUIFormElementNatural();

        void init();

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_integer; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        inline unsigned int value() const { return m_value; }

        void setValue(unsigned int value);

        void setValue(const char *value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return std::string(m_buffer);};
        void setValueString(std::string value) override {setValue(std::stoul(value));};

        void setWildCard(char wildcard) {m_wildcard = wildcard;};

    protected:
        char *m_buffer;
        unsigned int m_value;
        char m_wildcard;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTNATURAL_H
