//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTRADIOS_H
#define SAW_ALL_AMUIFORMELEMENTRADIOS_H

#include "amcore/AMAssert.h"
#include <string>
#include "amdialogs/AMUIFormElement.h"
#include "amui/AMUIIView.h"

namespace AMDialogs {

    class AMUIFormElementRadios : public AMUIFormElement {
    public:
        AMUIFormElementRadios(const char *key);

        ~AMUIFormElementRadios();

        void init(const std::vector<std::string> &items);

        AMCore::AMDataType type() const override { return AMCore::AMDataType_integer;};

        void reset() override;

        void updateState() override;

        void setValue(int value);
        int value() {return m_value;};

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return std::to_string(this->m_value);};
        void setValueString(std::string value) override {setValue(std::stoi(value));};

        void loadDefault() override;

    protected:
        const std::vector<std::string> *m_items;
        int m_value;
    };

}

#endif //SAW_ALL_AMUIFORMELEMENTRADIOS_H
