//
// Created by <PERSON><PERSON><PERSON> on 3.3.23.
//

#ifndef SAW_AMUINAME_H
#define SAW_AMUINAME_H

#include "amcore/AMNameBase.h"
#include "amdialogs/AMUIEnum.h"

namespace AMUI {
    class AMUIController;
}
namespace AMDialogs {

    extern int IDegreeBeforeTableId;
    extern int IDegreeAfterTableId;
    extern std::function<AMUI::AMUIController *()> IDegreeBeforeController;
    extern std::function<AMUI::AMUIController *()> IDegreeAfterController;

    template<typename TEnumType, typename TStringType>
    class AMUIName : public AMCore::AMNameBase<TEnumType, TStringType>
    {
    public:
        AMUIName(const std::string &strVoidParam);

    protected:
        void _refreshDegreeTables() override;
        void _discardDegreeTables() override;
        const std::string &m_strVoidParam;
    };


    template<typename TEnumType, typename TStringType>
    AMUIName<TEnumType, TStringType>::AMUIName(const std::string &strVoidParam)
        :AMCore::AMNameBase<TEnumType, TStringType>(),
         m_strVoidParam(strVoidParam)
    {
        if (AMCore::AMNameBase<TEnumType, TStringType>::g_instances == 1) {
            _refreshDegreeTables();
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIName<TEnumType, TStringType>::_refreshDegreeTables()
    {
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam);
        AMCore::AMNameBase<TEnumType, TStringType>::g_degreeBeforeTable = AMDialogs::twoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam);
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam);
        AMCore::AMNameBase<TEnumType, TStringType>::g_degreeAfterTable = AMDialogs::twoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam);

    }

    template<typename TEnumType, typename TStringType>
    void AMUIName<TEnumType, TStringType>::_discardDegreeTables()
    {

        AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam);
        AMCore::AMNameBase<TEnumType, TStringType>::g_degreeBeforeTable = nullptr;
        AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam);
        AMCore::AMNameBase<TEnumType, TStringType>::g_degreeAfterTable = nullptr;
    }
}
#endif //SAW_AMUINAME_H
