//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTDATETIME_H
#define SAW_ALL_AMUIFORMELEMENTDATETIME_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include <ctime>
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"

namespace AMDialogs {

    class AMUIDialogDatetime;
    class AMUIDialog;

    class AMUIFormElementDatetime : public AMUIFormElement {
    public:
        AMUIFormElementDatetime(const std::string key = "", bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementDatetime();

        void init(AMCore::AMDataType type);

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return m_nullable ? m_nullType : m_type;};

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        inline std::tm value() const { return m_valueMin; }
        inline AMCore::AMDatetimeus valueus() const { return m_valueMin; }
        inline AMCore::AMNullable<std::tm> valueNullable() const;
        inline AMCore::AMNullable<AMCore::AMDatetimeus> valueusNullable() const;
        void setValue(std::tm &value);
        void setValue(AMCore::AMDatetimeus &value);
        void setValue(AMCore::AMNullable<std::tm> &value);
        void setValue(AMCore::AMNullable<AMCore::AMDatetimeus> &value);

        void setValue(const char *buffer);



        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override;
        void setValueString(std::string value) override {setValue(value.c_str());};

        void setWildcard(char wildcard);

        char wildcard() {return m_wildcard;}

        void onDialogChange(AMUIDialog*);
        void onDialogClose(AMUIDialog*);

        bool focused() {return m_focused;}


        void loadDefault() override;
    protected:
        const char *m_format;
        AMCore::AMDataType m_type;
        char *m_buffer;
        AMCore::AMDatetimeus m_valueMin;
        AMCore::AMDatetimeus m_valueMax;
        GLuint m_icon;
        char m_wildcard;
        AMUIDialogDatetime *m_dialog;
        bool m_focused;
        int m_preventRefreshDialog;
        //bool m_notNull;
        AMCore::AMDataType m_nullType;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTDATETIME_H
