//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGBOOLTRISTATE_H
#define SAW_AMUIDIALOGBOOLTRISTATE_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementBoolTristate.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIFormElementBoolTristate;

    template<typename TStringType>
    class AMUIDialogBoolTristate:public  AMUIDialog {
    public:
        AMUIDialogBoolTristate();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, TStringType &trueString, TStringType falseString);
        std::vector<AMUIFormElement *> &elements() override;
        AMUIFormElementBoolTristate<TStringType> &boolElement();

    protected:
        AMUIFormElementBoolTristate<TStringType> m_boolElement;
        std::vector<AMUIFormElement *> m_elements;
    };

} // AMDialogs

#include "src/AMUIDialogBoolTristate.hpp"

#endif //SAW_AMUIDIALOGAUTHORTAG_H
