//
// Created by <PERSON><PERSON><PERSON> on 3.3.23.
//

#ifndef SAW_AMUINAMEFLASHBACK_H
#define SAW_AMUINAMEFLASHBACK_H

#include <map>
#include <functional>
#include "amcore/AMNameBase.h"
#include "amdialogs/AMUIEnum.h"

namespace AMUI {
    class AMUIController;
}
namespace AMDialogs {

    extern std::function<AMUI::AMUIController *()> IDegreeBeforeController;
    extern std::function<AMUI::AMUIController *()> IDegreeAfterController;
    extern int IDegreeBeforeTableId;
    extern int IDegreeAfterTableId;

    template<typename TEnumType, typename TStringType>
    class AMUINameFlashBack : public AMCore::AMNameBase<TEnumType, TStringType>
    {
    public:
        AMUINameFlashBack(const std::string &strVoidParam, std::string flashbackTimepoint);
        ~AMUINameFlashBack();
        void setTimepoint(std::string &flashbackTimepoint);
        static AMCore::AMTwoWayTable<TEnumType, TStringType>& degreeBeforeTable(std::string strVoidParam, std::string flashbackTimepoint);
        static AMCore::AMTwoWayTable<TEnumType, TStringType>& degreeAfterTable(std::string strVoidParam, std::string flashbackTimepoint);
        AMCore::AMTwoWayTable<TEnumType, TStringType>& mdegreeBeforeTable() {return degreeBeforeTable(m_strVoidParam, m_flashbackTimepoint);};
        AMCore::AMTwoWayTable<TEnumType, TStringType>& mdegreeAfterTable() {return degreeAfterTable(m_strVoidParam, m_flashbackTimepoint);};
        const std::string &strVoidParam() const {return m_strVoidParam;}
    protected:
        void _refreshDegreeTables() override;
        void _discardDegreeTables() override;
        const std::string &m_strVoidParam;
        std::string m_flashbackTimepoint;
        static std::map<std::string, int> g_fInstances;
        static std::map<std::string, AMCore::AMTwoWayTable<TEnumType, TStringType>*> g_fDegreeBeforeTable;
        static std::map<std::string, AMCore::AMTwoWayTable<TEnumType, TStringType>*> g_fDegreeAfterTable;
    };

    template<typename TEnumType, typename TStringType>
    std::map<std::string, int> AMUINameFlashBack<TEnumType, TStringType>::g_fInstances;
    template<typename TEnumType, typename TStringType>
    std::map<std::string, AMCore::AMTwoWayTable<TEnumType, TStringType>*> AMUINameFlashBack<TEnumType, TStringType>::g_fDegreeBeforeTable;
    template<typename TEnumType, typename TStringType>
    std::map<std::string, AMCore::AMTwoWayTable<TEnumType, TStringType>*> AMUINameFlashBack<TEnumType, TStringType>::g_fDegreeAfterTable;

    template<typename TEnumType, typename TStringType>
    AMUINameFlashBack<TEnumType, TStringType>::AMUINameFlashBack(const std::string &strVoidParam, std::string flashbackTimepoint)
        : AMCore::AMNameBase<TEnumType, TStringType>(),
          m_strVoidParam(strVoidParam),
          m_flashbackTimepoint(flashbackTimepoint)
    {
        auto it = g_fInstances.find(flashbackTimepoint);
        if (it == g_fInstances.end()) {
            g_fInstances.insert({flashbackTimepoint, 1});
            _refreshDegreeTables();
        } else {
            it->second++;
        }
    }

    template<typename TEnumType, typename TStringType>
    AMUINameFlashBack<TEnumType, TStringType>::~AMUINameFlashBack()
    {

        auto it = g_fInstances.find(m_flashbackTimepoint);
        AMAssert(it != g_fInstances.end());
        AMAssert(it->second >= 1);
        if (it->second == 1) {
            g_fInstances.erase(it);
            _discardDegreeTables();
        } else {
            it->second--;
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUINameFlashBack<TEnumType, TStringType>::setTimepoint(std::string &flashbackTimepoint)
    {
        auto it = g_fInstances.find(m_flashbackTimepoint);
        AMAssert(it != g_fInstances.end());
        AMAssert(it->second >= 1);
        if (it->second == 1) {
            g_fInstances.erase(it);
            _discardDegreeTables();
        } else {
            it->second--;
        }
        m_flashbackTimepoint = flashbackTimepoint;
        it = g_fInstances.find(flashbackTimepoint);
        if (it == g_fInstances.end()) {
            g_fInstances.insert({flashbackTimepoint, 1});
            _refreshDegreeTables();
        } else {
            it->second++;
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUINameFlashBack<TEnumType, TStringType>::_refreshDegreeTables()
    {
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, this->m_strVoidParam, m_flashbackTimepoint);
        auto itb = g_fDegreeBeforeTable.find(m_flashbackTimepoint);
        //printf("JJJJJ %i: [%s][%s][%lx] th=%lx  gfi=%i\n", IDegreeBeforeTableId, this->m_strVoidParam.c_str(), m_flashbackTimepoint.c_str(), itb->second, this,
        //       itb == g_fDegreeBeforeTable.end() ? -9999 : g_fInstances[m_flashbackTimepoint]);
        //if (itb != g_fDegreeBeforeTable.end()) {
            //g_fInstances[m_flashbackTimepoint] ++;
        //    return;
       // }
        AMAssert(itb == g_fDegreeBeforeTable.end());
        g_fDegreeBeforeTable.insert({m_flashbackTimepoint, AMDialogs::twoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, this->m_strVoidParam, m_flashbackTimepoint)});
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, this->m_strVoidParam, m_flashbackTimepoint);
        auto ita = g_fDegreeAfterTable.find(m_flashbackTimepoint);
        AMAssert(ita == g_fDegreeAfterTable.end());
        g_fDegreeAfterTable.insert({m_flashbackTimepoint, AMDialogs::twoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, this->m_strVoidParam, m_flashbackTimepoint)});
    }

    template<typename TEnumType, typename TStringType>
    void AMUINameFlashBack<TEnumType, TStringType>::_discardDegreeTables()
    {

        AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, this->m_strVoidParam, m_flashbackTimepoint);
        auto itb = g_fDegreeBeforeTable.find(m_flashbackTimepoint);
        //printf("JJJJH %i: [%s][%s][%lx] th=%lx gfi=%i\n", IDegreeBeforeTableId, this->m_strVoidParam.c_str(), m_flashbackTimepoint.c_str(), itb->second, this,
        //       g_fInstances.find(m_flashbackTimepoint) == g_fInstances.end() ? -999 : g_fInstances[m_flashbackTimepoint]);
        AMAssert(itb != g_fDegreeBeforeTable.end());
        g_fDegreeBeforeTable.erase(itb);
        AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, this->m_strVoidParam, m_flashbackTimepoint);
        auto ita = g_fDegreeAfterTable.find(m_flashbackTimepoint);
        AMAssert(ita != g_fDegreeAfterTable.end());
        g_fDegreeAfterTable.erase(ita);
    }

    template<typename TEnumType, typename TStringType>
    AMCore::AMTwoWayTable<TEnumType, TStringType>& AMUINameFlashBack<TEnumType, TStringType>::degreeBeforeTable(std::string strVoidParam, std::string flashbackTimepoint)
    {
        auto itb = g_fDegreeBeforeTable.find(flashbackTimepoint);
        if (itb == g_fDegreeBeforeTable.end()) {
            AMUINameFlashBack<TEnumType, TStringType> temp(strVoidParam, flashbackTimepoint);
            itb = g_fDegreeBeforeTable.find(flashbackTimepoint);
            if (itb == g_fDegreeBeforeTable.end()) {
                return AMCore::AMUIAddressBaseEmptyTable<TEnumType, TStringType>;
            }
        }
        return *itb->second;
    }

    template<typename TEnumType, typename TStringType>
    AMCore::AMTwoWayTable<TEnumType, TStringType>& AMUINameFlashBack<TEnumType, TStringType>::degreeAfterTable(std::string strVoidParam, std::string flashbackTimepoint)
    {
        auto ita = g_fDegreeAfterTable.find(flashbackTimepoint);
        if (ita == g_fDegreeAfterTable.end()) {
            AMUINameFlashBack<TEnumType, TStringType> temp(strVoidParam, flashbackTimepoint);
            ita = g_fDegreeAfterTable.find(flashbackTimepoint);
            if (ita == g_fDegreeAfterTable.end()) {
                return AMCore::AMUIAddressBaseEmptyTable<TEnumType, TStringType>;
            }
        }
        return *ita->second;
    }
}
#endif //SAW_AMUINAMEFLASHBACK_H
