//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTDATETIMENULLABLE_H
#define SAW_ALL_AMUIFORMELEMENTDATETIMENULLABLE_H

#include "AMUIFormElementDatetime.h"

namespace AMDialogs {


    class AMUIFormElementDatetimeNullable : public AMUIFormElementDatetime {
    public:
        AMUIFormElementDatetimeNullable(const std::string key = "");

        ~AMUIFormElementDatetimeNullable();

        void init(AMCore::AMDataType type);

        void reset() override;

        AMCore::AMDataType type() const override { return m_nullType;};

        void updateState() override;

        inline AMCore::AMNullable<std::tm> valueNullable() const;
        inline AMCore::AMNullable<AMCore::AMDatetimeus> valueusNullable() const;

        void setValue(AMCore::AMNullable<std::tm> &value);
        void setValue(AMCore::AMNullable<AMCore::AMDatetimeus> &value);

        void setValue(const char *buffer);


        std::string valueAsString() override;
        void setValueString(std::string value) override {setValue(value.c_str());};

    protected:
        bool m_isNotNull;
        AMCore::AMDataType m_nullType;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTDATETIMENULLABLE_H
