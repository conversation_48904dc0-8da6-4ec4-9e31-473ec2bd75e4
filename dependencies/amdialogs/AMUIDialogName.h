//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGNAME_H
#define SAW_AMUIDIALOGNAME_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementString.h"
#include "AMUIFormElementEnum.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    class AMUIDialogName:public  AMUIDialog {
    public:
        AMUIDialogName();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, char wildcard, const std::string &strVoidParam, AMUI::AMUIController *controller, std::string flashbackTimepoint = "");
        std::vector<AMUIFormElement *> &elements() override;
        AMUIFormElementEnum<TEnumType, TStringType> &degreeBefore() {return m_degreeBefore;};
        AMUIFormElementString &name() {return m_name;};
        AMUIFormElementString &surname() {return  m_surname;};
        AMUIFormElementEnum<TEnumType, TStringType> &degreeAfter() {return m_degreeAfter;};

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}
    protected:
        AMUIFormElementEnum<TEnumType, TStringType> m_degreeBefore;
        AMUIFormElementString m_name;
        AMUIFormElementString m_surname;
        AMUIFormElementEnum<TEnumType, TStringType> m_degreeAfter;

        std::vector<AMUIFormElement *> m_elements;
        char m_wildcard;
    };

} // AMDialogs

#include "src/AMUIDialogName.hpp"

#endif //SAW_AMUIDIALOGNAME_H
