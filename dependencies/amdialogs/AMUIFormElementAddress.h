//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTADDRESS_H
#define SAW_ALL_AMUIFORMELEMENTADDRESS_H

#include "amcore/AMAssert.h"
#include <string>
#include "AMUIFormElement.h"
#include "amcore/AMDataType.h"
#include "amui/AMUIUrlUtils.h"
#include "amui/AMUIGLWrappers.h"


namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAddress;
    class AMUIDialog;

    template<typename TStringType>
    class AMUIFormElementAddress : public AMUIFormElement {
    public:
        AMUIFormElementAddress(const char *key);

        ~AMUIFormElementAddress();

        void init();

        void reset() override;

        void updateState() override;
        //void updateStateNoDialog(AMCore::AMAddress<TStringType> &a);

        [[nodiscard]] int length() const override { return m_length + 1; }

        [[nodiscard]] AMCore::AMDataType type() const override { return AMCore::AMDataType_address; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        virtual void setValue(const char *value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return AMUI::url_encode_string(TStringType(m_buffer));};
        void setValueString(std::string value) override {setValue(value.c_str());};

        void onDialogChange(AMUIDialog*);
        void onDialogClose(AMUIDialog*);

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}

        void loadDefault() override;
    protected:
        char *m_buffer;
        GLuint m_icon;
        AMUIDialogAddress<TStringType> *m_dialog;
        char m_wildcard;
        float m_dialogPositionOfffsetX;
        int m_preventRefreshDialog;
    };

}

#include "src/AMUIFormElementAddress.hpp"

#endif //SAW_ALL_AMUIFORMELEMENTADDRESS_H
