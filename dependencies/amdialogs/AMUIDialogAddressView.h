//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGADDRESSVIEW_H
#define SAW_AMUIDIALOGADDRESSVIEW_H

#include "AMUIDialogView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAddressView: public AMDialogs::AMUIDialogView
    {
    public:
        AMUIDialogAddressView();
        void render() override;
    protected:

    };

} // AMDialogs

#include "src/AMUIDialogAddressView.hpp"

#endif //SAW_AMUIDIALOGADDRESSVIEW_H
