//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTFLOAT_H
#define SAW_ALL_AMUIFORMELEMENTFLOAT_H

#include "AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include "amcore/AMDataType.h"

namespace AMDialogs {

    class AMUIFormElementFloat : public AMUIFormElement {
    public:
        AMUIFormElementFloat(const char *key, bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementFloat();

        void init();

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_float; };

        inline char *buffer() {
            AMAssert(m_buffer);
            return m_buffer;
        }

        inline float value() const { return m_value; }

        void setValue();

        void setValue(float value);

        void setValue(const char *value);

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override;
        void setValueString(std::string value) override;

        bool isNull() override;

        void setNull(bool _null) override;

        void setWildCard(char wildcard) {m_wildcard = wildcard;};

        char wildcard() {return m_wildcard;}

        void loadDefault() override;

    protected:
        char *m_buffer;
        float m_value;
        char m_wildcard;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTFLOAT_H
