#include "amdialogs/AMUIParsers.h"
//#include "amdialog/src/AMUIParsers.hpp"
#include <sstream>
#include "amdialogs/src/AMUIParsers.hpp"
#include "gtest/gtest.h"
#include "../googlemock/include/gmock/gmock-matchers.h"

#include "test_twowaytable.hpp"

using namespace AMCore;
using namespace AMDialogs;
using namespace testing;

TEST(AMParsers, nullable)
{
    std::istringstream is1("");
    std::ostringstream os1;
    bool brv;
    EXPECT_TRUE(AMUIParseNull(is1, &brv, &os1) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is1.get(), EOF);
    EXPECT_STREQ(os1.str().c_str(), "");

    std::istringstream is2(" nuLL ");
    std::ostringstream os2;
    EXPECT_TRUE(AMUIParseNull(is2, &brv, &os2) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is2.get(), ' ');
    EXPECT_STREQ(os2.str().c_str(), "nuLL");

    std::istringstream is3(" Ňú ");
    std::ostringstream os3;
    EXPECT_TRUE(AMUIParseNull(is3, &brv, &os3) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is3.get(), ' ');
    EXPECT_STREQ(os3.str().c_str(), "Ňú");

    brv = false;
    std::istringstream is4(" ŇúX ");
    std::ostringstream os4;
    EXPECT_TRUE(AMUIParseNull(is4, &brv, &os4) == AMUIParseResult::FAIL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is4.get(), 'X');
    EXPECT_STREQ(os4.str().c_str(), "Ňú");

    std::istringstream is5(" NULLD ");
    std::ostringstream os5;
    EXPECT_TRUE(AMUIParseNull(is5, &brv, &os5) == AMUIParseResult::FAIL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is5.get(), 'D');
    EXPECT_STREQ(os5.str().c_str(), "NULL");

    std::istringstream is6(" 514 ");
    std::ostringstream os6;
    EXPECT_TRUE(AMUIParseNull(is6, &brv, &os6) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is6.get(), '5');
    EXPECT_STREQ(os6.str().c_str(), "");
}


TEST(AMParsers, naturalType)
{

    std::istringstream is01("123");
    int irvMin;
    int irvMax;
    EXPECT_TRUE(AMUIParseNatural(is01, &irvMin, &irvMax) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 123);
    EXPECT_EQ(is01.get(), EOF);
    EXPECT_EQ(irvMin, irvMax);

    std::istringstream is02(" 346 ");
    EXPECT_TRUE(AMUIParseNatural(is02, &irvMin, &irvMax) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 346);
    EXPECT_EQ(is02.get(), ' ');
    EXPECT_EQ(irvMin, irvMax);

    std::istringstream is03(" 347H");
    std::ostringstream os03;
    EXPECT_TRUE(AMUIParseNatural(is03, &irvMin, &irvMax, &os03) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 347);
    EXPECT_EQ(is03.get(), 'H');
    EXPECT_STREQ(os03.str().c_str(), "347");
    EXPECT_EQ(irvMin, irvMax);

    std::istringstream is04("2147483647");
    std::ostringstream os04;
    EXPECT_TRUE(AMUIParseNatural(is04, &irvMin, &irvMax, &os04) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 2147483647);
    EXPECT_EQ(is04.get(), EOF);
    EXPECT_STREQ(os04.str().c_str(), "2147483647");
    EXPECT_EQ(irvMin, irvMax);

    std::istringstream is05("2147483648");
    std::ostringstream os05;
    EXPECT_TRUE(AMUIParseNatural(is05, &irvMin, &irvMax, &os05) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(irvMin, 214748364);
    EXPECT_EQ(is05.get(), '8');
    EXPECT_STREQ(os05.str().c_str(), "214748364");
    EXPECT_EQ(irvMin, irvMax);

    irvMin = 78956;
    irvMax = 78956;
    std::istringstream is06("");
    std::ostringstream os06;
    EXPECT_TRUE(AMUIParseNatural(is06, &irvMin, &irvMax, &os06) == AMUIParseResult::FAIL);
    EXPECT_EQ(irvMin, 78956);
    EXPECT_EQ(is06.get(), EOF);
    EXPECT_STREQ(os06.str().c_str(), "");
    EXPECT_EQ(irvMin, irvMax);

    unsigned int uirvMin;
    unsigned int uirvMax;

    std::istringstream is07("4294967295");
    std::ostringstream os07;
    EXPECT_TRUE(AMUIParseNatural(is07, &uirvMin, &uirvMax, &os07) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(uirvMin, 4294967295);
    EXPECT_EQ(is07.get(), EOF);
    EXPECT_STREQ(os07.str().c_str(), "4294967295");
    EXPECT_EQ(uirvMin, uirvMax);

    std::istringstream is08("4294967296");
    std::ostringstream os08;
    EXPECT_TRUE(AMUIParseNatural(is08, &uirvMin, &uirvMax, &os08) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(uirvMin, 429496729);
    EXPECT_EQ(is08.get(), '6');
    EXPECT_STREQ(os08.str().c_str(), "429496729");
    EXPECT_EQ(uirvMin, uirvMax);

    char crvMin;
    char crvMax;

    std::istringstream is09("  127 ");
    std::ostringstream os09;
    EXPECT_TRUE(AMUIParseNatural(is09, &crvMin, &crvMax, &os09) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(crvMin, 127);
    EXPECT_EQ(is09.get(), ' ');
    EXPECT_STREQ(os09.str().c_str(), "127");
    EXPECT_EQ(crvMin, crvMax);

    std::istringstream is10("128k");
    std::ostringstream os10;
    EXPECT_TRUE(AMUIParseNatural(is10, &crvMin, &crvMax, &os10) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(crvMin, 12);
    EXPECT_EQ(is10.get(), '8');
    EXPECT_STREQ(os10.str().c_str(), "12");
    EXPECT_EQ(crvMin, crvMax);

    std::istringstream is11(" 347??H");
    std::ostringstream os11;
    EXPECT_TRUE(AMUIParseNatural(is11, &irvMin, &irvMax, &os11, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMax, 34800);
    EXPECT_EQ(irvMin, 34700);
    EXPECT_EQ(is11.get(), 'H');
    EXPECT_STREQ(os11.str().c_str(), "347??");

    std::istringstream is12("2????????");
    std::ostringstream os12;
    EXPECT_TRUE(AMUIParseNatural(is12, &irvMin, &irvMax, &os12, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMax, 300000000);
    EXPECT_EQ(irvMin, 200000000);
    EXPECT_EQ(is12.get(), EOF);
    EXPECT_STREQ(os12.str().c_str(), "2????????");

    std::istringstream is13("214748364?");
    std::ostringstream os13;
    EXPECT_TRUE(AMUIParseNatural(is13, &irvMin, &irvMax, &os13, '?') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(irvMax, 214748364);
    EXPECT_EQ(irvMin, 214748364);
    EXPECT_EQ(is13.get(), '?');
    EXPECT_STREQ(os13.str().c_str(), "214748364");

    std::istringstream is14("?");
    std::ostringstream os14;
    EXPECT_TRUE(AMUIParseNatural(is14, &irvMin, &irvMax, &os14, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMax, 10);
    EXPECT_EQ(irvMin, 0);
    EXPECT_EQ(is14.get(), EOF);
    EXPECT_STREQ(os14.str().c_str(), "?");

    AMNullable<int> nrvMax;
    AMNullable<int> nrvMin;

    std::istringstream is15("132");
    std::ostringstream os15;
    EXPECT_TRUE(AMUIParseNatural(is15, &nrvMin, &nrvMax, &os15, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(nrvMin.get(), 132);
    EXPECT_EQ(nrvMax.get(), 132);
    EXPECT_EQ(is15.get(), EOF);
    EXPECT_STREQ(os15.str().c_str(), "132");


    AMNullable<float> nrvfMax;
    AMNullable<float> nrvfMin;

    std::istringstream is16("200.25");
    std::ostringstream os16;
    EXPECT_TRUE(AMUIParseFloat(is16, &nrvfMin, &nrvfMax, &os16, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(nrvfMin.get(), 200.25);
    EXPECT_EQ(nrvfMax.get(), 200.25);
    EXPECT_EQ(is16.get(), EOF);
    EXPECT_STREQ(os16.str().c_str(), "200.25");

    std::istringstream is17("null");
    std::ostringstream os17;
    EXPECT_TRUE(AMUIParseFloat(is17, &nrvfMin, &nrvfMax, &os17, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(nrvfMin.isNull(), true);
    EXPECT_EQ(nrvfMax.isNull(), true);
    EXPECT_EQ(is17.get(), EOF);
    EXPECT_STREQ(os17.str().c_str(), "null");
}

TEST(AMParsers, integerType)
{
    int irvMin = -1;
    int irvMax = -1;

    std::istringstream is1("2147483647");
    std::ostringstream os1;
    EXPECT_TRUE(AMUIParseInteger(is1, &irvMin, &irvMax, &os1, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 2147483647);
    EXPECT_EQ(irvMax, 2147483647);
    EXPECT_EQ(is1.get(), EOF);
    EXPECT_STREQ(os1.str().c_str(), "2147483647");

    std::istringstream is2("+2147483648");
    std::ostringstream os2;
    EXPECT_TRUE(AMUIParseInteger(is2, &irvMin, &irvMax, &os2, '?')  == AMUIParseResult::PARTIAL);
    EXPECT_EQ(irvMin, 214748364);
    EXPECT_EQ(irvMax, 214748364);
    EXPECT_EQ(is2.get(), '8');
    EXPECT_STREQ(os2.str().c_str(), "+214748364");

    std::istringstream is3(" - 214748369");
    std::ostringstream os3;
    EXPECT_TRUE(AMUIParseInteger(is3, &irvMin, &irvMax,&os3, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, -214748369);
    EXPECT_EQ(irvMax, -214748369);
    EXPECT_EQ(is3.get(), EOF);
    EXPECT_STREQ(os3.str().c_str(), "-214748369");

    std::istringstream is4("-2147483648M");
    std::ostringstream os4;
    EXPECT_TRUE(AMUIParseInteger(is4, &irvMin, &irvMax, &os4, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, -2147483648);
    EXPECT_EQ(irvMax, -2147483648);
    EXPECT_EQ(is4.get(), 'M');
    EXPECT_STREQ(os4.str().c_str(), "-2147483648");

    irvMin = 5555;
    irvMax = 5566;
    std::istringstream is5("-M");
    std::ostringstream os5;
    EXPECT_TRUE(AMUIParseInteger(is5, &irvMin, &irvMax, &os5, '?') == AMUIParseResult::FAIL);
    EXPECT_EQ(irvMin, 5555);
    EXPECT_EQ(irvMax, 5566);
    EXPECT_EQ(is5.get(), 'M');
    EXPECT_STREQ(os5.str().c_str(), "-");

    std::istringstream is6("P");
    std::ostringstream os6;
    EXPECT_TRUE(AMUIParseInteger(is6, &irvMin, &irvMax, &os6, '?') == AMUIParseResult::FAIL);
    EXPECT_EQ(irvMin, 5555);
    EXPECT_EQ(irvMax, 5566);
    EXPECT_EQ(is6.get(), 'P');
    EXPECT_STREQ(os6.str().c_str(), "");

    std::istringstream is7("???");
    std::ostringstream os7;
    EXPECT_TRUE(AMUIParseInteger(is7, &irvMin, &irvMax, &os7, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, 0);
    EXPECT_EQ(irvMax, 1000);
    EXPECT_EQ(is7.get(), EOF);
    EXPECT_STREQ(os7.str().c_str(), "???");

    std::istringstream is8("-???9");
    std::ostringstream os8;
    EXPECT_TRUE(AMUIParseInteger(is8, &irvMin, &irvMax, &os8, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(irvMin, -1000);
    EXPECT_EQ(irvMax, 0);
    EXPECT_EQ(is8.get(), '9');
    EXPECT_STREQ(os8.str().c_str(), "-???");
}


TEST(AMParsers, datetimeType)
{
    std::tm trvMin;
    std::tm trvMax;
    std::tm ref;

    std::istringstream is1("2017-12-25 02:25:30");
    std::ostringstream os1;
    ref.tm_sec = 30;
    ref.tm_min = 25;
    ref.tm_hour = 2;
    ref.tm_mday = 25;
    ref.tm_mon = 12 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is1, &trvMin, &trvMax,&os1, std::string("%Y-%m-%d %H:%M:%S"), '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is1.get(), EOF);
    EXPECT_STREQ(os1.str().c_str(), "2017-12-25 02:25:30");

    std::istringstream is2("2017-2-29 2:25:30");
    std::ostringstream os2;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 28;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is2, &trvMin, &trvMax, &os2, std::string("%Y-%m-%d %H:%M:%S"), '\0') == AMUIParseResult::FAIL);
    EXPECT_EQ(trvMin, ref);
    //char buffer[26];strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", &trvMax);printf("%s\n",buffer);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is2.get(), ' ');
    EXPECT_STREQ(os2.str().c_str(), "2017-2-29");

    std::istringstream is3("2017-2-23 2:25");
    std::ostringstream os3;
    ref.tm_sec = 0;
    ref.tm_min = 25;
    ref.tm_hour = 2;
    ref.tm_mday = 23;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is3, &trvMin, &trvMax, &os3, std::string("%Y-%m-%d %H:%M:%S"), '\0') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is3.get(), EOF);
    EXPECT_STREQ(os3.str().c_str(), "2017-2-23 2:25");

    std::istringstream is4("2017-2-23 2:25:1 ");
    std::ostringstream os4;
    ref.tm_sec = 1;
    ref.tm_min = 25;
    ref.tm_hour = 2;
    ref.tm_mday = 23;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is4, &trvMin, &trvMax, &os4, std::string("%Y-%m-%d %H:%M:%S"), '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is4.get(), ' ');
    EXPECT_STREQ(os4.str().c_str(), "2017-2-23 2:25:1");

    std::istringstream is5("3.4.2016l");
    std::ostringstream os5;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 3;
    ref.tm_mon = 4 - 1;
    ref.tm_year = 2016 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is5, &trvMin, &trvMax, &os5, std::string("%d.%m.%Y"), '\0') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is5.get(), 'l');
    EXPECT_STREQ(os5.str().c_str(), "3.4.2016");

    std::istringstream is6("2017-2-? ??:??:?? ");
    std::ostringstream os6;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    std::tm refMax;
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 9;
    refMax.tm_mon = 2 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is6, &trvMin, &trvMax, &os6, std::string("%Y-%m-%d %H:%M:%S"), '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is6.get(), ' ');
    EXPECT_STREQ(os6.str().c_str(), "2017-2-? ??:??:??");

    std::istringstream is7("2017-2-2? ??:?:?? ");
    std::ostringstream os7;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 20;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_mday = 28;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is7, &trvMin, &trvMax, &os7, std::string("%Y-%m-%d %H:%M:%S"), '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is7.get(), ' ');
    EXPECT_STREQ(os7.str().c_str(), "2017-2-2? ??:?:??");

    std::istringstream is8("2017-2-?? ??::?? ");
    std::ostringstream os8;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is8, &trvMin, &trvMax, &os8, std::string("%Y-%m-%d %H:%M:%S"), '?') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is8.get(), ':');
    EXPECT_STREQ(os8.str().c_str(), "2017-2-?? ??:");

    std::istringstream is9a("2017.2.2?? ??::?? ");
    std::ostringstream os9a;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is9a, &trvMin, &trvMax, &os9a, std::string("%Y.%m.%d %H:%M:%S"), '?') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 28;
    refMax.tm_mon = 2 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is9a.get(), ' ');
    EXPECT_STREQ(os9a.str().c_str(), "2017.2.");

    std::istringstream is10a("2017");
    std::ostringstream os10a;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 1 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 31;
    refMax.tm_mon = 12 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is10a, &trvMin, &trvMax, &os10a, std::string("%Y.%m.%d %H:%M:%S"), '%') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is10a.get(), EOF);
    EXPECT_STREQ(os10a.str().c_str(), "2017");

    std::istringstream is10b("2017.2");
    std::ostringstream os10b;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 28;
    refMax.tm_mon = 2 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is10b, &trvMin, &trvMax, &os10b, std::string("%Y.%m.%d %H:%M:%S"), '%') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is10b.get(), EOF);
    EXPECT_STREQ(os10b.str().c_str(), "2017.2");

    std::istringstream is10c("2017.2.5");
    std::ostringstream os10c;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 5;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 5;
    refMax.tm_mon = 2 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is10c, &trvMin, &trvMax, &os10c, std::string("%Y.%m.%d %H:%M:%S"), '%') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is10c.get(), EOF);
    EXPECT_STREQ(os10c.str().c_str(), "2017.2.5");

    std::istringstream is11a("2017.2.%");
    std::ostringstream os11a;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 0;
    ref.tm_mday = 1;
    ref.tm_mon = 2 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 23;
    refMax.tm_mday = 9;
    refMax.tm_mon = 2 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is11a, &trvMin, &trvMax, &os11a, std::string("%Y.%m.%d %H:%M:%S"), '%') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    //char buffer[26];strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", &trvMax);printf("%s\n",buffer);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is11a.get(), EOF);
    EXPECT_STREQ(os11a.str().c_str(), "2017.2.%");

    std::istringstream is11b("2017.3.10 15:20");
    std::ostringstream os11b;
    ref.tm_sec = 0;
    ref.tm_min = 20;
    ref.tm_hour = 15;
    ref.tm_mday = 10;
    ref.tm_mon = 3 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 20;
    refMax.tm_hour = 15;
    refMax.tm_mday = 10;
    refMax.tm_mon = 3 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is11b, &trvMin, &trvMax, &os11b, std::string("%Y.%m.%d %H:%M:%S"), '%') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is11b.get(), EOF);
    EXPECT_STREQ(os11b.str().c_str(), "2017.3.10 15:20");

    std::istringstream is11c("2017.3.10 15:__:__");
    std::ostringstream os11c;
    ref.tm_sec = 0;
    ref.tm_min = 0;
    ref.tm_hour = 15;
    ref.tm_mday = 10;
    ref.tm_mon = 3 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    refMax.tm_sec = 59;
    refMax.tm_min = 59;
    refMax.tm_hour = 15;
    refMax.tm_mday = 10;
    refMax.tm_mon = 3 - 1;
    refMax.tm_year = 2017 - 1900;
    refMax.tm_isdst = -1;
    mktime(&refMax);
    EXPECT_TRUE(AMUIParseDatetime(is11c, &trvMin, &trvMax, &os11c, std::string("%Y.%m.%d %H:%M:%S"), '_') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, refMax);
    EXPECT_EQ(is11c.get(), EOF);
    EXPECT_STREQ(os11c.str().c_str(), "2017.3.10 15:__:__");
}

TEST(AMParsers, datetimeusType)
{
    AMCore::AMDatetimeus trvMin;
    AMCore::AMDatetimeus trvMax;
    AMCore::AMDatetimeus ref;

    std::istringstream is1("2017-12-25 02:25:30.4567");
    std::ostringstream os1;
    ref.usec = 456700;
    ref.tm_sec = 30;
    ref.tm_min = 25;
    ref.tm_hour = 2;
    ref.tm_mday = 25;
    ref.tm_mon = 12 - 1;
    ref.tm_year = 2017 - 1900;
    ref.tm_isdst = -1;
    mktime(&ref);
    EXPECT_TRUE(AMUIParseDatetime(is1, &trvMin, &trvMax,&os1, std::string("%Y-%m-%d %H:%M:%S.%Q"), '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin, ref);
    EXPECT_EQ(trvMax, ref);
    EXPECT_EQ(is1.get(), EOF);
    EXPECT_STREQ(os1.str().c_str(), "2017-12-25 02:25:30.4567");
}

TEST(AMParsers, nullableTypes)
{
    AMNullable<int> nrvMin;
    AMNullable<int> nrvMax;

    std::istringstream is1("-???9");
    std::ostringstream os1;
    EXPECT_TRUE(AMUIParseInteger(is1, &nrvMin, &nrvMax, &os1, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(nrvMin.get(), -1000);
    EXPECT_EQ(nrvMax.get(), 0);
    EXPECT_EQ(is1.get(), '9');
    EXPECT_STREQ(os1.str().c_str(), "-???");

    std::istringstream is2(" nu");
    std::ostringstream os2;
    EXPECT_TRUE(AMUIParseInteger(is2, &nrvMin, &nrvMax, &os2, '?') == AMUIParseResult::PARTIAL);
    EXPECT_EQ(nrvMin.isNull(), true);
    EXPECT_EQ(nrvMax.isNull(), true);
    EXPECT_EQ(is2.get(), EOF);
    EXPECT_STREQ(os2.str().c_str(), "nu");

    std::istringstream is3("2???9");
    std::ostringstream os3;
    EXPECT_TRUE(AMUIParseNatural(is3, &nrvMin, &nrvMax, &os3, '?') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(nrvMin.get(), 2000);
    EXPECT_EQ(nrvMax.get(), 3000);
    EXPECT_EQ(is3.get(), '9');
    EXPECT_STREQ(os3.str().c_str(), "2???");

    AMNullable<bool> brv;
    std::istringstream is4(" muž ");
    std::ostringstream os4;
    EXPECT_TRUE(AMUIParseBool(is4, &brv, "muž", "žena", &os4) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is4.get(), ' ');
    EXPECT_STREQ(os4.str().c_str(), "muž");

    std::istringstream is4a(" null ");
    std::ostringstream os4a;
    EXPECT_TRUE(AMUIParseBool(is4a, &brv, "muž", "žena", &os4a) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv.isNull(), true);
    EXPECT_EQ(is4a.get(), ' ');
    EXPECT_STREQ(os4a.str().c_str(), "null");

    std::istringstream is4b(" nullov ");
    std::ostringstream os4b;
    EXPECT_TRUE(AMUIParseBool(is4b, &brv, "nullove", "nullička", &os4b) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is4b.get(), ' ');
    EXPECT_STREQ(os4b.str().c_str(), "nullov");

    AMNullable<std::tm> trvMin;
    AMNullable<std::tm> trvMax;
    std::istringstream is5(" nuLl ");
    std::ostringstream os5;
    EXPECT_TRUE(AMUIParseDatetime(is5, &trvMin, &trvMax, &os5) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(trvMin.isNull(), true);
    EXPECT_EQ(trvMax.isNull(), true);
    EXPECT_EQ(is5.get(), ' ');
    EXPECT_STREQ(os5.str().c_str(), "nuLl");


    AMNullable<std::string> srv;
    bool lkPrPr = false;
    std::istringstream is6(" nuLl ");
    std::ostringstream os6;
    EXPECT_TRUE(AMUIParseString<std::string>(is6, &srv, &lkPrPr, &os6, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_EQ(srv.isNull(), true);
    EXPECT_EQ(is6.get(), ' ');
    EXPECT_STREQ(os6.str().c_str(), "nuLl");


    std::istringstream is6b(" nuL ");
    std::ostringstream os6b;
    EXPECT_TRUE(AMUIParseString<std::string>(is6b, &srv, &lkPrPr, &os6b, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.get().c_str(), "%nuL%");
    EXPECT_EQ(is6b.get(), ' ');
    EXPECT_STREQ(os6b.str().c_str(), "nuL");
}

TEST(AMParsers, boolType)
{
    bool brv = false;

    std::istringstream is1(" muž ");
    std::ostringstream os1;
    EXPECT_TRUE(AMUIParseBool(is1, &brv, "muž", "žena", &os1) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is1.get(), ' ');
    EXPECT_STREQ(os1.str().c_str(), "muž");

    std::istringstream is2(" žen");
    std::ostringstream os2;
    EXPECT_TRUE(AMUIParseBool(is2, &brv, "muž", "žena", &os2) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is2.get(), EOF);
    EXPECT_STREQ(os2.str().c_str(), "žen");


    std::istringstream is3(" mŮ ");
    std::ostringstream os3;
    EXPECT_TRUE(AMUIParseBool(is3, &brv, "muž", "žena", &os3) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is3.get(), ' ');
    EXPECT_STREQ(os3.str().c_str(), "mŮ");

    std::istringstream is4(" muz ");
    std::ostringstream os4;
    EXPECT_TRUE(AMUIParseBool(is4, &brv, "muž", "mužatka", &os4) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is4.get(), ' ');
    EXPECT_STREQ(os4.str().c_str(), "muz");

    std::istringstream is5(" muza ");
    std::ostringstream os5;
    EXPECT_TRUE(AMUIParseBool(is5, &brv, "muž", "mužatka", &os5) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is5.get(), ' ');
    EXPECT_STREQ(os5.str().c_str(), "muza");

    std::istringstream is6(" muzatka");
    std::ostringstream os6;
    EXPECT_TRUE(AMUIParseBool(is6, &brv, "muž", "mužatka", &os6) == AMUIParseResult::SUCCESS);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is6.get(), EOF);
    EXPECT_STREQ(os6.str().c_str(), "muzatka");

    brv = true;
    std::istringstream is7("mezatka");
    std::ostringstream os7;
    EXPECT_TRUE(AMUIParseBool(is7, &brv, "muž", "mužatka", &os7) == AMUIParseResult::FAIL);
    EXPECT_EQ(brv, true);
    EXPECT_EQ(is7.get(), 'e');
    EXPECT_STREQ(os7.str().c_str(), "m");

    std::istringstream is8("neni nu");
    std::ostringstream os8;
    EXPECT_TRUE(AMUIParseBool(is8, &brv, "je null", "neni null", &os8) == AMUIParseResult::PARTIAL);
    EXPECT_EQ(brv, false);
    EXPECT_EQ(is8.get(), EOF);
    EXPECT_STREQ(os8.str().c_str(), "neni nu");
}

TEST(AMParsers, stringType)
{
    std::string srv;
    bool likePrPr = false;
    std::istringstream is1("He ");
    std::ostringstream os1;
    EXPECT_TRUE(AMUIParseString<std::string>(is1, &srv, &likePrPr, &os1, '?', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "%He%");
    EXPECT_TRUE(likePrPr);
    EXPECT_EQ(is1.get(), ' ');
    EXPECT_STREQ(os1.str().c_str(), "He");

    std::istringstream is1b("He)");
    std::ostringstream os1b;
    EXPECT_TRUE(AMUIParseString<std::string>(is1b, &srv, &likePrPr, &os1b, '?', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "%He%");
    EXPECT_TRUE(likePrPr);
    EXPECT_EQ(is1b.get(), ')');
    EXPECT_STREQ(os1b.str().c_str(), "He");

    std::istringstream is2(" H ");
    std::ostringstream os2;
    likePrPr = false;
    EXPECT_TRUE(AMUIParseString<std::string>(is2, &srv, &likePrPr, &os2, '?', '%') == AMUIParseResult::PARTIAL);
    EXPECT_STREQ(srv.c_str(), "%H%");
    EXPECT_TRUE(likePrPr);
    EXPECT_EQ(is2.get(), ' ');
    EXPECT_STREQ(os2.str().c_str(), "H");

    std::istringstream is3(" Helena");
    std::ostringstream os3;
    likePrPr = false;
    EXPECT_TRUE(AMUIParseString<std::string>(is3, &srv, &likePrPr, &os3, '?', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "%Helena%");
    EXPECT_TRUE(likePrPr);
    EXPECT_EQ(is3.get(), EOF);
    EXPECT_STREQ(os3.str().c_str(), "Helena");

    std::istringstream is4(" 'Helena a George' ");
    std::ostringstream os4;
    EXPECT_TRUE(AMUIParseString<std::string>(is4, &srv, &likePrPr, &os4, '?', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "Helena a George");
    EXPECT_FALSE(likePrPr);
    EXPECT_EQ(is4.get(), ' ');
    EXPECT_STREQ(os4.str().c_str(), "'Helena a George'");

    std::istringstream is5(" 'Helena a George ");
    std::ostringstream os5;
    EXPECT_TRUE(AMUIParseString<std::string>(is5, &srv, &likePrPr, &os5, '?', '%') == AMUIParseResult::PARTIAL);
    EXPECT_STREQ(srv.c_str(), "Helena a George ");
    EXPECT_FALSE(likePrPr);
    EXPECT_EQ(is5.get(), EOF);
    EXPECT_STREQ(os5.str().c_str(), "'Helena a George '");

    std::istringstream is6(" 'Helena a George_%' ");
    std::ostringstream os6;
    EXPECT_TRUE(AMUIParseString<std::string>(is6, &srv, &likePrPr, &os6, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "Helena a George_%");
    EXPECT_FALSE(likePrPr);
    EXPECT_EQ(is6.get(), ' ');
    EXPECT_STREQ(os6.str().c_str(), "'Helena a George_%'");

    std::istringstream is7(" '%Helena a George%' ");
    std::ostringstream os7;
    EXPECT_TRUE(AMUIParseString<std::string>(is7, &srv, &likePrPr, &os7, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "%Helena a George%");
    EXPECT_FALSE(likePrPr);
    EXPECT_EQ(is7.get(), ' ');
    EXPECT_STREQ(os7.str().c_str(), "'%Helena a George%'");

    std::istringstream is8(" 'Helena a George%' ");
    std::ostringstream os8;
    EXPECT_TRUE(AMUIParseString<std::string>(is8, &srv, &likePrPr, &os8, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "Helena a George%");
    EXPECT_FALSE(likePrPr);
    EXPECT_EQ(is8.get(), ' ');
    EXPECT_STREQ(os8.str().c_str(), "'Helena a George%'");


    std::istringstream is9(" Helena% ");
    std::ostringstream os9;
    EXPECT_TRUE(AMUIParseString<std::string>(is9, &srv, &likePrPr, &os9, '_', '%') == AMUIParseResult::SUCCESS);
    EXPECT_STREQ(srv.c_str(), "Helena%");
    EXPECT_TRUE(likePrPr);
    EXPECT_EQ(is9.get(), ' ');
    EXPECT_STREQ(os9.str().c_str(), "Helena%");
}

TEST(AMParsers, floatType)
{
    float frvMin, tvMin, frvMax, tvMax;

    std::istringstream is01("");
    std::ostringstream os01;
    EXPECT_EQ(AMUIParseFloat(is01, &frvMin, &frvMax, &os01, '_') , AMUIParseResult::FAIL);
    EXPECT_EQ(is01.get(), EOF);
    EXPECT_STREQ(os01.str().c_str(), "");

    std::istringstream is02("1");
    std::ostringstream os02;
    tvMin = 1.0f;
    EXPECT_EQ(AMUIParseFloat(is02, &frvMin, &frvMax, &os02, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is02.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os02.str().c_str(), "1");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x78000000 >> 23, (*((unsigned int*)&tvMin))&0x78000000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7FFFFFF , (*((unsigned int*)&tvMin))&0x7FFFFFF);

    std::istringstream is03("4 ");
    std::ostringstream os03;
    tvMin = 4.0;
    EXPECT_EQ(AMUIParseFloat(is03, &frvMin, &frvMax, &os03, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is03.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os03.str().c_str(), "4");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x78000000 >> 23, (*((unsigned int*)&tvMin))&0x78000000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFFF , (*((unsigned int*)&tvMin))&0x07FFFFFF);

    std::istringstream is04("40000");
    std::ostringstream os04;
    tvMin = 40000.0;
    EXPECT_EQ(AMUIParseFloat(is04, &frvMin, &frvMax, &os04, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is04.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os04.str().c_str(), "40000");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is05("400000000000000");
    std::ostringstream os05;
    tvMin = 400000000000000.0;
    EXPECT_EQ(AMUIParseFloat(is05, &frvMin, &frvMax, &os05, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is05.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os05.str().c_str(), "4000000000e5");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is06("25E17");
    std::ostringstream os06;
    tvMin = 25E17;
    EXPECT_EQ(AMUIParseFloat(is06, &frvMin, &frvMax, &os06, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is06.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os06.str().c_str(), "25E17");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is07("25E1700");
    std::ostringstream os07;
    tvMin = std::numeric_limits<float>::infinity();
    EXPECT_EQ(AMUIParseFloat(is07, &frvMin, &frvMax, &os07, '_') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is07.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os07.str().c_str(), "25E1700");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is08("25E-1700");
    std::ostringstream os08;
    tvMin = 0.0;//std::numeric_limits<float>::infinity();
    EXPECT_EQ(AMUIParseFloat(is08, &frvMin, &frvMax, &os08, '_') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is08.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os08.str().c_str(), "25E-1700");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is09("25.75E17");
    std::ostringstream os09;
    tvMin = 25.75e17;
    EXPECT_EQ(AMUIParseFloat(is09, &frvMin, &frvMax, &os09, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is09.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os09.str().c_str(), "25.75E17");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is10("40.00000000000005");
    std::ostringstream os10;
    tvMin = 40.0;
    EXPECT_EQ(AMUIParseFloat(is10, &frvMin, &frvMax, &os10, '_') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is10.get(), EOF);
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os10.str().c_str(), "40.00000000");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is10b("40.987654321987654");
    std::ostringstream os10b;
    tvMin = 40.987652;
    EXPECT_EQ(AMUIParseFloat(is10b, &frvMin, &frvMax, &os10b, '_') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is10.get(), EOF);
    EXPECT_FLOAT_EQ(frvMin, tvMin);
    EXPECT_FLOAT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os10b.str().c_str(), "40.98765432");


    std::istringstream is11(".2575E-17H");
    std::ostringstream os11;
    tvMin = .2575e-17;
    EXPECT_EQ(AMUIParseFloat(is11, &frvMin, &frvMax, &os11, '_') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is11.get(), 'H');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMin);
    EXPECT_STREQ(os11.str().c_str(), ".2575E-17");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is12("1??E+5G");
    std::ostringstream os12;
    tvMax = 200.0e+5;
    tvMin = 100.0e+5;
    EXPECT_EQ(AMUIParseFloat(is12, &frvMin, &frvMax, &os12, '?') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is12.get(), 'G');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os12.str().c_str(), "1??E+5");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is13("1??.???e+7 ");
    std::ostringstream os13;
    tvMax = 200.0e7;
    tvMin = 100.0e7;
    EXPECT_EQ(AMUIParseFloat(is13, &frvMin, &frvMax, &os13, '?') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is13.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os13.str().c_str(), "1??.???e+7");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is14("1??.5??E7 ");
    std::ostringstream os14;
    tvMax = 200.0;
    tvMin = 100.0;
    EXPECT_EQ(AMUIParseFloat(is14, &frvMin, &frvMax, &os14, '?') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is14.get(), '5');
    //EXPECT_EQ(AMStreamToLowercaseStripDiaGet(is14), '.');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os14.str().c_str(), "1??");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);


    std::istringstream is15("177.E7 ");
    std::ostringstream os15;
    tvMin = 177.e7;
    tvMax = 177.e7;
    EXPECT_EQ(AMUIParseFloat(is15, &frvMin, &frvMax, &os15, '?') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is15.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os15.str().c_str(), "177E7");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is16("177.E1? ");
    std::ostringstream os16;
    tvMin = 177.e10;
    tvMax = 177.e20;
    EXPECT_EQ(AMUIParseFloat(is16, &frvMin, &frvMax, &os16, '?') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is16.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os16.str().c_str(), "177E1?");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is17("177?E1? ");
    std::ostringstream os17;
    tvMin = 1770e10;
    tvMax = 1780e20;
    EXPECT_EQ(AMUIParseFloat(is17, &frvMin, &frvMax, &os17, '?') , AMUIParseResult::SUCCESS);
    EXPECT_EQ(is17.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os17.str().c_str(), "177?E1?");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

    std::istringstream is18("177?E ");
    std::ostringstream os18;
    tvMin = 1770.0;
    tvMax = 1780.0;
    EXPECT_EQ(AMUIParseFloat(is18, &frvMin, &frvMax, &os18, '?') , AMUIParseResult::PARTIAL);
    EXPECT_EQ(is18.get(), ' ');
    EXPECT_EQ(frvMin, tvMin);
    EXPECT_EQ(frvMax, tvMax);
    EXPECT_STREQ(os18.str().c_str(), "177?E");
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x80000000, (*((unsigned int*)&tvMin))&0x80000000);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x7F800000 >> 23, (*((unsigned int*)&tvMin))&0x7F800000 >> 23);
    EXPECT_EQ((*((unsigned int*)&frvMin))&0x07FFFFF , (*((unsigned int*)&tvMin))&0x07FFFFF);

}

#define CINT(value) (unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -2 + 4]) | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -3 + 4])) << 8 | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -4 + 4])) << 16 | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -5 + 4])) << 24


TEST(AMParsers, enumTypes)
{
    //std::map<std::string, int, std::less<> > table{{"red", 55}, {"green and lime", 57}, {"blue", 58}, {"blur", 59}, {"green and", 60}};
    AMCore::AMTwoWayTable<int, std::string> table;
    table.insert(55, "red");
    table.insert(57, "green and lime");
    table.insert(58, "blue");
    table.insert(59, "blur");
    table.insert(60, "green and");

    std::istringstream is1(" blue ");
    std::ostringstream os1;
    int rv1 = 999;
    EXPECT_EQ(AMUIParseEnum(is1, &rv1, table, &os1), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 58);
    EXPECT_EQ(is1.get(), ' ');
    EXPECT_STREQ(os1.str().c_str(), "blue");

    //one loop takes 417 ns on macbook with i7CPU
    //std::string hh("asdgfasdfgafgdfgfagafsgafgadfgfadghfdagafsgasfg");
    //for(int i=0;i<10000000;i++) {
    //    std::istringstream s(hh);
    //    s.get();
    //}

    std::istringstream is2("Re");
    std::ostringstream os2;
    EXPECT_EQ(AMUIParseEnum(is2, &rv1, table, &os2), AMUIParseResult::PARTIAL);
    EXPECT_EQ(rv1, 55);
    EXPECT_EQ(is2.get(), EOF);
    EXPECT_STREQ(os2.str().c_str(), "Re");

    std::istringstream is3(" bLu ");
    std::ostringstream os3;
    EXPECT_EQ(AMUIParseEnum(is3, &rv1, table, &os3), AMUIParseResult::FAIL);
    EXPECT_EQ(rv1, 55);
    EXPECT_EQ(is3.get(), ' ');
    EXPECT_STREQ(os3.str().c_str(), "bLu");

    std::istringstream is4(" green and ");
    std::ostringstream os4;
    EXPECT_EQ(AMUIParseEnum(is4, &rv1, table, &os4), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 60);
    EXPECT_EQ(is4.get(), EOF);
    EXPECT_STREQ(os4.str().c_str(), "green and");

    std::istringstream is5(" green and l");
    std::ostringstream os5;
    EXPECT_EQ(AMUIParseEnum(is5, &rv1, table, &os5), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 60);
    EXPECT_EQ(is5.get(), EOF);
    EXPECT_STREQ(os5.str().c_str(), "green and l");

    std::istringstream is6(" green and lime");
    std::ostringstream os6;
    EXPECT_EQ(AMUIParseEnum(is6, &rv1, table, &os6), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 57);
    EXPECT_EQ(is6.get(), EOF);
    EXPECT_STREQ(os6.str().c_str(), "green and lime");

    std::string s1("bLue");
    EXPECT_EQ(AMUIParseEnum(s1, &rv1, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 58);

    std::string s2("bL");
    EXPECT_EQ(AMUIParseEnum(s2, &rv1, table), AMUIParseResult::FAIL);
    EXPECT_EQ(rv1, 58);

    std::string s3("re");
    EXPECT_EQ(AMUIParseEnum(s3, &rv1, table), AMUIParseResult::PARTIAL);
    EXPECT_EQ(rv1, 55);

    std::string s4("green and lime");
    EXPECT_EQ(AMUIParseEnum(s4, &rv1, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 57);

    std::string s5("green and");
    EXPECT_EQ(AMUIParseEnum(s5, &rv1, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 60);

    std::string s6("green and l");
    EXPECT_EQ(AMUIParseEnum(s6, &rv1, table), AMUIParseResult::PARTIAL);
    EXPECT_EQ(rv1, 57);

    std::string s7("green an");
    EXPECT_EQ(AMUIParseEnum(s7, &rv1, table), AMUIParseResult::FAIL);
    EXPECT_EQ(rv1, 57);

}

TEST(AMParsers, enumNullableTypes)
{

    //std::map<std::string, int, std::less<> > table{{"red", 55}, {"green and lime", 57}, {"blue", 58}, {"blur", 59}, {"green and", 60}};
    AMCore::AMTwoWayTable<int, std::string> table;
    table.insert(55, "red");
    table.insert(57, "green and lime");
    table.insert(58, "blue");
    table.insert(59, "blur");
    table.insert(60, "green and");

    std::string s1("null");
    AMNullable<int> rv;
    EXPECT_EQ(AMUIParseEnum(s1, &rv, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv.isNull(), true);

    std::string s2("nu");
    EXPECT_EQ(AMUIParseEnum(s2, &rv, table), AMUIParseResult::FAIL);
    EXPECT_EQ(rv.isNull(), true);

    std::string s3("green and lime");
    EXPECT_EQ(AMUIParseEnum(s3, &rv, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv.isNull(), false);
    EXPECT_EQ(rv.get(), 57);

    std::istringstream ss1("null");
    EXPECT_EQ(AMUIParseEnum(ss1, &rv, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv.isNull(), true);

    std::istringstream ss2("nu");
    EXPECT_EQ(AMUIParseEnum(ss2, &rv, table), AMUIParseResult::PARTIAL);
    EXPECT_EQ(rv.isNull(), true);

    std::istringstream ss3("green and lime");
    EXPECT_EQ(AMUIParseEnum(ss3, &rv, table), AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv.isNull(), false);
    EXPECT_EQ(rv.get(), 57);
}

const char* profeseNdxToStr[] = {
    "Řidič",
    "Pekař",
    "Šatnářka",
    "Vedoucí",
    "Účetní",
    "Zedník",
    "Chemik",
    "Programátor",
    "Elektrikář",
    "Instalatér",
    "Prodavač",
    "Lékař",
    "Zdravotní personál"
};

AMCore::AMTwoWayTable<int, AMCore::AMStringCZ> *createEnumProfese()
{
    AMCore::AMTwoWayTable<int, AMCore::AMStringCZ> *tab = new AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>();
    for (int i = 0; i < sizeof(profeseNdxToStr) / sizeof(const char *); i++) {
        tab->insert(i+50, profeseNdxToStr[i]);
    }
    return tab;
}
TEST(AMParsers, enumLine)
{
    AMCore::AMTwoWayTable<int, std::string> table;
    table.insert(55, "red");
    table.insert(57, "green and lime");
    table.insert(58, "blue");
    table.insert(59, "blur");
    table.insert(60, "green and");

    std::map<int, int> rv;
    std::string s1("red, blue, green and");
    EXPECT_EQ(AMUIParseEnumLine(s1, rv, table), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0), Pair(58, 1), Pair(60, 2)));

    std::string s2("re, blue, green and");
    EXPECT_EQ(AMUIParseEnumLine(s2, rv, table), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0), Pair(58, 1), Pair(60, 2)));

    std::string s3("red, blu, green and");
    EXPECT_EQ(AMUIParseEnumLine(s3, rv, table), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0), Pair(60, 1)));

    std::string s4("red, null, green and");
    bool null = false;
    EXPECT_EQ(AMUIParseEnumLine(s4, rv, null, table), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0), Pair(60, 1)));
    EXPECT_EQ(null, true);

    std::string s5("red");
    EXPECT_EQ(AMUIParseEnumLine(s5, rv, table), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0)));

    std::string s6("re");
    EXPECT_EQ(AMUIParseEnumLine(s6, rv, table), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 0)));

    std::string s7("blue,r");
    EXPECT_EQ(AMUIParseEnumLine(s7, rv, table), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(Pair(55, 1), Pair(58, 0)));


    AMCore::AMTwoWayTable<int, AMCore::AMStringCZ> &tablep = *createEnumProfese();
    AMCore::AMStringCZ s8("Řidič");
    EXPECT_EQ(AMUIParseEnumLine(s8, rv, tablep), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(Pair(50, 0)));

    AMCore::AMStringCZ s8b("uc");
    EXPECT_EQ(AMUIParseEnumLine(s8b, rv, tablep), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(Pair(54, 0)));

}

TEST(AMParsers, enumMapLine)
{
    AMCore::AMTwoWayTable<int, std::string> table;
    table.insert(55, "red");
    table.insert(57, "green and lime");
    table.insert(58, "blue");
    table.insert(59, "blur");
    table.insert(60, "green and");

    std::map<int, int> rs;
    std::map<int, std::map<int, int> > rv;
    std::string s1("red, blue ; blur, green and");
    EXPECT_EQ(AMUIParseEnumMapLine(s1, rs, rv, table), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rs, ElementsAre(Pair(55, 0), Pair(60, 2)));
    EXPECT_THAT(rv.begin()->second, ElementsAre(Pair(58, 0), Pair(59, 1)));

    std::string s2("re, blue;blur, green and");
    EXPECT_EQ(AMUIParseEnumMapLine(s2, rs, rv, table), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rs, ElementsAre(Pair(55, 0), Pair(60, 2)));
    EXPECT_THAT(rv.begin()->second, ElementsAre(Pair(58, 0), Pair(59, 1)));

    std::string s3("red, blue ; blur, green and,,,");
    EXPECT_EQ(AMUIParseEnumMapLine(s3, rs, rv, table), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rs, ElementsAre(Pair(55, 0), Pair(60, 2)));
    EXPECT_THAT(rv.begin()->second, ElementsAre(Pair(58, 0), Pair(59, 1)));
}


TEST(AMParsers, expression)
{
    std::istringstream is01("-55");
    std::vector<AMUIParseExpressionElement<std::string> > rv;
    int ef = -100;
    int et = -100;
    EXPECT_EQ(AMUIParseExpression(is01, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("-55"))
        ));
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is02(" > -56");
    EXPECT_EQ(AMUIParseExpression(is02, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("-56"))
        ));
    EXPECT_EQ(ef, 3);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is03(" <= 59");
    EXPECT_EQ(AMUIParseExpression(is03, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::LESSEQUAL, std::string("59"))
        ));
    EXPECT_EQ(ef, 4);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is04(" >= 10 A <= 59");
    EXPECT_EQ(AMUIParseExpression(is04, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MOREEQUAL, std::string("10")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::LESSEQUAL, std::string("59"))
        ));
    EXPECT_EQ(ef, 12);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is05("5nebo6nebo7nebo8");
    EXPECT_EQ(AMUIParseExpression(is05, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("6")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8"))
        ));
    EXPECT_EQ(ef, 15);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is06("5__ nebo 7__");
    EXPECT_EQ(AMUIParseExpression(is06, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5__")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7__"))
        ));
    EXPECT_EQ(ef, 9);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is07("5555789456123 nebo 7__");
    EXPECT_EQ(AMUIParseExpression(is07, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("555578945")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("6123")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("nebo")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7__"))
        ));
    EXPECT_EQ(ef, 9);
    EXPECT_EQ(et, 13);

    rv.clear();
    std::istringstream is08("5555 a (7__ nebo 8__)");
    EXPECT_EQ(AMUIParseExpression(is08, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7__")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8__")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 21);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is09("5555 a (7__ nebo 8__");
    EXPECT_EQ(AMUIParseExpression(is09, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7__")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8__")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 17);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is10("5555 a (7__ )");
    EXPECT_EQ(AMUIParseExpression(is10, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7__")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 13);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is11("5555 a (7_5 )");
    EXPECT_EQ(AMUIParseExpression(is11, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7_")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("5")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 10);
    EXPECT_EQ(et, 11);

    rv.clear();
    std::istringstream is12("5555 anebo (7_ )");
    EXPECT_EQ(AMUIParseExpression(is12, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("anebo")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7_")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 5);
    EXPECT_EQ(et, 10);

    rv.clear();
    std::istringstream is13("5558 a (7 nebo (8 nebo (9 ");
    EXPECT_EQ(AMUIParseExpression(is13, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 26);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is14("5555 <=");
    EXPECT_EQ(AMUIParseExpression(is14, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("<="))
        ));
    EXPECT_EQ(ef, 5);
    EXPECT_EQ(et, 2147483647);

    rv.clear();
    std::istringstream is15("5555) ");
    EXPECT_EQ(AMUIParseExpression(is15, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5555")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string(")"))
        ));
    EXPECT_EQ(ef, 4);
    EXPECT_EQ(et, 5);

    rv.clear();
    std::istringstream is16(" <> 88");
    EXPECT_EQ(AMUIParseExpression(is16, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88"))
            ));
    EXPECT_EQ(ef, 4);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is17("");
    EXPECT_EQ(AMUIParseExpression(is17, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_TRUE(rv.empty() );
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is18("23 nebo 56 nebo");
    ef = -1;
    et = -1;
    EXPECT_EQ(AMUIParseExpression(is18, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("56")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("NEBO"))
                               ));
    EXPECT_EQ(ef, 11);
    EXPECT_EQ(et, 2147483647);

}

TEST(AMParsers, expressionTypes) {
    std::istringstream is01("-55");
    std::vector<AMUIParseExpressionElement<std::string> > rv;
    int ef = -100;
    int et = -100;
    EXPECT_EQ(AMUIParseExpression(is01, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("-55"))
        ));
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is02(" <> 88 a <> null");
    EXPECT_EQ(AMUIParseExpression(is02, rv, AMCore::AMDataType_integer_nullable, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDNULL, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::NOTNULL, std::string(""))
        ));
    EXPECT_EQ(ef, 12);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is03(" 88.7 nebo null");
    EXPECT_EQ(AMUIParseExpression(is03, rv, AMCore::AMDataType_float_nullable, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("88.7")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDNULL, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::ISNULL, std::string(""))
        ));
    EXPECT_EQ(ef, 11);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is04(" = 9__ nebo 2__e4 ");
    EXPECT_EQ(AMUIParseExpression(is04, rv, AMCore::AMDataType::AMDataType_float, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9__")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("2__e4"))
                               ));
    EXPECT_EQ(ef, 18);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is05(" > 9__ ");
    EXPECT_EQ(AMUIParseExpression(is05, rv, AMCore::AMDataType::AMDataType_float, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("9")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("__"))
        ));
    EXPECT_EQ(ef, 4);
    EXPECT_EQ(et, 6);

    rv.clear();
    std::istringstream is06(" > 'ahoj' nebo 'naz'");
    EXPECT_EQ(AMUIParseExpression(is06, rv, AMCore::AMDataType::AMDataType_string, &ef, &et), AMUIParseResult::SUCCESS);
    AMUIParseExpressionElement<std::string> &dd = rv[0];
    //printf(" XXXX ident=%i comboid=%i oper=%i combotab=%i inputsz=%i val=[%s]\n",dd.indent, dd.comboId, dd.operation, dd.comboIdsTab, dd.inputSize, dd.inputValue.c_str());
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("ahoj")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDSTRING, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("naz"))
        ));
    EXPECT_EQ(ef, 20);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is07(" ad nebo 'ma%'");
    EXPECT_EQ(AMUIParseExpression(is07, rv, AMCore::AMDataType::AMDataType_string, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::LIKE, std::string("ad")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDSTRING, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("ma%"))
                               ));
    EXPECT_EQ(ef, 14);
    EXPECT_EQ(et, -1);


    rv.clear();
    std::istringstream is07b(" ad nebo ma%");
    EXPECT_EQ(AMUIParseExpression(is07b, rv, AMCore::AMDataType::AMDataType_string, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::LIKE, std::string("ad")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDSTRING, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::LIKE, std::string("ma%"))
                               ));
    EXPECT_EQ(ef, 9);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is08(" > ad ");
    EXPECT_EQ(AMUIParseExpression(is08, rv, AMCore::AMDataType::AMDataType_string, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("ad"))
        ));
    EXPECT_EQ(ef, 6);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is09(" ad nebo <> null ");
    EXPECT_EQ(AMUIParseExpression(is09, rv, AMCore::AMDataType::AMDataType_string_nullable, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRINGNULL, AMUIParseExpressionElement<std::string>::Operations::LIKE, std::string("ad")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDSTRINGNULL, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRINGNULL, AMUIParseExpressionElement<std::string>::Operations::NOTNULL, std::string(""))
        ));
    EXPECT_EQ(ef, 17);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is10(" > ad_ ");
    EXPECT_EQ(AMUIParseExpression(is10, rv, AMCore::AMDataType::AMDataType_string, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENSTRING, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("ad")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("_"))
        ));
    EXPECT_EQ(ef, 5);
    EXPECT_EQ(et, 6);

    rv.clear();
    ef = -100;
    et = -100;
    std::istringstream is11("<null");
    EXPECT_EQ(AMUIParseExpression(is11, rv, AMCore::AMDataType::AMDataType_string_nullable, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("null"))
                               ));
    EXPECT_EQ(ef, 1);
    EXPECT_EQ(et, 2147483647);

    rv.clear();
    std::istringstream is12("< 2000.1.1 0:0:0");
    EXPECT_EQ(AMUIParseExpression(is12, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::LESS, std::string("2000.1.1 0:0:0"))
        ));
    EXPECT_EQ(ef, 2);
    EXPECT_EQ(et, -1);


    rv.clear();
    std::istringstream is13("= 2000.1.1 __:__:__");
    EXPECT_EQ(AMUIParseExpression(is13, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("2000.1.1 __:__:__"))
                               ));
    EXPECT_EQ(ef, 2);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13.get(), -1);

    rv.clear();
    std::istringstream is13b("=20.1.1 __:");
    EXPECT_EQ(AMUIParseExpression(is13b, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("20.1.1 __:"))
                               ));
    EXPECT_EQ(ef, 1);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13b.get(), -1);

    rv.clear();
    std::istringstream is13c("23");
    EXPECT_EQ(AMUIParseExpression(is13c, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23"))
                               ));
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13c.get(), -1);

    rv.clear();
    std::istringstream is13d("20.1.1 __ ");
    EXPECT_EQ(AMUIParseExpression(is13d, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("20.1.1 __"))
                               ));
    EXPECT_EQ(ef, 10);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13d.get(),-1);

    rv.clear();
    std::istringstream is13e("23 ");
    EXPECT_EQ(AMUIParseExpression(is13e, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23"))
                               ));
    EXPECT_EQ(ef, 3);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13e.get(),-1);

    rv.clear();
    std::istringstream is13f("20.1.1 __ nebo 12");
    EXPECT_EQ(AMUIParseExpression(is13f, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("20.1.1 __")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("12"))
                               ));
    EXPECT_EQ(ef, 15);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13f.get(),-1);

    rv.clear();
    std::istringstream is13g("23 nebo >56");
    EXPECT_EQ(AMUIParseExpression(is13g, rv, AMCore::AMDataType::AMDataType_datetime, &ef, &et), AMUIParseResult::PARTIAL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("56"))
                               ));
    EXPECT_EQ(ef, 9);
    EXPECT_EQ(et, -1);
    EXPECT_EQ(is13g.get(),-1);

    rv.clear();
    std::istringstream is14("> 25.12.2017");
    EXPECT_EQ(AMUIParseExpression(is14, rv, AMCore::AMDataType::AMDataType_date, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MORE, std::string("25.12.2017"))
        ));
    EXPECT_EQ(ef, 2);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is15(">= 14:30:00 a <= 15:0:0");
    EXPECT_EQ(AMUIParseExpression(is15, rv, AMCore::AMDataType::AMDataType_time, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::MOREEQUAL, std::string("14:30:00")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::LESSEQUAL, std::string("15:0:0"))
                               ));
    EXPECT_EQ(ef, 17);
    EXPECT_EQ(et, -1);

    rv.clear();
    std::istringstream is16("__.12.2017 nebo null");
    EXPECT_EQ(AMUIParseExpression(is16, rv, AMCore::AMDataType::AMDataType_date_nullable, &ef, &et), AMUIParseResult::SUCCESS);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("__.12.2017")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODDNULL, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVENNULL, AMUIParseExpressionElement<std::string>::Operations::ISNULL, std::string(""))
                               ));
    EXPECT_EQ(ef, 16);
    EXPECT_EQ(et, -1);


    rv.clear();
    std::istringstream is17("2017 nebo ()");
    EXPECT_EQ(AMUIParseExpression(is17, rv, AMCore::AMDataType::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("2017")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string(")")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));
    EXPECT_EQ(ef, 11);
    EXPECT_EQ(et, 12);


    rv.clear();
    std::istringstream is18("nebo 23");
    EXPECT_EQ(AMUIParseExpression(is18, rv, AMCore::AMDataType::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("nebo")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23"))
                               ));
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, 4);
}

TEST(AMParsers, expressionUpdate)
{
    //"5558 a (7 nebo (8 nebo (9)nebo 5)) "
    std::vector<AMUIParseExpressionElement<std::string> > t01 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        };
    AMUIUpdateExpression(t01, AMCore::AMDataType::AMDataType_integer, 8, AMUIParseExpressionElement<std::string>::Operations::EQUAL);
    EXPECT_THAT(t01, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("0")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        ));

    //"5558 a (7 nebo (8 nebo (9)nebo 5)) "
    std::vector<AMUIParseExpressionElement<std::string> > t02 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
    };
    AMUIUpdateExpression(t02, AMCore::AMDataType::AMDataType_integer, 8, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t02, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
                                ));


    //"5558 a (7 nebo (8 nebo (9)nebo 5)) "
    std::vector<AMUIParseExpressionElement<std::string> > t03 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
    };
    AMUIUpdateExpression(t03, AMCore::AMDataType::AMDataType_integer, 10, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t03, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
                                ));

    //"5558 a (7 nebo (8 nebo (9)nebo 5)) "
    std::vector<AMUIParseExpressionElement<std::string> > t04 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
    };
    AMUIUpdateExpression(t04, AMCore::AMDataType::AMDataType_integer, 10, AMUIParseExpressionElement<std::string>::Operations::AND);
    EXPECT_THAT(t04, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5558")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("7")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("8")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("9")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("0")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(3, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("5")),
        AMUIParseExpressionElement(2, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
                                ));

    //std::istringstream is02(" <> 88 a <> 96 nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t05 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t05, AMCore::AMDataType::AMDataType_integer, 2, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t05, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
                                ));

    //std::istringstream is02(" <> 88 a <> 96 nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t06 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t06, AMCore::AMDataType::AMDataType_integer, 0, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t06, ElementsAre(
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
                                ));

    //std::istringstream is02(" <> 88 a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t07 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t07, AMCore::AMDataType::AMDataType_integer, 3, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t07, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        //AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));

    //std::istringstream is02(" <> 88 a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t08 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t08, AMCore::AMDataType::AMDataType_integer, 0, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t08, ElementsAre(
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));

    //std::istringstream is02("( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t09 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t09, AMCore::AMDataType::AMDataType_integer, 1, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t09, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));

    //std::istringstream is02(" <> 88 a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t10 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t10, AMCore::AMDataType::AMDataType_integer, 0, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN);
    EXPECT_THAT(t10, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));

    //std::istringstream is02(" <> 88 a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t11 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t11, AMCore::AMDataType::AMDataType_integer, 1, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t11, ElementsAre(
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
                                ));

    //std::istringstream is02(" <> 88 a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t12 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t12, AMCore::AMDataType::AMDataType_integer, 5, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t12, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
                                ));

    //std::istringstream is02(" (<> 88) a ( <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t13 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t13, AMCore::AMDataType::AMDataType_integer, 5, AMUIParseExpressionElement<std::string>::Operations::NOTHING);
    EXPECT_THAT(t13, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        //AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
                                ));

    //std::istringstream is02(" (<> 88 a <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t14 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIUpdateExpression(t14, AMCore::AMDataType::AMDataType_integer, 2, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED);
    EXPECT_THAT(t14, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96"))
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        //AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));
    std::vector<AMUIParseExpressionElement<std::string> > t15 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23")),
    };
    AMUIUpdateExpression(t15, AMCore::AMDataType::AMDataType_integer, 0, AMUIParseExpressionElement<std::string>::Operations::NOTHING, "");
    EXPECT_THAT(t15, ElementsAre(
                                ));
}

TEST(AMParsers, expressionInsert)
{
//std::istringstream is02(" (<> 88 a <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t1 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIInsertExpression(t1, AMCore::AMDataType::AMDataType_integer, 7);
    EXPECT_THAT(t1, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("0"))
        ));

    //std::istringstream is02(" (<> 88 a <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t2 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIInsertExpression(t2, AMCore::AMDataType::AMDataType_integer, 0);
    EXPECT_THAT(t2, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("0")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));

    //std::istringstream is02(" (<> 88 a <> 96) nebo <> 101");
    std::vector<AMUIParseExpressionElement<std::string> > t3 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
    };
    AMUIInsertExpression(t3, AMCore::AMDataType::AMDataType_integer, 4);
    EXPECT_THAT(t3, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("88")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("96")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("0")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::NOTEQUAL, std::string("101"))
        ));
}

TEST(AMParsers, expressionRepair)
{
    std::vector<AMUIParseExpressionElement<std::string> > t01 = {
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::INVALID, AMUIParseExpressionElement<std::string>::Operations::INVALID, std::string("")),
    };
    AMUIRepairExpression(t01, AMCore::AMDataType::AMDataType_integer);
    EXPECT_THAT(t01, ElementsAre(
                               ));


    std::vector<AMUIParseExpressionElement<std::string> > rv;
    std::istringstream is01("23 nebo nebo");
    int ef = -100;
    int et = -100;
    EXPECT_EQ(AMUIParseExpression(is01, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    AMUIRepairExpression(rv, AMCore::AMDataType_integer);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23"))
                               ));
    EXPECT_EQ(ef, 8);
    EXPECT_EQ(et, INT32_MAX);

    std::istringstream is02("23 a (27 56");
    ef = -100;
    et = -100;
    rv.clear();
    EXPECT_EQ(AMUIParseExpression(is02, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    AMUIRepairExpression(rv, AMCore::AMDataType_integer);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::AND, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::BRACEOPEN, std::string("")),
        AMUIParseExpressionElement(1, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("27")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::BRACECLOSED, std::string(""))
                               ));
    EXPECT_EQ(ef, 9);
    EXPECT_EQ(et, INT32_MAX);

    std::istringstream is03("qwerqw ewq 23");
    ef = -100;
    et = -100;
    rv.clear();
    EXPECT_EQ(AMUIParseExpression(is03, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    AMUIRepairExpression(rv, AMCore::AMDataType_integer);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("23"))
                               ));
    EXPECT_EQ(ef, 0);
    EXPECT_EQ(et, 6);

    std::istringstream is04("11 22 33");
    ef = -100;
    et = -100;
    rv.clear();
    EXPECT_EQ(AMUIParseExpression(is04, rv, AMCore::AMDataType_integer, &ef, &et), AMUIParseResult::FAIL);
    AMUIRepairExpression(rv, AMCore::AMDataType_integer);
    EXPECT_THAT(rv, ElementsAre(
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("11")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::ODD, AMUIParseExpressionElement<std::string>::Operations::OR, std::string("")),
        AMUIParseExpressionElement(0, AMUIParseExpressionElement<std::string>::ComboId::EVEN, AMUIParseExpressionElement<std::string>::Operations::EQUAL, std::string("33"))
                               ));
    EXPECT_EQ(ef, 3);
    EXPECT_EQ(et, 5);
}

TEST(AMParsers, enumsAdvanced)
{
    //std::map<std::string, int, std::less<> > table{{"red", 55}, {"green and lime", 57}, {"blue", 58}, {"blur", 59}, {"green and", 60}};
    AMCore::AMTwoWayTable<int, std::string> table;
    table.insert(55, "red");
    table.insert(57, "green and lime");
    table.insert(58, "blue");
    table.insert(59, "blur");
    table.insert(60, "green and");

    int rv1;
    AMUIParseResult rs;
    std::string os;

    std::string s1("green and lime or even pink ");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s1, &rv1, table, false, &os);
    EXPECT_EQ(rs, AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 57);
    EXPECT_STREQ(os.c_str(), "or even pink ");

    std::string s2(" green and lime or even pink ");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s2, &rv1, table, false, &os);
    EXPECT_EQ(rs, AMUIParseResult::FAIL);
    EXPECT_EQ(rv1, 999);
    EXPECT_STREQ(os.c_str(), "or even pink ");

    std::string s4("green and p");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s4, &rv1, table, false, &os);
    EXPECT_EQ(rs, AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 60);
    EXPECT_STREQ(os.c_str(), "p");

    std::string s5("rex ");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s5, &rv1, table, false, &os);
    EXPECT_EQ(rs, AMUIParseResult::FAIL);
    EXPECT_EQ(rv1, 999);
    EXPECT_STREQ(os.c_str(), "p");

    std::string s6("re ");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s6, &rv1, table, false, &os);
    EXPECT_EQ(rs, AMUIParseResult::PARTIAL);
    EXPECT_EQ(rv1, 55);
    EXPECT_STREQ(os.c_str(), "");

    std::string s7("lime and red");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s7, &rv1, table, true, &os);
    EXPECT_EQ(rs, AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 55);
    EXPECT_STREQ(os.c_str(), "lime and");

    std::string s8("lime and green and lime");
    rv1 = 999;
    rs = AMUIParseEnumStartsWith(s8, &rv1, table, true, &os);
    EXPECT_EQ(rs, AMUIParseResult::SUCCESS);
    EXPECT_EQ(rv1, 57);
    EXPECT_STREQ(os.c_str(), "lime and");

}

//std::map<std::string, int, std::less<> > dg_bef {{"Ing", 20}, {"Mgr", 21}, {"Phdr", 22}};
//std::map<std::string, int, std::less<> > dg_aft {{"Csc", 30}, {"PhD", 31}, {"MBA", 32}};

AMCore::AMTwoWayTable<int, std::string> dg_bef;
AMCore::AMTwoWayTable<int, std::string> dg_aft;
std::string empty_str = "";
class tname: public AMCore::AMNameBase<int, std::string>
{
public:
    tname()
        :AMCore::AMNameBase<int, std::string>()
    {
        if (g_instances == 1) {
            _refreshDegreeTables();
        }
    }
protected:
    void _refreshDegreeTables() override
    {
        dg_bef.insert(20, "Ing");
        dg_bef.insert(21, "Mgr");
        dg_bef.insert(24, "PhdrA");
        dg_bef.insert(22, "Phdr");
        dg_bef.insert(23, "");
        dg_aft.insert(30, "Csc");
        dg_aft.insert(31, "PhD");
        dg_aft.insert(32, "MBA");
        dg_aft.insert(33, "");

        g_degreeBeforeTable = &dg_bef;
        g_degreeAfterTable = &dg_aft;

    }
    void _discardDegreeTables() override
    {

    }
    //static std::map<std::string, int, std::less<> > AMUIAddressBaseEmptyTable
};

namespace AMUI {
    int DEFAULT_INT_ID = -99999;
}

TEST(AMParsers, names)
{
    std::string s1 = "Franta Jetel";
    tname nm;
    AMUIParseResult res = AMUIParseName<TESTEnum>(s1, &nm);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Franta");
    EXPECT_STREQ(nm.surname.c_str(), "Jetel");

    std::string s2 = "Ing Franta Jetel MBA";
    res = AMUIParseName<TESTEnum>(s2, &nm);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 20);
    EXPECT_EQ(nm.degreeAfter, 32);
    EXPECT_STREQ(nm.name.c_str(), "Franta");
    EXPECT_STREQ(nm.surname.c_str(), "Jetel");

    std::string s3 = "Ing Franta Jetel, MBA";
    res = AMUIParseName<TESTEnum>(s3, &nm);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 20);
    EXPECT_EQ(nm.degreeAfter, 32);
    EXPECT_STREQ(nm.name.c_str(), "Franta");
    EXPECT_STREQ(nm.surname.c_str(), "Jetel");

    std::string s4 = "Inc Franta Jetel, MBA";
    res = AMUIParseName<TESTEnum>(s4, &nm);
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_EQ(nm.degreeBefore, 20);
    EXPECT_EQ(nm.degreeAfter, 32);
    EXPECT_STREQ(nm.name.c_str(), "Franta");
    EXPECT_STREQ(nm.surname.c_str(), "Jetel");

    std::string s5 = "Inc Franta  Jetel, MBA";
    res = AMUIParseName<TESTEnum>(s5, &nm);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 32);
    EXPECT_STREQ(nm.name.c_str(), "Inc Franta");
    EXPECT_STREQ(nm.surname.c_str(), "Jetel");

    std::string s6 = "mgr Frantík";
    res = AMUIParseName<TESTEnum>(s6, &nm);
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "Frantík");

    std::string s7 = "mg Frantík Jetelů";
    res = AMUIParseName<TESTEnum>(s7, &nm);
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");

    std::string s8 = "mg Frantík22 Jetelů";
    res = AMUIParseName<TESTEnum>(s8, &nm);
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");

    std::string s9 = "mgr Frantík Franz  Jetelů";
    res = AMUIParseName<TESTEnum>(s9, &nm);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík Franz");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");

    std::string s10a = "mgr Frantík_% Franz  Jetelů";
    res = AMUIParseName<TESTEnum>(s10a, &nm);
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík Franz");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");

    std::string s10b = "mgr Frantík_% Franz  Jetelů";
    res = AMUIParseName<TESTEnum>(s10b, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 21);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík_% Franz");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");

    std::string s11 = "Phdr Frantík_% Franz  Jetelů";
    res = AMUIParseName<TESTEnum>(s11, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 22);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Frantík_% Franz");
    EXPECT_STREQ(nm.surname.c_str(), "Jetelů");


    std::string s11b = "PhdrA Jasánek";
    res = AMUIParseName<TESTEnum>(s11b, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 24);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "Jasánek");

    std::string s12 = "Mat Animák";
    res = AMUIParseName<TESTEnum>(s12, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Mat");
    EXPECT_STREQ(nm.surname.c_str(), "Animák");

    std::string s13 = "je";
    res = AMUIParseName<TESTEnum>(s13, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "je");

    std::string s14 = "ma";
    res = AMUIParseName<TESTEnum>(s14, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "ma");

    //prefix of many options
    std::string s15 = "P";
    res = AMUIParseName<TESTEnum>(s15, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "P");

    //prefix of many options
    std::string s20 = "Pa";
    res = AMUIParseName<TESTEnum>(s20, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "Pa");

    //prefix of one option
    std::string s16 = "m";
    res = AMUIParseName<TESTEnum>(s16, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "m");



    std::string s17 = "raf";
    res = AMUIParseName<TESTEnum>(s17, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "raf");

    std::string s18 = "mu";
    res = AMUIParseName<TESTEnum>(s18, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "mu");

    std::string s19 = "";
    res = AMUIParseName<TESTEnum>(s19, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "mu");

    std::string s21a = "Ing";
    res = AMUIParseName<TESTEnum>(s21a, &nm, '\0');
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_EQ(nm.degreeBefore, 20);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "");

    std::string s21 = "% Animák";
    res = AMUIParseName<TESTEnum>(s21, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    //EXPECT_EQ(nm.degreeBefore, 23);
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "Animák");

    std::string s22 = "% Pat %";
    res = AMUIParseName<TESTEnum>(s22, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_TRUE(nm.degreeBefore.isNull());
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "Pat");
    EXPECT_STREQ(nm.surname.c_str(), "");

    std::string s23 = "% % % MBA";
    res = AMUIParseName<TESTEnum>(s23, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_TRUE(nm.degreeBefore.isNull());
    EXPECT_EQ(nm.degreeAfter, 32);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "");

    std::string s24 = "% % Animák";
    res = AMUIParseName<TESTEnum>(s24, &nm, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_TRUE(nm.degreeBefore.isNull());
    EXPECT_EQ(nm.degreeAfter, 33);
    EXPECT_STREQ(nm.name.c_str(), "");
    EXPECT_STREQ(nm.surname.c_str(), "Animák");

}

TEST(AMParsers, authortag)
{
    AMAuthorTag<std::string> at;
    std::string s1("2002.9.15 11:11:11 Aleš Voborník");
    AMUIParseResult res = AMUIParseAuthorTag(s1, &at);
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_STREQ(at.author.c_str(), "Aleš Voborník");
    EXPECT_STREQ(at.time.c_str(), "2002.9.15 11:11:11");

    std::string s2a("2002.9.15 11:11:__ Aleš Voborník");
    res = AMUIParseAuthorTag(s2a, &at);
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_STREQ(at.author.c_str(), "");
    EXPECT_STREQ(at.time.c_str(), "");

    std::string s2b("2002.9.15 11:11:__ Aleš Voborník");
    res = AMUIParseAuthorTag(s2b, &at, '_');
    EXPECT_EQ(res, AMUIParseResult::SUCCESS);
    EXPECT_STREQ(at.author.c_str(), "Aleš Voborník");
    EXPECT_STREQ(at.time.c_str(), "2002.9.15 11:11:__");

    std::string s3("Aleš Voborník");
    res = AMUIParseAuthorTag(s3, &at);
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_STREQ(at.author.c_str(), "Aleš Voborník");
    EXPECT_STREQ(at.time.c_str(), "");

    std::string s4("2002.9.15 11:11:11");
    res = AMUIParseAuthorTag(s4, &at);
    EXPECT_EQ(res, AMUIParseResult::PARTIAL);
    EXPECT_STREQ(at.author.c_str(), "");
    EXPECT_STREQ(at.time.c_str(), "2002.9.15 11:11:11");


    std::string s5("k");
    res = AMUIParseAuthorTag(s5, &at);
    EXPECT_EQ(res, AMUIParseResult::FAIL);
    EXPECT_STREQ(at.author.c_str(), "");
    EXPECT_STREQ(at.time.c_str(), "");
}

TEST(AMParsers, address)
{
    std::string s1("Robotea: Taussigova 1153/25, Praha 8, 182 00, Česko");
    AMCore::AMAddress<std::string> r;
    EXPECT_EQ(AMUIParseAddress(s1, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");


    std::string s2a("Robotea: Taussigova 1153/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s2a, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s2b("Robotea: Taussigova 1153/25, Praha 8");
    EXPECT_EQ(AMUIParseAddress(s2b, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "");
    EXPECT_STREQ(r.country.c_str(), "");

    std::string s2c("Robotea: Taussigova 1153/25, Praha 8,,");
    EXPECT_EQ(AMUIParseAddress(s2c, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "");
    EXPECT_STREQ(r.country.c_str(), "");

    std::string s3a("Robotea: Taussigova 1153, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s3a, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s3b("Taussigova 1153/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s3b, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s4a("Robotea: Taussigova /25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s4a, &r), AMUIParseResult::PARTIAL);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 9999);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");


    std::string s4b("Robotea: 1153/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s4b, &r), AMUIParseResult::PARTIAL);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s4c("Robotea: Taussigova 1153/25, , 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s4c, &r), AMUIParseResult::PARTIAL);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s5a("Taussigova 1153, Praha - Kobylisy  ");
    EXPECT_EQ(AMUIParseAddress(s5a, &r), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "");
    EXPECT_STREQ(r.city.c_str(), "Praha - Kobylisy");
    EXPECT_STREQ(r.zip.c_str(), "");
    EXPECT_STREQ(r.country.c_str(), "");

    std::string s5b("Taussigova /25d, Praha");
    EXPECT_EQ(AMUIParseAddress(s5b, &r), AMUIParseResult::PARTIAL);
    EXPECT_STREQ(r.place.c_str(), "");
    EXPECT_STREQ(r.street.c_str(), "Taussigova");
    EXPECT_EQ(r.parcelNum, 9999);
    EXPECT_STREQ(r.orientationNum.c_str(), "25d");
    EXPECT_STREQ(r.city.c_str(), "Praha");
    EXPECT_STREQ(r.zip.c_str(), "");
    EXPECT_STREQ(r.country.c_str(), "");

    std::string s6("Robotea: Taussigova 1153k/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s6, &r), AMUIParseResult::PARTIAL);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova 1153k");
    EXPECT_EQ(r.parcelNum, 9999);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s7a("Robotea: Taussigova_ 1153k/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s7a, &r), AMUIParseResult::FAIL);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova 1153k");
    EXPECT_EQ(r.parcelNum, 9999);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");

    std::string s7b("Robotea: Taussigova__ 1153/25, Praha 8, 182 00, Česko");
    EXPECT_EQ(AMUIParseAddress(s7b, &r, '_'), AMUIParseResult::SUCCESS);
    EXPECT_STREQ(r.place.c_str(), "Robotea");
    EXPECT_STREQ(r.street.c_str(), "Taussigova__");
    EXPECT_EQ(r.parcelNum, 1153);
    EXPECT_STREQ(r.orientationNum.c_str(), "25");
    EXPECT_STREQ(r.city.c_str(), "Praha 8");
    EXPECT_STREQ(r.zip.c_str(), "182 00");
    EXPECT_STREQ(r.country.c_str(), "Česko");
}

TEST(AMParsers, splitenum)
{
    std::string s1a("abcd ");
    int num = -9999;
    std::string rv = AMUISplitStringIntoEnumvalAndId(s1a, &num);
    EXPECT_STREQ(rv.c_str(), "abcd");
    EXPECT_EQ(num, AMUI::DEFAULT_INT_ID);

    std::string s1b("mnbv");
    rv = AMUISplitStringIntoEnumvalAndId(s1b, &num);
    EXPECT_STREQ(rv.c_str(), "mnbv");
    EXPECT_EQ(num, AMUI::DEFAULT_INT_ID);

    std::string s2a("abcd (");
    rv = AMUISplitStringIntoEnumvalAndId(s2a, &num);
    EXPECT_STREQ(rv.c_str(), "abcd");
    EXPECT_EQ(num, AMUI::DEFAULT_INT_ID);

    std::string s2b("mnbv(");
    rv = AMUISplitStringIntoEnumvalAndId(s2b, &num);
    EXPECT_STREQ(rv.c_str(), "mnbv");
    EXPECT_EQ(num, AMUI::DEFAULT_INT_ID);

    std::string s3a("abcd (8");
    rv = AMUISplitStringIntoEnumvalAndId(s3a, &num);
    EXPECT_STREQ(rv.c_str(), "abcd");
    EXPECT_EQ(num, 8);

    std::string s3b("mnbv(9");
    rv = AMUISplitStringIntoEnumvalAndId(s3b, &num);
    EXPECT_STREQ(rv.c_str(), "mnbv");
    EXPECT_EQ(num, 9);

    std::string s4a("abcd (56)");
    rv = AMUISplitStringIntoEnumvalAndId(s4a, &num);
    EXPECT_STREQ(rv.c_str(), "abcd");
    EXPECT_EQ(num, 56);

    std::string s4b("mnbv(84)");
    rv = AMUISplitStringIntoEnumvalAndId(s4b, &num);
    EXPECT_STREQ(rv.c_str(), "mnbv");
    EXPECT_EQ(num, 84);
}


int main(int argc, char **argv) {

    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

int defaultTimeOffset = 3600;//sec - GMT+1int defaultTimeOffset = 3600;//sec - GMT+1