//
// Created by <PERSON><PERSON><PERSON> on 16.8.23.
//

#ifndef AMDIALOGS_TEST_TWOWAYTABLE_CPP
#define AMDIALOGS_TEST_TWOWAYTABLE_CPP

//
// Created by z<PERSON><PERSON> on 1.2.23.
//
#include <functional>
#include <cassert>
#include "amcore/AMTwoWayTable.h"

typedef int TESTEnum;
typedef std::string TESTString;

namespace AMUI {
    class AMUIController;
}
namespace AMDialogs {

    int IDegreeBeforeTableId = 3;
    int IDegreeAfterTableId = 4;
    std::function<AMUI::AMUIController *()> IDegreeBeforeController = []{return nullptr;};
    std::function<AMUI::AMUIController *()> IDegreeAfterController = []{return nullptr;};



    template<typename TEnumType, typename TStringType>
    AMCore::AMTwoWayTable<TEnumType, TStringType> *twoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint, std::chrono::time_point<std::chrono::steady_clock> *clock)
    {

        return nullptr;
    }

    template AMCore::AMTwoWayTable<TESTEnum, TESTString> *twoWayTable<TESTEnum, TESTString>(int,const  std::string&, std::string flashTimepoint, std::chrono::time_point<std::chrono::steady_clock> *);



    template<typename TEnumType, typename TStringType>
    void prepareTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint)
    {
    }

    template void prepareTwoWayTable<TESTEnum, TESTString>(int index, const std::string&, std::string flashTimepoint);

    template<typename TEnumType, typename TStringType>
    void destroyTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint)
    {
    }

    template void destroyTwoWayTable<TESTEnum, TESTString>(int, const std::string&, std::string flashTimepoint);

    template<typename TEnumType, typename TStringType>
    void lockTwoWayTable(int index)
    {
    }

    template void lockTwoWayTable<TESTEnum, TESTString>(int);

    template<typename TEnumType, typename TStringType>
    void unlockTwoWayTable(int index)
    {
    }

    template void unlockTwoWayTable<TESTEnum, TESTString>(int);

    template<typename TEnumType, typename TStringType>
    void twoWayTableWasUpdated(int index, const std::string &strVoidParam, std::string flashTimepoint)
    {
    }

    template void twoWayTableWasUpdated<TESTEnum, TESTString>(int, const std::string&, std::string flashTimepoint);

    template<typename TEnumType, typename TStringType>
    void refreshTwoWayTable(int index, const std::string &strVoidParam, std::string flashTimepoint)
    {
    }

    template void refreshTwoWayTable<TESTEnum, TESTString>(int, const std::string&, std::string flashTimepoint);
}

#endif //AMDIALOGS_TEST_TWOWAYTABLE_CPP
