//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTBUTTON_H
#define SAW_ALL_AMUIFORMELEMENTBUTTON_H

#include "amcore/AMAssert.h"
#include <functional>
#include "AMUIFormElement.h"
#include "amui/AMUIIView.h"

namespace AMDialogs {

    class AMUIFormElementButton : public AMUIFormElement {


    public:

        AMUIFormElementButton(const std::string key = "");

        ~AMUIFormElementButton();

        void init(std::function<void(int)> callback, int buttonId = 0);

        void reset() override;

        void updateState() override;

        AMCore::AMDataType type() const override { return AMCore::AMDataType_void;};

        void render(bool formGrayed, int element_id) override;

        std::string valueAsString() override {return m_value;};
        void setValueString(std::string value) override {m_value = value;};

        void setId(int buttonId) {m_buttonId = buttonId;};

        ImVec2 onclickOrigin() {return m_onclickOrigin;}
        ImVec2 onclickSize() {return  m_onclickSize;}
    protected:
        std::function<void(int)> m_customCallback;
        int m_buttonId;
        std::string m_value;
        ImVec2 m_onclickOrigin;
        ImVec2 m_onclickSize;
    };

}

//#include "src/AMUIFormElementButton.cpp"

#endif //SAW_ALL_AMUIFORMELEMENTBUTTON_H
