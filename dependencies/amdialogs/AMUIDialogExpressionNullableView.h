//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGEXPRESSIONNULLABLEVIEW_H
#define SAW_AMUIDIALOGEXPRESSIONNULLABLEVIEW_H

#include "AMUIDialogExpressionView.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogExpressionNullableView: public AMDialogs::AMUIDialogExpressionView<TStringType>
    {
    public:
        AMUIDialogExpressionNullableView();
        bool renderNullable() override;

    protected:

    };

} // AMDialogs

#include "src/AMUIDialogExpressionNullableView.hpp"

#endif //SAW_AMUIDIALOGEXPRESSIONNULLABLEVIEW_H
