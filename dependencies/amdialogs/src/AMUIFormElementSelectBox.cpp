//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementSelectBox.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {


    AMUIFormElementSelectBox::AMUIFormElementSelectBox(const char *key)
        : AMUIFormElement(key),
          m_value(0),
          m_items(nullptr)
    {
    }

    void AMUIFormElementSelectBox::init(const std::vector<std::string> &items)
    {
        m_length = 1;
        m_items = &items;
    }

    AMUIFormElementSelectBox::~AMUIFormElementSelectBox()
    {
    }

    void AMUIFormElementSelectBox::reset()
    {
        m_value = 0;
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    void AMUIFormElementSelectBox::updateState()
    {
        AMUIFormElement::updateState();
    }

    void AMUIFormElementSelectBox::setValue(int value)
    {
        m_value = value;
        updateState();
    }

    void AMUIFormElementSelectBox::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        //int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        //const float button_size = ImGui::GetFrameHeight();
        const float max_width = ImGui::GetContentRegionAvail().x;
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        char label[128];
        snprintf(label, sizeof(label) - 1, "##elselectbox_%i", element_id);
        const char* combo_preview_value =  (m_value < 0 || m_value >= m_items->size()) ? "" : m_items->at(m_value).c_str();  // Pass in the preview value visible before opening the combo (it could be anything)
        if (ImGui::BeginCombo(label, combo_preview_value, 0))
        {
            for (int n = 0; n < m_items->size(); n++)
            {
                const bool is_selected = (m_value == n);
                if (ImGui::Selectable(m_items->at(n).c_str(), is_selected)) {
                    m_value = n;
                    wasModified();
                }

                // Set the initial focus when opening the combo (scrolling + keyboard navigation focus)
                if (is_selected)
                    ImGui::SetItemDefaultFocus();
            }
            ImGui::EndCombo();
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }
    }

    void AMUIFormElementSelectBox::loadDefault() {
        setValue(0);
    }
}
