//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#include "../AMUIFormElementEnum.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
//#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"
#include "amui/AMUIApp.h"
#include "amdialogs/AMUIEnum.h"
#include "ameliteui/AMEliteResources.h"

namespace AMDialogs {

    //const int FORM_ELEMENT_DATETIME_BUFFER_SIZE = 48;

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnum<TEnumType, TStringType>::AMUIFormElementEnum(const std::string key, bool mandatory , bool nullable, bool hidden)
        : AMUIFormElement(key, mandatory, nullable, hidden),
        m_valueIndex(AMUI::DEFAULT_INT_ID),
        m_valueValue(AMUI::DEFAULT_INT_ID),
        m_enumId(-1),
        m_icon(-1),
        m_focused(false),
        m_updateFromDialog(false),
        m_titles(),
        m_ids(),
        m_tooltips(),
        m_tableTime(std::chrono::time_point<std::chrono::steady_clock>::max()),
        m_controllerFactory(),
        m_controller(nullptr),
        m_parentController(nullptr),
        m_parentMaximized(false),
        m_strVoidParam(),
        m_flashbackTimepoint()
    {
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::init(int enumId, const std::string &strVoidParam)
    {
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_enumId = enumId;
        m_strVoidParam = strVoidParam;
        if (m_enumId >= 0) {
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_tableTime = std::chrono::time_point<std::chrono::steady_clock>::max();
        if (m_icon == -1) {
            bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
                MEMIMG(Login_png), &m_icon, nullptr, nullptr
                                                                           );
            AMAssert(rv);
        }
        load();
    }

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnum<TEnumType, TStringType>::~AMUIFormElementEnum()
    {
        if (m_controller) {
            if(std::find(AMUI::AMUIApp::instance().controllers().begin(), AMUI::AMUIApp::instance().controllers().end(), m_controller) != AMUI::AMUIApp::instance().controllers().end()) {
                std::function<void(AMUI::AMUIController *)> f;
                m_controller->setCallbackOnDestroy(f);
            }
        }
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        if (m_icon != -1) {
            AMEliteUI::AMEliteImageManager::unloadTexture(m_icon);
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::reset()
    {
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_ids.size()) {
            m_valueIndex = 0;
            m_state = AMUIWidgetState::FILLED;
        } else {
            m_valueIndex = AMUI::DEFAULT_INT_ID;
            m_state = AMUIWidgetState::EMPTY;
        }
        m_tableTime = std::chrono::time_point<std::chrono::steady_clock>::max();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::updateState()
    {
        AMUIFormElement::updateState();
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_ids.size() && m_valueIndex != AMUI::DEFAULT_INT_ID) {
            m_state = AMUIWidgetState::FILLED;
        } else {
            m_state = AMUIWidgetState::EMPTY;
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setValue(TEnumType value)
    {
        m_valueValue = value;
        auto it = std::find(m_ids.cbegin(), m_ids.cend(), value);
        if (it != m_ids.cend()) {
            m_valueIndex = it - m_ids.cbegin();
        } else {
            m_valueIndex = AMUI::DEFAULT_INT_ID;
        }
        m_tableTime = std::chrono::time_point<std::chrono::steady_clock>::max();
        updateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::render(bool formGrayed, int element_id)
    {
        if (m_hidden) {
            return;
        }
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        const float button_size = ImGui::GetFrameHeight();

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width + (m_controllerFactory ? - button_size - style.ItemInnerSpacing.x : 0.0f)));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        if (m_valueIndex >= 0 && m_valueIndex < m_ids.size()) {
            drawIconFE(m_ids[m_valueIndex], bgWidgetColors[(int) state]);
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elenum_%i", element_id);
        const char* combo_preview_value =  (m_valueIndex < 0 || m_valueIndex >= m_titles.size()) ? "" : m_titles[m_valueIndex].c_str();  // Pass in the preview value visible before opening the combo (it could be anything)
        bool bHover = false;
        if (ImGui::BeginCombo(label, combo_preview_value, 0))
        {
            for (int n = 0; n < m_titles.size(); n++)
            {
                drawIcon(m_ids[n]);
                const bool is_selected = (m_valueIndex == n);
                char sl[256];
                snprintf(sl, sizeof(sl) - 1, "%s%s--sel", m_titles[n].c_str(), label);
                if (ImGui::Selectable(sl, is_selected)) {
                    m_valueIndex = n;
                    m_valueValue = m_ids[n];
                    wasModified();
                }
                if (m_tooltips.size() && !bHover && !m_tooltips[n].empty() && ImGui::IsItemHovered(ImGuiHoveredFlags_AllowWhenDisabled))
                {
                    ImGui::SetTooltip("%s", m_tooltips[n].c_str());
                    bHover = true;
                }

                // Set the initial focus when opening the combo (scrolling + keyboard navigation focus)
                if (is_selected)
                    ImGui::SetItemDefaultFocus();
            }
            ImGui::EndCombo();
        }
        if (!bHover && m_valueIndex >= 0 && m_valueIndex < m_tooltips.size() && !m_tooltips[m_valueIndex].empty() && ImGui::IsItemHovered(ImGuiHoveredFlags_AllowWhenDisabled))
        {
            ImGui::SetTooltip("%s", m_tooltips[m_valueIndex].c_str());
        }

        ImGui::PopStyleColor();
        bool foc = ImGui::IsItemFocused();
        if (foc != m_focused) {
            m_focused = foc;
            if (foc == false) {
                lostFocus();
            }
        }
        ImVec2 rmin = ImGui::GetItemRectMin();
        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y = 0;
        ImGuiButtonFlags button_flags = 0;/*ImGui::ImGuiButtonFlags_Repeat |*/ /*ImGuiButtonFlags_DontClosePopups*/;

        if (m_controllerFactory) {
            ImGui::SameLine(0, style.ItemInnerSpacing.x);
            snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
            ImGui::PushID(label);
            if (ImGui::ImageButton(label, (ImTextureID) (long) m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1))) {
                if (!m_controller) {
                    if (m_parentController) {
                        m_parentMaximized = m_parentController->isMaximized();
                    }
                    AMUI::AMUIController *con = m_controllerFactory();
                    con->setCallbackOnDestroy(std::bind(&AMUIFormElementEnum<TEnumType, TStringType>::onControllerClose, this, std::placeholders::_1));
                    m_controller = con;
                }
            }
            ImGui::PopID();
        }

        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }

        style.FramePadding = backup_frame_padding;


    }

    template<typename TEnumType, typename TStringType>
    TEnumType AMUIFormElementEnum<TEnumType, TStringType>::value() const
    {
        if (m_valueIndex <0 || m_valueIndex >= m_ids.size()) {
            return TEnumType(AMUI::DEFAULT_INT_ID);
        }
        return m_ids[m_valueIndex];
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::onControllerClose(AMUI::AMUIController *controller)
    {
        AMAssert(m_controller == controller);
        int rectId = controller->selectedRecordId();
        if (rectId != AMUI::DEFAULT_INT_ID) {
            refreshTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
            setValue(TEnumType(rectId));
            //if (this->m_valueIndex != AMUI::DEFAULT_INT_ID) {
                wasModified();
            //}// else {
            //    refreshTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
            //}
        }
        if (m_parentController) {
            if (m_parentMaximized) {
                m_parentController->setMaximized(true);
            } else {
                m_parentController->bringToForeground();
            }
        }
        m_controller = nullptr;
        //m_valueIndex = AMUI::DEFAULT_INT_ID;
        //AMDialogs::refreshTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setController(std::function<AMUI::AMUIController *()> controllerFactory, AMUI::AMUIController *parentController)
    {
        m_controllerFactory = controllerFactory;
        m_parentController = parentController;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::load()
    {
        if (m_enumId <0) {
            return;
        }
        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumId);
        AMCore::AMTwoWayTable<TEnumType, TStringType> *table = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        if (!table) {
            return;
        }
        TStringType oldTitle = m_valueIndex != AMUI::DEFAULT_INT_ID ? m_titles[m_valueIndex] : "";
        int sz = table->setB().size();
        if(this->nullable()) {
            sz++;
        }
        m_ids.resize(sz);
        m_titles.resize(sz);
        m_tooltips.resize(sz);
        int i = 0;
        if (this->nullable()) {
            m_ids[i] = AMUI::DEFAULT_INT_ID;
            m_titles[i] = "Vyberte";
            m_tooltips[i] = "";
            i++;
        }
        for(auto it = table->setB().cbegin(); it != table->setB().cend(); it++, i++) {
            std::size_t pos = it->first.find("###");
            m_ids[i] = *it->second;
            m_titles[i] = pos == std::string::npos ? it->first : (TStringType)it->first.substr(0, pos);
            m_tooltips[i] = pos == std::string::npos ? "" : it->first.substr(pos + 3);
        }
        TEnumType oldValue = value();
        auto it = std::find(m_ids.cbegin(), m_ids.cend(), m_valueValue);
        if (it != m_ids.cend()) {
            m_valueIndex = it - m_ids.cbegin();
        } else {
            m_valueIndex = this->nullable() ? 0 : (m_ids.size() > 0 ? 0 : AMUI::DEFAULT_INT_ID);
        }
        TStringType newTitle = m_valueIndex != AMUI::DEFAULT_INT_ID ? m_titles[m_valueIndex] : "";
        if (oldValue != value() || newTitle != oldTitle) {
            if (m_tableTime != std::chrono::time_point<std::chrono::steady_clock>::max()) {
                m_modified = true;
            }
            m_needUpdateState = true;
        }
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnum<TEnumType, TStringType>::focused()
    {
        return m_focused;
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnum<TEnumType, TStringType>::needUpdateState()
    {
        if (m_enumId >= 0) {
            std::chrono::time_point<std::chrono::steady_clock> datetime;
            AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint, &datetime);
            if (datetime != m_tableTime && datetime != std::chrono::time_point<std::chrono::steady_clock>::max()) {
                load();
                m_tableTime = datetime;
            }
        } else{
            if (!m_ids.empty()) {
                m_ids.clear();
                m_titles.clear();
            }
        }
        return AMUIFormElement::needUpdateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setState(AMUIWidgetState _state)
    {
        if (_state == AMUIWidgetState::EMPTY && m_valueIndex != AMUI::DEFAULT_INT_ID) {
            _state = AMUIWidgetState::FILLED;
        }
        AMUIFormElement::setState(_state);
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setFlashbackTimepoint(std::string flashbackTimepoint)
    {
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_flashbackTimepoint = flashbackTimepoint;
        if (m_enumId >= 0) {
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }

    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnum<TEnumType, TStringType>::isNull()
    {
        return m_nullable && m_valueValue == AMUI::DEFAULT_INT_ID;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setNull(bool _null)
    {
        setValue();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setValue()
    {
        m_valueIndex = m_nullable ? 0 : AMUI::DEFAULT_INT_ID;
        m_valueValue = AMUI::DEFAULT_INT_ID;
        updateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::setValueString(std::string value)
    {
        if (m_nullable && value == "null") {
            setValue();
        } else {
            long l = AMUI::DEFAULT_INT_ID;
            try {
                l = std::stol(value);
            } catch(std::exception e) {
                l = AMUI::DEFAULT_INT_ID;
            }
            if (l == AMUI::DEFAULT_INT_ID) {
                setValue();
            } else {
                setValue(l);
            }
        }
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUIFormElementEnum<TEnumType, TStringType>::valueAsString()
    {
        if (m_nullable && isNull()) {
            return "null";
        }
        if (m_state == AMUIWidgetState::INVALID) {
            return "";
        }
        return AMUI::url_encode_string(std::to_string(value()));
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnum<TEnumType, TStringType>::loadDefault()
    {
        m_valueIndex = m_nullable ? 0 : AMUI::DEFAULT_INT_ID;
        m_valueValue = AMUI::DEFAULT_INT_ID;
    }

}