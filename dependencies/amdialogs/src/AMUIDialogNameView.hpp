//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogNameView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogName.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIDialogNameView<TEnumType, TStringType>::AMUIDialogNameView()
        : AMUIDialogView()
    {

    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogNameView<TEnumType, TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogName<TEnumType, TStringType> *dlg
                = static_cast<AMUIDialogName<TEnumType, TStringType> *>(m_dialog);
            ImGui::Text("Titul:");
            dlg->elements()[0]->render(false, dlg->id() * 6600);
            ImGui::Text("Jméno:");
            dlg->elements()[1]->render(false, dlg->id() * 6600 + 1);
            ImGui::Text("Příjmení:");
            dlg->elements()[2]->render(false, dlg->id() * 6600 + 2);
            ImGui::Text("Titul za:");
            dlg->elements()[3]->render(false, dlg->id() * 6600 + 3);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs