//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogDatetimeView.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amui/AMUIFonts.h"

namespace AMDialogs {

    AMUIDialogDatetimeView::AMUIDialogDatetimeView()
        : AMUIDialogView()
    {

    }

    void AMUIDialogDatetimeView::render()
    {
        if (renderBegin()) {

            AMUIDialogDatetime *dlg = static_cast<AMUIDialogDatetime *>(m_dialog);
            const float button_size = ImGui::GetFrameHeight();

            ImGuiContext &g = *GImGui;
            ImGuiStyle &style = g.Style;

            if (dlg->showDate()) {
                ImGui::BeginGroup();
                {
                    dlg->year().render(false, 1000);
                    ImGui::SameLine();
                    dlg->month().render(false, 1001);
                    ImGui::SameLine(0, style.ItemInnerSpacing.x);
                    const float button_size = ImGui::GetFrameHeight();
                    dlg->monthPlus().setSizeX(button_size);
                    dlg->monthPlus().render(false, 1003);
                    ImGui::SameLine(0, style.ItemInnerSpacing.x);
                    dlg->monthMinus().setSizeX(button_size);
                    dlg->monthMinus().render(false, 1004);
                    float tabSzX = 2 * (button_size + style.ItemInnerSpacing.x) + style.ItemSpacing.x + dlg->month().sizeX() + dlg->year().sizeX();
                    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 20.0f);
                    float colSizeX = (tabSzX - 7 * style.CellPadding.x - 2 * style.ItemSpacing.x) / 7;
                    if (ImGui::BeginTable("calendar", 7, 0, ImVec2(tabSzX, 0))) {
                        for (int column = 0; column < 7; column++) {
                            ImGui::TableSetupColumn("", ImGuiTableColumnFlags_WidthFixed, colSizeX);
                        }
                        for (int row = -1; row < 6; row++) {
                            ImGui::TableNextRow();
                            if (row == -1) {
                                for (int column = 0; column < 7; column++) {
                                    ImGui::TableSetColumnIndex(column);
                                    ImGui::PushFont(AMUI::BoldFont);
                                    float sz = ImGui::CalcTextSize(dlg->wDayLabels()[column].c_str()).x;
                                    ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (colSizeX - sz) / 2.0f);
                                    ImGui::Text("%s", dlg->wDayLabels()[column].c_str());
                                    ImGui::PopFont();
                                }
                            } else {
                                for (int column = 0; column < 7; column++) {
                                    ImGui::TableSetColumnIndex(column);
                                    int j = row * 7 + column;
                                    if (dlg->days()[j].state == AMDialogs::AMUIWidgetState::READONLY) {
                                        ImGui::PushStyleColor(ImGuiCol_Text, 0xFFA0A0A0);
                                        ImGui::PushStyleColor(ImGuiCol_Button, 0x00000000);
                                        ImGui::BeginDisabled();
                                        ImGui::Button(dlg->days()[j].label.c_str(), ImVec2(-FLT_MIN, 0.0f));
                                        ImGui::EndDisabled();
                                        ImGui::PopStyleColor(2);
                                    } else if (dlg->days()[j].state == AMDialogs::AMUIWidgetState::FILLED) {
                                        ImGui::PushStyleColor(ImGuiCol_Text, 0xFFFF0000);
                                        ImGui::PushStyleColor(ImGuiCol_Button, 0x00000000);
                                        ImGui::PushFont(AMUI::BoldFont);
                                        ImGui::BeginDisabled();
                                        ImGui::Button(dlg->days()[j].label.c_str(), ImVec2(-FLT_MIN, 0.0f));
                                        ImGui::EndDisabled();
                                        ImGui::PopFont();
                                        ImGui::PopStyleColor(2);
                                    } else if (dlg->days()[j].state == AMDialogs::AMUIWidgetState::EMPTY) {
                                        ImGui::PushStyleColor(ImGuiCol_Text, 0xFF000000);
                                        ImGui::PushStyleColor(ImGuiCol_Button, 0x00000000);
                                        if (ImGui::Button(dlg->days()[j].label.c_str(), ImVec2(-FLT_MIN, 0.0f))) {
                                            dlg->onDayButton(dlg->days()[j].day);
                                        }
                                        ImGui::PopStyleColor(2);
                                    } else {
                                        ImGui::PushStyleColor(ImGuiCol_Text, 0x00000000);
                                        ImGui::PushStyleColor(ImGuiCol_Button, 0x00000000);
                                        ImGui::BeginDisabled();
                                        char label[32];
                                        snprintf(label, sizeof(label), "##EMPTYSPACE %i %i", row, column);
                                        ImGui::Button(label, ImVec2(-FLT_MIN, 0.0f));
                                        ImGui::EndDisabled();
                                        ImGui::PopStyleColor(2);
                                    }
                                }
                            }
                        }
                        ImGui::EndTable();
                    }
                    ImGui::EndGroup();
                }
            }
            if (dlg->showDate() && dlg->showTime()) {
                ImGui::SameLine();
            }
            if (dlg->showTime()) {
                ImGui::BeginGroup();
                {
                    if (dlg->showDate()) {
                        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + button_size + style.ItemSpacing.y + style.CellPadding.y + 1.0f /*???*/ /*+ style.ItemInnerSpacing.y*/ + 20.0f);
                    }
                    ImGui::Text("Hodina:");
                    dlg->hours().render(false, 1100);
                    ImGui::Text("Minuta:");
                    dlg->mins().render(false, 1101);
                    ImGui::Text("Vteřina:");
                    dlg->secs().render(false, 1102);
                    if (dlg->showUsecs()) {
                        ImGui::Text("Mikrosekunda:");
                        dlg->usec().render(false, 1103);
                    }
                }
                ImGui::EndGroup();
            }
            if (dlg->wildcard() != '\0') {
                ImGui::BeginGroup();
                {
                    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 20.0f);
                    ImGui::Text("Přesnost:");
                    dlg->precision().render(false, dlg->id() * 1000);
                }
                ImGui::EndGroup();
            }
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs