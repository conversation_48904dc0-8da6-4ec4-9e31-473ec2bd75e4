//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

//#include "../AMUIFormElementBool.h"
//#include "amcore/AMAssert.h"
//#include <cstdio>
//#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {

    const int FORM_ELEMENT_INT_BUFFER_SIZE = 32;

    template<typename TStringType>
    AMUIFormElementBool<TStringType>::AMUIFormElementBool(const char *key)
        : AMUIFormElement(key),
          m_value(0),
          m_true<PERSON>abel("Ano"),
          m_falseLabel("Ne") {
        m_callback = AMUIInputCallbackInteger;
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::init(TStringType falseLabel, TStringType trueLabel) {
        m_trueLabel = trueLabe<PERSON>;
        m_falseLabel = falseLabel;
        reset();
    }

    template<typename TStringType>
    AMUIFormElementBool<TStringType>::~AMUIFormElementBool() {
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::reset() {
        m_value = false;
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::updateState()
    {
        AMUIFormElement::updateState();
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::setValue(bool value)
    {
        m_value = value;
        updateState();
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
            ImGui::PushStyleColor(ImGuiCol_CheckMark, bgWidgetColors[(int) AMUIWidgetState::READONLY]);
        } else {
            ImGui::PushStyleColor(ImGuiCol_FrameBg, 0xFFFFFFFF);
        }
        char label[128];
        snprintf(
            label, sizeof(label) - 1, "%s##elradio_%i", m_trueLabel.c_str(), element_id
                );
        if (ImGui::RadioButton(label, valuePtr(), 1)) {
            wasModified();
        }
        ImGui::SameLine();
        snprintf(
            label, sizeof(label) - 1, "%s##elradio_%i", m_falseLabel.c_str(), element_id
                );
        if (ImGui::RadioButton(label, valuePtr(), 0)) {
            wasModified();
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopStyleColor();
            ImGui::PopItemFlag();
        } else {
            ImGui::PopStyleColor();
        }
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::setState(AMUIWidgetState _state)
    {
        if (_state == AMUIWidgetState::EMPTY) {
            _state = AMUIWidgetState::FILLED;
        }
        AMUIFormElement::setState(_state);
    }

    template<typename TStringType>
    void AMUIFormElementBool<TStringType>::loadDefault() {
        m_value = false;
        updateState();
    }
}
