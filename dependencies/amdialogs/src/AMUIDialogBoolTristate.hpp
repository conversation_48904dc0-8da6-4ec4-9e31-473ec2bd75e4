//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogBoolTristate.h"
#include "amdialogs/AMUIDialogBoolTristateView.h"

namespace AMDialogs {

    template<typename TStringType>
    void AMUIDialogBoolTristate<TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            TStringType &trueString,
            TStringType falseString
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogBoolTristateView<TStringType>();
        v->init(this);
        m_view = v;
        m_boolElement.initBase(32, "bool");
        m_boolElement.init(falseString, trueString);
        m_boolElement.setState(AMUIWidgetState::FILLED);
    }

    template<typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogBoolTristate<TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TStringType>
    AMUIDialogBoolTristate<TStringType>::AMUIDialogBoolTristate()
        : AMUIDialog(),
          m_boolElement("bool"),
          m_elements{&m_boolElement}
    {
    }

    template<typename TStringType>
    AMUIFormElementBoolTristate<TStringType> &AMUIDialogBoolTristate<TStringType>::boolElement()
    {
        return m_boolElement;
    }


} // AMDialogs