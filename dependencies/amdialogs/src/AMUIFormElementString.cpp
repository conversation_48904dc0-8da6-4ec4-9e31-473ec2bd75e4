//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementString.h"
#include "amcore/AMAssert.h"
#include <cstring>
//#include "../cfg/config.h"
#include "../AMUIFormCallbacks.h"
#include "amdialogs/AMUIFormConfig.h"

namespace AMDialogs {

    AMUIFormElementString::AMUIFormElementString(const char *key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElement(key, mandatory, nullable, hidden),
          m_buffer(nullptr)//,
          //m_wildcard('\0')
          //m_notNull(false)
    {
          m_callback = AMUIInputCallbackString;
    }

    void AMUIFormElementString::init()
    {
        AMAssert(m_length > 0);
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[m_length + 1];
        m_buffer[0] = '\0';
        AMAssert(m_buffer);
        reset();
    }

    AMUIFormElementString::~AMUIFormElementString()
    {
        if (m_buffer) {
            delete[] m_buffer;
        }
    }

    void AMUIFormElementString::reset()
    {
        AMAssert(m_buffer);
        AMAssert(m_length > 0);
        m_buffer[0] = '\0';
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
    }

    void AMUIFormElementString::updateState()
    {
        AMUIFormElement::updateState();
        AMAssert(m_length > 0);
        if (m_buffer[0] == '\0') {
            m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
        } else {
            AMUIParseResult res = AMUIParseResult::SUCCESS;
            m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIToWidgetState(res);
        }
    }

    void AMUIFormElementString::setValue(const char *value)
    {
        AMAssert(m_buffer);
        strncpy(m_buffer, value, m_length);
        m_buffer[m_length - 1] = '\0';
        updateState();
    }

    void AMUIFormElementString::render(bool formGrayed, int element_id)
    {
        if (m_hidden) {
            return;
        }

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : this->m_state;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        /*
        const float button_size = ImGui::GetFrameHeight();


        char chk_label[128];
        snprintf(chk_label, sizeof(chk_label) - 1, "##elcheckstring_%i", element_id);
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
        }

        if (ImGui::Checkbox(chk_label, &m_notNull)) {
            this->wasModified();
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopItemFlag();
        }
        ImGui::SameLine();



        state = (m_nullable && !m_notNull) ? AMUIWidgetState::READONLY : state;
         */

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width /*- (m_nullable ? button_size + style.ItemInnerSpacing.x : 0.0f)*/));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elstring_%i", element_id);

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);
        if (ImGui::InputText(
            label, m_buffer, m_length,
            ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_CallbackAlways | ImGuiInputTextFlags_EnterReturnsTrue
            , m_callback
            , (void *) this
            )) {
            wasHitEnter();
        }
        if (ImGui::IsItemFocused()) {
            if (ImGui::IsKeyPressed(ImGuiKey_Delete)) {
                wasHitSpecialKey(ELastEnterBackspaceEvent::DELETE);
            }
            if (ImGui::IsKeyPressed(ImGuiKey_Backspace)) {
                wasHitSpecialKey(ELastEnterBackspaceEvent::BACKSPACE);
            }
            if (ImGui::IsKeyPressed(ImGuiKey_DownArrow)) {
                wasHitSpecialKey(ELastEnterBackspaceEvent::ARROW_DOWN);
            }
            if (ImGui::IsKeyPressed(ImGuiKey_UpArrow)) {
                wasHitSpecialKey(ELastEnterBackspaceEvent::ARROW_UP);
            }
        }
        if (m_activateRequest) {
            ImGui::SetKeyboardFocusHere(-1);
        }
        m_activateRequest = false;


        ImGui::PopStyleColor();
        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }
    }

    std::string AMUIFormElementString::valueAsString()
    {
        return AMUI::url_encode_string(std::string(m_buffer));
    }

    void AMUIFormElementString::setValueString(std::string value)
    {
        setValue(AMUI::url_decode_string(value).c_str());
    }

    bool AMUIFormElementString::isNull()
    {
        return m_nullable && m_buffer[0] == '\0';
    }

    void AMUIFormElementString::setValue()
    {
        AMAssert(m_buffer);
        m_buffer[0] = '\0';
        updateState();
    }

    void AMUIFormElementString::loadDefault()
    {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }

}
