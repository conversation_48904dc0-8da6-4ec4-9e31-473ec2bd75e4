//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs/AMUIFormElementAuthorTag.h"
#include "amcore/AMAssert.h"
#include <cstring>
#include "amdialogs/AMUIFormCallbacks.h"
#include "ameliteui/AMEliteImageManager.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIDialogAuthorTag.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogAuthorTagView.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIFormElementAuthorTag<TStringType>::AMUIFormElementAuthorTag(const char *key)
        : AMUIFormElement(key),
          m_buffer(nullptr),
          m_dialog(nullptr),
          m_wildcard('\0'),
          m_preventRefreshDialog(0)
    {
          m_callback = AMUIInputCallbackAuthorTag<TStringType>;
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::init()
    {
        AMAssert(m_length > 0);
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[m_length + 1];
        AMAssert(m_buffer);
        reset();

        bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
            MEMIMG(Login_png), &m_icon, nullptr, nullptr
                                                       );
        AMAssert(rv);
    }

    template<typename TStringType>
    AMUIFormElementAuthorTag<TStringType>::~AMUIFormElementAuthorTag()
    {
        if (m_dialog) {
            delete m_dialog;
        }
        if (m_buffer) {
            delete[] m_buffer;
        }
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::reset()
    {
        AMAssert(m_buffer);
        AMAssert(m_length > 0);
        m_buffer[0] = '\0';
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::updateState()
    {
        AMUIFormElement::updateState();
        AMCore::AMAuthorTag<TStringType> at;
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::EMPTY;
        } else {
            TStringType is(m_buffer);
            AMUIParseResult res = AMUIParseAuthorTag(is, &at, m_wildcard);
            m_state = res != AMUIParseResult::FAIL ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
        }
        if (m_preventRefreshDialog) {
            m_preventRefreshDialog--;
            return;
        }
        if (m_dialog) {
            m_dialog->author().setValue(at.author.c_str());
            m_dialog->datetime().setValue(at.time.c_str());
        }
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::setValue(const char *value)
    {
        AMAssert(m_buffer);
        snprintf(m_buffer, m_length, "%s", value);
        m_buffer[m_length] = '\0';
        updateState();
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        const float button_size = ImGui::GetFrameHeight();

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elstring_%i", element_id);
        ImGui::SetNextItemWidth(ImMax(1.0f, ImGui::CalcItemWidth() - (button_size + style.ItemInnerSpacing.x)));
        //ImGui::PushItemWidth(-FLT_MIN);
        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        if (ImGui::InputText(
            label, m_buffer, m_length,
            flags | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_CallbackAlways
            , m_callback
            , (void *) this
            )) {
            wasHitEnter();
        }
        ImVec2 rmin = ImGui::GetItemRectMin();

        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y = 0;
        //ImGuiButtonFlags button_flags = ImGuiButtonFlags_Repeat | ImGuiButtonFlags_DontClosePopups;
        if (flags & ImGuiInputTextFlags_ReadOnly)
            ImGui::BeginDisabled();
        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
        ImGui::PushID(label);
        if (ImGui::ImageButton(label, (ImTextureID)(long)m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1)))
        {
            if(!m_dialog) {
                AMUIDialogAuthorTag<TStringType> *dlg = new AMUIDialogAuthorTag<TStringType>();
                m_dialog = dlg;
                dlg->init(
                    std::string(m_label),
                    std::bind(&AMUIFormElementAuthorTag<TStringType>::onDialogClose, this, std::placeholders::_1),
                    std::bind(&AMUIFormElementAuthorTag<TStringType>::onDialogChange, this, std::placeholders::_1),
                    m_wildcard
                    );
                std::string is(m_buffer);
                AMCore::AMAuthorTag<std::string> at;
                AMUIParseAuthorTag(is, &at, m_wildcard);
                m_dialog->author().setValue(at.author.c_str());
                m_dialog->datetime().setValue(at.time.c_str());
                auto v = static_cast<AMUIDialogAuthorTagView<TStringType> *>(dlg->view());
                ImVec2 rmax = ImGui::GetItemRectMax();
                ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
                ImVec2 sz(std::max(rmax.x - rmin.x + 2 * backup_frame_padding.x, 250.0f), -FLT_MIN);
                v->setOrigin(pos);
                v->setSize(sz);
            }
        }
        ImGui::PopID();
        /*
         * ImVec2 rmax = ImGui::GetItemRectMax();
         */
        /*
        if (m_dialog) {
            AMUIDialogAuthorTag<TStringType> *dlg = static_cast<AMUIDialogAuthorTag<TStringType> *>(m_dialog);
            auto v = static_cast<AMUIDialogAuthorTagView<TStringType> *>(dlg->view());
            ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
            ImVec2 sz(rmax.x - rmin.x + 2 * backup_frame_padding.x, -FLT_MIN);
            v->setOrigin(pos);
            v->setSize(sz);
        }*/

        if (flags & ImGuiInputTextFlags_ReadOnly)
            ImGui::EndDisabled();
        style.FramePadding = backup_frame_padding;
        ImGui::PopStyleColor();
        if (m_dialog) {
            m_dialog->view()->render();
        }
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::onDialogChange(AMUIDialog *dlg)
    {
        AMAssert(dlg == m_dialog);
        m_state = m_dialog->state();
        strncpy(m_buffer, m_dialog->datetime().buffer(), m_length);
        size_t written = strlen(m_buffer);
        if (written > 0) {
            if (written + 1 < m_length) {
                m_buffer[written] = ' ';
                written++;
            }
        }
        strncpy(m_buffer + written, m_dialog->author().buffer(), m_length - written -1);
        m_buffer[m_length - 1] = '\0';
        m_preventRefreshDialog++;
        wasModified();
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::onDialogClose(AMUIDialog *)
    {
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
        m_dialog = nullptr;
    }

    template<typename TStringType>
    void AMUIFormElementAuthorTag<TStringType>::loadDefault()
    {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }

}
