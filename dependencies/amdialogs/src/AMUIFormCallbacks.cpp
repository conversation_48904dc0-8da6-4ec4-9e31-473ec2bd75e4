//
// Created by <PERSON><PERSON><PERSON> on 15.9.22.
//

#include "amdialogs//AMUIFormCallbacks.h"
#include <sstream>
#include "amdialogs/AMUIFormElementNatural.h"
#include "amdialogs/AMUIFormElementInteger.h"
#include "amdialogs/AMUIFormElementFloat.h"
#include "amdialogs/AMUIFormElementString.h"
#include "amdialogs/AMUIFormElementStringNullable.h"
#include "imgui/imgui.h"

namespace AMDialogs {

    int AMUIInputCallbackString(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementString *el = dynamic_cast<AMUIFormElementString *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }

    int AMUIInputCallbackStringNullable(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementStringNullable *el = dynamic_cast<AMUIFormElementStringNullable *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        return 0;
    }

    int AMUIInputCallbackEmpty(ImGuiInputTextCallbackData *data)
    {
        return 0;
    }

    int AMUIInputCallbackInteger(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementInteger *el = dynamic_cast<AMUIFormElementInteger *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackCharFilter) {
            if (data->EventChar < 256 && strchr("0123456789+-", (char) data->EventChar)) {
                return 0;
            } else {
                return 1;
            }
        }
        return 0;
    }

    int AMUIInputCallbackFloat(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementFloat *el = dynamic_cast<AMUIFormElementFloat *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackCharFilter) {
            if (data->EventChar < 256 && strchr("0123456789+-,eE", (char) data->EventChar)) {
                return 0;
            } else {
                return 1;
            }
        }

        return 0;
    }

    int AMUIInputCallbackNatural(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementNatural *el = dynamic_cast<AMUIFormElementNatural *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackCharFilter) {
            if (data->EventChar < 256 && strchr("0123456789", (char) data->EventChar)) {
                return 0;
            } else {
                return 1;
            }
        }
        return 0;
    }

    int AMUIInputCallbackDatetime(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementDatetime *el = dynamic_cast<AMUIFormElementDatetime *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackAlways) {
            el->setCursorPos(data->CursorPos);
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackCharFilter) {
            if (data->EventChar < 256 && strchr("0123456789 .:", (char) data->EventChar)) {
                return 0;
            } else {
                return 1;
            }
        }
        return 0;
    }
/*
    int AMUIInputCallbackDatetimeLostFocus(::ImGuiInputTextCallbackData *data)
    {
        AMUIFormElementDatetime *el = dynamic_cast<AMUIFormElementDatetime *>((AMUIFormElement *) data->UserData);
        AMAssert(el);
        if (data->EventFlag & ImGuiInputTextFlags_CallbackEdit) {
            el->wasModified();
            if (data->EventKey == ImGuiKey_Enter) {
                el->wasHitEnter();
            }

        }
        if (data->EventFlag == ImGuiInputTextFlags_CallbackHistory) {
            if (data->EventKey == ImGuiKey_UpArrow) {
                data->DeleteChars(0, data->BufTextLen);
                data->InsertChars(0, "Pressed Up!");
                data->SelectAll();
            } else if (data->EventKey == ImGuiKey_Enter) {
                el->wasHitEnter();
            }
        }
        if (data->EventFlag & ImGuiInputTextFlags_CallbackCharFilter) {
            if (data->EventKey == ImGuiKey_Enter) {
                el->wasHitEnter();
            }
            if (data->EventChar < 256 && strchr("0123456789 .:", (char) data->EventChar)) {
                return 0;
            } else {
                return 1;
            }
        }
        return 0;
    }*/

}
