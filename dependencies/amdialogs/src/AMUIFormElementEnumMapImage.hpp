//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#include "../AMUIFormElementEnumMapImage.h"
#include <cassert>
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"
#include "amui/AMUIApp.h"
#include "amdialogs/AMUIEnum.h"
#include "ameliteui/AMEliteResources.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumMapImage<TEnumType, TStringType>::AMUIFormElementEnumMapImage(const std::string key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElementEnumMap<TEnumType, TStringType>(key, mandatory, nullable, hidden),
        m_icons(nullptr),
        m_iconsForeign(false)
    {
    }


    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumMapImage<TEnumType, TStringType>::~AMUIFormElementEnumMapImage()
    {
        destroyImageTable(this->m_enumId);
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMapImage<TEnumType, TStringType>::init(int enumId, const std::string &strVoidParam)
    {
        AMUIFormElementEnumMap<TEnumType, TStringType>::init(enumId, strVoidParam);
        if (!m_icons) {
            prepareImageTable(enumId);
            m_icons = imageTable(enumId);
        }
    }


    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnumMapImage<TEnumType, TStringType>::drawIconTest()
    {
        return true;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMapImage<TEnumType, TStringType>::drawIcon()
    {

        ImGuiWindow* window = ImGui::GetCurrentWindow();
        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;
        float xpos = window->DC.CursorPos.x;
        if (m_icons) {
            for(auto itn:this->m_valueInt) {
                if (itn.first <= m_icons->size() && itn.first > 0) {
                    if (xpos + IMAGE_IN_IMAGETABLE_SIZE > ImGui::GetItemRectMax().x) {
                        break;
                    }
                    ImVec2 pos = ImGui::GetCursorPos();
                    const ImRect bb(ImVec2(xpos, window->DC.CursorPos.y), ImVec2(ImGui::GetFrameHeight() + xpos, ImGui::GetFrameHeight() + window->DC.CursorPos.y));
                    float off = (ImGui::GetFrameHeight() - IMAGE_IN_IMAGETABLE_SIZE) / 2.0;
                    window->DrawList->AddImage(
                        (ImTextureID) (long) m_icons->at(itn.first - 1), ImVec2(bb.Min.x + off, bb.Min.y + off), ImVec2(bb.Min.x + IMAGE_IN_IMAGETABLE_SIZE + off, bb.Min.y + IMAGE_IN_IMAGETABLE_SIZE + off), ImVec2(0, 0), ImVec2(1, 1), 0xFF000000
                                              );
                    xpos += IMAGE_IN_IMAGETABLE_SIZE + style.ItemInnerSpacing.x / 2.0f;
                }
            }
        }
    }

}