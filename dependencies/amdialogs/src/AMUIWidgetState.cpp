//
// Created by <PERSON><PERSON><PERSON> on 4.2.23.
//

#include "amdialogs/AMUIWidgetState.h"

namespace AMDialogs {

    AMUIWidgetState operator + (AMUIWidgetState s1, AMUIWidgetState s2)
    {
        static const AMUIWidgetState table[5][5] = {
            {AMUIWidgetState::READONLY,AMUIWidgetState::EMPTY,AMUIWidgetState::FILLED,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::INVALID},
            {AMUIWidgetState::EMPTY,AMUIWidgetState::EMPTY,AMUIWidgetState::FILLED,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::INVALID},
            {AMUIWidgetState::FILLED,AMUIWidgetState::FILLED,AMUIWidgetState::FILLED,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::INVALID},
            {AMUIWidgetState::WITHMISTA<PERSON>,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::WITHMISTAKE,AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,AMUIWidgetState::INVALID,AMUIWidgetState::INVALID,AMUIWidgetState::INVALID,AMUIWidgetState::INVALID}
        };
        return table[(int)s1][int(s2)];
    }

    AMUIWidgetState operator * (AMUIWidgetState s1, AMUIWidgetState s2)
    {
        static const AMUIWidgetState table[5][5] = {
            {AMUIWidgetState::READONLY,     AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::FILLED,        AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::WITHMISTAKE,  AMUIWidgetState::EMPTY,         AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::FILLED,       AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::FILLED,        AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::WITHMISTAKE,  AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,      AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID}
        };
        return table[(int)s1][int(s2)];
    }

    AMUIWidgetState operator - (AMUIWidgetState s1, AMUIWidgetState s2)
    {
        static const AMUIWidgetState table[5][5] = {
            {AMUIWidgetState::INVALID,     AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,        AMUIWidgetState::INVALID,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,      AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,   AMUIWidgetState::INVALID,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,   AMUIWidgetState::FILLED,        AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,  AMUIWidgetState::INVALID,   AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::WITHMISTAKE,   AMUIWidgetState::INVALID},
            {AMUIWidgetState::INVALID,      AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID,       AMUIWidgetState::INVALID}
        };
        return table[(int)s1][int(s2)];
    }
}