//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogManager.h"
#include "amui/AMUIApp.h"

namespace AMDialogs {

    AMUIDialogManager::AMUIDialogManager()
        :m_queue()
    {
        AMUI::AMUIApp::instance().addPerformCallback(this, &AMUIDialogManager::perform);
    }

    void AMUIDialogManager::deleteDialog(AMUIDialog *dlg)
    {
        m_queue.push(dlg);
    }

    void AMUIDialogManager::perform() {
        while(!m_queue.empty()) {
            AMUIDialog *dlg = m_queue.front();
            m_queue.pop();
            AMUI::AMUIApp::instance().removeDialog(dlg);
            delete dlg;
        }
        //AMUI::AMUIApp::instance().removePerformCallback(
        //    AMUI::AMUIApp::AMUIStdFunctionPerformWrap{this, std::bind(&AMUIDialogManager::perform, this)}
        //                                               );
    }

    AMUIDialogManager &AMUIDialogManager::instance()
    {
        if (!m_instance) {
            m_instance = new AMUIDialogManager();
        }
        return *m_instance;
    }

    AMUIDialogManager *AMUIDialogManager::m_instance = nullptr;
} // AMDialogs