//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogName.h"
#include "amdialogs/AMUIDialogNameView.h"
#include "amdialogs/AMUINameFlashback.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    void AMUIDialogName<TEnumType, TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            char wildcard,
            const std::string &strVoidParam,
            AMUI::AMUIController *controller,
            std::string flashbackTimepoint
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogNameView<TEnumType, TStringType>();
        v->init(this);
        m_view = v;
        //m_strVoidParam = strVoidParam;
        m_degreeBefore.initBase(1, "degree-before");
        m_degreeBefore.init(IDegreeBeforeTableId, strVoidParam);
        if (controller) {
            m_degreeBefore.setController(IDegreeBeforeController, controller);
        }
        m_degreeBefore.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_degreeBefore.setFlashbackTimepoint(flashbackTimepoint);
        m_name.initBase(64, "name");
        m_name.init();
        m_name.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_surname.initBase(64, "surname");
        m_surname.init();
        m_surname.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_degreeAfter.initBase(1, "degree-after");
        m_degreeAfter.init(IDegreeAfterTableId, strVoidParam);
        if (controller) {
            m_degreeAfter.setController(IDegreeAfterController, controller);
        }
        m_degreeAfter.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_degreeAfter.setFlashbackTimepoint(flashbackTimepoint);
        m_wildcard = wildcard;
    }

    template<typename TEnumType, typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogName<TEnumType, TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TEnumType, typename TStringType>
    AMUIDialogName<TEnumType, TStringType>::AMUIDialogName()
        : AMUIDialog(),
          m_degreeBefore(""),
          m_name(""),
          m_surname(""),
          m_degreeAfter(""),
          m_elements{&m_degreeBefore, &m_name, &m_surname, &m_degreeAfter},
          m_wildcard('\0')//,
          //m_strVoidParam()
    {
    }

} // AMDialogs