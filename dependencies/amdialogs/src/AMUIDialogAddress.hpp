//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogAddress.h"
#include "amdialogs/AMUIDialogAddressView.h"

namespace AMDialogs {

    template<typename TStringType>
    void AMUIDialogAddress<TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            char wildcard
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogAddressView<TStringType>();
        v->init(this);
        m_view = v;
        m_place.initBase(128, "place");
        m_place.init();
        m_place.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_street.initBase(256, "street");
        m_street.init();
        m_street.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_parcelNum.initBase(16, "descriptive-number");
        m_parcelNum.init();
        m_parcelNum.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_parcelNum.setWildCard(wildcard);
        m_parcelNum.setSizeX(100.0f);
        m_orientationNum.initBase(16, "orientation-number");
        m_orientationNum.init();
        m_orientationNum.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_orientationNum.setSizeX(50.0f);
        m_city.initBase(128, "city");
        m_city.init();
        m_city.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_zip.initBase(10, "zip");
        m_zip.init();
        m_zip.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_country.initBase(96, "country");
        m_country.init();
        m_country.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_wildcard = wildcard;
    }

    template<typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogAddress<TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TStringType>
    AMUIDialogAddress<TStringType>::AMUIDialogAddress()
        : AMUIDialog(),
          m_place(""),
          m_street(""),
          m_parcelNum(""),
          m_orientationNum(""),
          m_city(""),
          m_zip(""),
          m_country(""),
          m_elements{&m_place, &m_street, &m_parcelNum, &m_orientationNum, &m_city, &m_zip, &m_country},
          m_wildcard('\0')
    {
    }

} // AMDialogs