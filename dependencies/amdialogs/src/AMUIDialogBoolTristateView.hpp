//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogBoolTristateView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogBoolTristate.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIDialogBoolTristateView<TStringType>::AMUIDialogBoolTristateView()
        : AMUIDialogView()
    {

    }

    template<typename TStringType>
    void AMUIDialogBoolTristateView<TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogBoolTristate<TStringType> *dlg = static_cast<AMUIDialogBoolTristate<TStringType> *>(m_dialog);
            dlg->elements()[0]->render(false, dlg->id() * 1000);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs