//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs/AMUIFormElementAddressNullable.h"
#include "amcore/AMAssert.h"
#include <cstring>
#include "amdialogs/AMUIFormCallbacks.h"
#include "ameliteui/AMEliteImageManager.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIDialogAddress.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogAuthorTagView.h"


namespace AMDialogs {

    template<typename TStringType>
    AMUIFormElementAddressNullable<TStringType>::AMUIFormElementAddressNullable(const char *key)
        : AMUIFormElementAddress<TStringType>(key),
          m_notNull(false)
    {
    }

    template<typename TStringType>
    void AMUIFormElementAddressNullable<TStringType>::setValue(const char *value)
    {
        this->AMUIFormElementAddress<TStringType>::setValue(value);
        m_notNull = true;
    }

    template<typename TStringType>
    void AMUIFormElementAddressNullable<TStringType>::render(bool formGrayed, int element_id)
    {

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : this->m_state;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        const float button_size = ImGui::GetFrameHeight();

        this->m_dialogPositionOfffsetX = - button_size - style.ItemSpacing.x;

        char chk_label[128];
        snprintf(chk_label, sizeof(chk_label) - 1, "##elcheck_%i", element_id);
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
        }

        if (ImGui::Checkbox(chk_label, &m_notNull)) {
            this->wasModified();
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopItemFlag();
        }
        ImGui::SameLine();

        this->AMUIFormElementAddress<TStringType>::render(formGrayed || !m_notNull, element_id);
    }

    template<typename TStringType>
    void AMUIFormElementAddressNullable<TStringType>::setValue()
    {
        m_notNull = false;
    }

    template<typename TStringType>
    bool AMUIFormElementAddressNullable<TStringType>::isNull()
    {
        return !m_notNull;
    }

}
