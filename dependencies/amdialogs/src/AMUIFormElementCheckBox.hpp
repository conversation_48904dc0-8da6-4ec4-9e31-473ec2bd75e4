//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

//#include "../AMUIFormElementCheckBox.h"
//#include <cassert>
//#include <cstdio>
//#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIFormElementCheckBox<TStringType>::AMUIFormElementCheckBox(const char *key)
        : AMUIFormElement(key),
          m_value(false),
          m_labelOnRight("")
    {
        m_callback = AMUIInputCallbackInteger;
    }

    template<typename TStringType>
    AMUIFormElementCheckBox<TStringType>::AMUIFormElementCheckBox()
        : AMUIFormElement(nullptr),
          m_value(false),
          m_labelOnRight("")
    {
        m_callback = AMUIInputCallbackInteger;
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::init(TStringType label) {
        m_labelOnRight = label;
        reset();
    }

    template<typename TStringType>
    AMUIFormElementCheckBox<TStringType>::~AMUIFormElementCheckBox()
    {
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::reset() {
        m_value = false;
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::updateState()
    {
        AMUIFormElement::updateState();
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::setValue(bool value)
    {
        m_value = value;
        updateState();
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        const float max_width = ImGui::GetContentRegionAvail().x;
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(
            label, sizeof(label) - 1, "%s##elcheckbox_%i", m_labelOnRight.c_str(), element_id
                );
        label[127] = '\0';
        if (ImGui::Checkbox(label, &m_value)) {
            wasModified();
        }
        if (state == AMUIWidgetState::READONLY) {
            //ImGui::PopItemFlag();
            ImGui::EndDisabled();
        } else {
            //ImGui::PopStyleColor();
        }
    }

    template<typename TStringType>
    void AMUIFormElementCheckBox<TStringType>::loadDefault() {
        m_value = false;
        updateState();
    }
}
