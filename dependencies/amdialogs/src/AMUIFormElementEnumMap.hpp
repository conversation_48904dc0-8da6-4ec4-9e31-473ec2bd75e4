//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#include "../AMUIFormElementEnumMap.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
//#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogEnumLine.h"
#include "amdialogs/AMUIDialogEnumLineView.h"
#include "amui/AMUIApp.h"
#include "amdialogs/AMUIEnum.h"
#include "ameliteui/AMEliteResources.h"
#include "amdialogs/AMUIParsers.h"
#include "amdialogs/AMUISynthesizers.h"

namespace AMDialogs {

    //const int FORM_ELEMENT_DATETIME_BUFFER_SIZE = 48;

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumMap<TEnumType, TStringType>::AMUIFormElementEnumMap(const std::string key, bool mandatory , bool nullable, bool hidden)
        : AMUIFormElement(key, mandatory, nullable, hidden),
        m_buffer(nullptr),
        m_lines(3),
        m_valueInt(),
        m_valueValue(),
        m_enumId(-1),
        m_icon(-1),
        m_focused(false),
        m_updateFromDialog(false),
        //m_titles(),
        //m_ids(),
        m_tableTime(std::chrono::time_point<std::chrono::steady_clock>::max()),
        //m_controllerFactory(),
        //m_controller(nullptr),
        //m_parentController(nullptr),
        //m_parentMaximized(false),
        m_strVoidParam(),
        m_dialog(nullptr),
        m_preventRefreshDialog(0),
        m_flashbackTimepoint()
    {
            m_callback = AMDialogs::AMUIInputCallbackEnumMap<TEnumType, TStringType>;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::init(int enumId, const std::string &strVoidParam)
    {
        //AMAssert(m_length > 0);
        m_length = 1024;
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[m_length + 1];
        m_buffer[0] = '\0';
        AMAssert(m_buffer);
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_enumId = enumId;
        m_strVoidParam = strVoidParam;
        if (m_enumId >= 0) {
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_tableTime = std::chrono::time_point<std::chrono::steady_clock>::max();
        if (m_icon == -1) {
            bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
                MEMIMG(Login_png), &m_icon, nullptr, nullptr
                                                                           );
            AMAssert(rv);
        }
    }

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumMap<TEnumType, TStringType>::~AMUIFormElementEnumMap()
    {
        /*if (m_controller) {
            if(std::find(AMUI::AMUIApp::instance().controllers().begin(), AMUI::AMUIApp::instance().controllers().end(), m_controller) != AMUI::AMUIApp::instance().controllers().end()) {
                std::function<void(AMUI::AMUIController *)> f;
                m_controller->setCallbackOnDestroy(f);
            }
        }*/

        if (m_dialog) {
            delete m_dialog;
        }
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        if (m_buffer) {
            delete[] m_buffer;
        }
        if (m_icon != -1) {
            AMEliteUI::AMEliteImageManager::unloadTexture(m_icon);
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::reset()
    {
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        m_valueInt.clear();
        m_valueValue.clear();
        m_buffer[0] = '\0';
        m_state = AMUIWidgetState::EMPTY;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::updateState()
    {
        AMUIFormElement::updateState();
        AMAssert(m_length > 0);
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::EMPTY;
        } else {
            TStringType is(m_buffer);
            AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumId);
            AMCore::AMTwoWayTable<TEnumType, TStringType> *table = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
            if (!table) {
                m_state = AMDialogs::AMUIWidgetState::EMPTY;
            } else {
                AMUIParseResult res = AMUIParseEnumLine(is, m_valueInt, *table);
                m_state = res != AMUIParseResult::FAIL ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
            }
        }
        if (m_preventRefreshDialog) {
            m_preventRefreshDialog--;
            return;
        }
        if (m_dialog) {
            m_dialog->update();
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::updateValue()
    {
        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumId);
        AMCore::AMTwoWayTable<TEnumType, TStringType> *table = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        if (!table) {
            return;
        }
        m_valueInt.clear();
        for (auto it: m_valueValue) {
            auto itx = table->findB(it.first);
            if (itx) {
                m_valueInt.insert({it.first, it.second});
            }
        }
        std::string s = AMUISynthesizeEnumLine(table, m_valueInt);
        strncpy(m_buffer, s.c_str(), m_length);
        m_buffer[m_length - 1] = '\0';
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setValue(std::map<TEnumType, int> &value)
    {
        m_valueValue = value;
        updateValue();
        updateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::render(bool formGrayed, int element_id)
    {
        if (m_hidden) {
            return;
        }

        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        const float button_size = ImGui::GetFrameHeight();

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        width = ImMax(1.0f, width - button_size - style.ItemInnerSpacing.x /*+ (m_controllerFactory ? - button_size - style.ItemInnerSpacing.x : 0.0f)*/);
        float height = ImGui::GetTextLineHeight() * m_lines + ImGui::GetStyle().FramePadding.y * 2.0f;
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elenummap_%i", element_id);
        char emtybuf[1] = "";
        ImVec2 inputPos = ImGui::GetCursorPos();
        if (ImGui::InputTextMultiline(
            label, drawIconTest() ? emtybuf : m_buffer, drawIconTest() ? 0 : m_length, ImVec2(width, height),
            ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_EnterReturnsTrue
            | ImGuiInputTextFlags_CtrlEnterForNewLine | ImGuiInputTextFlags_CallbackAlways, m_callback, (void *) this
                                     )) {
            setValue(m_valueInt);
            wasHitEnter();
        }
        if (drawIconTest()) {
            ImVec2 icnPos = ImGui::GetCursorPos();
            ImGui::SetCursorPos(inputPos);
            drawIcon();
            ImGui::SetCursorPos(icnPos);
        }

        ImGui::PopStyleColor();
        bool foc = ImGui::IsItemFocused();
        if (foc != m_focused) {
            m_focused = foc;
            if (foc == false) {
                lostFocus();
            }
        }
        ImVec2 rmin = ImGui::GetItemRectMin();
        const ImVec2 backup_frame_padding = style.FramePadding;

        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "##elicon2_%i", element_id);
        ImGui::PushID(label);
        style.FramePadding = ImVec2(1.0f, (height - 24.0f) / 2.0);
        //ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 24.0 - backup_frame_padding.y + 2.0f/*probably button border*/ + height);
        if (ImGui::ImageButton(label, (ImTextureID) (long) m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1)/*, -1*/)) {
            if(!m_dialog) {
                AMUIDialogEnumLine<TEnumType, TStringType> *dlg = new AMUIDialogEnumLine<TEnumType, TStringType>();
                m_dialog = dlg;
                dlg->init(
                    std::string(m_label),
                    std::bind(&AMUIFormElementEnumMap<TEnumType, TStringType>::onDialogClose, this, std::placeholders::_1),
                    std::bind(&AMUIFormElementEnumMap<TEnumType, TStringType>::onDialogChange, this, std::placeholders::_1),
                    m_enumId,
                    m_strVoidParam,
                    &m_valueInt,
                    m_flashbackTimepoint
                         );
                auto v = static_cast<AMUIDialogDatetimeView *>(dlg->view());
                ImVec2 rmax = ImGui::GetItemRectMax();
                ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
                ImVec2 sz(std::max(rmax.x - rmin.x + 2 * backup_frame_padding.x, 500.0f), -FLT_MIN);
                v->setOrigin(pos);
                v->setSize(sz);
            }
        }
        style.FramePadding = backup_frame_padding;
        //::SetCursorPosY(lineYPos - 100);
        ImGui::PopID();


        /*
        style.FramePadding.x = style.FramePadding.y = 0;
        ImGuiButtonFlags button_flags = ImGuiButtonFlags_Repeat | ImGuiButtonFlags_DontClosePopups;

        if (m_controllerFactory) {
            ImGui::SameLine(0, style.ItemInnerSpacing.x);
            snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
            ImGui::PushID(label);
            if (ImGui::ImageButton((ImTextureID) (long) m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1), (button_size - 24.0f) / 2.0f)) {
                if (!m_controller) {
                    if (m_parentController) {
                        m_parentMaximized = m_parentController->isMaximized();
                    }
                    AMUI::AMUIController *con = m_controllerFactory();
                    con->setCallbackOnDestroy(std::bind(&AMUIFormElementEnum<TEnumType, TStringType>::onControllerClose, this, std::placeholders::_1));
                    m_controller = con;
                }
            }
            ImGui::PopID();
        }*/

        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }

        style.FramePadding = backup_frame_padding;

        if (m_dialog) {
            m_dialog->view()->render();
        }
    }

    template<typename TEnumType, typename TStringType>
    std::map<TEnumType, int> &AMUIFormElementEnumMap<TEnumType, TStringType>::value() const
    {
        return m_valueInt;
    }

    /*
    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::onControllerClose(AMUI::AMUIController *controller)
    {
        AMAssert(m_controller == controller);
        int rectId = controller->selectedRecordId();
        if (rectId != AMUI::DEFAULT_INT_ID) {
            setValue(TEnumType(rectId));
        }
        if (m_parentController) {
            if (m_parentMaximized) {
                m_parentController->setMaximized(true);
            } else {
                m_parentController->bringToForeground();
            }
        }
        m_controller = nullptr;
        m_valueInt.clear();
        AMDialogs::refreshTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam);
        wasModified();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setController(std::function<AMUI::AMUIController *()> controllerFactory, AMUI::AMUIController *parentController)
    {
        m_controllerFactory = controllerFactory;
        m_parentController = parentController;
    }*/

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::load()
    {
        if (m_enumId <0) {
            return;
        }
        std::map<TEnumType, int> oldValInt = m_valueInt;
        updateValue();
        updateState();
        if (m_tableTime != std::chrono::time_point<std::chrono::steady_clock>::max()) {
            if (oldValInt != m_valueInt) {
                wasModified();
            }
        }
        m_valueValue = m_valueInt;
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnumMap<TEnumType, TStringType>::focused()
    {
        return m_focused;
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnumMap<TEnumType, TStringType>::needUpdateState()
    {
        if (m_enumId >= 0) {
            std::chrono::time_point<std::chrono::steady_clock> datetime;
            AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint, &datetime);
            if (datetime != m_tableTime) {
                load();
                m_tableTime = datetime;
            }
        }
        return AMUIFormElement::needUpdateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setState(AMUIWidgetState _state)
    {
        AMUIFormElement::setState(_state);
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::onDialogChange(AMUIDialog *dlg)
    {
        AMAssert(dlg == m_dialog);
        m_state = m_dialog->state();
        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumId);
        AMCore::AMTwoWayTable<TEnumType, TStringType> *table = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        if (!table) {
            return;
        }
        std::string xs = AMUISynthesizeEnumLine<TEnumType, TStringType>(table, m_valueInt);
        strncpy(m_buffer, xs.c_str(), m_length);
        m_preventRefreshDialog++;
        wasModified();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::onDialogClose(AMUIDialog *)
    {
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
        m_dialog = nullptr;
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUIFormElementEnumMap<TEnumType, TStringType>::valueAsString()
    {
        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumId);
        AMCore::AMTwoWayTable<TEnumType, TStringType> *table = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        if (!table) {
            return "";
        }
        std:std::ostringstream os;
        os << '{';
        bool first = true;
        for(auto it: m_valueInt) {
            if (first) {
                first = false;
            } else {
                os << ',';
            }
            os << it.first;
        }
        os << '}';
        return os.str();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setValueString(std::string value)
    {
        AMAssert(m_buffer);
        strncpy(m_buffer, value.c_str(),m_length);
        m_buffer[m_length - 1] = '\0';
        updateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setLines(int lines)
    {
        m_lines = lines;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::setFlashbackTimepoint(std::string flashbackTimepoint)
    {
        if (m_enumId >= 0) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_flashbackTimepoint = flashbackTimepoint;
        if (m_enumId >= 0) {
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(m_enumId, m_strVoidParam, m_flashbackTimepoint);
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumMap<TEnumType, TStringType>::loadDefault() {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }
}
