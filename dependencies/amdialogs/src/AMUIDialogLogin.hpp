//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//
#define IMGUI_DEFINE_MATH_OPERATORS
#include "amdialogs/AMUIDialogLogin.h"
#include "amdialogs/AMUIDialogLoginView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    void AMUIDialogLogin<TEnumType, TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            int unused
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogLoginView<TEnumType, TStringType>();
        v->init(this);
        m_view = v;
        m_userName.initBase(64, "userName");
        m_userName.init();
        m_userName.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_password.initBase(64, "password");
        m_password.init();
        m_password.setState(AMDialogs::AMUIWidgetState::EMPTY);
    }

    template<typename TEnumType, typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogLogin<TEnumType, TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TEnumType, typename TStringType>
    AMUIDialogLogin<TEnumType, TStringType>::AMUIDialogLogin()
        : AMUIDialog(),
          m_userName(""),
          m_password(""),
          m_elements{&m_userName, &m_password},
          m_icon(std::numeric_limits<GLuint>::max()),
          m_iconRect(0.0f, 0.0f, 0.0f, 0.0f),
          m_errorText()
    {
        m_closeButtonText = "Login";
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogLogin<TEnumType, TStringType>::setIcon(GLuint icon, AMUI::AMUIRect<float> iconRect)
    {
        m_icon = icon;
        m_iconRect = iconRect;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogLogin<TEnumType, TStringType>::setErrorText(std::string text)
    {
        m_errorText = text;
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUIDialogLogin<TEnumType, TStringType>::errorText()
    {
        return m_errorText;
    }

} // AMDialogs