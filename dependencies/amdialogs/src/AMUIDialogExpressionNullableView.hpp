//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogExpressionNullableView.h"
#include "amdialogs/AMUIDialogExpressionNullable.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIDialogExpressionNullableView<TStringType>::AMUIDialogExpressionNullableView()
        : AMUIDialogExpressionView<TStringType>()
    {

    }

    template<typename TStringType>
    bool AMUIDialogExpressionNullableView<TStringType>::renderNullable()
    {
        AMUIDialogExpressionNullable<TStringType> *dlg = static_cast<AMUIDialogExpressionNullable<TStringType> *>(this->m_dialog);

        if (dlg->nullableCheckBox().value() != !dlg->isNull()) {
            dlg->nullableCheckBox().setValue(!dlg->isNull());
        }
        dlg->nullableCheckBox().render(false, dlg->id() * 1000 + 777);
        if (dlg->nullableCheckBox().value() != !dlg->isNull()) {
            dlg->setNull(!dlg->nullableCheckBox().value());
            dlg->setCausedString(nullptr);
            dlg->setCausedOperation(AMUIParseExpressionElement<TStringType>::Operations::AND);
            dlg->setCausedByButton(true);
        }
        return dlg->isNull();
    }
} // AMDialogs