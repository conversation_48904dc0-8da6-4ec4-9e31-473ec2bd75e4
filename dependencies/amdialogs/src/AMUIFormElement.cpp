//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElement.h"
#include "amcore/AMAssert.h"
#include "../AMUIFormCallbacks.h"

namespace AMDialogs {

    AMUIFormElement::AMUIFormElement(const std::string key, bool mandatory, bool nullable, bool hidden)
        : m_changeRequestByEnter(false),
          m_modified(false),
          m_silentModified(false),
          m_state(AMUIWidgetState::READONLY),
          m_length(0),
          m_key(key),
          m_label(),
          m_callback(AMUIInputCallbackEmpty),
          m_needUpdateState(false),
          m_align(LEFT),
          m_sizeX(-FLT_MAX),
          m_enterCallback(),
          m_enterCallbackParam(0),
          m_lastKeyEvent(NONE),
          m_activateRequest(false),
          m_nullable(nullable),
          //m_null(false),
          m_mandatory(mandatory),
          m_hidden(hidden)
          //m_needUpdateStateLostFocus(false)
    {
    };


    void AMUIFormElement::initBase(int length, const std::string label) {
        m_length = length;
        m_label = label;
        m_needUpdateState = true;
    }

    void AMUIFormElement::wasModified() {
        if (m_changeRequestByEnter ) {
            m_silentModified = true;
        } else {
            m_modified = true;
        }
        m_needUpdateState = true;
    }

    AMUIWidgetState AMUIFormElement::state() {
        return m_state;
    }

    void AMUIFormElement::updateState() {
        m_needUpdateState = false;
    }

    void AMUIFormElement::wasHitEnter()
    {
        if (m_changeRequestByEnter) {
            m_modified = true;
            m_silentModified = false;
            m_needUpdateState = true;
        }
        m_lastKeyEvent = ENTER;
    }

    void AMUIFormElement::lostFocus()
    {
        if (m_changeRequestByEnter && m_silentModified) {
            m_modified = true;
            m_silentModified = false;
            m_needUpdateState = true;
        }
    }

    void AMUIFormElement::setEnterCallback(std::function<void(int, AMUIFormElement*, ELastEnterBackspaceEvent)> callback, int param)
    {
        m_enterCallback = callback;
        m_enterCallbackParam = param;
    }

    void AMUIFormElement::wasHitSpecialKey(AMUIFormElement::ELastEnterBackspaceEvent ev)
    {
        m_lastKeyEvent = ev;
    }

    void AMUIFormElement::activate()
    {
        m_activateRequest = true;
    }

    void AMUIFormElement::loadDefault()
    {
    }

}
