//
// Created by <PERSON><PERSON><PERSON> on 11.10.22.
//

#include "../AMUIParsers.h"
#include "amcore/AMAssert.h"
#include <map>
#include <cstring>
#include "amcore/AMRange.h"
#include "AMUIParsers.hpp"
#include "amdialogs/AMUISynthesizers.h"

extern int defaultTimeOffset;

namespace AMDialogs {

    static const int wilcardsPows[] = {0, 9, 99, 999, 9999, 99999, 999999};

    bool _parseDatetimeInt(int &nameMin, int &nameMax, int min, int max, int wildcardsMax, char wildcard, std::istream &is, std::ostream *os, int *digitsRead = nullptr)
    {
        std::ostringstream temps;
        if (!AMUIParseNatural(is, &nameMin, &nameMax, &temps, wildcard, digitsRead)) {
            nameMin = -1;//min;
            nameMax = -1;//min;
            return false;
        }
        if (nameMin != nameMax) {
            nameMax--;
        }
        if (nameMin == nameMax && (nameMax > max || nameMin < min)) {
            nameMin = -1;//min;
            nameMax = -1;//min;
            return false;
        }
        if (nameMax > wilcardsPows[wildcardsMax]) {
            nameMin = -1;//min;
            nameMax = -1;//min;
            return false;
        }
        AMCore::AMRange possibilities = AMCore::AMRange(min, max).intersect(
            AMCore::AMRange(nameMin, nameMax));
        if (nameMin == nameMax || (nameMin != nameMax && !possibilities.empty())) {
            std::string s = temps.str();
            if (os) {
                os->write(s.c_str(), s.size());
            }
            if (wildcard) {
                nameMin = possibilities.from;
                nameMax = possibilities.to;
            }
        } else {
            nameMin = -1;//min;
            nameMax = -1;//min;
            return false;
        }
        return true;
    }
/*
    static bool _parseDatetimeWildcard(int desiredWildcards, char wildcard, std::istream &is, std::ostream *os)
    {
        int wc = 0;
        int c;
        c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        while (c == wildcard) {
            wc++;
            //if (wc > desiredWildcards) {
            //    return false;
            //}
            if (os) {
                AMCore::AMStreamPut(*os, c);
            }
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        }
        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (wc != desiredWildcards) {
            return false;
        }
        return true;
    }*/

    bool _parseDayMonth(bool &dateChecked, int &year, int &month, int &day)
    {
        if (!dateChecked && year >= 0 && month >= 1 && day >= 1) {
            dateChecked = true;
            // Return true if year
            // is a multiple of 4 and
            // not multiple of 100.
            // OR year is multiple of 400.
            bool leap =
                    (((year % 4 == 0) &&
                      (year % 100 != 0)) ||
                      (year % 400 == 0));
            // Handle February month
            // with leap year
            if (month == 2) {
                if (leap) {
                    if (day > 29) {
                        day = 29;
                        return false;
                    }
                } else {
                    if (day > 28) {
                        day = 28;
                        return false;
                    }
                }
            }
            // Months of April, June,
            // Sept and Nov must have
            // number of days less than
            // or equal to 30.
            if (month == 4 || month == 6 ||
                month == 9 || month == 11) {
                if (day > 30) {
                    day = 30;
                    return false;
                }
            } else {
                if (day > 31) {
                    day = 31;
                    return false;
                }
            }
        }
        return true;
    }

    AMUIParseResult AMUIParseDatetime(std::istream &is, AMCore::AMDatetimeus *rMin, AMCore::AMDatetimeus *rMax, std::ostream *os, std::string format, char wildcard, bool dateShift)
    {
        AMCore::AMStreamResetContext(is);
        //printf("LI DATE=%s\n", nl_langinfo(D_T_FMT));
        std::string::iterator fit = format.begin();
        //int wildcards = 0;
        //bool anyWildcard = false;
        int yearMax = -1, dayMax = -1, monthMax = -1, hoursMax = -1, minsMax = -1, secsMax = -1, usecMax = -1;
        int yearMin = -1, dayMin = -1, monthMin = -1, hoursMin = -1, minsMin = -1, secsMin = -1, usecMin = -1;
        bool dateCheckedMin = false;
        bool dateCheckedMax = false;
        bool empty = true;
        while (fit != format.end()) {
            unsigned int c;
            bool dcMin = _parseDayMonth(dateCheckedMin, yearMin, monthMin, dayMin);
            bool dcMax = _parseDayMonth(dateCheckedMax, yearMax, monthMax, dayMax);
            if (!dcMin && !dcMax) {
                empty = true;
                break;
            }
            /*
            if (anyWildcard) {
                if (*fit == '%') {
                    fit++;
                    bool cont = false;
                    AMAssert(fit != format.end());
                    switch (*fit) {
                        case 'Y':
                            cont = _parseDatetimeWildcard(4, wildcard, is, os);
                            yearMin = 0;
                            yearMax = 9999;
                            break;
                        case 'm':
                            cont = _parseDatetimeWildcard(2, wildcard, is, os);
                            monthMin = 1;
                            monthMax = 12;
                            break;
                        case 'd':
                            cont = _parseDatetimeWildcard(2, wildcard, is, os);
                            dayMin = 1;
                            dayMax = 31;
                            break;
                        case 'H':
                            cont = _parseDatetimeWildcard(2, wildcard, is, os);
                            hoursMin = 0;
                            hoursMax = 23;
                            break;
                        case 'M':
                            cont = _parseDatetimeWildcard(2, wildcard, is, os);
                            minsMin = 0;
                            minsMax = 59;
                            break;
                        case 'S':
                            cont = _parseDatetimeWildcard(2, wildcard, is, os);
                            secsMin = 0;
                            secsMax = 59;
                            break;
                        case 'Q':
                            cont = _parseDatetimeWildcard(6, wildcard, is, os);
                            usecMin = 0;
                            usecMax = 999999;
                            break;
                        default:
                            break;
                    }
                    if (cont) {
                        fit++;
                        continue;
                    }

                     //   AMCore::AMStreamToLowercaseStripDiaUnget(is);

                    break;
                } else {
                    c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                    if (c != *fit) {
                        if (c != UEOF) {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                        } else {
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                        }
                        break;
                    } else {
                        if (os) {
                            AMCore::AMStreamPut(*os, c);
                        }
                    }
                }
                fit++;
                continue;
            }
             */
            if (*fit == '%') {
                fit++;
                bool nexit = false;
                AMAssert(fit != format.end());
                switch (*fit) {
                    case '%': {
                        c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                        if (c == '%') {
                            nexit = true;
                        }
                        break;
                    }
                    case 'n': {
                        c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                        if (c != '\n' && c != '\r') {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                        } else {
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            nexit = true;
                        }
                        break;
                    }
                    case 't': {
                        c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                        if (c != '\t') {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                        } else {
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            nexit = true;
                        }
                        break;
                    }
                    case 'Y':
                        nexit = _parseDatetimeInt(yearMin, yearMax, 0, 9999, 4, wildcard, is, os);
                        //if (yearMin != yearMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'm':
                        nexit = _parseDatetimeInt(monthMin, monthMax, 1, 12, 2, wildcard, is, os);
                        //if (monthMin != monthMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'd':
                        nexit = _parseDatetimeInt(dayMin, dayMax, 1, 31, 2, wildcard, is, os);
                        //if (dayMin != dayMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'H':
                        nexit = _parseDatetimeInt(hoursMin, hoursMax, 0, 23, 2, wildcard, is, os);
                        //if (hoursMin != hoursMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'M':
                        nexit = _parseDatetimeInt(minsMin, minsMax, 0, 59, 2, wildcard, is, os);
                        //if (minsMin != minsMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'S':
                        nexit = _parseDatetimeInt(secsMin, secsMax, 0, 59, 2, wildcard, is, os);
                        //if (secsMin != secsMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    case 'Q': {
                        int digitsRead = -1;
                        nexit = _parseDatetimeInt(usecMin, usecMax, 0, 999999, 6, wildcard, is, os, &digitsRead);
                        switch(digitsRead) {
                            case 5: usecMin *= 10; usecMax *= 10; break;
                            case 4: usecMin *= 100; usecMax *= 100; break;
                            case 3: usecMin *= 1000; usecMax *= 1000; break;
                            case 2: usecMin *= 10000; usecMax *= 10000; break;
                            case 1: usecMin *= 100000; usecMax *= 100000; break;
                            default: break;
                        }
                        //if (usecMin != usecMax) {
                        //    anyWildcard = true;
                        //}
                        break;
                    }
                    default:
                        break;
                }
                //TODO upravit
                //anyWildcard = false;
                if (!nexit) {
                    //fit--;
                    //if (fit == format.begin()) {
                    //    break;
                    //}
                    //fit --;
                    //if (std::isspace(*fit)) {
                        AMCore::AMStreamToLowercaseStripDiaUnget(is);
                    //}
                    break;
                }
                empty = false;
            } else {
                c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                if (c != *fit) {
                    //if (c != UEOF) {
                        AMCore::AMStreamToLowercaseStripDiaUnget(is);
                    /*} else {
                        if (os) {
                            AMCore::AMStreamPut(*os, c);
                        }
                    }*/
                    break;
                } else {
                    if (os) {
                        AMCore::AMStreamPut(*os, c);
                    }
                }
            }
            fit++;
        }


        if (yearMin >= 0 && yearMax >= 0 && yearMin != yearMax) {
            monthMin = 1;
            monthMax = 12;
        }
        if (monthMin >= 0 && monthMax >= 0 && monthMin != monthMax) {
            dayMin = 1;
            dayMax = 31;
        }
        if (dayMin >= 0 && dayMax >= 0 && dayMin != dayMax) {
            hoursMin = 0;
            hoursMax = 23;
        }
        if (hoursMin >= 0 && hoursMax >= 0 && hoursMin != hoursMax) {
            minsMin = 0;
            minsMax = 59;
        }
        if (minsMin >= 0 && minsMax >= 0 && minsMin != minsMax) {
            secsMin = 0;
            secsMax = 59;
        }
        if (secsMin >= 0 && secsMax >= 0 && secsMin != secsMax) {
            usecMin = 0;
            usecMax = 999999;
        }


        if (rMin) {
            rMin->tm_year = (yearMin >= 0) ? yearMin - 1900 : 0;
            rMin->tm_mon = (monthMin >= 0) ? monthMin - 1 : 0;
            rMin->tm_mday = (dayMin >= 1) ? dayMin : 1;
            rMin->tm_hour = (hoursMin >= 0) ? hoursMin : 0;
            rMin->tm_min = (minsMin >= 0) ? minsMin : 0;
            rMin->tm_sec = (secsMin >= 0) ? secsMin : 0;
            rMin->usec = (usecMin >= 0) ? usecMin : 0;
            rMin->tm_isdst = -1;
            rMin->tm_gmtoff = 0;
            if (dateShift) {
                AMMktimeWithShift(rMin);
            } else {
                AMMktimeNoShift(rMin);
            }
        }
        if (rMax) {
            rMax->tm_year = (yearMax >= 0) ? yearMax - 1900 : (/*anyWildcard*/wildcard != '\0' ? 9999 - 1900 : 0);
            rMax->tm_mon = (monthMax >= 0) ? monthMax - 1 : (/*anyWildcard*/wildcard != '\0' ? 12 - 1 : 0);
            int iday = 99;
            bool bunused = false;
            monthMax = monthMax < 0 ? 1 : monthMax;
            bool b = _parseDayMonth(bunused,yearMax, monthMax, iday);
            dayMax = dayMax > iday ? iday : dayMax;
            rMax->tm_mday = (dayMax >= 1) ? dayMax : (/*anyWildcard*/wildcard != '\0' ? iday : 1);
            rMax->tm_hour = (hoursMax >= 0) ? hoursMax : (/*anyWildcard*/wildcard != '\0' ? 23 : 0);
            rMax->tm_min = (minsMax >= 0) ? minsMax : (/*anyWildcard*/wildcard != '\0' ? 59 : 0);
            rMax->tm_sec = (secsMax >= 0) ? secsMax : (/*anyWildcard*/wildcard != '\0' ? 59 : 0);
            rMax->usec = (usecMax >= 0) ? usecMax : (/*anyWildcard*/wildcard != '\0' ? 999999 : 0);
            rMax->tm_isdst = -1;
            rMax->tm_gmtoff = 0;
            if (dateShift) {
                AMMktimeWithShift(rMax);
            } else {
                AMMktimeNoShift(rMax);
            }
        }
        if (rMax && (rMax->tm_year < 0 || rMax->tm_year > 138) || rMin && (rMin->tm_year < 0 || rMin->tm_year > 138)) {
            return AMUIParseResult::FAIL;
        }
        return fit == format.end() ? AMUIParseResult::SUCCESS : (empty ? AMUIParseResult::FAIL : AMUIParseResult::PARTIAL);
    }

    AMUIParseResult AMUIParseDatetime(std::istream &is, std::tm *rMin, std::tm *rMax, std::ostream *os, std::string format, char wildcard)
    {
        AMCore::AMDatetimeus urMin, urMax;
        AMUIParseResult res = AMUIParseDatetime(is, &urMin, &urMax, os, format, wildcard);
        if (rMin) {
            *rMin = static_cast<std::tm>(urMin);
        }
        if (rMax) {
            *rMax = static_cast<std::tm>(urMax);
        }
        return res;
    }

    AMUIParseResult AMUIParseDatetime(std::istream &is, AMCore::AMNullable<std::tm> *rMin, AMCore::AMNullable<std::tm> *rMax, std::ostream *os, std::string format,char wildcard)
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (rMin) {
                rMin->set();
            }
            if (rMax) {
                rMax->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (rMin) {
                rMin->set();
            }
            if (rMax) {
                rMax->set();
            }
            return AMUIParseResult::PARTIAL;
        }
        std::tm tMin, tMax;
        AMUIParseResult pr = AMUIParseDatetime(is, &tMin, &tMax, os, format, wildcard);
        if (pr != AMUIParseResult::FAIL) {
            if (rMax) {
                rMax->set(tMax);
            }
            if (rMin) {
                rMin->set(tMin);
            }
        }
        return pr;
    }

    AMUIParseResult AMUIParseDatetime(std::istream &is, AMCore::AMNullable<AMCore::AMDatetimeus> *rMin, AMCore::AMNullable<AMCore::AMDatetimeus> *rMax, std::ostream *os, std::string format,char wildcard)
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (rMin) {
                rMin->set();
            }
            if (rMax) {
                rMax->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (rMin) {
                rMin->set();
            }
            if (rMax) {
                rMax->set();
            }
            return AMUIParseResult::PARTIAL;
        }
        AMCore::AMDatetimeus tMin, tMax;
        AMUIParseResult pr = AMUIParseDatetime(is, &tMin, &tMax, os, format, wildcard);
        if (pr != AMUIParseResult::FAIL) {
            if (rMax) {
                rMax->set(tMax);
            }
            if (rMin) {
                rMin->set(tMin);
            }
        }
        return pr;
    }

    AMUIParseResult AMUIParseBool(std::istream &is, bool *r, std::string _trueString, std::string _falseString, std::ostream *os)
    {
        AMCore::AMStreamResetContext(is);
        bool empty = true;
        unsigned int original_c;
        unsigned int c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        bool is_true = true;
        bool is_false = true;
        std::string trueString = _trueString;
        std::string falseString = _falseString;
        AMCore::AMStringToLowercaseStripDia(trueString);
        AMCore::AMStringToLowercaseStripDia(falseString);
        std::string::iterator tit = trueString.begin();
        std::string::iterator fit = falseString.begin();
        AMAssert(tit != trueString.end());
        AMAssert(fit != falseString.end());
        while (std::isspace(c)) {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        }
        do {
            if (c == UEOF) {
                break;
            }
            if (is_true && tit != trueString.end()) {
                if (std::isspace(c) && !std::isspace(*tit)) {
                    break;
                }
                is_true = c == *tit;
                tit++;
                if (!is_true) {
                    tit = trueString.end();
                }
            }
            if (is_false && fit != falseString.end()) {
                if (std::isspace(c) && !std::isspace(*fit)) {
                    break;
                }
                is_false = c == *fit;
                fit++;
                if (!is_false) {
                    fit = falseString.end();
                }
            }
            if (!is_false && !is_true) {
                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                return AMUIParseResult::FAIL;
            }
            if (os) {
                AMCore::AMStreamPut(*os, original_c);
            }
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);

            if (is_true && tit == trueString.end()) {
                if (!std::isspace(c) && c != UEOF) {
                    is_true = false;
                }
            }
            if (is_false && fit == falseString.end()) {
                if (!std::isspace(c) && c != UEOF) {
                    is_false = false;
                }
            }

        } while (fit != falseString.end() || tit != trueString.end());
        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (is_true && is_false) {
            if (r) {
                *r = (trueString.size() < falseString.size());
            }
            return AMUIParseResult::SUCCESS;
        }
        if (r) {
            *r = is_true;
        }
        if (is_true) {
            return tit == trueString.end() ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
        }
        return fit == falseString.end() ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
    }

    AMUIParseResult AMUIParseBool(std::istream &is, AMCore::AMNullable<bool> *r, std::string _trueString, std::string _falseString, std::ostream *os)
    {
        AMCore::AMStreamResetContext(is);
        bool brv;
        int tg  = is.tellg();
        std::ostringstream nos;
        AMUIParseResult npr = AMUIParseNull(is, &brv, &nos);
        if (npr == AMUIParseResult::SUCCESS) {
            if (r) {
                r->set();
            }
            if (os) {
                (*os)<<nos.str();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (r) {
                r->set();
            }
            if (os) {
                (*os)<<nos.str();
            }
            return AMUIParseResult::PARTIAL;
        }
        bool trv;
        is.seekg(tg, std::ios_base::beg);
        AMUIParseResult pr = AMUIParseBool(is, &trv, _trueString, _falseString, os);
        if (r && pr != AMUIParseResult::FAIL) {
            r->set(trv);
        }
        return pr;
    }
    AMUIParseResult AMUIParseNull(std::istream &is, bool *r, std::ostream *os)
    {
        AMCore::AMStreamResetContext(is);
        bool non_empty = false;
        unsigned int original_c;
        unsigned int c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        while (std::isspace(c)) {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        }
        const char* null_ptr = AMCore::AMNullString;
        do {
            if (c == UEOF) {
                if (r) {
                    *r = non_empty;
                }
                return AMUIParseResult::PARTIAL;
            } else if (c == (int)*null_ptr) {
                if (os) {
                    AMCore::AMStreamPut(*os, original_c);
                }
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
                null_ptr++;
                non_empty = true;
            } else if (std::isspace(c)) {
                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                return AMUIParseResult::PARTIAL;
            } else {
                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                if (non_empty) {
                    return AMUIParseResult::FAIL;
                }
                if (r) {
                    *r = false;
                }
                return AMUIParseResult::PARTIAL;
            }
        } while (*null_ptr != '\0');
        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (c != UEOF && !std::isspace(c)) {
            if (non_empty) {
                return AMUIParseResult::FAIL;
            }
        }
        if (r) {
            *r = non_empty;
        }
        return AMUIParseResult::SUCCESS;
    }



    template AMUIParseResult AMUIParseNatural(std::istream &, int *, int *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<int> *, AMCore::AMNullable<int> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, unsigned int *, unsigned int *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<unsigned int> *, AMCore::AMNullable<unsigned int> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, short *, short *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<short> *, AMCore::AMNullable<short> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, unsigned short *, unsigned short *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<unsigned short> *, AMCore::AMNullable<unsigned short> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, long *, long *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<long> *, AMCore::AMNullable<long> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long *, unsigned long *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<unsigned long> *, AMCore::AMNullable<unsigned long> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, long long *, long long *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<long long> *, AMCore::AMNullable<long long> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, unsigned long long *, unsigned long long *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<unsigned long long> *, AMCore::AMNullable<unsigned long long> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, char *, char*, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<char> *, AMCore::AMNullable<char> *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, unsigned char *, unsigned char *, std::ostream *, char, int *);
    template AMUIParseResult AMUIParseNatural(std::istream &, AMCore::AMNullable<unsigned char> *, AMCore::AMNullable<unsigned char> *, std::ostream *, char, int *);

    template AMUIParseResult AMUIParseInteger(std::istream &, int *, int *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<int> *, AMCore::AMNullable<int> *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, short *, short *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<short> *, AMCore::AMNullable<short> *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, long *, long *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<long> *, AMCore::AMNullable<long> *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, long long *, long long *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<long long> *, AMCore::AMNullable<long long> *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, char *, char *, std::ostream *, char);
    template AMUIParseResult AMUIParseInteger(std::istream &, AMCore::AMNullable<char> *, AMCore::AMNullable<char> *, std::ostream *, char);

    template AMUIParseResult AMUIParseFloat(std::istream &, float *, float *, std::ostream *, char);
    template AMUIParseResult AMUIParseFloat(std::istream &, AMCore::AMNullable<float> *, AMCore::AMNullable<float> *, std::ostream *, char);
    template AMUIParseResult AMUIParseFloat(std::istream &, double *, double *, std::ostream *, char);
    template AMUIParseResult AMUIParseFloat(std::istream &, AMCore::AMNullable<double> *, AMCore::AMNullable<double> *, std::ostream *, char);

    template AMUIParseResult AMUIParseEnum(std::istream &, int *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, std::ostream *);
    template AMUIParseResult AMUIParseEnum(AMCore::AMStringCZ &, int *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);
    template AMUIParseResult AMUIParseEnumStartsWith(AMCore::AMStringCZ &, int *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, bool, AMCore::AMStringCZ*);
    template AMUIParseResult AMUIParseEnum(std::istream &, AMCore::AMNullable<int> *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, std::ostream *);
    template AMUIParseResult AMUIParseEnum(AMCore::AMStringCZ &, AMCore::AMNullable<int> *, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);
    template AMUIParseResult AMUIParseEnumLine(AMCore::AMStringCZ &, std::map<int, int> &,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table, AMCore::AMStringCZ separator);
    template AMUIParseResult AMUIParseEnumMapLine(AMCore::AMStringCZ &, std::map<int, int> &, std::map<int, std::map<int, int> >&, const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>&,AMCore::AMStringCZ, AMCore::AMStringCZ);
    template AMUIParseResult AMUIParseEnumLine(AMCore::AMStringCZ &, std::map<int, int> &, bool &,  const AMCore::AMTwoWayTable<int, AMCore::AMStringCZ>& table);



    std::string _AMGetStringWithTime(const char *format)
    {
        auto t = std::time(nullptr);
        auto tm = *std::localtime(&t);
        char buffer[128];
        strftime(buffer, sizeof(buffer) / sizeof(char), format, &tm);
        return std::string(buffer);
    }

    //template<typename TStringType>
    //TStringType AMUISynthesizeDatetime(std::string format, const AMCore::AMDatetimeus *min, const AMCore::AMDatetimeus *max = nullptr, char wildcard = '\0');

    std::string _AMGetStringWithTimeus(const char *format)
    {
        auto t = std::time(nullptr);
        AMCore::AMDatetimeus tm = *std::localtime(&t);
        tm.usec = 0;
        return AMDialogs::AMUISynthesizeDatetime<std::string>(std::string(format), &tm);
    }


    void AMMktimeNoShift(std::tm *t)
    {
        t->tm_isdst = 0;
        t->tm_gmtoff = 0;
        t->tm_zone = nullptr;

        std::tm referenceTm = *t;
        referenceTm.tm_mon = 0;
        referenceTm.tm_mday = 1;
        mktime(&referenceTm);

        mktime(t);

        long off = referenceTm.tm_gmtoff - t->tm_gmtoff;
        if (off != 0) {
            t->tm_sec += off;
            mktime(t);
        }
    }

    void AMMktimeWithShift(std::tm *t)
    {
        t->tm_sec += defaultTimeOffset;
        mktime(t);
    }

    std::set<int> si_from_string(std::string input)
    {
        std::string str = input.substr(1, input.size() - 2);
        auto inps = AMCore::AMExplode(",", str);
        std::set<int> rv;
        for(auto it: inps) {
            rv.insert(std::stoi(it.second));
        }
        return rv;
    }

    template<typename TEnumType>
    std::map<TEnumType, int> em_from_string(std::string input)
    {
        std::string str = input.substr(1, input.size() - 2);
        auto inps = AMCore::AMExplode(",", str);
        std::map<TEnumType, int> rv;
        int ndx = 0;
        for(auto it: inps) {
            rv.insert({std::stoi(it.second), ndx++});
        }
        return rv;
    }

    template std::map<int, int> em_from_string<int>(std::string input);

    const char andText[32] = "A";
    const char orText[32] = "NEBO";
    const char datetimeFormatText[32] = "%Y.%m.%d %H:%M:%S";
    const char datetimeusFormatText[32] = "%Y.%m.%d %H:%M:%S.%Q";
    const char dateFormatText[32] = "%d.%m.%Y";
    const char timeFormatText[32] = "%H:%M:%S";




}

bool operator==(const std::tm& left, const std::tm& right)
{
    if (left.tm_year != right.tm_year) {
        return false;
    }
    std::tm tml = left;
    //tml.tm_year = 0;
    time_t tl = mktime(&tml);
    std::tm tmr = right;
    //tmr.tm_year = 0;
    time_t tr = mktime(&tmr);
    return tl == tr;
}

bool operator<(const std::tm& left, const std::tm& right)
{
    if (left.tm_year < right.tm_year) {
        return true;
    }
    if (left.tm_year > right.tm_year) {
        return false;
    }
    std::tm tml = left;
    //tml.tm_year = 0;
    time_t tl = mktime(&tml);
    std::tm tmr = right;
    //tmr.tm_year = 0;
    time_t tr = mktime(&tmr);
    return tl < tr;
}

int yearsTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int yrs = right.tm_year - left.tm_year;
    if (yrs >= 0) {
        if (right.tm_mon < left.tm_mon) {
            yrs --;
        } else if (right.tm_mon == left.tm_mon) {
            if (right.tm_mday < left.tm_mday) {
                yrs --;
            } else if (right.tm_mday == left.tm_mday) {
                if (right.tm_hour < left.tm_hour) {
                    yrs --;
                } else if (right.tm_hour == left.tm_hour) {
                    if (right.tm_min < left.tm_min) {
                        yrs --;
                    } else if (right.tm_min == left.tm_min) {
                        if (right.tm_sec < left.tm_sec) {
                            yrs --;
                        } else if (right.tm_sec == left.tm_sec) {
                            if (right.usec < left.usec) {
                                yrs --;
                            }
                        }
                    }
                }
            }
        }
    } else { //yrs < 0
        if (right.tm_mon > left.tm_mon) {
            yrs ++;
        } else if (right.tm_mon == left.tm_mon) {
            if (right.tm_mday > left.tm_mday) {
                yrs ++;
            } else if (right.tm_mday == left.tm_mday) {
                if (right.tm_hour > left.tm_hour) {
                    yrs ++;
                } else if (right.tm_hour == left.tm_hour) {
                    if (right.tm_min > left.tm_min) {
                        yrs ++;
                    } else if (right.tm_min == left.tm_min) {
                        if (right.tm_sec > left.tm_sec) {
                            yrs ++;
                        } else if (right.tm_sec == left.tm_sec) {
                            if (right.usec > left.usec) {
                                yrs ++;
                            }
                        }
                    }
                }
            }
        }
    }
    return yrs;
}
int yearsTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return yearsTo(left, right);
}
int monthsTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int mons = right.tm_mon - left.tm_mon;
    if (mons >= 0) {
        if (right.tm_mday < left.tm_mday) {
            mons --;
        } else if (right.tm_mday == left.tm_mday) {
            if (right.tm_hour < left.tm_hour) {
                mons --;
            } else if (right.tm_hour == left.tm_hour) {
                if (right.tm_min < left.tm_min) {
                    mons --;
                } else if (right.tm_min == left.tm_min) {
                    if (right.tm_sec < left.tm_sec) {
                        mons --;
                    } else if (right.tm_sec == left.tm_sec) {
                        if (right.usec < left.usec) {
                            mons --;
                        }
                    }
                }
            }
        }
    } else { //mons < 0
        if (right.tm_mday > left.tm_mday) {
            mons ++;
        } else if (right.tm_mday == left.tm_mday) {
            if (right.tm_hour > left.tm_hour) {
                mons ++;
            } else if (right.tm_hour == left.tm_hour) {
                if (right.tm_min > left.tm_min) {
                    mons ++;
                } else if (right.tm_min == left.tm_min) {
                    if (right.tm_sec > left.tm_sec) {
                        mons ++;
                    } else if (right.tm_sec == left.tm_sec) {
                        if (right.usec > left.usec) {
                            mons ++;
                        }
                    }
                }
            }
        }
    }
    return mons;
}
int monthsTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return monthsTo(left, right);
}
int daysTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int days = right.tm_mday - left.tm_mday;
    if (days >= 0) {
        if (right.tm_hour < left.tm_hour) {
            days --;
        } else if (right.tm_hour == left.tm_hour) {
            if (right.tm_min < left.tm_min) {
                days --;
            } else if (right.tm_min == left.tm_min) {
                if (right.tm_sec < left.tm_sec) {
                    days --;
                } else if (right.tm_sec == left.tm_sec) {
                    if (right.usec < left.usec) {
                        days --;
                    }
                }
            }
        }
    } else { //days < 0
        if (right.tm_hour > left.tm_hour) {
            days ++;
        } else if (right.tm_hour == left.tm_hour) {
            if (right.tm_min > left.tm_min) {
                days ++;
            } else if (right.tm_min == left.tm_min) {
                if (right.tm_sec > left.tm_sec) {
                    days ++;
                } else if (right.tm_sec == left.tm_sec) {
                    if (right.usec > left.usec) {
                        days ++;
                    }
                }
            }
        }
    }
    return days;
}
int daysTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return daysTo(left, right);
}
int hoursTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int hours = right.tm_hour - left.tm_hour;
    if (hours >= 0) {
        if (right.tm_min < left.tm_min) {
            hours --;
        } else if (right.tm_min == left.tm_min) {
            if (right.tm_sec < left.tm_sec) {
                hours --;
            } else if (right.tm_sec == left.tm_sec) {
                if (right.usec < left.usec) {
                    hours --;
                }
            }
        }
    } else { //hours < 0
        if (right.tm_min > left.tm_min) {
            hours ++;
        } else if (right.tm_min == left.tm_min) {
            if (right.tm_sec > left.tm_sec) {
                hours ++;
            } else if (right.tm_sec == left.tm_sec) {
                if (right.usec > left.usec) {
                    hours ++;
                }
            }
        }
    }
    return hours;
}
int hoursTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return hoursTo(left, right);
}
int minsTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int mins = right.tm_min - left.tm_min;
    if (mins >= 0) {
        if (right.tm_sec < left.tm_sec) {
            mins --;
        } else if (right.tm_sec == left.tm_sec) {
            if (right.usec < left.usec) {
                mins --;
            }
        }
    } else { //mins < 0
        if (right.tm_sec > left.tm_sec) {
            mins ++;
        } else if (right.tm_sec == left.tm_sec) {
            if (right.usec > left.usec) {
                mins ++;
            }
        }
    }
    return mins;
}
int minsTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return minsTo(left, right);
}
int secsTo(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    int secs = right.tm_sec - left.tm_sec;
    if (secs >= 0) {
        if (right.usec < left.usec) {
            secs --;
        }
    } else { //secs < 0
        if (right.usec > left.usec) {
            secs ++;
        }
    }
    return secs;
}
int secsTo(const std::tm& _left, const std::tm& _right) {
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec = 0;
    return secsTo(left, right);
}
AMCore::AMDatetimeus subractTime(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    const int monDays[12] = {31,28,31,30};
    //std::tm res = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};
    AMCore::AMDatetimeus res;
    res.tm_year = yearsTo(right, left);
    res.tm_mon = monthsTo(right, left);
    if (res.tm_mon < 0 && res.tm_year >= 0) {
        res.tm_mon += 12;
    } else if (res.tm_mon >= 0 && res.tm_year < 0) {
        res.tm_mon -= 12;
    }
    res.tm_mday = daysTo(right, left);
    if (res.tm_mday < 0 && res.tm_mon >= 0) {
        bool dateChecked = false;
        int day = 31;
        int year = left.tm_year;
        int month= left.tm_mon;
        AMDialogs::_parseDayMonth(dateChecked, year, month, day);
        res.tm_mday += day;
    } else if (res.tm_mday >= 0 && res.tm_mon < 0) {
        bool dateChecked = false;
        int day = 31;
        int year = left.tm_year;
        int month= left.tm_mon;
        month --;
        if (month < 0) {
            month += 12;
        }
        AMDialogs::_parseDayMonth(dateChecked, year, month, day);
        res.tm_mday -= day;
    }
    res.tm_hour = hoursTo(right, left);
    if (res.tm_hour < 0 && res.tm_mday >= 0) {
        res.tm_hour += 24;
    } else if (res.tm_hour >= 0 && res.tm_mday < 0) {
        res.tm_hour -= 24;
    }
    res.tm_min = minsTo(right, left);
    if (res.tm_min < 0 && res.tm_hour >= 0) {
        res.tm_min += 60;
    } else if (res.tm_min >= 0 && res.tm_hour < 0) {
        res.tm_min -= 60;
    }
    res.tm_sec = secsTo(right, left);
    if (res.tm_sec < 0 && res.tm_min >= 0) {
        res.tm_sec += 60;
    } else if (res.tm_sec >= 0 && res.tm_min < 0) {
        res.tm_sec -= 60;
    }
    res.usec = left.usec - right.usec;
    if (res.usec < 0 && res.tm_sec >= 0) {
        res.usec += 1000000;
    } else if (res.usec >= 0 && res.tm_sec < 0) {
        res.usec -= 1000000;
    }
    return res;
}
std::tm subractTime(const std::tm& _left, const std::tm& _right)
{
    AMCore::AMDatetimeus left = _left;
    left.usec = 0;
    AMCore::AMDatetimeus right = _right;
    right.usec;
    return (std::tm) subractTime(left, right);
}

bool operator==(const AMCore::AMDatetimeus& left, const AMCore::AMDatetimeus& right)
{
    std::tm tml = left;
    time_t tl = mktime(&tml);
    std::tm tmr = right;
    time_t tr = mktime(&tmr);
    if (tl == tr) {
        return left.usec == right.usec;
    }
    return false;
}

bool isDateZero(const std::tm& t)
{
    return t.tm_year == 0 && t.tm_mon == 0 && t.tm_mday == 0;
}
