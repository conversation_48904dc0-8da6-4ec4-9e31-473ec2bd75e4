//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogAuthorTag.h"
#include "amdialogs/AMUIDialogAuthorTagView.h"

namespace AMDialogs {

    template<typename TStringType>
    void AMUIDialogAuthorTag<TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            char wildcard
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogAuthorTagView<TStringType>();
        v->init(this);
        m_view = v;
        m_datetime.initBase(32, "Datum a čas");
        m_datetime.init(AMCore::AMDataType_datetime);
        m_datetime.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_datetime.setWildcard(wildcard);
        m_author.initBase(32, "Autor");
        m_author.init();
        m_author.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_wildcard = wildcard;
    }

    template<typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogAuthorTag<TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TStringType>
    AMUIDialogAuthorTag<TStringType>::AMUIDialogAuthorTag()
        : AMUIDialog(),
          m_datetime("datetime"),
          m_author("author"),
          m_elements{&m_datetime, &m_author},
          m_wildcard('\0')
    {
    }

    template<typename TStringType>
    AMUIFormElementDatetime &AMUIDialogAuthorTag<TStringType>::datetime()
    {
        return m_datetime;
    }

    template<typename TStringType>
    AMUIFormElementString &AMUIDialogAuthorTag<TStringType>::author()
    {
        return m_author;
    }


} // AMDialogs