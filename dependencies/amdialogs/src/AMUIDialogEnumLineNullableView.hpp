//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogEnumLineNullableView.h"
#include "amdialogs/AMUIDialogEnumLineNullable.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIDialogEnumLineNullableView<TEnumType, TStringType>::AMUIDialogEnumLineNullableView()
        : AMUIDialogEnumLineView<TEnumType, TStringType>()
    {

    }

    template<typename TEnumType, typename TStringType>
    bool AMUIDialogEnumLineNullableView<TEnumType, TStringType>::renderNullable()
    {
        AMUIDialogEnumLineNullable<TEnumType, TStringType> *dlg = static_cast<AMUIDialogEnumLineNullable<TEnumType, TStringType> *>(this->m_dialog);

        if (dlg->nullableCheckBox().value() != !dlg->isNull()) {
            dlg->nullableCheckBox().setValue(!dlg->isNull());
        }
        dlg->nullableCheckBox().render(false, dlg->id() * 1000 + 777);
        if (dlg->nullableCheckBox().value() != !dlg->isNull()) {
            dlg->setNull(!dlg->nullableCheckBox().value());
        }
        return dlg->isNull();

    }
} // AMDialogs