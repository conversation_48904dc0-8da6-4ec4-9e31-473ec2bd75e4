//
// Created by <PERSON><PERSON><PERSON> on 16.4.24.
//
#include "amcore/AMDatetimeus.h"

namespace AMDialogs {
    AMCore::AMDatetimeus subtractTime(AMCore::AMDatetimeus &t, int secs)
    {
        AMCore::AMDatetimeus rv = t;
        int tsecs = secs % 60;
        int tmins = secs / 60;
        int thours = tmins / 60;
        tmins = tmins % 60;
        int tdays = thours / 24;
        thours = thours % 24;
        rv.tm_sec -= tsecs;
        rv.tm_min -= tmins;
        rv.tm_hour -= thours;
        rv.tm_mday -= tdays;

        if (rv.tm_sec < 0) {
            rv.tm_sec += 60;
            rv.tm_min--;
        }
        if (rv.tm_min < 0) {
            rv.tm_min += 60;
            rv.tm_hour--;
        }
        if (rv.tm_hour < 0) {
            rv.tm_hour += 24;
            rv.tm_mday--;
        }
        if (rv.tm_mday < 1) {
            rv.tm_mon--;
            if (rv.tm_mon < 0) {
                rv.tm_mon += 12;
                rv.tm_year--;
            }
            bool leap =
                (((rv.tm_year % 4 == 0) &&
                  (rv.tm_year % 100 != 0)) ||
                 (rv.tm_year % 400 == 0));
            // Handle February month
            // with leap tt.tm_year
            if (rv.tm_mon == 1) {
                if (leap) {
                    rv.tm_mday = 29;
                } else {
                    rv.tm_mday = 28;
                }
            }
            // Months of April, June,
            // Sept and Nov must have
            // number of days less than
            // or equal to 30.
            else if (rv.tm_mon == 3 || rv.tm_mon == 5 ||
                rv.tm_mon == 8 || rv.tm_mon == 10) {
                rv.tm_mday = 30;
            } else {
                rv.tm_mday = 31;
            }
        }
        return rv;
    }
    AMCore::AMDatetimeus addTime(AMCore::AMDatetimeus &t, int secs)
    {
        AMCore::AMDatetimeus rv = t;
        int tsecs = secs % 60;
        int tmins = secs / 60;
        int thours = tmins / 60;
        tmins = tmins % 60;
        int tdays = thours / 24;
        thours = thours % 24;
        rv.tm_sec += tsecs;
        rv.tm_min += tmins;
        rv.tm_hour += thours;
        rv.tm_mday += tdays;

        if (rv.tm_sec >= 60) {
            rv.tm_sec -= 60;
            rv.tm_min++;
        }
        if (rv.tm_min >= 60) {
            rv.tm_min -= 60;
            rv.tm_hour++;
        }
        if (rv.tm_hour >= 24) {
            rv.tm_hour -= 24;
            rv.tm_mday++;
        }
        int maxMday;
        do {

            bool leap =
                (((rv.tm_year % 4 == 0) &&
                  (rv.tm_year % 100 != 0)) ||
                 (rv.tm_year % 400 == 0));
            // Handle February month
            // with leap tt.tm_year
            if (rv.tm_mon == 1) {
                if (leap) {
                    maxMday = 29;
                } else {
                    maxMday = 28;
                }
            }
            // Months of April, June,
            // Sept and Nov must have
            // number of days less than
            // or equal to 30.
            else if (rv.tm_mon == 3 || rv.tm_mon == 5 ||
                     rv.tm_mon == 8 || rv.tm_mon == 10) {
                maxMday = 30;
            } else {
                maxMday = 31;
            }
            if (maxMday < rv.tm_mday) {
                rv.tm_mon ++;
                rv.tm_mday -= maxMday;
            } else {
                break;
            }
        } while (maxMday < rv.tm_mday);
        if (rv.tm_mon >= 12) {
            rv.tm_mon -= 12;
            rv.tm_year ++;
        }

        return rv;
    }
}