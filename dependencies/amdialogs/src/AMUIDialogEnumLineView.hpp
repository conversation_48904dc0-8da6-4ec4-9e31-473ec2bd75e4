//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogEnumLineView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogEnumLine.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIDialogEnumLineView<TEnumType, TStringType>::AMUIDialogEnumLineView()
        : AMUIDialogView()
    {

    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogEnumLineView<TEnumType, TStringType>::render()
    {
        if (renderBegin()) {

            bool grayed = renderNullable();

            AMUIDialogEnumLine<TEnumType, TStringType> *dlg = static_cast<AMUIDialogEnumLine<TEnumType, TStringType> *>(m_dialog);

            if (!dlg->hintText().empty()) {
                ImGui::Text("%s\n", dlg->hintText().c_str());
            }


            for(auto &el: dlg->elementsStor()) {
                bool oldValue = el.element.value();
                el.element.render(grayed, dlg->id() * 1000);
                if (el.element.value() != oldValue) {
                    if (el.element.value()) {
                        dlg->selected()->insert({el.value, dlg->selected()->size()});
                    } else {
                        auto itd = dlg->selected()->find(el.value);
                        int pos = itd->second;
                        dlg->selected()->erase(itd);
                        for(auto &it: *dlg->selected()) {
                            if (it.second > pos) {
                                it.second--;
                            }
                        }
                    }
                }
            }

            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIDialogEnumLineView<TEnumType, TStringType>::renderNullable()
    {
        return false;
    }
} // AMDialogs