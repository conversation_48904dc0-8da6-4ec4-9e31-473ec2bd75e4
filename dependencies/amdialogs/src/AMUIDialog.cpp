//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialog.h"
#include "amui/AMUIApp.h"
#include "amdialogs/AMUIDialogManager.h"
#include "amdialogs/AMUIDialogView.h"
namespace AMDialogs {

    int AMUIDialog::g_id = 0;

    AMUIDialog::AMUIDialog()
        : m_view(nullptr),
          m_dlgName(),
          m_id(g_id),
          m_onClose(),
          m_onChange(),
          m_closeRequest(false),
          m_closedByCloseButton(false),
          m_closeButtonText("Zavřít"),
          m_enterHitClosesDialog(true)
    {
        g_id++;
    };

    AMUIDialog::~AMUIDialog()
    {
        AMUI::AMUIApp::instance().removePerformCallback(this);
        if (m_view) {
            delete m_view;
        }
    }

    void AMUIDialog::init(std::string name, std::function<void(AMUIDialog *)> on_close, std::function<void(AMUIDialog *)> on_change)
    {
        m_dlgName = name;
        m_onClose = std::move(on_close);
        m_onChange = std::move(on_change);
        AMUI::AMUIApp::instance().addPerformCallback(this, &AMUIDialog::perform);
    }

    AMUIDialogView *AMUIDialog::view()
    {
        return m_view;
    }

    int AMUIDialog::id() const
    {
        return m_id;
    }

    std::string AMUIDialog::name()
    {
        return m_dlgName;
    }

    std::vector<AMUIFormElement *> &AMUIDialog::elements()
    {
        static std::vector<AMUIFormElement *> empty{};
        return empty;
    }

    void AMUIDialog::perform()
    {
        std::vector<AMUIFormElement *> &elems = elements();
        bool inform = false;
        for (AMUIFormElement *el: elems) {
            if (el->needUpdateState()) {
                el->updateState();
                if (el->modified()) {
                    inform = true;
                }
            }
        }
        if (inform) {
            onChange();
        }
        if (m_closeRequest) {
            m_closeRequest = false;
            onClose();
        }
    }

    void AMUIDialog::onChange()
    {
        if (m_onChange) {
            m_onChange(this);
        }
    }

    void AMUIDialog::onClose()
    {
        if (m_onClose) {
            m_onClose(this);
        }
        AMUIDialogManager::instance().deleteDialog(this);
    }

    void AMUIDialog::closeRequest(bool byCloseButton)
    {
        m_closeRequest = true;
        m_closedByCloseButton = byCloseButton;
    }

    AMUIWidgetState AMUIDialog::state()
    {
        AMUIWidgetState state = AMUIWidgetState::EMPTY;
        std::vector<AMUIFormElement *> &elems = elements();
        for (AMUIFormElement *el: elems) {
            state = state + el->state();
        }
        return state;
    }

    bool AMUIDialog::isClosedByCloseButton()
    {
        return m_closedByCloseButton;
    }

    std::string AMUIDialog::closeButtontext()
    {
        return m_closeButtonText;
    }

    bool AMUIDialog::enterHitClosesDialog()
    {
        return m_enterHitClosesDialog;
    }

    void AMUIDialog::setEnterHitClosesDialog(bool value)
    {
        m_enterHitClosesDialog = value;
    }
} // AMDialogs