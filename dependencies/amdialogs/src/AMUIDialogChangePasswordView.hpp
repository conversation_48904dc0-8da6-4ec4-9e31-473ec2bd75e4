//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//
#include "amdialogs/AMUIDialogChangePasswordView.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIDialogChangePassword.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIDialogChangePasswordView<TEnumType, TStringType>::AMUIDialogChangePasswordView()
        : AMUIDialogView()
    {

    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogChangePasswordView<TEnumType, TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogChangePassword<TEnumType, TStringType> *dlg = static_cast<AMUIDialogChangePassword<TEnumType, TStringType> *>(m_dialog);

            ImGui::Text("Současné heslo:");
            dlg->elements()[0]->render(false, dlg->id() * 6800 + 1);
            ImGui::Text("Nové heslo:");
            dlg->elements()[1]->render(false, dlg->id() * 6800 + 2);
            ImGui::Text("Nové heslo znovu:");
            dlg->elements()[2]->render(false, dlg->id() * 6800 + 3);
            float yp = ImGui::GetCursorPosY();
            ImGui::SetCursorPosY(yp + 20);
            ImGui::Text("%s", dlg->text().c_str());
            ImGui::SetCursorPosY(yp + 70);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs