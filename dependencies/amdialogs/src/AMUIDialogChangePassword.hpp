//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//
#define IMGUI_DEFINE_MATH_OPERATORS
#include "amdialogs/AMUIDialogChangePassword.h"
#include "amdialogs/AMUIDialogChangePasswordView.h"
#include "amui/AMUIApp.h"

namespace AMDialogs {


    int strongNess(std::string& input)
    {
        int n = input.length();
        if (n == 0) {
            return 0;
        }

        // Checking lower alphabet in string
        bool hasLower = false, hasUpper = false;
        bool hasDigit = false, specialChar = false;
        std::string normalChars = "abcdefghijklmnopqrstu"
                             "vwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890 ";

        for (int i = 0; i < n; i++) {
            if (islower(input[i]))
                hasLower = true;
            if (isupper(input[i]))
                hasUpper = true;
            if (isdigit(input[i]))
                hasDigit = true;

            size_t special = input.find_first_not_of(normalChars);
            if (special != std::string::npos)
                specialChar = true;
        }

        if (hasLower && hasUpper && hasDigit && specialChar && (n >= 8))
            return 3;
        else if ((hasLower || hasUpper) && specialChar && (n >= 6))
            return 2;
        else
            return 1;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogChangePassword<TEnumType, TStringType>::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            bool forceChange
            )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogChangePasswordView<TEnumType, TStringType>();
        v->init(this);
        m_view = v;
        m_forceChange = forceChange;
        m_password.initBase(64, "password");
        m_password.init();
        m_password.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_newPassword1.initBase(64, "newpassword1");
        m_newPassword1.init();
        m_newPassword1.setState(AMDialogs::AMUIWidgetState::EMPTY);
        m_newPassword2.initBase(64, "newpassword2");
        m_newPassword2.init();
        m_newPassword2.setState(AMDialogs::AMUIWidgetState::EMPTY);
    }

    template<typename TEnumType, typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogChangePassword<TEnumType, TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TEnumType, typename TStringType>
    AMUIDialogChangePassword<TEnumType, TStringType>::AMUIDialogChangePassword()
        : AMUIDialog(),
          m_password(""),
          m_newPassword1(""),
          m_newPassword2(""),
          m_elements{&m_password, &m_newPassword1, &m_newPassword2}
    {
        m_closeButtonText = "Změnit";
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogChangePassword<TEnumType, TStringType>::onChange()
    {
        std::string psws1 = m_newPassword1.buffer();
        std::string psws2 = m_newPassword2.buffer();
        int pass_strenth = strongNess(psws1);
        switch (pass_strenth) {
            case 0: m_text = "Heslo je prázdné.\n";break;
            case 1: m_text = "Heslo je slabé.\n";break;
            case 2: m_text = "Heslo je ucházející.\n";break;
            case 3: m_text = "Heslo je slušné.\n";break;
        }
        if (psws1 != psws2) {
            m_text += "Nové heslo není napsáno dvakrát stejně.";
        }
        AMUIDialog::onChange();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogChangePassword<TEnumType, TStringType>::onClose()
    {
        if (!m_closedByCloseButton) {
            AMUIDialog::onClose();
            return;
        }
        std::string psws1 = m_newPassword1.buffer();
        std::string psws2 = m_newPassword2.buffer();
        int pass_strenth = strongNess(psws1);
        if (pass_strenth < 2) {
            AMUI::AMUIApp::instance().errorMessageBox("Chyba", "Nové heslo je je velmi slabé.");
        } else if (psws1 != psws2) {
            AMUI::AMUIApp::instance().errorMessageBox("Chyba", "Nové heslo není napsáno dvakrát stejně.");
        } else {
            AMUIDialog::onClose();
        }
    }

} // AMDialogs