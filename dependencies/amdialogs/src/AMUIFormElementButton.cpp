//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementButton.h"
//#include "amcore/AMAssert.h"
//#include <cstdio>
//#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {


    AMUIFormElementButton::AMUIFormElementButton(const std::string key)
        : AMUIFormElement(key),
          m_customCallback(),
          m_buttonId(0),
          m_value(m_label),
          m_onclickOrigin(),
          m_onclickSize()
    {
    }

    
    void AMUIFormElementButton::init(std::function<void(int)> callback, int buttonId)
    {
        m_customCallback = callback;
        m_buttonId = buttonId;
        reset();
    }

    
    AMUIFormElementButton::~AMUIFormElementButton()
    {
    }

    
    void AMUIFormElementButton::reset()
    {
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    
    void AMUIFormElementButton::updateState()
    {
        AMUIFormElement::updateState();
    }

    
    void AMUIFormElementButton::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;


        const float button_size = ImGui::GetFrameHeight();
        const float max_width = ImGui::CalcItemWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }
        char label[128];
        snprintf(label, sizeof(label) - 1, "%s##elbutton_%i", m_value.c_str(), element_id);
        if (ImGui::ButtonEx(label, ImVec2(width, button_size), flags)) {
            ImVec2 rmin = ImGui::GetItemRectMin();
            //wasModified();
            ImVec2 rmax = ImGui::GetItemRectMax();
            m_onclickOrigin = ImVec2(rmin.x - style.FramePadding.x, rmax.y);
            m_onclickSize = ImVec2(std::max(rmax.x - rmin.x + 2 * style.FramePadding.x, 500.0f), -FLT_MIN);
            m_customCallback(m_buttonId);
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }
    }

}

