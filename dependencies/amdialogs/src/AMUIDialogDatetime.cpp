//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//
#include <langinfo.h>
#include <locale.h>
#include <sstream>
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"
#include "amdialogs/AMUISynthesizers.h"
#include "amcore/AMDatetimeus.h"

namespace AMDialogs {

    const std::vector<std::string> AMUIDialogDatetime::c_precisionsLabels = {"Roky", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Min<PERSON>y", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>k<PERSON>"};
    const std::vector<std::string> AMUIDialogDatetime::c_hoursLabels = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11",
                                                                        "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23",};
    const std::vector<std::string> AMUIDialogDatetime::c_minSecLabels = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09",
                                                                         "10", "11", "12", "13", "14", "15", "16", "17", "18", "19",
                                                                         "20", "21", "22", "23", "24", "25", "26", "27", "28", "29",
                                                                         "30", "31", "32", "33", "34", "35", "36", "37", "38", "39",
                                                                         "40", "41", "42", "43", "44", "45", "46", "47", "48", "49",
                                                                         "50", "51", "12", "53", "54", "55", "56", "57", "58", "59",
                                                                         };
    
    void AMUIDialogDatetime::init(
            std::string name,
            std::function<void(AMUIDialog *)> onClose,
            std::function<void(AMUIDialog *)> onChange,
            char wildcard,
            AMCore::AMDataType type
            )
    {
        switch(type) {
            case AMCore::AMDataType_datetime:
                m_showTime = true;
                m_showDate = true;
                m_showUsec = false;
                m_format = datetimeFormatText;
                break;
            case AMCore::AMDataType_date:
                m_showTime = false;
                m_showDate = true;
                m_showUsec = false;
                m_format = dateFormatText;
                break;
            case AMCore::AMDataType_time:
                m_showTime = true;
                m_showDate = false;
                m_showUsec = false;
                m_format = timeFormatText;
                break;
            case AMCore::AMDataType_datetimeus:
                m_showTime = true;
                m_showDate = true;
                m_showUsec = true;
                m_format = datetimeusFormatText;
                break;
            default:
                break;
        }
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogDatetimeView();
        v->init(this);
        m_view = v;
        m_year.initBase(5,"year");
        m_year.init();
        m_year.setValue(0);
        m_year.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_year.setSizeX(100.0f);

#if defined __EMSCRIPTEN__ || defined __APPLE__
        setlocale(LC_ALL, "cs_CZ.UTF-8");
        const nl_item nl_months[12] = {MON_1, MON_2, MON_3, MON_4, MON_5, MON_6, MON_7, MON_8, MON_9, MON_10, MON_11, MON_12};
        m_monthsLabels[0] = "Leden";
        m_monthsLabels[1] = "Únor";
        m_monthsLabels[2] = "Březen";
        m_monthsLabels[3] = "Duben";
        m_monthsLabels[4] = "Květen";
        m_monthsLabels[5] = "Červen";
        m_monthsLabels[6] = "Červenec";
        m_monthsLabels[7] = "Srpen";
        m_monthsLabels[8] = "Září";
        m_monthsLabels[9] = "Říjen";
        m_monthsLabels[10] = "Listopad";
        m_monthsLabels[11] = "Prosinec";
#else
        const nl_item nl_months[12] = {ALTMON_1, ALTMON_2, ALTMON_3, ALTMON_4, ALTMON_5, ALTMON_6, ALTMON_7, ALTMON_8, ALTMON_9, ALTMON_10, ALTMON_11, ALTMON_12};
        for (int i = 0;i < 12;i++) {
            m_monthsLabels[i] = nl_langinfo(nl_months[i]);
        }
#endif

        m_month.initBase(0,"month");
        m_month.init(m_monthsLabels);
        m_month.setValue(0);
        m_month.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_month.setSizeX(120.0f);
        m_monthPlus.initBase(1,"+");
        m_monthPlus.setValueString("+") ;
        m_monthPlus.init(std::bind(&AMUIDialogDatetime::onButtonMonth, this, std::placeholders::_1), 0);
        m_monthPlus.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_monthMinus.initBase(1,"-");
        m_monthMinus.setValueString("-");
        m_monthMinus.init(std::bind(&AMUIDialogDatetime::onButtonMonth, this, std::placeholders::_1), 1);
        m_monthMinus.setState(AMDialogs::AMUIWidgetState::FILLED);

#if defined __EMSCRIPTEN__ || defined __APPLE__
        m_firstWeekday = 1;
        m_wdayLabels[0] = "Po";
        m_wdayLabels[1] = "Út";
        m_wdayLabels[2] = "St";
        m_wdayLabels[3] = "Čt";
        m_wdayLabels[4] = "Pá";
        m_wdayLabels[5] = "So";
        m_wdayLabels[6] = "Ne";
#else
        const nl_item nl_wdays[7] = {ABDAY_1, ABDAY_2, ABDAY_3, ABDAY_4, ABDAY_5, ABDAY_6, ABDAY_7};
        char tc = *nl_langinfo(_NL_TIME_FIRST_WEEKDAY);
        m_firstWeekday = (int)tc - 1;
        for (int i = 0;i < 7;i++) {
            int j = m_firstWeekday + i;
            if (j >= 7) {
                j -= 7;
            }
            m_wdayLabels[i] = nl_langinfo(nl_wdays[j]);
        }
#endif
        m_hours.initBase(1, "Hours");
        m_hours.init(c_hoursLabels);
        m_hours.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_hours.setSizeX(120.0f);
        m_mins.initBase(1, "Mins");
        m_mins.init(c_minSecLabels);
        m_mins.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_mins.setSizeX(120.0f);
        m_secs.initBase(1, "Secs");
        m_secs.init(c_minSecLabels);
        m_secs.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_secs.setSizeX(120.0f);
        m_usec.initBase(7,"Mikrosec");
        m_usec.init();
        m_usec.setValue(0);
        m_usec.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_usec.setSizeX(120.0f);
        m_precision.initBase(0, "Přesnost:");
        m_precision.init(c_precisionsLabels);
        m_precision.setState(AMDialogs::AMUIWidgetState::FILLED);
        m_precision.setValue(0);
        m_wildcard = wildcard;
        fillElements();
    }

    
    /*std::vector<AMUIFormElement *> &AMUIDialogDatetime::elements()
    {
        return m_elements;
    }*/

    
    AMUIDialogDatetime::AMUIDialogDatetime()
        :   AMUIDialog(),
            m_showDate(true),
            m_showTime(true),
            m_showUsec(false),
            m_wildcard('\0'),
            m_format(datetimeFormatText),
            m_year(""),
            m_monthsLabels(12),
            m_month(""),
            m_monthPlus(""),
            m_monthMinus(""),
            m_wdayLabels(7),
            m_days(42),
            m_day(0),
            m_hours(""),
            m_mins(""),
            m_secs(""),
            m_usec(""),
            m_precision(""),
            m_firstWeekday(0),
            m_elements{&m_year, &m_month, &m_hours, &m_mins, &m_secs}
    {
    }

    std::string AMUIDialogDatetime::datetime()
    {
        AMCore::AMDatetimeus min,max;
        min.tm_year = m_year.value() - 1900 ;
        max.tm_year = min.tm_year;
        if (m_precision.value() < 1) {
            min.tm_mon = 0;
            max.tm_mon = 99;
        } else {
            min.tm_mon = m_month.value();
            max.tm_mon = m_month.value();
        }
        if (m_precision.value()  < 2) {
            min.tm_mday = 0;
            max.tm_mday = 99;
        } else {
            min.tm_mday = m_day;
            max.tm_mday = m_day;
        }
        if (m_precision.value() < 3) {
            min.tm_hour = 0;
            max.tm_hour = 99;
        } else {
            min.tm_hour = m_hours.value();
            max.tm_hour = m_hours.value();
        }
        if (m_precision.value() < 4) {
            min.tm_min = 0;
            max.tm_min = 99;
        } else {
            min.tm_min = m_mins.value();
            max.tm_min = m_mins.value();
        }
        if (m_precision.value() < 5) {
            min.tm_sec = 0;
            max.tm_sec = 99;
        } else {
            min.tm_sec = m_secs.value();
            max.tm_sec = m_secs.value();
        }
        if (m_precision.value() < 6) {
            min.usec = 0;
            max.usec = 999999;
        } else {
            min.usec = m_usec.value();
            max.usec = m_usec.value();
        }
        return AMUISynthesizeDatetime<std::string>(m_format, &min, &max, m_wildcard);
    }

    void AMUIDialogDatetime::fillDays(AMCore::AMDatetimeus &t)
    {
        for (int i = 0; i < 42; i++) {
            m_days[i].state = AMUIWidgetState::INVALID;
        }
        int dayOff = t.tm_wday - m_firstWeekday;
        if (dayOff < 0) {
            dayOff += 7;
        }
        int blines = (m_day - dayOff + 6 - 1) / 7;
        int dayIndex = blines * 7 + dayOff;
        int prevMonthDays = monthDays(t.tm_year, (t.tm_mon == 0) ? 11 : t.tm_mon - 1);
        for(int i = dayIndex - 1, j = m_day - 1; i >= 0; i--, j--) {
            if (j <= 0) {
                m_days[i].state = AMUIWidgetState::READONLY;
                m_days[i].day = prevMonthDays - j;
                m_days[i].label = std::to_string(m_days[i].day);
            } else {
                m_days[i].state = AMUIWidgetState::EMPTY;
                m_days[i].day = j;
                m_days[i].label = std::to_string(m_days[i].day) + "##" + std::to_string((unsigned long)&m_days[i]);
            }
        }
        m_days[dayIndex].state = AMUIWidgetState::FILLED;
        m_days[dayIndex].day = m_day;
        m_days[dayIndex].label = std::to_string(m_days[dayIndex].day) + "##" + std::to_string((unsigned long)&m_days[dayIndex]);
        int imonthDays = monthDays(t.tm_year, t.tm_mon);
        int alines  = (imonthDays - m_day + 1 + dayOff + 6) / 7;
        AMAssert(alines + blines <= 6);
        for(int i = dayIndex + 1, j = m_day + 1; i < (blines + alines ) * 7; i++, j++) {
            if (j > imonthDays) {
                m_days[i].state = AMUIWidgetState::READONLY;
                m_days[i].day = j - imonthDays;
                m_days[i].label = std::to_string(m_days[i].day);
            } else {
                m_days[i].state = AMUIWidgetState::EMPTY;
                m_days[i].day = j ;
                m_days[i].label = std::to_string(m_days[i].day) + "##" + std::to_string((unsigned long)&m_days[i]);
            }
        }
    }

    void AMUIDialogDatetime::setDatetime(std::string datetime)
    {
        m_year.setValue(0);
        m_month.setValue(0);
        m_day = 1;
        m_hours.setValue(0);
        m_mins.setValue(0);
        m_secs.setValue(0);
        m_precision.setValue(5);

        AMCore::AMDatetimeus min;
        AMCore::AMDatetimeus max;
        bool refresh = false;
        std:std::istringstream is(datetime);
        AMUIParseResult res = AMUIParseDatetime(is, &min, &max, nullptr, m_format, m_wildcard);
        if (res == AMUIParseResult::FAIL) {
            std::time_t t = std::time(0);   // get time now
            min = *std::localtime(&t);
            max = min;
            refresh = true;
        }
        m_precision.setValue(0);
        m_year.setValue(min.tm_year + 1900);
        if (min.tm_mon != max.tm_mon) {
            fillDays(min);
            return;
        }
        m_precision.setValue(1);
        m_month.setValue(min.tm_mon);
        if (min.tm_mday != max.tm_mday) {
            fillDays(min);
            return;
        }
        m_precision.setValue(2);
        m_day = min.tm_mday;
        fillDays(min);
        if (min.tm_hour != max.tm_hour) {
            return;
        }
        m_precision.setValue(3);
        m_hours.setValue(min.tm_hour);
        if (min.tm_min != max.tm_min) {
            return;
        }
        m_precision.setValue(4);
        m_mins.setValue(min.tm_min);
        if (min.tm_sec != max.tm_sec) {
            return;
        }
        m_precision.setValue(5);
        m_secs.setValue(min.tm_sec);
        if (min.usec != max.usec) {
            return;
        }
        m_precision.setValue(6);
        m_usec.setValue(min.usec);
        if (refresh) {
            AMUIDialog::onChange();
        }
    }

    void AMUIDialogDatetime::onButtonMonth(int id)
    {
        if (id == 0) {
            if (m_month.value() != 11) {
                m_month.setValue(m_month.value() + 1);
            } else {
                m_month.setValue(0);
                m_year.setValue(m_year.value() + 1);
            }
        } else {
            if (m_month.value() != 0) {
                m_month.setValue(m_month.value() - 1);
            } else {
                m_month.setValue(11);
                m_year.setValue(m_year.value() - 1);
            }
        }
        int md = monthDays(m_year.value(), m_month.value());
        m_day = (m_day > md) ? md : m_day;
        onChange();
    }

    int AMUIDialogDatetime::monthDays(int year, int month)
    {
        bool leap =
            (((year % 4 == 0) &&
              (year % 100 != 0)) ||
             (year % 400 == 0));
        // Handle February month
        // with leap year
        if (month == 1) {
            if (leap) {
                return 29;
            } else {
                return 28;
            }
        }
        // Months of April, June,
        // Sept and Nov must have
        // number of days less than
        // or equal to 30.
        if (month == 3 || month == 5 ||
            month == 8 || month == 10) {
            return 30;
        }
        return 31;
    }

    void AMUIDialogDatetime::recomputeDays()
    {
        AMCore::AMDatetimeus t;
        t.tm_year = m_year.value() - 1900;
        t.tm_mon = m_month.value();
        t.tm_mday = m_day;
        t.tm_hour = m_hours.value();
        t.tm_min = m_mins.value();
        t.tm_sec = m_secs.value();
        AMDialogs::AMMktimeNoShift(&t);
        fillDays(t);
    }

    void AMUIDialogDatetime::fillElements()
    {
        m_elements.clear();
        if (m_showDate) {
            m_elements.push_back(&m_year);
            m_elements.push_back(&m_month);
        }
        if (m_showTime) {
            m_elements.push_back(&m_hours);
            m_elements.push_back(&m_mins);
            m_elements.push_back(&m_secs);
        }
        if (m_wildcard != '\0') {
            m_elements.push_back(&m_precision);
        }
    }

    void AMUIDialogDatetime::onChange()
    {
        recomputeDays();
        AMUIDialog::onChange();
    }

    void AMUIDialogDatetime::onDayButton(int newDay)
    {
        m_day = newDay;
        onChange();
    }


} // AMDialogs