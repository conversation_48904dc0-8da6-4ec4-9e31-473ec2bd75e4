//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogAuthorTagView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogAuthorTag.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIDialogAuthorTagView<TStringType>::AMUIDialogAuthorTagView()
        : AMUIDialogView()
    {

    }

    template<typename TStringType>
    void AMUIDialogAuthorTagView<TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogAuthorTag<TStringType> *dlg = static_cast<AMUIDialogAuthorTag<TStringType> *>(m_dialog);
            ImGui::Text("Datum a čas:");
            dlg->elements()[0]->render(false, dlg->id() * 7000);
            ImGui::Text("Autor:");
            dlg->elements()[1]->render(false, dlg->id() * 7000 + 1);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs