//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogView.h"
#include "amdialogs/AMUIDialog.h"
#include "imgui/imgui_internal.h"
#include "imgui/imgui.h"

namespace AMDialogs {
    AMUIDialogView::AMUIDialogView()
        : m_dialog(nullptr),
        m_origin(-FLT_MIN, -FLT_MIN),
        m_size(800.0f, 600.0f),
        m_originLast(FLT_MIN, FLT_MIN),
        //m_sizeLast(500.0f, 600.0f),
        m_name(""),
        m_focused(false),
        m_windowIsOpened(true),
        m_starting(true)
    {

    }

    void AMUIDialogView::render()
    {
        if (renderBegin()) {


            renderEnd(true);
        } else {
            renderEnd(false);
        }

    }

    void AMUIDialogView::init(AMUIDialog *dialog)
    {
        m_dialog = dialog;
        snprintf(m_name, sizeof(m_name) / sizeof(char), "%s###_popup_%lx", m_dialog->name().c_str(), (unsigned long) m_dialog->id());
    }

    bool AMUIDialogView::renderBegin()
    {
        if (m_origin.x == FLT_MIN) {
            m_origin = ImVec2((ImGui::GetMainViewport()->Size.x - m_size.x) / 2.0f, (ImGui::GetMainViewport()->Size.y - m_size.y) / 2.0f);
        }
        bool my_window_focused = ImGui::IsWindowFocused(ImGuiFocusedFlags_ChildWindows);
        if (m_origin.x != m_originLast.x || m_origin.y != m_originLast.y) {
            ImGui::SetNextWindowPos(m_origin);
            m_originLast = m_origin;
        }
        //if (m_size.x != m_sizeLast.x || m_size.y != m_sizeLast.y) {
            ImGui::SetNextWindowSize(ImVec2(m_size.x, 0.0));
          //  m_sizeLast = m_size;
        //}

        ImGui::SetNextWindowSizeConstraints(ImVec2(0.0, 0.0), ImVec2(-FLT_MAX, m_size.y));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(10.0, 10.0));
        m_windowIsOpened = true;

        if (ImGui::Begin(m_name, &m_windowIsOpened, ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_Modal)) {

            /* dialog can be drawed multiple if you draw multiple form, whicch dialog initiates*/
            ImGuiWindow* wnd = ImGui::FindWindowByName(m_name);
            int cnt_wnd = wnd ? wnd->BeginCount : 0;
            if (cnt_wnd >= 2) {
                return false;
            }


            m_focused = ImGui::IsWindowFocused();
            if (my_window_focused) {
                ImGui::BringWindowToDisplayFront(ImGui::GetCurrentWindow());
            }
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + ImGui::GetTextLineHeightWithSpacing());
            ImGui::PushItemWidth(ImGui::GetWindowContentRegionMax().x);

            if (m_starting && !ImGui::IsAnyItemActive() && !ImGui::IsMouseClicked(0)) {
                m_starting = false;
                ImGui::SetKeyboardFocusHere(0);
            }
            return true;
        }
        return false;
    }

    void AMUIDialogView::renderEnd(bool beginSuccessful)
    {
        if (beginSuccessful) {

            ImGui::PopItemWidth();
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + ImGui::GetTextLineHeightWithSpacing());
            if (ImGui::Button(m_dialog->closeButtontext().c_str())) {
                m_focused = false;
                m_dialog->closeRequest(true);
            } else if (!m_windowIsOpened) {
                m_focused = false;
                m_dialog->closeRequest(false);
            }
            if (m_dialog->enterHitClosesDialog()) {
                auto io = ImGui::GetIO();
                bool pressed_enter = ImGui::IsKeyPressed(ImGuiKey_Enter);
                if (pressed_enter) {
                    m_focused = false;
                    m_dialog->closeRequest(true);
                }
            }
        }
        ImGui::End();
        ImGui::PopStyleVar();
    }

    void AMUIDialogView::setOrigin(ImVec2 &origin)
    {
        m_origin = origin;
    }

    void AMUIDialogView::setSize(ImVec2 &size)
    {
        m_size = size;
    }

    bool AMUIDialogView::focused()
    {
        return m_focused;
    }
} // AMDialogs