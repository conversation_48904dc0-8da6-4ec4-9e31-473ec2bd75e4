//
// Created by <PERSON><PERSON><PERSON> on 7.11.22.
//

#ifndef AMCore_AMPARSERSIMPL_HPP
#define AMCore_AMPARSERSIMPL_HPP

#include "amdialogs/AMUIParsers.h"
#include "amui/AMUIConfig.h"
#include "amcore/AMAssert.h"
#include <locale>
#include <cmath>
#include <iomanip>

namespace AMDialogs {


    std::string _AMGetStringWithTime(const char *format);
    std::string _AMGetStringWithTimeus(const char *format);

    template<typename TReturnType>
    AMUIParseResult AMUIParseNatural(std::istream &is, TReturnType *rMin, TReturnType *rMax, std::ostream *os, char wildcard, int *digitsRead)
    {
        AMCore::AMStreamResetContext(is);
        static_assert(std::is_integral<TReturnType>::value, "Integral or AMCore::AMNullable<integral> required.");

        enum class PState {
            EMPTY,
            NUM
        };

        PState state = PState::EMPTY;
        unsigned int c;
        bool overflow = false;
        TReturnType valueMin = 0;
        TReturnType valueMax = 0;
        int digits = 0;
        c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        while (isspace(c)) {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        }
        int wildcards = 0;
        while (std::isdigit(c)) {
            TReturnType tempres;
            if (!AMCore::AMIntegerMultiply(valueMax, (TReturnType) 10, tempres)) {
                overflow = true;
                break;
            }
            if (!AMCore::AMIntegerAdd(tempres, (TReturnType) (c - '0'), tempres)) {
                overflow = true;
                break;
            }
            digits++;
            valueMax = tempres;
            valueMin = tempres;
            if (os) {
                AMCore::AMStreamPut(*os, c);
            }
            state = PState::NUM;
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        }
        if (wildcard != '\0') {
            while (c == wildcard) {
                TReturnType tempres;
                if (!AMCore::AMIntegerMultiply(valueMax, (TReturnType) 10, tempres)) {
                    overflow = true;
                    break;
                }
                if (wildcards == 0) {
                    if (!AMCore::AMIntegerAdd(tempres, (TReturnType) 10, tempres)) {
                        overflow = true;
                        break;
                    }
                }
                digits++;
                wildcards++;
                valueMax = tempres;
                valueMin = valueMin * 10; //impossible overflow
                if (os) {
                    AMCore::AMStreamPut(*os, c);
                }
                state = PState::NUM;
                c = AMCore::AMStreamToLowercaseStripDiaGet(is);
            }
        }
        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (state != PState::NUM) {
            return AMUIParseResult::FAIL;
        }
        if (rMin) {
            *rMin = valueMin;
        }
        if (rMax) {
            *rMax = valueMax;
        }
        if (digitsRead) {
            *digitsRead = digits;
        }
        return overflow ? AMUIParseResult::PARTIAL : AMUIParseResult::SUCCESS;
    }

    template<typename TReturnType>
    AMUIParseResult AMUIParseNatural(std::istream &is, AMCore::AMNullable<TReturnType> *rMin, AMCore::AMNullable<TReturnType> *rMax, std::ostream *os, char wildcard, int *digitsRead)
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            if (digitsRead) {
                *digitsRead = 0;
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            if (digitsRead) {
                *digitsRead = 0;
            }
            return AMUIParseResult::PARTIAL;
        }
        TReturnType tMin, tMax;
        AMUIParseResult pr = AMUIParseNatural(is, &tMin, &tMax, os, wildcard, digitsRead);
        if (pr != AMUIParseResult::FAIL) {
            if (rMax) {
                rMax->set(tMax);
            }
            if (rMin) {
                rMin->set(tMin);
            }
        }
        return pr;
    }

    template<typename TReturnType>
    AMUIParseResult AMUIParseInteger(std::istream &is, TReturnType *rMin, TReturnType *rMax, std::ostream *os, char wildcard)
    {
        AMCore::AMStreamResetContext(is);
        static_assert(std::is_integral<TReturnType>::value, "Integral required.");
        static_assert(std::is_signed<TReturnType>::value, "Signed integral type required.");

        enum class PState {
            EMPTY,
            NUM,
            SIGN
        };

        PState state = PState::EMPTY;
        unsigned int c;
        TReturnType valueMin = 0;
        TReturnType valueMax = 0;
        bool negative = false;
        //int numWildcards = 0;
        bool bbreak = false;
        bool overflow = false;
        int exponent = 0;
        do {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        } while (isspace(c));
        do {
            switch (state) {
                case PState::EMPTY:
                    switch (c) {
                        case '+':
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            state = PState::SIGN;
                            break;
                        case '-':
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            negative = true;
                            state = PState::SIGN;
                            break;
                        case '0':
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                        case '7':
                        case '8':
                        case '9':
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            state = PState::NUM;
                            break;
                        case UEOF:
                            bbreak = true;
                            break;
                        default:
                            if (c == wildcard) {
                                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                                state = PState::NUM;
                            } else {
                                bbreak = true;
                                break;
                            }
                    }
                    break;
                case PState::NUM: {
                    AMCore::AMStreamToLowercaseStripDiaUnget(is);
                    AMUIParseResult pr = AMUIParseNatural(is, &valueMin, &valueMax, os, wildcard);
                    if (pr == AMUIParseResult::SUCCESS) {
                        if (negative) {
                            TReturnType tv = valueMax;
                            valueMax = -valueMin;
                            valueMin = -tv;
                        }
                    } else {
                        if (negative && valueMin == valueMax && valueMax == std::numeric_limits<TReturnType>::max() / ((TReturnType) 10)) {
                            int pc = is.peek();
                            TReturnType netTemp = -valueMax;
                            if (AMCore::AMIntegerMultiply(netTemp, (TReturnType) 10, netTemp)) {
                                if (AMCore::AMIntegerAdd(netTemp, (TReturnType) ('0' - pc), netTemp)) {
                                    valueMin = netTemp;
                                    valueMax = netTemp;
                                    AMCore::AMStreamToLowercaseStripDiaGet(is);
                                    c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                                    if (os) {
                                        AMCore::AMStreamPut(*os, pc);
                                    }
                                    bbreak = true;
                                    break;
                                }
                            }
                        }
                        overflow = pr == AMUIParseResult::PARTIAL;
                        state = PState::EMPTY;
                    }
                    c = AMCore::AMStreamToLowercaseStripDiaGet(is);
                    bbreak = true;
                    break;
                }
                case PState::SIGN:
                    if (isdigit(c) || std::isspace(c) || c == wildcard) {
                        AMCore::AMStreamToLowercaseStripDiaUnget(is);
                        state = PState::NUM;
                    } else {
                        bbreak = true;
                        break;
                    }
                    break;
            }
            if (bbreak) {
                break;
            }
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
        } while (1);

        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (state != PState::NUM && !(state == PState::EMPTY && overflow)) {
            return AMUIParseResult::FAIL;
        }
        if (rMax) {
            *rMax = valueMax;
        }
        if (rMin) {
            *rMin = valueMin;
        }
        return state == PState::NUM ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
    }

    template<typename TReturnType>
    AMUIParseResult AMUIParseInteger(std::istream &is, AMCore::AMNullable<TReturnType> *rMin, AMCore::AMNullable<TReturnType> *rMax, std::ostream *os, char wildcard)
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            return AMUIParseResult::PARTIAL;
        }
        TReturnType tMin, tMax;
        AMUIParseResult pr = AMUIParseInteger(is, &tMin, &tMax, os, wildcard);
        if (pr != AMUIParseResult::FAIL) {
            if (rMax) {
                rMax->set(tMax);
            }
            if (rMin) {
                rMin->set(tMin);
            }
        }
        return pr;
    }

    template<typename TResult, typename TMantisa>
    TResult AMConstructFloat(TMantisa mantisa, int exponent, bool sign)
    {
        TMantisa stripped_mantissa = mantisa;// & ( (1 << (std::numeric_limits<TResult>::digits - 1)) -1 );
        if (stripped_mantissa == 0) {
            return (TResult) 0.0;
        }
        unsigned int stripped_exponent = ((unsigned int)(exponent + std::numeric_limits<TResult>::max_exponent - 1) & ((std::numeric_limits<TResult>::max_exponent << 1) -1));
        if constexpr (std::is_same<TResult, long double>::value) {
            return 0.0;
        } else if constexpr (std::is_same<TResult, double>::value){
            return 0.0;
        } else if constexpr (std::is_same<TResult, float>::value){
            int exp =  std::numeric_limits<TResult>::digits-1;
            if (stripped_mantissa < (1 << (std::numeric_limits<TResult>::digits-1)))
                while( stripped_mantissa < (1 << (std::numeric_limits<TResult>::digits-1))) {
                    stripped_mantissa <<= 1;
                    exp--;
                }
            while( stripped_mantissa >= (1 << (std::numeric_limits<TResult>::digits))) {
                stripped_mantissa >>= 1;
                exp++;
            }
            stripped_mantissa = stripped_mantissa & ( (1 << (std::numeric_limits<TResult>::digits - 1)) -1 );
            stripped_exponent = ((unsigned int)(exp + std::numeric_limits<TResult>::max_exponent - 1) & ((std::numeric_limits<TResult>::max_exponent << 1) -1));
            TMantisa res = (sign ? (std::numeric_limits<TMantisa>::max() >> 1) + 1 : 0)
                           | (stripped_exponent << (std::numeric_limits<TResult>::digits - 1))
                           | stripped_mantissa;
            TResult rv = *((TResult*)&res);
            if (exponent == 0) {
                return rv;
            }
            return rv * pow(10, exponent);
        }
        return 0.0;
    }

    template<typename TReturnType>
    AMUIParseResult AMUIParseFloat(std::istream &is, TReturnType *rMin, TReturnType *rMax, std::ostream *os, char wildcard)
    {
        std::string word;
        is >> word;
        std::size_t ndx = -1;
        TReturnType res = 0.0;
        try {
            if constexpr (std::is_same<TReturnType, float>::value) {
                res = std::stof(word, &ndx);
            } else {
                res = std::stod(word, &ndx);
            }
            if (ndx != word.size()) {
                return AMUIParseResult::FAIL;
            }
            if (rMin) {
                *rMin = res;
            }
            if (rMax) {
                *rMax = res;
            }
            if (os) {
                (*os) << word;
            }
            return AMUIParseResult::SUCCESS;
        } catch (std::exception &e) {
            return AMUIParseResult::FAIL;
        }
        /*
        AMCore::AMStreamResetContext(is);
        static_assert(std::is_floating_point<TReturnType>::value, "Floating required.");

        enum class PState {
            EMPTY,
            NUM,
            SIGN,
            DOT,
            FRACTNUM,
            EXP,
          //  EXPNUM,
           // EXPSIGN,
        };

        PState state = PState::EMPTY;
        unsigned int oc;
        unsigned int c;
        TReturnType value = 0.0;
        typedef typename std::conditional<std::is_same<TReturnType, float>::value, unsigned int, unsigned long long>::type TMantissa;
        TMantissa mantissaMin = 0;
        TMantissa mantissaMax = 0;
        int exponentMin = 0;
        int exponentMax = 0;
        int mantissa_chars = 0;
        bool negative = false;
        int numWildcards = 0;
        bool bbreak = false;
        bool overflow = false;
        do {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
        } while (isspace(c));
        do {
            switch (state) {
                case PState::EMPTY:
                    switch (c) {
                        case '+':
                            if (os) {
                                AMCore::AMStreamPut(*os, oc);
                            }
                            state = PState::SIGN;
                            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                            break;
                        case '-':
                            if (os) {
                                AMCore::AMStreamPut(*os, oc);
                            }
                            negative = true;
                            state = PState::SIGN;
                            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                            break;
                    }
                case PState::SIGN:
                    switch (c) {
                        case '0':
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                        case '7':
                        case '8':
                        case '9':
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            state = PState::NUM;
                            break;
                        case 'e':
                            if (os) {
                                AMCore::AMStreamPut(*os, oc);
                            }
                            state = PState::EXP;
                            break;
                        case UEOF:
                            return AMUIParseResult::FAIL;
                            break;
                        default:
                            if (c == wildcard) {
                                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                                state = PState::NUM;
                            } else if (c == std::use_facet< std::numpunct<char> >(std::locale()).decimal_point()) {
                                state = PState::FRACTNUM;
                                break;
                            }else {
                                bbreak = true;
                            }
                    }
                    break;
                case PState::NUM: {
                    switch (c) {
                        case '0':
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                        case '7':
                        case '8':
                        case '9': {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            AMUIParseResult pr = AMUIParseNatural(
                                is, &mantissaMin, &mantissaMax, os, wildcard, &mantissa_chars
                                                               );
                            if (pr != AMUIParseResult::SUCCESS) {
                                do {
                                    c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                                    if (c == '0') {
                                        exponentMin++;
                                        exponentMax++;
                                    } else if (std::isdigit(c)) {
                                        if (rMin) {
                                            *rMin = AMConstructFloat<TReturnType>(mantissaMin, exponentMin, negative);
                                        }
                                        if (rMax) {
                                            *rMax= AMConstructFloat<TReturnType>(mantissaMax, exponentMin, negative);
                                        }
                                        return AMUIParseResult::PARTIAL;
                                    } else {
                                        AMCore::AMStreamToLowercaseStripDiaUnget(is);
                                        break;
                                    }
                                } while (1);
                                (*os)<<'e'<<exponentMin;
                            }
                            state = PState::DOT;
                            break;
                        }
                        case 'e':
                            if (os) {
                                AMCore::AMStreamPut(*os, oc);
                            }
                            state = PState::EXP;
                            break;
                        case UEOF:
                            bbreak = true;
                            break;
                        default:
                            if (c == wildcard) {
                                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                                state = PState::NUM;
                            } else if (c == std::use_facet< std::numpunct<char> >(std::locale()).decimal_point()) {
                                state = PState::FRACTNUM;
                                break;
                            }else {
                                bbreak = true;
                            }
                    }
                    break;
                }
                case PState::DOT: {
                    if (c == std::use_facet< std::numpunct<char> >(std::locale()).decimal_point()) {
                        state = PState::FRACTNUM;
                        break;
                    } else if (wildcard != '\0' && c == wildcard) {
                        state = PState::FRACTNUM;
                        break;
                    } else if (c == 'e') {
                        if (os) {
                            AMCore::AMStreamPut(*os, oc);
                        }
                        state = PState::EXP;
                    } else {
                        bbreak = true;
                    }
                    break;
                }
                case PState::FRACTNUM: {
                    switch (c) {
                        case '0':
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                        case '7':
                        case '8':
                        case '9': {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            if (mantissaMax == 0) {
                                while(c == '0') {
                                    c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                                    exponentMin--;
                                    exponentMax--;
                                }
                            }
                            TMantissa fracmanMin;
                            TMantissa fracmanMax;
                            std::ostringstream tos;
                            if (mantissaMin == mantissaMax) {
                                if (exponentMin > 0) {
                                    //exp is raise only when overflows
                                    if (rMin) {
                                        *rMin = AMConstructFloat<TReturnType>(mantissaMin, exponentMin, negative);
                                    }
                                    if (rMax) {
                                        *rMax= AMConstructFloat<TReturnType>(mantissaMax, exponentMin, negative);
                                    }
                                    return AMUIParseResult::PARTIAL;
                                }
                                int frac_mantissa_chars = 0;
                                bool overflows = false;
                                AMUIParseResult pr = AMUIParseNatural(
                                    is, &fracmanMin, &fracmanMax, &tos, wildcard, &frac_mantissa_chars
                                                                   );
                                if (pr != AMUIParseResult::SUCCESS) {
                                    //overflows
                                    overflow = true;
                                }
                                if (mantissaMax == 0) {
                                    mantissaMax = fracmanMax;
                                    mantissaMin = fracmanMin;
                                } else {
                                    TMantissa tManMax = mantissaMax;
                                    TMantissa tManMin = mantissaMin;
                                    do {
                                        if (overflows) {
                                            frac_mantissa_chars--;
                                            fracmanMin /= 10;
                                            fracmanMax /= 10;
                                            overflows = false;
                                        }
                                        if (frac_mantissa_chars > std::numeric_limits<TMantissa>::digits10) {
                                            overflows = true;
                                            overflow = true;
                                            continue;
                                        }
                                        TMantissa shift = AMCore::AMIntegerPowRaw((TMantissa) 10, frac_mantissa_chars);
                                        bool brv = AMCore::AMIntegerMultiply(mantissaMax, shift, tManMax);
                                        if (!brv) {
                                            //overflow
                                            overflows = true;
                                            overflow = true;
                                            continue;
                                        }
                                        tManMin = mantissaMin * shift;
                                        brv = AMCore::AMIntegerAdd(tManMax, fracmanMax, tManMax);
                                        if (!brv) {
                                            //overflow
                                            overflows = true;
                                            overflow = true;
                                            continue;
                                        }
                                        tManMin += fracmanMin;
                                    } while (overflows);
                                    mantissaMax = tManMax;
                                    mantissaMin = tManMin;

                                }
                                exponentMin -= frac_mantissa_chars;
                                exponentMax -= frac_mantissa_chars;
                                if (os) {
                                    (*os)<<std::use_facet< std::numpunct<char> >(std::locale()).decimal_point()<<tos.str().substr(0, frac_mantissa_chars);
                                }
                            } else {
                                overflow = true;
                            }
                            if (overflow) {
                                bbreak = true;
                                AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                            }
                            break;
                        }
                        case 'e':
                            if (os) {
                                AMCore::AMStreamPut(*os, oc);
                            }
                            state = PState::EXP;
                            break;
                        case UEOF:
                            bbreak = true;
                            break;
                        default:
                            if (wildcard != '\0' && c == wildcard) {
                                TMantissa wmantissa = mantissaMax;
                                bool dotWritten = false;
                                while(c == wildcard) {
                                    bool brv = AMCore::AMIntegerMultiply(wmantissa, (TMantissa)10, wmantissa);
                                    if (!brv) {
                                        //overflow
                                        break;
                                    }
                                    brv = AMCore::AMIntegerAdd(wmantissa, (TMantissa)9, wmantissa);
                                    if (!brv) {
                                        //overflow
                                        break;
                                    }
                                    if (!dotWritten) {
                                        (*os)<<std::use_facet< std::numpunct<char> >(std::locale()).decimal_point();
                                        dotWritten = true;
                                    }
                                    AMCore::AMStreamPut(*os, oc);
                                    c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                                }
                                AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            } else {
                                bbreak = true;
                                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                            }
                    }
                    break;
                }
                case PState::EXP: {
                    if (!((c >= '0' && c <= '9') || c == '+' || c == '-' || (wildcard != '\0' && c == wildcard))) {
                        overflow = true;
                        bbreak = true;
                        break;
                    }
                    int newexpMin;
                    int newexpMax;
                    AMCore::AMStreamToLowercaseStripDiaUnget(is);
                    AMUIParseResult pr = AMUIParseInteger(is, &newexpMin, &newexpMax, os, wildcard);
                    if (pr != AMUIParseResult::SUCCESS) {
                        //overflows
                        overflow = true;
                        bbreak = true;
                        AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                        break;
                    }
                    exponentMin += newexpMin;
                    exponentMax += newexpMax;
                    newexpMin += mantissa_chars;
                    newexpMax += mantissa_chars;
                    if ( newexpMin < std::numeric_limits<TReturnType>::min_exponent10) {
                        overflow = true;
                        bbreak = true;
                        break;
                    }
                    if ( newexpMax > std::numeric_limits<TReturnType>::max_exponent10) {
                        overflow = true;
                        bbreak = true;
                        break;
                    }
                    AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
                    bbreak = true;
                    break;
                }
            }
            if (bbreak) {
                break;
            }
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &oc);
        } while (1);

        if (c != UEOF) {
            AMCore::AMStreamToLowercaseStripDiaUnget(is);
        }
        if (rMin) {
            *rMin = AMConstructFloat<TReturnType>(mantissaMin, exponentMin, negative);
        }
        if (rMax) {
            *rMax= AMConstructFloat<TReturnType>(mantissaMax, exponentMax, negative);
        }
        return overflow ? AMUIParseResult::PARTIAL : AMUIParseResult::SUCCESS;*/
    }

    template<typename TReturnType>
    AMUIParseResult AMUIParseFloat(std::istream &is, AMCore::AMNullable<TReturnType> *rMin, AMCore::AMNullable<TReturnType> *rMax, std::ostream *os, char wildcard)
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (rMax) {
                rMax->set();
            }
            if (rMin) {
                rMin->set();
            }
            return AMUIParseResult::PARTIAL;
        }
        TReturnType tMin, tMax;
        AMUIParseResult pr = AMUIParseFloat(is, &tMin, &tMax, os, wildcard);
        if (pr != AMUIParseResult::FAIL) {
            if (rMax) {
                rMax->set(tMax);
            }
            if (rMin) {
                rMin->set(tMin);
            }
        }
        return pr;
    }

    template<typename TReturnType, typename TStringType, typename TInputType, typename TOutputType, bool reverse>
    AMUIParseResult _AMUIParseEnumStartsWith(
        TInputType &is,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        TOutputType *os
    )
    {
        TReturnType returnValue = AMUI::DEFAULT_INT_ID;
        unsigned int EOF_CHAR = 0;
        static_assert(
            std::is_base_of<std::istream, TInputType>::value && std::is_base_of<std::ostream, TOutputType>::value
            || std::is_base_of<std::string, TInputType>::value && std::is_base_of<std::string, TOutputType>::value
            , "_AMUIParseEnumStartsWith:: Unsupported types");
        struct mapItem {
            TReturnType value;
            mutable typename std::conditional<reverse, std::string::const_reverse_iterator, std::string::const_iterator>::type sit;
        };
        std::map<std::string, mapItem, std::less<> > localTable;
        for(auto it: table.setB()) {
            if (it.first.empty()) {
                returnValue = *it.second;
                localTable.insert({"''", {*(it.second)}});
            } else {
                localTable.insert({it.first, {*(it.second)}});
            }
        }
        for(auto &it: localTable) {
            if constexpr (reverse) {
                it.second.sit = it.first.crbegin();
            } else {
                it.second.sit = it.first.cbegin();
            }
        }
        typename std::map<std::string, mapItem, std::less<> >::const_iterator  it = localTable.cbegin();
        unsigned int c;
        typename std::conditional<
                std::is_base_of<std::istream, TInputType>::value,
                unsigned int,
                typename std::conditional<reverse, std::string::const_reverse_iterator, std::string::const_iterator>::type
            >::type it_or_oc;
        if constexpr (std::is_base_of<std::istream, TInputType>::value) {
            it_or_oc = UEOF;
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &it_or_oc);
            EOF_CHAR = UEOF;
            while (c != UEOF && std::isspace(c)) {
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &it_or_oc);
            }
        } else {
            if constexpr (reverse) {
                it_or_oc = is.crbegin();
            } else {
                it_or_oc = is.cbegin();
            }
            c = AMCore::AMStringToLowercaseStripDiaGet(it_or_oc, is);
        }
        std::ostringstream osStripped;
        int charsParsed = 0;
        bool afterSpace = false;
        AMCore::AMNullable<TReturnType> eqval;
        int endSpaces = 0;
        while (c != EOF_CHAR) {
            //remove unmatched options
            for (it = localTable.begin(); it != localTable.end();) {
                unsigned int nc = UEOF;
                if constexpr (reverse) {
                    if (it->second.sit == it->first.crend()) {
                        eqval.set(it->second.value);
                    } else {
                        nc = AMCore::AMStringToLowercaseStripDiaGet(it->second.sit, it->first);
                    }
                } else {
                    if (it->second.sit == it->first.cend()) {
                        eqval.set(it->second.value);
                    } else {
                        nc = AMCore::AMStringToLowercaseStripDiaGet(it->second.sit, it->first);
                    }
                }
                if (nc != c) {
                    auto jit = localTable.erase(it);
                    it = jit;
                    continue;
                } else {
                    it++;
                }
            }
            charsParsed++;

            //if no options, try return last completely matched
            if (localTable.size() == 0) {
                if constexpr (std::is_base_of<std::istream, TInputType>::value) {
                    AMCore::AMStreamToLowercaseStripDiaUnget(is);
                } else {
                    AMCore::AMStringToLowercaseStripDiaUnget(it_or_oc, is);
                }
                if (!eqval.isNull()) {
                    returnValue = eqval.get();
                }
                if (returnValue != AMUI::DEFAULT_INT_ID) {
                    if (r) {
                        *r = returnValue;
                    }
                    if (!eqval.isNull()) {
                        if constexpr (reverse) {
                            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                                if (os) {
                                    *os = is.substr(0, is.size() - (it_or_oc - is.crbegin()));
                                }
                            }
                        } else {
                            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                                if (os) {
                                    *os = is.substr(it_or_oc - is.cbegin(), is.size());
                                }
                            }
                        }
                    }
                    return AMUIParseResult::SUCCESS;
                }
                return AMUIParseResult::FAIL;
            }

            //put char to output (spaces only with last)
            if constexpr (std::is_base_of<std::ostream, TOutputType>::value) {
                if (os) {
                    if (std::isspace(it_or_oc)) {
                        endSpaces++;
                    } else {
                        if (endSpaces) {
                            (*os) << std::setw(endSpaces) << ' ';
                            endSpaces = 0;
                        }
                        AMCore::AMStreamPut(*os, it_or_oc);
                    }
                }
            }

            // only one option, take it (is partial or success) an finish
            if (localTable.size() == 1) {
                it = localTable.cbegin();
                break;
            }

            if constexpr (reverse) {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (it_or_oc >= is.crend()) {
                        //printf("TTTTTTTTTTTTTTTbeeeeeeeeRR\n");
                        break;
                    }
                }
            } else {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (it_or_oc >= is.cend()) {
                        //printf("TTTTTTTTTTTTTTTTTbeeeeeeee\n");
                        break;
                    }
                }
            }

            if constexpr (std::is_base_of<std::istream, TInputType>::value) {
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &it_or_oc);
            } else {
                c = AMCore::AMStringToLowercaseStripDiaGet(it_or_oc, is);
            }

            afterSpace = std::isspace(c);
        }

        //here is only one option (not parsed whole) or end of parsed string -- localtable size cant be zero

        //return if end of parsed string - not needed
        unsigned int nc = UEOF;
        /*
        if (c == UEOF || c == 0) {
            if (localTable.size() != 1) {
                if (returnValue == AMUI::DEFAULT_INT_ID) {
                    return AMUIParseResult::FAIL;
                }
                if (r) {
                    *r = returnValue;
                }
                return AMUIParseResult::SUCCESS;
            }
            if (r) {
                *r = returnValue;
            }
            it = localTable.cbegin();
            nc = AMCore::AMStringToLowercaseStripDiaGet(it->second.sit, it->first);
            if constexpr (reverse) {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        *os = is.substr(0, is.size() - (it_or_oc - is.crbegin()));
                    }
                }
            } else {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        *os = is.substr(it_or_oc - is.cbegin(), is.size());
                    }
                }
            }

            if (nc == UEOF || nc == 0) {
                return AMUIParseResult::SUCCESS;
            }
            return AMUIParseResult::PARTIAL;
        }*/

        it = localTable.cbegin();

        //exit if found ueof
        unsigned int tnc = AMCore::AMStringToLowercaseStripDiaUnget(it->second.sit, it->first);
        if (tnc == EOF_CHAR) {
            if (returnValue != AMUI::DEFAULT_INT_ID) {
                if (r) {
                    *r = returnValue;
                }
                return AMUIParseResult::SUCCESS;
            }
            return AMUIParseResult::FAIL;
        }
        nc = AMCore::AMStringToLowercaseStripDiaGet(it->second.sit, it->first);


//        if constexpr (reverse) {
//            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
//                if (it_or_oc > is.crend()) {
//                    printf("FFFFFFbeeeeeeeeRR\n");
//                }
//            }
//        } else {
//            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
//                if (it_or_oc > is.cend()) {
//                    printf("FFFFFFbeeeeeeee\n");
//                }
//            }
//        }
        //compare to end of word
        auto it_or_oc_end = it_or_oc;
        while (1) {
            if constexpr (reverse) {
                if (it->second.sit == it->first.crend()) {
                    break;
                }
            } else {
                if (it->second.sit == it->first.cend()) {
                    break;
                }
            };


//            if (c == EOF_CHAR) {
//                printf("huuuuuuuu\n");
//                //break;
//            }

            if constexpr (reverse) {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (it_or_oc_end >= is.crend()) {
                        //printf("beeeeeeeeRR\n");
                        break;
                    }
                }
            } else {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (it_or_oc_end >= is.cend()) {
                        //printf("beeeeeeee\n");
                        break;
                    }
                }
            }

            //printf("CCCCCC c=%x ",c);
//            if constexpr (!reverse) {
//            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
//                int start = it_or_oc_end - is.cbegin();
//                printf("[%i] =>", start);
//            }}
            if constexpr (std::is_base_of<std::istream, TInputType>::value) {
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &it_or_oc_end);
            } else {
                c = AMCore::AMStringToLowercaseStripDiaGet(it_or_oc_end, is);
            }

//            printf("%x ioc=", c);
//            if constexpr (!reverse) {
//            if constexpr (std::is_base_of<std::string, TOutputType>::value) {
//                int start = it_or_oc_end - is.cbegin();
//                printf("%i\n", start);
//            }}
            nc = AMCore::AMStringToLowercaseStripDiaGet(it->second.sit, it->first);
            if (c == EOF_CHAR || nc != c) {
                break;
            }
            if constexpr (std::is_base_of<std::ostream, TOutputType>::value) {
                if (os) {
                    if (std::isspace(it_or_oc_end)) {
                        endSpaces++;
                    } else {
                        if (endSpaces) {
                            (*os) << std::setw(endSpaces) << ' ';
                            endSpaces = 0;
                        }
                        AMCore::AMStreamPut(*os, it_or_oc_end);
                    }
                }
            }
        }


        //if i am en ts return success
        if constexpr (reverse) {
            if (it->second.sit == it->first.crend() && (nc == c || c== 0) ) {
                if (r) {
                    *r = it->second.value;
                }
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        *os = is.substr(0, is.size() - (it_or_oc_end - is.crbegin()) - 1);
                    }
                }
                return AMUIParseResult::SUCCESS;
            }
        } else {
            if (it->second.sit == it->first.cend() && (nc == c || c== 0) ) {
                if (r) {
                    *r = it->second.value;
                }
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        int start = it_or_oc_end - is.cbegin() + 1;
                        *os = start > is.size() ? TStringType() : is.substr(start, is.size());
                    }
                }
                return AMUIParseResult::SUCCESS;
            }
        }


        AMUIParseResult res = AMUIParseResult::FAIL;
        if (std::isspace(c) || c == EOF_CHAR) {
            //succes or partial whe i am on end of word
            if (c != EOF_CHAR) {
                if constexpr (std::is_base_of<std::istream, TInputType>::value) {
                    AMCore::AMStreamToLowercaseStripDiaUnget(is);
                } else {
                    AMCore::AMStringToLowercaseStripDiaUnget(it_or_oc, is);
                }
            }
            if (!eqval.isNull()) {
                returnValue = eqval.get();
                res = AMUIParseResult::SUCCESS;
            } else {
                returnValue = it->second.value;

                res = AMUIParseResult::PARTIAL;
            }
            it_or_oc = it_or_oc_end;
        } else if (!eqval.isNull()) {
            //if already shorete option fits take it
            returnValue = eqval.get();
            res = AMUIParseResult::SUCCESS;
        }
        if (res == AMUIParseResult::FAIL) {
            if (returnValue != AMUI::DEFAULT_INT_ID) {
                if (r) {
                    *r = returnValue;
                }
                return AMUIParseResult::SUCCESS;
            }
            return res;
        }
        if constexpr (reverse) {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        int len = it_or_oc_end - is.crbegin();
                        *os = len > 0 ? is.substr(0, is.size() - (len)) : "";
                        //*os = is.substr(0, is.size() - (len));
                    }
                }
        } else {
                if constexpr (std::is_base_of<std::string, TOutputType>::value) {
                    if (os) {
                        int start = it_or_oc - is.cbegin();
                        //if (start > is.size()) {
                        //    printf("BLEEE\n");
                        //}
                        *os = start < is.size() ? is.substr(start, is.size()) : "";
                        //*os = is.substr(it_or_oc - is.cbegin(), is.size());
                    }
                }
        }
        if (r) {
            *r = returnValue;
        }
        return res;
    }

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumStartsWith(
        TStringType &is,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        bool reverse,
        TStringType* os
    )
    {
        return reverse
            ? _AMUIParseEnumStartsWith<TReturnType, TStringType, TStringType, TStringType, true>(is, r, table, os)
            : _AMUIParseEnumStartsWith<TReturnType, TStringType, TStringType, TStringType, false>(is, r, table, os);
    }


    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnum(
        std::istream &is,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        std::ostream *os
    )
    {
        return _AMUIParseEnumStartsWith<TReturnType, TStringType, std::istream, std::ostream, false>(is, r, table, os);
    }

    template<typename TReturnType, typename TStringType, typename TCompareStringType>
    AMUIParseResult AMUIParseEnum(
        TStringType &input,
        TReturnType *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    )
    {

        if (input == "''") {
            const TReturnType *rv = table.findA("");
            if (rv) {
                if (r) {
                    *r = *rv;
                }
                return AMUIParseResult::SUCCESS;
            }
        }
        typename std::set<AMCore::AMTwoWayPair<TStringType, const TReturnType*>>::const_iterator it = table.setB().begin();
        TCompareStringType strData(input);
        TCompareStringType strTable;
        typename std::set<AMCore::AMTwoWayPair<TStringType, const TReturnType*>>::const_iterator res = table.setB().end();
        while (it != table.setB().end()) {
            strTable = it->first;
            if (strncmp(strData.c_str(), strTable.c_str(), strData.length()) == 0) {
                if (strData.length() == strTable.length()) {
                    //presise equality
                    res = it;
                    break;
                }
                if (res != table.setB().end()) {
                    return AMUIParseResult::FAIL;
                }
                res = it;
            }
            it ++;
        }
        if (res == table.setB().end()) {
            return AMUIParseResult::FAIL;
        }
        /*
        if (strData.length() < strTable.length()) {
            typename std::set<AMCore::AMTwoWayPair<TStringType, const TReturnType*>>::const_iterator it2 = it;
            it2++;
            TCompareStringType strTable2;
            while (it2 != table.setB().end()) {
                strTable2 = it2->first;
                if (strncmp(strData.c_str(), strTable2.c_str(), std::min(strTable2.length(), strData.length())) == 0) {
                    if (it->first.length()  < it2->first.length()) {
                        it = it2;
                        strTable = strTable2;
                    }
                } else {
                    break;
                }
                it2 ++;
            }
        }*/
        if (r) {
            *r = *(res->second);
        }
        return strTable.length() == strData.length() ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
    }

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnum(
        std::istream &is,
        AMCore::AMNullable<TReturnType> *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        std::ostream *os
    )
    {
        bool brv;
        AMUIParseResult npr = AMUIParseNull(is, &brv, os);
        if (npr == AMUIParseResult::FAIL) {
            return AMUIParseResult::FAIL;
        }
        if (npr == AMUIParseResult::SUCCESS) {
            if (r) {
                r->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        if (npr == AMUIParseResult::PARTIAL && brv == true) {
            if (r) {
                r->set();
            }
            return AMUIParseResult::PARTIAL;
        }
        TReturnType t;
        AMUIParseResult pr = AMUIParseEnum(is, &t, table, os);
        if (pr != AMUIParseResult::FAIL) {
            if (r) {
                r->set(t);
            }
        }
        return pr;
    }

    template<typename TReturnType, typename TStringType, typename TCompareStringType>
    AMUIParseResult AMUIParseEnum(
        TStringType &input,
        AMCore::AMNullable<TReturnType> *r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    )
    {
        TStringType si = input;
        AMCore::AMStringToLowercaseStripDia(si);
        TStringType sn = AMCore::AMNullString;
        AMCore::AMStringToLowercaseStripDia(sn);
        if (si == sn) {
            if (r) {
                r->set();
            }
            return AMUIParseResult::SUCCESS;
        }
        TReturnType t;
        AMUIParseResult rv = AMUIParseEnum(input, &t,table);
        if (r && rv != AMUIParseResult::FAIL) {
            r->set(t);
        }
        return rv;
    }

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumLine(
        TStringType &input,
        std::map<TReturnType, int>& r,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        TStringType separator
    )
    {
        std::map<int, TStringType> fields = AMCore::AMExplode(separator, input);
        int ndx = 0;
        r.clear();
        AMUIParseResult returnValue = AMUIParseResult::SUCCESS;
        for(auto& rPair: fields) {
            TReturnType rr;
            AMUIParseResult pr = AMUIParseEnum((TStringType&)rPair.second, &rr, table);
            if (pr == AMUIParseResult::FAIL) {
                returnValue = AMUIParseResult::FAIL;
                continue;
            } else if (pr == AMUIParseResult::PARTIAL) {
                if (returnValue != AMUIParseResult::FAIL) {
                    returnValue = AMUIParseResult::PARTIAL;
                }
            }
            r.insert({rr, ndx});
            ndx++;
        }
        return returnValue;
    }

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumMapLine(
        TStringType &input,
        std::map<TReturnType, int>& rs,
        std::map<int, std::map<TReturnType, int> >& rv,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table,
        TStringType separatorAnd,
        TStringType separatorOr
    )
    {
        rv.clear();
        rs.clear();
        AMUIParseResult prv = AMUIParseResult::SUCCESS;
        std::map<int, TStringType> fields = AMCore::AMExplode(separatorOr, input);
        int ndx = 0;
        for(auto it:fields) {
            std::map<TReturnType, int> r;
            AMUIParseResult pr = AMUIParseEnumLine(it.second, r, table, separatorAnd);
            if (pr == AMUIParseResult::SUCCESS) {
                if (r.size() == 1) {
                    rs.insert({r.begin()->first, ndx++});
                } else if (r.size() >= 2) {
                    rv.insert({ndx++, r});
                }
            } else if (pr == AMUIParseResult::PARTIAL) {
                prv = (prv == AMUIParseResult::FAIL ? AMUIParseResult::FAIL : AMUIParseResult::PARTIAL);
                if (r.size() == 1) {
                    rs.insert({r.begin()->first, ndx++});
                } else if (r.size() >= 2) {
                    rv.insert({ndx++, r});
                }
            } else if (pr == AMUIParseResult::FAIL) {
                prv = AMUIParseResult::FAIL;
            }
        }
        return prv;
    }

    template<typename TReturnType, typename TStringType>
    AMUIParseResult AMUIParseEnumLine(
        TStringType &input,
        std::map<TReturnType, int>& r,
        bool& null,
        const AMCore::AMTwoWayTable<TReturnType, TStringType>& table
    )
    {
        std::map<int, TStringType> fields = AMCore::AMExplode(",", input);
        int ndx = 0;
        r.clear();
        AMUIParseResult returnValue = AMUIParseResult::SUCCESS;
        bool nullDetected = false;
        for(auto& rPair: fields) {
            AMCore::AMNullable<TReturnType> rr;
            AMUIParseResult pr = AMUIParseEnum((TStringType&)rPair.second, &rr, table);
            if (pr == AMUIParseResult::FAIL) {
                returnValue = AMUIParseResult::FAIL;
                continue;
            } else if (pr == AMUIParseResult::PARTIAL) {
                if (returnValue != AMUIParseResult::FAIL) {
                    returnValue = AMUIParseResult::PARTIAL;
                }
            }
            if (rr.isNull()) {
                nullDetected = true;
            } else {
                r.insert({rr.get(), ndx});
                ndx++;
            }
        }
        null = nullDetected;
        return returnValue;
    }

    template<typename TStringType>
    AMUIParseResult AMUIParseAddress(TStringType& input, AMCore::AMAddress<TStringType>* r, char wildcard)
    {
        TStringType in = AMCore::AMStringStripSpaces(input);
        std::map<int, TStringType> places = AMCore::AMExplode(":", in);
        TStringType place;
        if (places.size() == 1) {
            place = "";
        } else if (places.size() == 2) {
            place = places[0];
            in = places[1];
        } else {
            return AMUIParseResult::FAIL;
        }
        std::map<int, TStringType> segments = AMCore::AMExplode(",", in);
        if (segments.size() < 1 || segments.size() > 4) {
            return AMUIParseResult::FAIL;
        }
        TStringType  fmts("%l%0 .-");
        if (wildcard != '\0') {
            fmts+="%%";
            fmts.push_back(wildcard);
        }
        TStringType street = segments[0];
        TStringType orientationNum;
        int num = 9999;
        typename TStringType::size_type slash = street.find_last_of("/");
        if (slash != TStringType::npos) {
            orientationNum = street.substr(slash + 1);
            street = street.substr(0, slash);
            orientationNum = AMCore::AMStringStripSpaces(orientationNum);
            street = AMCore::AMStringStripSpaces(street);

            typename TStringType::size_type sp = street.find_last_of(" ");
            TStringType snum;
            if (sp != TStringType::npos) {
                snum = street.substr(sp + 1);
                street = street.substr(0, sp);
            } else {
                snum = street;
                street = "";
            }
            std::istringstream is(snum);
            int charsParsed = -1;
            AMUIParseResult res = AMUIParseNatural(is, &num, (int *)nullptr, nullptr, '\0', &charsParsed);
            if (res == AMUIParseResult::FAIL || charsParsed != snum.size()) {
                return AMUIParseResult::FAIL;
                //num = 9999;
                //street += " " + snum;
            }
            street = AMCore::AMStringStripSpaces(street);
        } else {
            typename TStringType::size_type sp = street.find_last_of(" ");
            if (sp != TStringType::npos) {
                TStringType snum = street.substr(sp + 1);
                std::istringstream is(snum);
                int charsParsed = -1;
                AMUIParseResult res = AMUIParseNatural(is, &num, (int *)nullptr, nullptr, '\0', &charsParsed);
                if (res == AMUIParseResult::FAIL || charsParsed != snum.size()) {
                    return AMUIParseResult::FAIL;
                    //num = 9999;
                    //orientationNum = snum;
                }
                street = street.substr(0, sp);
                street = AMCore::AMStringStripSpaces(street);
            } else {
                /*
                std::istringstream is(street);
                int charsParsed = -1;
                AMUIParseResult res = AMUIParseNatural(is, &num, (int *) nullptr, nullptr, '\0', &charsParsed);
                if (res != AMUIParseResult::FAIL && charsParsed == street.size()) {
                    street = "";
                } else {
                    num = -1;
                }*/
                return AMUIParseResult::FAIL;
            }
        }

        if (!AMCore::AMStringContainsOnlyCharacters(fmts, street)) {
            return AMUIParseResult::FAIL;
        }
        //AMCore::AMStringStripSpaces(street);
        /*TStringType  fmtc("%l%0 -");
        if (wildcard != '\0') {
            fmtc+="%%";
            fmtc.push_back(wildcard);
        }*/
        TStringType city = segments[1];
        if (!AMCore::AMStringContainsOnlyCharacters(fmts, city)) {
            return AMUIParseResult::FAIL;
        }
        TStringType zip;
        if (segments.size() >= 3) {
            zip = segments[2];
            TStringType  fmtz("%0 %a%A");
            if (wildcard != '\0') {
                fmtz+="%%";
                fmtz.push_back(wildcard);
            }
            if (!AMCore::AMStringContainsOnlyCharacters(fmtz, zip)) {
                return AMUIParseResult::FAIL;
            }
        }
        TStringType country;
        if (segments.size() == 4) {
            country = segments[3];
            TStringType  fmtco("%l -");
            if (wildcard != '\0') {
                fmtco+="%%";
                fmtco.push_back(wildcard);
            }
            if (!AMCore::AMStringContainsOnlyCharacters(fmtco, country)) {
                return AMUIParseResult::FAIL;
            }
        }
        AMUIParseResult res = AMUIParseResult::SUCCESS;
        if (street.size() < 2) {
            res = AMUIParseResult::PARTIAL;
        }
        if (num == 9999) {
            res = AMUIParseResult::PARTIAL;
        }
        if (city.size() < 2) {
            res = AMUIParseResult::PARTIAL;
        }
        //if (zip.size() < 2) {
        //    res = AMUIParseResult::PARTIAL;
        //}
        if (r) {
            r->place = place;
            r->street = street;
            r->parcelNum = num;
            r->orientationNum = orientationNum;
            r->city = city;
            r->zip = zip;
            r->country = country;
        }
        return res;
    }

    template<typename TEnumType, typename TStringType, typename TReturnType>
    AMUIParseResult AMUIParseName(const TStringType& input, TReturnType/*AMCore::AMNameBase<TEnumType, TStringType>*/* r, char wildcard)
    {
        AMUIParseResult result = AMUIParseResult::SUCCESS;
        int degreeBefore = AMUI::DEFAULT_INT_ID;
        int degreeAfter = AMUI::DEFAULT_INT_ID;
        TStringType in = AMCore::AMStringStripSpaces(input);
        if (in.empty()) {
            return AMUIParseResult::FAIL;
        }
        AMUIParseResult res = AMUIParseResult::FAIL;
        bool endsWithPercent = false;
        bool startWithPercent = false;
        if (wildcard != '\0' && input.length() >=2 && input[0] == '%') {
            in = input.substr(1);
            startWithPercent = true;
            const int *dg = r->mdegreeBeforeTable().findA("");
            if (dg) {
                degreeBefore = *dg;
            }
        } else {
            AMUIParseResult res = AMUIParseEnumStartsWith(in, &degreeBefore, r->mdegreeBeforeTable(), false, &in);
            if (res == AMUIParseResult::PARTIAL) {
                result = AMUIParseResult::PARTIAL;
            }
        }
        in = AMCore::AMStringStripSpaces(in);
        if (wildcard != '\0' && input.length() >=2 && input[input.length() - 1] == '%') {
            in.pop_back();
            endsWithPercent = true;
            const int *dg = r->mdegreeAfterTable().findA("");
            if (dg) {
                degreeAfter = *dg;
            }
        } else {
            res = AMUIParseEnumStartsWith(in, &degreeAfter, r->mdegreeAfterTable(), true, &in);
            if (res == AMUIParseResult::PARTIAL) {
                result = AMUIParseResult::PARTIAL;
            }
        }
        in = AMCore::AMStringStripSpaces(in);
        if (res != AMUIParseResult::FAIL && (*in.rbegin() == ',')) {
            in.pop_back();
            in = AMCore::AMStringStripSpaces(in);
        }
        TStringType  fmt("%l .-");
        if (wildcard != '\0') {
            fmt+="%%";
            fmt.push_back(wildcard);
        }
        if (!AMCore::AMStringContainsOnlyCharacters(fmt, in)) {
            return AMUIParseResult::FAIL;
        }


        std::map<int, TStringType> multi_names = AMCore::AMExplode("  ", in);
        if (multi_names.size() == 1) {
            std::map<int, TStringType> single_names = AMCore::AMExplode(" ", in);
            if (single_names.size() == 2) {
                r->name = single_names[0] == "%" ? "" : single_names[0];
                r->surname = single_names[1] == "%" ? "" : single_names[1];
                if (degreeBefore == AMUI::DEFAULT_INT_ID || startWithPercent) {
                    r->degreeBefore.set();
                } else {
                    r->degreeBefore = degreeBefore;
                }
                if (degreeAfter == AMUI::DEFAULT_INT_ID) {
                    r->degreeAfter.set();
                } else {
                    r->degreeAfter = degreeAfter;
                }
                return result;
            }if (single_names.size() == 1) {
                if (degreeBefore == AMUI::DEFAULT_INT_ID) {
                    r->degreeBefore.set();
                } else {
                    r->degreeBefore = degreeBefore;
                }
                if (endsWithPercent) {
                    r->name = single_names[0] == "%" ? "" : single_names[0];
                    r->surname = "";
                    if (startWithPercent) {
                        r->degreeBefore.set();
                    }
                    return AMUIParseResult::SUCCESS;
                } else {
                    r->name = "";
                    r->surname = single_names[0] == "%" ? "" : single_names[0];
                    if (degreeAfter == AMUI::DEFAULT_INT_ID) {
                        r->degreeAfter.set();
                    } else {
                        r->degreeAfter = degreeAfter;
                    }
                    if (startWithPercent) {
                        return AMUIParseResult::SUCCESS;
                    }
                    return AMUIParseResult::PARTIAL;
                }

            } else {
                return AMUIParseResult::FAIL;
            }
        } else if (multi_names.size() == 2) {
            std::map<int, TStringType> single_names_0 = AMCore::AMExplode(" ", multi_names[0]);
            std::map<int, TStringType> single_names_1 = AMCore::AMExplode(" ", multi_names[1]);
            if (
                (single_names_0.size() == 2 || single_names_0.size() == 1)
                && (single_names_1.size() == 2 || single_names_1.size() == 1)) {
                r->name = multi_names[0] == "%" ? "" : multi_names[0];
                r->surname = multi_names[1] == "%" ? "" : multi_names[1];
                if (degreeBefore == AMUI::DEFAULT_INT_ID) {
                    r->degreeBefore.set();
                } else {
                    r->degreeBefore = degreeBefore;
                }
                if (degreeAfter == AMUI::DEFAULT_INT_ID) {
                    r->degreeAfter.set();
                } else {
                    r->degreeAfter = degreeAfter;
                }
                return result;
            }else {
                return AMUIParseResult::FAIL;
            }
        } else if (multi_names.size() == 0) {
            if (startWithPercent || endsWithPercent || degreeBefore != AMUI::DEFAULT_INT_ID || degreeAfter != AMUI::DEFAULT_INT_ID) {
                if (degreeBefore == AMUI::DEFAULT_INT_ID) {
                    r->degreeBefore.set();
                } else {
                    r->degreeBefore = degreeBefore;
                }
                if (degreeAfter == AMUI::DEFAULT_INT_ID) {
                    r->degreeAfter.set();
                } else {
                    r->degreeAfter = degreeAfter;
                }
                r->name = "";
                r->surname = "";
                return AMUIParseResult::PARTIAL;
            }
        }
        return AMUIParseResult::FAIL;
    }

    template<typename TStringType>
    bool isExpressionType(AMCore::AMDataType type)
    {
        return AMUIParseExpressionElement<TStringType>::comboIdsTab[0][(int)type] != AMUIParseExpressionElement<TStringType>::ComboId::INVALID;
    }

    template<typename TStringType>
    AMUIParseResult AMUIParseString(std::istream &is, TStringType *r, bool *likePrPr, std::ostream *os, char wildcard, char wildcardMulti, bool wildcardEnabled)
    {
        unsigned int original_c;
        unsigned int c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        while (std::isspace(c)) {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
        }
        std::ostringstream rv;

        if (c == '\'') {
            bool backslash = false;
            //unsigned int original_d;
            //unsigned int d = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
            /*if (d != wildcardMulti || !wildcardEnabled) {
                AMCore::AMStreamPut(rv, c);
                if (os) {
                    AMCore::AMStreamPut(*os, c);
                }
                AMCore::AMStreamToLowercaseStripDiaUnget(is);
            } else {*/
            //    AMCore::AMStreamPut(rv, d);
            //    if (os) {
            //        AMCore::AMStreamPut(*os, c);
            //        AMCore::AMStreamPut(*os, d);
            //    }
            //}
            //AMCore::AMStreamPut(rv, c);
            if (os) {
                AMCore::AMStreamPut(*os, c);

            }
            //bool rvLikePtr = false;
            do {
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
                //if (wildcardEnabled && (c == wildcard || c == wildcardMulti)) {
                //    rvLikePtr = true;
                //}
                switch (c) {
                    case '\\':
                        backslash = true;
                        break;
                    case '\'':
                        if (backslash) {
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            AMCore::AMStreamPut(rv, c);
                            backslash = false;
                        } else {
                            TStringType rvs;
                            /*if (d == wildcardMulti && wildcardEnabled) {
                                if (os) {
                                    AMCore::AMStreamPut(*os, c);
                                }
                                rvs = rv.str();
                                //unsigned int e = *rvs.rbegin();
                                //if (e == wildcardMulti) {
                                //    rvs = rvs.substr(0, rvs.length() - 1);
                                //}
                                if (rvs.size() < 1) {
                                    return AMUIParseResult::FAIL;
                                }
                            } else {*/
                                //AMCore::AMStreamPut(rv, c);
                                if (os) {
                                    AMCore::AMStreamPut(*os, c);
                                }
                                rvs = rv.str();
                                //if (rvs.length()  >= 2 && rvLikePtr) {
                                //    rvs = rvs.substr(1, rvs.length() -2);
                                //}
                                if (rvs.size() < 2) {
                                    return AMUIParseResult::FAIL;
                                }
                            //}
                            if (r) {
                                *r = rvs;
                            }
                            if (likePrPr) {
                                *likePrPr = false;//rvLikePtr;
                            }
                            return AMUIParseResult::SUCCESS;
                        }
                        break;
                    case UEOF: {
                        TStringType rvs;
                        /*if (d == wildcardMulti && wildcardEnabled) {
                            if (os) {
                                AMCore::AMStreamPut(*os, c);
                            }
                            rvs = rv.str();
                            //unsigned int e = *rvs.rbegin();
                            //if (e == wildcardMulti) {
                            //    rvs = rvs.substr(0, rvs.length() - 1);
                            //}
                            if (rvs.size() < 1) {
                                return AMUIParseResult::FAIL;
                            }
                        } else {*/
                            //AMCore::AMStreamPut(rv, '\'');
                            if (os) {
                                AMCore::AMStreamPut(*os, '\'');
                            }
                            rvs = rv.str();
                            //if (rvs.length()  >= 2 && rvLikePtr) {
                            //    rvs = rvs.substr(1, rvs.length() -2);
                            //}
                            if (rvs.size() < 2) {
                                return AMUIParseResult::FAIL;
                            }
                        //}
                        if (r) {
                            *r = rvs;
                        }
                        if (likePrPr) {
                            *likePrPr = false;//rvLikePtr;
                        }
                        return AMUIParseResult::PARTIAL;
                    }
                    default:
                        /*if (c == wildcard || c == wildcardMulti) {
                            if(!wildcardEnabled) {

                                TStringType rvs = rv.str();
                                if (rvs.size() < 2) {
                                    return AMUIParseResult::FAIL;
                                }
                                if (r) {
                                    *r = rvs;
                                }
                                if (likePrPr) {
                                    *likePrPr = rvLikePtr;
                                }
                                return AMUIParseResult::PARTIAL;
                            } else {
                                rvLikePtr = true;
                            }
                        }*/
                        if (os) {
                            AMCore::AMStreamPut(*os, original_c);
                        }
                        AMCore::AMStreamPut(rv, original_c);
                        backslash = false;
                }
            }while(1);
        } else {
            //AMCore::AMStreamPut(rv, wildcardMulti);
            bool needWildards = true;
            do {

                switch (c) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                    case ')':
                        AMCore::AMStreamToLowercaseStripDiaUnget(is);
                    case UEOF: {
                        //AMCore::AMStreamPut(rv, wildcardMulti);
                        TStringType rvs = rv.str();
                        if (rvs.size() <= 0) {
                            return AMUIParseResult::FAIL;
                        }
                        if (r) {
                            if (needWildards) {
                                *r = std::string(1, wildcardMulti) + rvs + wildcardMulti;
                            } else {
                                *r = rvs;
                            }
                        }
                        if (likePrPr) {
                            *likePrPr = true;
                        }
                        return rvs.size() >= 2 ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
                    }
                    default:
                        if ((c == wildcard || c == wildcardMulti) && !wildcardEnabled) {
                            AMCore::AMStreamToLowercaseStripDiaUnget(is);
                            //AMCore::AMStreamPut(rv, wildcardMulti);
                            TStringType rvs = rv.str();
                            if (rvs.size() == 0) {
                                return AMUIParseResult::FAIL;
                            }
                            if (r) {
                                *r = std::string(1, wildcardMulti) + rvs + wildcardMulti;
                            }
                            if (likePrPr) {
                                *likePrPr = true;
                            }
                            return rvs.size() >= 2 ? AMUIParseResult::SUCCESS : AMUIParseResult::PARTIAL;
                        } else if ((c == wildcard || c == wildcardMulti)) {
                            needWildards = false;
                        }
                        if (os) {
                            AMCore::AMStreamPut(*os, original_c);
                        }
                        AMCore::AMStreamPut(rv, original_c);
                }
                c = AMCore::AMStreamToLowercaseStripDiaGet(is, &original_c);
            }while(1);
        }
    }

    template<typename TStringType>
    AMUIParseResult AMUIParseString(std::istream &is, AMCore::AMNullable<TStringType> *r, bool *likePrPr, std::ostream *os, char wildcard, char wildcardMulti, bool wildcardEnabled)
    {
        bool brv;
        std::ostringstream nos;
        int tg  = is.tellg();
        AMUIParseResult npr = AMUIParseNull(is, &brv, &nos);
        if (npr == AMUIParseResult::SUCCESS) {
            if (r) {
                r->set();
            }
            if (os) {
                (*os)<<nos.str();
            }
            return AMUIParseResult::SUCCESS;
        }
        is.seekg(tg, std::ios_base::beg);
        TStringType srv;
        AMUIParseResult pr = AMUIParseString(is, &srv, likePrPr, os, wildcard, wildcardMulti, wildcardEnabled);
        if (r && pr != AMUIParseResult::FAIL) {
            r->set(srv);
        }
        return pr;
    }

#define _AMCALLNULLABLETYPE(nulableType, function) \
    {\
    AMCore::AMNullable<nulableType> irv;\
    res = function(_input, &irv, &irv, &s, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');\
    if (irv.isNull()) {\
        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::ISNULL;  \
            s.str("");                                          \
        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTNULL; \
            s.str("");                                          \
        } else {\
            res = AMUIParseResult::FAIL;\
        }\
    }\
    }

#define _AMCALLNULLABLETMTYPE(format) \
    {\
    AMCore::AMNullable<std::tm> trv;\
    res = AMUIParseDatetime(_input, &trv, &trv, &s, format, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');\
    if (trv.isNull()) {\
        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::ISNULL;  \
            s.str("");                                          \
        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTNULL; \
            s.str("");                                          \
        } else {\
            res = AMUIParseResult::FAIL;\
        }\
    }\
    }

#define _AMCALLNULLABLETMUSTYPE(format) \
    {\
    AMCore::AMNullable<AMCore::AMDatetimeus> trv;\
    res = AMUIParseDatetime(_input, &trv, &trv, &s, format, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');\
    if (trv.isNull()) {\
        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::ISNULL;  \
            s.str("");                                          \
        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {\
            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTNULL; \
            s.str("");                                          \
        } else {\
            res = AMUIParseResult::FAIL;\
        }\
    }\
    }

    template<typename TStringType>
    AMUIParseResult AMUIParseExpression(std::istream &_input, std::vector<AMUIParseExpressionElement<TStringType> > &_result, AMCore::AMDataType _type, int* errorFrom, int* errorTo, char wildcard)
    {
        AMCore::AMStreamResetContext(_input);
        int indent = 0;
        //bool needStripStringValue = false;
        bool canWriteError = errorFrom && errorTo;
        if (!_result.empty()) {
            indent = _result.rbegin()->indent + 1;
        } else {
            if (errorFrom && errorTo) {
                *errorFrom = -1;
                *errorTo = -1;
            }
        }
        std::ostringstream s;
        typename AMUIParseExpressionElement<TStringType>::Operations operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
        bool exit = false;
        bool brace = false;
        unsigned int c = 0;
        bool empty = true;

        AMUIParseResult parseResult = AMUIParseResult::SUCCESS;

        enum class PState
        {
            CONTINUES,
            OP,
            NUMCOMPLETE
        };

        PState ps = PState::CONTINUES;

        while(c != UEOF) {
            if (exit) {
                break;
            }
            if (canWriteError && *errorTo == -1) {int tg = _input.tellg();if (tg != EOF) {*errorFrom = tg;}};
            c = AMCore::AMStreamToLowercaseStripDiaGet(_input);
            switch (ps) {

                case PState::CONTINUES: {
                    switch (c) {
                        case ' ':
                            break;
                        case '=':
                            operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
                            ps = PState::OP;
                            break;
                        case '>': {
                            operation = AMUIParseExpressionElement<TStringType>::Operations::MORE;
                            ps = PState::OP;
                            unsigned int next = AMCore::AMStreamToLowercaseStripDiaGet(_input);
                            if (next == '=') {
                                operation = AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL;
                            } else {
                                AMCore::AMStreamToLowercaseStripDiaUnget(_input);
                            }
                            break;
                        }
                        case '<': {
                            operation = AMUIParseExpressionElement<TStringType>::Operations::LESS;
                            ps = PState::OP;
                            unsigned int next = AMCore::AMStreamToLowercaseStripDiaGet(_input);
                            if (next == '=') {
                                operation = AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL;
                            } else if (next == '>') {
                                operation = AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL;
                            } else {
                                AMCore::AMStreamToLowercaseStripDiaUnget(_input);
                            }
                            break;
                        }
                        case '(': {
                            _result.push_back(AMUIParseExpressionElement<TStringType>(indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[0][(int)_type], AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN));
                            AMUIParseResult res = AMUIParseExpression(_input, _result, _type, errorFrom, errorTo, wildcard);
                            if (res == AMUIParseResult::FAIL) {
                                parseResult = AMUIParseResult::FAIL;
                               if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                            } else if (res == AMUIParseResult::PARTIAL) {
                                parseResult = AMUIParseResult::PARTIAL;
                            }
                            _result.push_back(AMUIParseExpressionElement<TStringType>(indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][(int)_type], AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED));
                            ps = PState::NUMCOMPLETE;
                            brace = true;
                            break;
                        }
                        case UEOF: {
                            exit = true;
                            break;
                        }
                        default: {
                            {
                                AMCore::AMStreamToLowercaseStripDiaUnget(_input);
                                operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
                                ps = PState::OP;
                                break;
                            }
                        }
                    }
                    break;
                }
                case PState::OP: {
                    switch (c) {
                        case ' ':
                            break;
                        default: {
                            AMCore::AMStreamToLowercaseStripDiaUnget(_input);
                            AMUIParseResult res;
                            switch(_type) {
                                case AMCore::AMDataType_address:
                                case AMCore::AMDataType_string: {
                                    bool likePrPr;
                                    TStringType ts;
                                    res = AMUIParseString(
                                        _input,
                                        &ts,
                                        &likePrPr,
                                        nullptr,
                                        wildcard,
                                        '%',
                                        operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL
                                                         );
                                    if (likePrPr) {
                                        if (ts.length() >= 2 && ts[0] == '%' && *ts.rbegin() == '%') {
                                            ts = ts.substr(1, ts.length() - 2);
                                        }
                                        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::LIKE;
                                        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE;
                                        }
                                    }
                                    s << ts;
                                    //needStripStringValue = !likePrPr;
                                    break;
                                }
                                case AMCore::AMDataType_integer: res = AMUIParseInteger(_input, (int *) nullptr,  (int *) nullptr, &s, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');break;
                                case AMCore::AMDataType_long: res = AMUIParseInteger(_input, (long long *) nullptr,  (long long *) nullptr, &s, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');break;
                                case AMCore::AMDataType_natural: res = AMUIParseNatural(
                                        _input, (unsigned int *) nullptr, (unsigned int *) nullptr, &s,
                                        operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'
                                                                                       );break;
                                case AMCore::AMDataType_natural_long: res = AMUIParseNatural(
                                        _input, (unsigned long long *) nullptr, (unsigned long long *) nullptr, &s,
                                        operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'
                                                                                            );break;
                                case AMCore::AMDataType_float: res = AMUIParseFloat(_input, (float *) nullptr, (float *) nullptr, &s, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');break;
                                case AMCore::AMDataType_double: res = AMUIParseFloat(_input, (double *) nullptr, (double *) nullptr, &s, operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0');break;
                                case AMCore::AMDataType_datetime: res = AMUIParseDatetime(_input, (std::tm *) nullptr, (std::tm *) nullptr, &s, std::string(datetimeFormatText), operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'); break;
                                case AMCore::AMDataType_datetimeus: res = AMUIParseDatetime(_input, (AMCore::AMDatetimeus *) nullptr, (AMCore::AMDatetimeus *) nullptr, &s, std::string(datetimeusFormatText), operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'); break;
                                case AMCore::AMDataType_date: res = AMUIParseDatetime(_input, (std::tm *) nullptr, (std::tm *) nullptr, &s, std::string(dateFormatText), operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'); break;
                                case AMCore::AMDataType_time: res = AMUIParseDatetime(_input, (std::tm *) nullptr, (std::tm *) nullptr, &s, std::string(timeFormatText), operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL ? wildcard : '\0'); break;
                                case AMCore::AMDataType_address_nullable:
                                case AMCore::AMDataType_string_nullable: {
                                    bool likePrPr;
                                    AMCore::AMNullable<TStringType> srv;
                                    res = AMUIParseString(
                                        _input,
                                        &srv,
                                        &likePrPr,
                                        nullptr,
                                        wildcard,
                                        '%',
                                        operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL
                                                         );
                                    if (srv.isNull()) {
                                        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::ISNULL;
                                            s.str("");
                                        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTNULL;
                                            s.str("");
                                        } else {
                                            s << "null";
                                            res = AMUIParseResult::FAIL;
                                            empty = false;
                                        }
                                    } else if (likePrPr) {
                                        if (srv.get().length() >= 2 && srv.get()[0] == '%' && *srv.get().rbegin() == '%') {
                                            srv.set(srv.get().substr(1, srv.get().length() - 2));
                                        }
                                        s << srv.get();
                                        if (operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::LIKE;
                                        } else if (operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL) {
                                            operation = AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE;
                                        }
                                    } else {
                                        s << srv.get();
                                    }
                                    //needStripStringValue = !likePrPr;
                                    break;
                                }
                                case AMCore::AMDataType_integer_nullable: _AMCALLNULLABLETYPE(int, AMUIParseInteger);break;
                                case AMCore::AMDataType_long_nullable: _AMCALLNULLABLETYPE(long long, AMUIParseInteger);break;
                                case AMCore::AMDataType_natural_nullable: _AMCALLNULLABLETYPE(unsigned int, AMUIParseNatural);break;
                                case AMCore::AMDataType_natural_long_nullable: _AMCALLNULLABLETYPE(unsigned long long, AMUIParseNatural);break;
                                case AMCore::AMDataType_float_nullable: _AMCALLNULLABLETYPE(float, AMUIParseFloat);break;
                                case AMCore::AMDataType_double_nullable: _AMCALLNULLABLETYPE(double, AMUIParseFloat);break;
                                case AMCore::AMDataType_datetime_nullable: _AMCALLNULLABLETMTYPE(datetimeFormatText);break;
                                case AMCore::AMDataType_datetimeus_nullable: _AMCALLNULLABLETMUSTYPE(datetimeFormatText);break;
                                case AMCore::AMDataType_date_nullable: _AMCALLNULLABLETMTYPE(dateFormatText);break;
                                case AMCore::AMDataType_time_nullable: _AMCALLNULLABLETMTYPE(timeFormatText);break;
                                default: return AMUIParseResult::FAIL;
                            }
                            if (res == AMUIParseResult::FAIL) {
                                parseResult = AMUIParseResult::FAIL;
                                if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                                operation = AMUIParseExpressionElement<TStringType>::Operations::INVALID;
                            } else if (res == AMUIParseResult::PARTIAL) {
                                parseResult = AMUIParseResult::PARTIAL;
                                empty = false;
                            } else {
                                empty = false;
                            }
                            ps = PState::NUMCOMPLETE;
                            break;
                        }
                    }
                    break;
                }
                case PState::NUMCOMPLETE: {
                    switch (c) {
                        case UEOF:
                            exit = true;
                            if (indent > 0) {
                                parseResult = AMUIParseResult::PARTIAL;
                            }
                            break;
                        case ' ':
                            break;
                        case ')': {
                            if (indent == 0) {
                                parseResult = AMUIParseResult::FAIL;
                                if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                                if (!brace) {
                                    std::string toInsert = s.str();
                                    if (!toInsert.empty()
                                            || operation == AMUIParseExpressionElement<TStringType>::Operations::ISNULL
                                            || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTNULL) {


/*                                        if ((_type == AMCore::AMDataType_string || _type == AMCore::AMDataType_address || _type == AMCore::AMDataType_string_nullable || _type == AMCore::AMDataType_address_nullable)
     //                                       &&(operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL
       //                                        || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL)
                                                && needStripStringValue
                                               ) {
                                            toInsert = toInsert.substr(1, toInsert.size() - 2);
                                        }
                                        */
                                        _result.push_back(
                                            AMUIParseExpressionElement<TStringType>(
                                                indent,
                                                AMUIParseExpressionElement<TStringType>::comboIdsTab[0][(int)_type],
                                                operation,
                                                toInsert
                                                                                   ));
                                    }
                                }
                                _result.push_back(AMUIParseExpressionElement<TStringType>(indent, AMUIParseExpressionElement<TStringType>::ComboId::INVALID, AMUIParseExpressionElement<TStringType>::Operations::INVALID, ")"));
                                brace = true;
                            }
                            exit = true;
                            break;
                        }
                        default:
                            std::string word;
                            typename AMUIParseExpressionElement<TStringType>::Operations st = AMUIParseExpressionElement<TStringType>::Operations::INVALID;
                            std::string keywordAnd(andText);
                            AMCore::AMStringToLowercaseStripDia(keywordAnd);
                            std::string keywordOr(orText);
                            AMCore::AMStringToLowercaseStripDia(keywordOr);
                            while(std::isalpha(c)) {
                                AMCore::AMStringPushback(word, c);
                                c = AMCore::AMStreamToLowercaseStripDiaGet(_input);
                            }
                            if (word.empty()) {
                                while(c != UEOF && !std::isspace(c) && c != ')') {
                                    AMCore::AMStringPushback(word, c);
                                    c = AMCore::AMStreamToLowercaseStripDiaGet(_input);
                                }
                            }
                            AMCore::AMStreamToLowercaseStripDiaUnget(_input);
                            AMCore::AMStringToLowercaseStripDia(word);
                            if (word == keywordAnd) {
                                st = AMUIParseExpressionElement<TStringType>::Operations::AND;
                            } else if (word == keywordOr) {
                                st = AMUIParseExpressionElement<TStringType>::Operations::OR;
                            } else {
                                parseResult = AMUIParseResult::FAIL;
                                if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                            }
                            if (!brace) {

                                std::string toInsert = s.str();
                                if (
                                    !toInsert.empty()
                                    || operation == AMUIParseExpressionElement<TStringType>::Operations::ISNULL
                                    || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTNULL
                                    /*|| ((_type == AMCore::AMDataType_string || _type == AMCore::AMDataType_address || _type == AMCore::AMDataType_string_nullable || _type == AMCore::AMDataType_address_nullable)
                                        &&(operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL
                                            || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL))*/
                                    //|| operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID

                                    ) {
/*                                    if ((_type == AMCore::AMDataType_string || _type == AMCore::AMDataType_address || _type == AMCore::AMDataType_string_nullable || _type == AMCore::AMDataType_address_nullable)
//                                        &&(operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL
  //                                         || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL)
                                          && needStripStringValue
                                           ) {
                                        toInsert = toInsert.substr(1, toInsert.size() - 2);
                                    }
                                    */
                                    _result.push_back(
                                        AMUIParseExpressionElement<TStringType>(
                                            indent,
                                            operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID ? AMUIParseExpressionElement<TStringType>::ComboId::INVALID : AMUIParseExpressionElement<TStringType>::comboIdsTab[0][(int)_type],
                                            operation,
                                            toInsert
                                                                               )
                                                     );
                                }
                            }
                            if (
                                _result.size() != 0
                                && (AMUIParseExpressionElement<TStringType>::isEven(_result.crbegin()->comboId)
                                    || _result.crbegin()->comboId == AMUIParseExpressionElement<TStringType>::ComboId::INVALID
                                )
                                ) {
                                _result.push_back(
                                    AMUIParseExpressionElement<TStringType>(
                                        indent, st == AMUIParseExpressionElement<TStringType>::Operations::INVALID
                                                ? AMUIParseExpressionElement<TStringType>::ComboId::INVALID
                                                : AMUIParseExpressionElement<TStringType>::comboIdsTab[1][(int) _type]
                                        , st,
                                        st == AMUIParseExpressionElement<TStringType>::Operations::INVALID ? word : ""
                                                                           )
                                                 );
                            } else {
                                _result.push_back(
                                    AMUIParseExpressionElement<TStringType>(
                                        indent, AMUIParseExpressionElement<TStringType>::ComboId::INVALID
                                        , AMUIParseExpressionElement<TStringType>::Operations::INVALID, word
                                                                           )
                                                 );
                                *errorTo = -1;
                                if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                            }
                            ps = PState::CONTINUES;
                            operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
                            brace = false;
                            s.str("");
                            break;
                    }
                    break;
                }
            }
        }
        if (!brace) {
            std::string toInsert = s.str();
            if (!toInsert.empty()
                || operation == AMUIParseExpressionElement<TStringType>::Operations::ISNULL
                || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTNULL
                ) {
/*
                if ((_type == AMCore::AMDataType_string || _type == AMCore::AMDataType_address || _type == AMCore::AMDataType_string_nullable || _type == AMCore::AMDataType_address_nullable)
                    //&&(operation == AMUIParseExpressionElement<TStringType>::Operations::EQUAL
                      // || operation == AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL)
                      && needStripStringValue
                       ) {
                    toInsert = toInsert.substr(1, toInsert.size() - 2);
                }
                */
                _result.push_back(
                    AMUIParseExpressionElement<TStringType>(
                        indent,
                        operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID ? AMUIParseExpressionElement<TStringType>::ComboId::INVALID : AMUIParseExpressionElement<TStringType>::comboIdsTab[0][(int)_type],
                        operation,
                        toInsert
                                                           ));
            }
        }

        if (empty && indent > 0) {
            if (c != UEOF) {
                if (canWriteError) {
                    if (c >= 0x1000000) {
                        (*errorTo)++;
                    }
                    if (c >= 0x10000) {
                        (*errorTo)++;
                    }
                    if (c >= 0x100) {
                        (*errorTo)++;
                    }
                    (*errorTo)++;
                }
            }
            std::string s;
            AMCore::AMStringPushback(s, c);
            _result.push_back(
                AMUIParseExpressionElement<TStringType>(
                    indent,
                    AMUIParseExpressionElement<TStringType>::ComboId::INVALID,
                    AMUIParseExpressionElement<TStringType>::Operations::INVALID,
                    s
                                                       ));
            return AMUIParseResult::FAIL;
        }
        if (_result.size() > 0) {
            auto rit = _result.begin() + _result.size() - 1;
            switch(rit->operation) {


                case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                    AMAssert(0 && "AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN");
                case AMUIParseExpressionElement<TStringType>::Operations::AND:
                case AMUIParseExpressionElement<TStringType>::Operations::OR:
                    rit->operation = AMUIParseExpressionElement<TStringType>::Operations::INVALID;
                    rit->comboId = AMUIParseExpressionElement<TStringType>::ComboId::INVALID;
                    rit->inputValue = rit->operation == AMUIParseExpressionElement<TStringType>::Operations::AND ? andText : orText;
                    parseResult = AMUIParseResult::FAIL;
                    if (canWriteError && *errorTo == -1) {int tg = _input.tellg(); *errorTo = (tg == EOF) ? std::numeric_limits<int>::max() : tg;};
                    break;
                case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                    break;
                case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                    break;
                case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                    AMAssert(0 && "AMUIParseExpressionElement<TStringType>::Operations::NOTHING");
                    break;
            }
        }
        return parseResult;
    }

    template<typename TStringType>
    const typename AMUIParseExpressionElement<TStringType>::ComboId AMUIParseExpressionElement<TStringType>::comboIdsTab[2][(int)AMCore::AMDataType_void + 1] = {
        {
            AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRING,//AMCore::AMDataType_string,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRING,//AMCore::AMDataType_address,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_author_tag,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_big_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_name_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_integer,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_long,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_natural,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_natural_long,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_float,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMDataType_double,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_bool,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_datetime,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_datetimeus,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_date_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVEN,//AMCore::AMDataType_time_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRINGNULL,//AMCore::AMDataType_string_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRINGNULL,//AMCore::AMDataType_address_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_author_tag_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_big_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_integer_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_long_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_natural_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_natural_long_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_float_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_double_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_bool_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_datetime_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_datetimeus_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_date_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL,//AMCore::AMDataType_time_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_enum_map,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_integer_vector,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID//AMCore::AMDataType_void, //must be last item !!!
        },
        {
            AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRING,//AMCore::AMDataType_string,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRING,//AMCore::AMDataType_address,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCOre::AMDataType_author_tag,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCOre::AMDataType_big_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCOre::AMDataType_name_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_integer,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_long,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_natural,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_natural_long,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_float,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMDataType_double,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_enum,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_bool,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_datetime,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_datetimeus,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_date_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODD,//AMCore::AMDataType_time_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRINGNULL,//AMCore::AMDataType_string_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRINGNULL,//AMCore::AMDataType_address_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_author_tag_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_big_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_name_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_integer_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_long_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_natural_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_natural_long_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_float_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_double_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_enum_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMCore::AMDataType_bool_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_datetime_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_datetimeus_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_date_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL,//AMCore::AMDataType_time_nullable,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_enum_map,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID,//AMDataType_integer_vector,
            AMUIParseExpressionElement<TStringType>::ComboId::INVALID//AMCore::AMDataType_void, //must be last item !!!
        }
    };

    template<typename TStringType>
    inline void _AMRemoveBraceClosed(std::vector<AMUIParseExpressionElement<TStringType>> &table, typename std::vector<AMUIParseExpressionElement<TStringType>>::iterator it, int indent)
    {
        while (it != table.end()) {
            if (it->indent > indent) {
                it->indent--;
            } else {
                if (it->operation == AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED) {
                    table.erase(it);
                }
                break;
            }
            it ++;
        }
    }

    template<typename TStringType>
    inline void _AMRemoveBraceOpened(std::vector<AMUIParseExpressionElement<TStringType>> &table, typename std::vector<AMUIParseExpressionElement<TStringType>>::reverse_iterator rit, int indent)
    {
        while (rit != table.rend()) {
            if (rit->indent > indent) {
                rit->indent--;
            } else {
                if (rit->operation == AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN) {
                    std::advance(rit, 1);
                    table.erase( rit.base() );
                }
                break;
            }
            rit ++;
        }
    }

    template<typename TStringType>
    void AMUIUpdateExpression(std::vector<AMUIParseExpressionElement<TStringType>> &table, AMCore::AMDataType type, int row, typename AMUIParseExpressionElement<TStringType>::Operations newState, const char *newValue)
    {
        AMAssert(row >= 0 && row < table.size());
        switch(table[row].operation) {
            case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                AMAssert(0 && "invalid operation");
                break;
            case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN: {
                switch(newState) {
                    case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        AMAssert(type == AMCore::AMDataType::AMDataType_string || type == AMCore::AMDataType::AMDataType_string_nullable
                               || type == AMCore::AMDataType::AMDataType_address || type == AMCore::AMDataType::AMDataType_address_nullable);
                    case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                    case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL: {
                        auto it = table.begin() + row;
                        it->operation = newState;
                        it->inputValue = AMUIParseDefaultValueForType<TStringType>(type);
                        int indent = it->indent;
                        it ++;
                        it = table.insert(it, AMUIParseExpressionElement<TStringType>(it->indent, AMUIParseExpressionElement<TStringType>::getOdd(it->comboId), AMUIParseExpressionElement<TStringType>::Operations::OR));
                        _AMRemoveBraceClosed(table, it, indent);
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL: {
                        AMAssert(AMCore::AMDataTypeIsNullable(type));
                        auto it = table.begin() + row;
                        it->operation = newState;
                        it->inputValue ="";
                        int indent = it->indent;
                        it ++;
                        _AMRemoveBraceClosed(table, it, indent);
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: {
                        auto it = table.begin() + row;
                        int indent = it->indent;
                        it = table.erase(it);
                        //it = table.begin() + row;
                        _AMRemoveBraceClosed(table, it, indent);
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                        break;
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                    case AMUIParseExpressionElement<TStringType>::Operations::AND:
                    case AMUIParseExpressionElement<TStringType>::Operations::OR:
                    case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        AMAssert(0 && "invalid operation");
                        break;
                    default:
                        AMAssert(0 && "invalid operation");
                        break;
                }
                break;
            }
            case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED: {
                switch(newState) {
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: {
                        auto it = table.begin() + row;
                        int indent = it->indent;
                        it = table.erase(it);
                        //it = table.begin() + row;
                        auto rit = std::make_reverse_iterator(it);
                        _AMRemoveBraceOpened(table, rit, indent);
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                        break;
                    case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                    case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                    case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                        AMAssert(0 && "invalid operation");
                        break;
                    case AMUIParseExpressionElement<TStringType>::Operations::AND:
                    case AMUIParseExpressionElement<TStringType>::Operations::OR: {
                        auto it = table.begin() + row;
                        it->operation = newState;
                        it->inputValue = "";
                        int indent = it->indent;
                        it->indent++;
                        typename AMUIParseExpressionElement<TStringType>::ComboId cid = AMUIParseExpressionElement<TStringType>::getEven(it->comboId);
                        it++;
                        it = table.insert(it, AMUIParseExpressionElement<TStringType>(indent, cid, AMUIParseExpressionElement<TStringType>::Operations::EQUAL, AMUIParseDefaultValueForType<TStringType>(
                            type
                                                                                                                                                                                                        )));
                        while (it != table.end()) {
                            it->indent++;
                            it ++;
                        }
                        table.insert(it, AMUIParseExpressionElement<TStringType>(0, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][type], AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED, ""));
                        break;
                    }
                    default:
                        AMAssert(0 && "invalid operation");
                        break;
                }
                break;
            }
            case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
            case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
            case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
            case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
            case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
            case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
            case AMUIParseExpressionElement<TStringType>::Operations::LESS:
            case AMUIParseExpressionElement<TStringType>::Operations::MORE:
            case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
            case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL: {
                switch(newState) {
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: {
                        auto it = table.begin() + row;
                        auto pit = it;
                        auto nit = it;
                        if (
                            pit != table.begin()
                            && (--pit)->indent == it->indent
                            && (pit->operation == AMUIParseExpressionElement<TStringType>::Operations::AND
                                || pit->operation == AMUIParseExpressionElement<TStringType>::Operations::OR
                            )
                            ) {
                            if (it != table.end()) {
                                it++;
                            }
                            table.erase(pit, it);
                        } else if (
                            it != table.end()
                            && (++nit) != table.end()
                            && nit->indent == it->indent
                            && (nit->operation == AMUIParseExpressionElement<TStringType>::Operations::AND
                                || nit->operation == AMUIParseExpressionElement<TStringType>::Operations::OR
                            )
                            ) {
                            if (nit != table.end()) {
                                nit++;
                            }
                            table.erase(it, nit);
                        } else {
                            auto xit = pit;
                            auto yit = nit;
                            if (
                                xit-- != table.begin()
                                && xit->indent == it->indent -1
                                && (xit->operation == AMUIParseExpressionElement<TStringType>::Operations::AND
                                    || xit->operation == AMUIParseExpressionElement<TStringType>::Operations::OR
                                )
                                ) {
                                pit = xit;
                            } else if (
                                yit != table.end()
                                && (++yit) != table.end()
                                && yit->indent == it->indent - 1
                                && (yit->operation == AMUIParseExpressionElement<TStringType>::Operations::AND
                                    || yit->operation == AMUIParseExpressionElement<TStringType>::Operations::OR
                                )
                                ) {
                                nit = yit;
                            }
                            if (nit != table.end()) {
                                nit++;
                            }
                            table.erase(pit, nit);
                        }
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN: {
                        auto it = table.begin() + row;
                        table.insert(
                            it,
                            2,
                            AMUIParseExpressionElement<TStringType>(
                                it->indent,
                                AMUIParseExpressionElement<TStringType>::comboIdsTab[0][type],
                                AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN
                                                                   )
                                    );
                        table[row + 1] = table[row + 2];
                        table[row + 1].indent++;
                        table[row + 2] = AMUIParseExpressionElement<TStringType>(
                            table[row].indent,
                            AMUIParseExpressionElement<TStringType>::comboIdsTab[1][type],
                            AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED
                                                                                );
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        AMAssert(type == AMCore::AMDataType::AMDataType_string || type == AMCore::AMDataType::AMDataType_string_nullable
                            || type == AMCore::AMDataType::AMDataType_address || type == AMCore::AMDataType::AMDataType_address_nullable);
                    case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                    case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL: {
                        AMAssert(
                            (!(!AMCore::AMDataTypeIsNullable(type)
                             && (newState == AMUIParseExpressionElement<TStringType>::Operations::ISNULL || newState == AMUIParseExpressionElement<TStringType>::Operations::NOTNULL)))
                              );
                        table[row].operation = newState;
                        if (newValue) {
                            table[row].inputValue = newValue;
                        }
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::AND:
                    case AMUIParseExpressionElement<TStringType>::Operations::OR:
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                    case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        AMAssert(0 && "invalid operation");
                        break;
                    default:
                        AMAssert(0 && "invalid operation");
                        break;
                }
                break;
            }
            case AMUIParseExpressionElement<TStringType>::Operations::AND:
            case AMUIParseExpressionElement<TStringType>::Operations::OR: {
                switch(newState) {
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: {
                        auto it = table.begin() + row;
                        auto pit = it;
                        auto nit = it;
                        if (
                            pit-- != table.begin()
                            && pit->indent == it->indent
                            && AMUIParseExpressionElement<TStringType>::isValue(pit->operation)
                            ) {
                            table.erase(pit, ++it);
                        } else if (
                            nit != table.end()
                            && (++nit) != table.end()
                            && nit->indent == it->indent
                            && AMUIParseExpressionElement<TStringType>::isValue(nit->operation)
                            ) {
                            table.erase(it, ++nit);
                        } else {
                            auto xit = pit;
                            auto yit = nit;
                            if (
                                xit-- != table.begin()
                                && xit->indent == it->indent -1
                                && AMUIParseExpressionElement<TStringType>::isValue(xit->operation)
                                ) {
                                pit = xit;
                            } else if (
                                yit != table.end()
                                && (++yit) != table.end()
                                && yit->indent == it->indent - 1
                                && AMUIParseExpressionElement<TStringType>::isValue(yit->operation)
                                ) {
                                nit = yit;
                            }
                            table.erase(pit, ++nit);
                        }
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED: {
                        auto it = table.begin() + row;
                        if (it->indent == 0) {
                            break;
                        }
                        it = table.insert(it, AMUIParseExpressionElement<TStringType>(it->indent , it->comboId, AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED, ""));
                        while (it != table.end()) {
                            it->indent--;
                            if (it->indent < 0) {
                                table.erase(it, table.end());
                                break;
                            }
                            it++;
                        }
                        break;
                    }
                    case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                    case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                    case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                    case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                    case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                    case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                    case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        AMAssert(0 && "invalid operation");
                        break;
                    case AMUIParseExpressionElement<TStringType>::Operations::AND:
                    case AMUIParseExpressionElement<TStringType>::Operations::OR: {
                        table[row].operation = newState;
                        break;
                    }
                    default:
                        AMAssert(0 && "invalid operation");
                        break;
                }
                break;
            }
            case AMUIParseExpressionElement<TStringType>::Operations::INVALID:{

                switch(newState) {
                    case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                    case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: {
                        AMUIRepairExpression(table, type);
                        break;
                    }
                    default:
                        AMAssert(0 && "invalid operation");
                        break;
                }
                break;

                break;
            }
            default:
                AMAssert(0 && "invalid operation");
                break;
        }
    }

    template<typename TStringType>
    bool AMUIIsValidExpression(std::vector<AMUIParseExpressionElement<TStringType> > &_table)
    {
        for(auto &it: _table) {
            if (it.operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID) {
                return false;
            }
        }
        return true;
    }
    template<typename TStringType>
    TStringType AMUIParseDefaultValueForType(AMCore::AMDataType type)
    {
        switch (type) {
            case AMCore::AMDataType_string: return "text";
            case AMCore::AMDataType_integer: return "0";
            case AMCore::AMDataType_long: return "0";
            case AMCore::AMDataType_natural: return "0";
            case AMCore::AMDataType_natural_long: return "0";
            case AMCore::AMDataType_float: return "0.0";
            case AMCore::AMDataType_double: return "0.0";
            case AMCore::AMDataType_datetime: return _AMGetStringWithTime(datetimeFormatText);
            case AMCore::AMDataType_datetimeus: return _AMGetStringWithTimeus(datetimeusFormatText);
            case AMCore::AMDataType_date: return _AMGetStringWithTime(dateFormatText);
            case AMCore::AMDataType_time: return _AMGetStringWithTime(timeFormatText);
            case AMCore::AMDataType_string_nullable: return "text";
            case AMCore::AMDataType_integer_nullable: return "0";
            case AMCore::AMDataType_long_nullable: return "0";
            case AMCore::AMDataType_natural_nullable: return "0";
            case AMCore::AMDataType_natural_long_nullable: return "0";
            case AMCore::AMDataType_float_nullable: return "0.0";
            case AMCore::AMDataType_double_nullable: return "0.0";
            case AMCore::AMDataType_datetime_nullable: return _AMGetStringWithTime(datetimeFormatText);
            case AMCore::AMDataType_datetimeus_nullable: return _AMGetStringWithTimeus(datetimeusFormatText);
            case AMCore::AMDataType_date_nullable: return _AMGetStringWithTime(dateFormatText);
            case AMCore::AMDataType_time_nullable: return _AMGetStringWithTime(timeFormatText);

            case AMCore::AMDataType_enum:
            case AMCore::AMDataType_bool:
            case AMCore::AMDataType_enum_nullable:
            case AMCore::AMDataType_bool_nullable:
            case AMCore::AMDataType_name:
            case AMCore::AMDataType_address:
            case AMCore::AMDataType_name_nullable:
            case AMCore::AMDataType_address_nullable:
            case AMCore::AMDataType_void:
            case AMCore::AMDataType_integer_vector:
            case AMCore::AMDataType_enum_map:
            case AMCore::AMDataType_author_tag_nullable:
            case AMCore::AMDataType_author_tag:
            default:
                return "";
        };
    }

    template<typename TStringType>
    void AMUIInsertExpression(std::vector<AMUIParseExpressionElement<TStringType>> &table, AMCore::AMDataType _type, int row) {
        AMAssert(row >= 0 && row <= table.size());
        typename std::vector<AMUIParseExpressionElement<TStringType>>::iterator it;
        if (row == table.size()) {
            typename std::vector<AMUIParseExpressionElement<TStringType>>::reverse_iterator rit = table.rbegin();
            //AMAssert(rit->indent == 0);

            if (rit != table.rend()) {
                table.insert(
                    table.end(), 2, AMUIParseExpressionElement<TStringType>(
                        rit->indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][_type]
                        , AMUIParseExpressionElement<TStringType>::Operations::OR
                                                                           ));
                rit = table.rbegin();
                rit->comboId = AMUIParseExpressionElement<TStringType>::comboIdsTab[0][_type];
                rit->operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
                rit->inputValue = AMUIParseDefaultValueForType<TStringType>(_type);
            } else {
                table.insert(
                    table.end(), 1, AMUIParseExpressionElement<TStringType>(
                        0, AMUIParseExpressionElement<TStringType>::comboIdsTab[0][_type]
                        , AMUIParseExpressionElement<TStringType>::Operations::EQUAL,
                        AMUIParseDefaultValueForType<TStringType>(_type)
                                                                           ));
            }

        } else {
            typename std::vector<AMUIParseExpressionElement<TStringType>>::iterator it = table.begin() + row;
            int indent =
                it->operation == AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED ? it->indent + 1 : it->indent;
            bool even = AMUIParseExpressionElement<TStringType>::isEven(it->comboId);
            it = table.insert(
                it, 2, AMUIParseExpressionElement<TStringType>(
                    indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][_type]
                    , AMUIParseExpressionElement<TStringType>::Operations::OR
                                                              ));
            if (!even) {
                it++;
            }
            it->comboId = AMUIParseExpressionElement<TStringType>::comboIdsTab[0][_type];
            it->operation = AMUIParseExpressionElement<TStringType>::Operations::EQUAL;
            it->inputValue = AMUIParseDefaultValueForType<TStringType>(_type);
        }
    }

    template<typename TStringType>
    void AMUIRepairExpression(std::vector<AMUIParseExpressionElement<TStringType> > &_table, AMCore::AMDataType _type)
    {
        auto it = _table.begin();
        int indent = 0;
        if (_table.size() > 0 && !AMUIParseExpressionElement<TStringType>::isValue(it->operation)) {
            AMAssert(it->operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID);
            it = _table.erase(it);
        }
        if (_table.size() < 2) {
            return;
        }
        it = _table.begin();

        while (it != _table.end()
                && (it->operation == AMUIParseExpressionElement<TStringType>::Operations::INVALID
                || it->operation == AMUIParseExpressionElement<TStringType>::Operations::NOTHING
                )
                ) {
            it = _table.erase(it);
        }
        auto nit = it;
        do {


            it = nit;
            nit = it + 1;
            it->indent = indent;
            if (nit == _table.end()) {
                break;
            }
            switch(it->operation) {
                case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                    AMAssert(_type == AMCore::AMDataType::AMDataType_string || _type == AMCore::AMDataType::AMDataType_string_nullable);
                case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                 {
                    switch(nit->operation) {
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                            if (indent == 0) {
                                _table.erase(nit);
                                nit = it;
                            }
                            indent--;
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::AND:
                        case AMUIParseExpressionElement<TStringType>::Operations::OR:
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                            indent++;
                        case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                        case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL: {
                            //it = nit;
                            _table.insert(
                                nit,
                                AMUIParseExpressionElement<TStringType>(it->indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][_type],AMUIParseExpressionElement<TStringType>::Operations::OR, "")
                                );
                            nit = it;
                            break;
                        }
                        case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                            nit = _table.erase(nit);
                            nit = it;
                            break;
                    }
                    break;
                }
                case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN: {
                    switch(nit->operation) {
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                        case AMUIParseExpressionElement<TStringType>::Operations::AND:
                        case AMUIParseExpressionElement<TStringType>::Operations::OR:
                            _table.erase(nit);
                            nit = it;
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:

                        case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                        case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                            indent++;
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                            nit = _table.erase(nit);
                            nit = it;
                            break;
                    }
                    break;
                }
                case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED: {
                    switch(nit->operation) {
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                        case AMUIParseExpressionElement<TStringType>::Operations::AND:
                        case AMUIParseExpressionElement<TStringType>::Operations::OR:
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                            _table.erase(nit);
                            nit = it;
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                        case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                            it = nit;
                            nit = _table.insert(
                                nit,
                                AMUIParseExpressionElement<TStringType>(it->indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][_type],AMUIParseExpressionElement<TStringType>::Operations::OR, "")
                                               );
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                            nit = _table.erase(nit);
                            nit = it;
                            break;
                    }
                    break;
                }
                case AMUIParseExpressionElement<TStringType>::Operations::AND:
                case AMUIParseExpressionElement<TStringType>::Operations::OR: {
                    switch(nit->operation) {
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                        case AMUIParseExpressionElement<TStringType>::Operations::AND:
                        case AMUIParseExpressionElement<TStringType>::Operations::OR:
                            _table.erase(nit);
                            nit = it;
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                        case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                        case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                        case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                        case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                        case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                        case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                            break;
                        case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                        case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                            nit = _table.erase(nit);
                            nit = it;
                            break;
                    }
                    break;
                }
                case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                    AMAssert(0 && "AMUIParseExpressionElement<TStringType>::Operations::INVALID after reapair");
                    break;
                default:
                    AMAssert(0 && "invalid operation");
                    break;
            }

        }while(nit != _table.end());
        if (_table.size() > 0) {
            auto rit = _table.begin() + _table.size() - 1;
            switch(rit->operation) {


                case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN:
                    indent --;
                    AMAssert(indent >= 0 && "AMUIParseExpressionElement<TStringType>::Operations::INVALID after reapair 4");
                case AMUIParseExpressionElement<TStringType>::Operations::AND:
                case AMUIParseExpressionElement<TStringType>::Operations::OR:
                    _table.erase(rit);
                    break;
                case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED:
                case AMUIParseExpressionElement<TStringType>::Operations::LIKE:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE:
                case AMUIParseExpressionElement<TStringType>::Operations::ISNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL:
                case AMUIParseExpressionElement<TStringType>::Operations::EQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::LESS:
                case AMUIParseExpressionElement<TStringType>::Operations::MORE:
                case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL:
                case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL:
                    break;
                case AMUIParseExpressionElement<TStringType>::Operations::INVALID:
                case AMUIParseExpressionElement<TStringType>::Operations::NOTHING:
                    AMAssert(0 && "AMUIParseExpressionElement<TStringType>::Operations::INVALID after reapair 3");
                    break;
            }
        }
        while(indent > 0) {
            indent --;
            _table.push_back(
                AMUIParseExpressionElement<TStringType>(indent, AMUIParseExpressionElement<TStringType>::comboIdsTab[1][_type],AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED, "")
                         );

        }
    }

    template<typename TStringType>
    AMUIParseResult AMUIParseAuthorTag(TStringType& input, AMCore::AMAuthorTag<TStringType>* r, char wildcard)
    {
        std::istringstream is(input);
        std::ostringstream os;
        AMUIParseResult res = AMUIParseDatetime(is, r ? &r->timeMin : (AMCore::AMDatetimeus*)nullptr, r ? &r->timeMax : (AMCore::AMDatetimeus*)nullptr, &os, datetimeFormatText, wildcard);
        unsigned int c = ' ';
        if (r) {
            r->timeState = (int)AMUIParseResult::FAIL;
            r->authorState = (int)AMUIParseResult::FAIL;
            r->time = "";
            r->author = "";
        }
        if (res == AMUIParseResult::FAIL) {
            if (os.str().size() != 0) {
                return AMUIParseResult::FAIL;
            }
        } else {
            c = AMCore::AMStreamToLowercaseStripDiaGet(is);
            if (r) {
                r->timeState = (int)res;
            }
        }

        std::string author;

        unsigned int ccc = AMCore::AMStreamToLowercaseStripDiaGet(is);
        AMCore::AMStreamToLowercaseStripDiaUnget(is);
        std::getline(is, author);
        author = AMCore::AMStringStripSpaces(author);
        if (author.size() <= 1) {
            if (res == AMUIParseResult::FAIL) {
                return AMUIParseResult::FAIL;
            }
            if (r) {
                r->author = TStringType(author);
                r->time = TStringType(os.str());
                //r->authorState = (int)AMUIParseResult::PARTIAL;
            }
            return AMUIParseResult::PARTIAL;
        }
        if (c != ' ') {
            return AMUIParseResult::FAIL;
        }
        TStringType  fmt("%l .-");
        if (wildcard != '\0') {
            fmt+="%%";
            fmt.push_back(wildcard);
        }
        if (!AMCore::AMStringContainsOnlyCharacters(fmt, author)) {
            return AMUIParseResult::FAIL;
        }
        if (r) {
            r->author = TStringType(author);
            r->time = TStringType(os.str());
            r->authorState = (int)AMUIParseResult::SUCCESS;
        }
        if (res != AMUIParseResult::SUCCESS) {
            return AMUIParseResult::PARTIAL;
        }
        return AMUIParseResult::SUCCESS;
    }

    template<typename TEnumType, typename TStringType>
    TStringType AMUISplitStringIntoEnumvalAndId(TStringType input, TEnumType *enumResult)
    {
        if (enumResult) {
            *enumResult = AMUI::DEFAULT_INT_ID;
        }
        typename TStringType::reverse_iterator rit = input.rbegin();
        bool bexit = false;
        bool numParsed = false;

        switch (*rit) {
            case '(':
                rit++;
                bexit = true;
                break;
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
                numParsed = true;
                break;
            case ')':
                rit++;
                break;
            default:
                bexit = true;
        }
        if (!bexit) {
            typename TStringType::reverse_iterator ritn = rit;
            while (std::isdigit(*ritn)) {
                ritn ++;
            }
            if (ritn != rit) {
                TStringType s((ritn).base(), (rit).base());
                unsigned long value = std::stoul(s);
                if (*ritn == '(') {
                    rit = ritn;
                    rit++;
                    if (enumResult) {
                        *enumResult = TEnumType(value);
                    }
                }
            } else {
                rit = input.rbegin();
            }
        }

        while (isspace(*rit)) {
            rit++;
        }
        return TStringType(input.begin(), rit.base());
    }

    /*
    template<typename TEnumType, typename TStringType>
    TStringType AMUISplitStringIntoEnumvalAndIdStripWildcardMulti(TStringType input, TEnumType *enumResult, char wildcardMulti)
    {
        std::string s = AMUISplitStringIntoEnumvalAndId(input, enumResult);
        std::string::size_type pos = s.find(wildcardMulti);
        if (pos == std::string::npos) {
            return s;
        }
        bool found = true;
        for(int i = 0; i < pos; i++) {
            if (!std::isspace(s[i])) {
                found = false;
                break;
            }
        }
        if (found) {
            s = s.substr(pos);
        }
        found = true;
        pos = s.rfind(wildcardMulti);
        if (pos == std::string::npos) {
            return s;
        }
        for(int i = pos; i < s.length(); i++) {
            if (!std::isspace(s[i])) {
                found = false;
                break;
            }
        }
        if (found) {
            s = s.substr(0, pos);
        }
        for(int i = 0; i < s.length() - 2; i++) {
            if (std::isspace(s[i]) && s[i + 1] == wildcardMulti && isspace(s[i + 2])) {
                s.erase(i+1, 2);
            }
        }
        return s;
    }*/

}

#endif //AMCore_AMPARSERSIMPL_HPP
