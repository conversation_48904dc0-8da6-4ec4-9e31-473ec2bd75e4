//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogExpressionView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogExpression.h"
#include "amdialogs/AMUIFormElementButton.h"
#include "amdialogs/AMUIFormElementInteger.h"
#include "amdialogs/AMUIFormElementNatural.h"
#include "amdialogs/AMUIFormElementDatetime.h"
#include "amdialogs/AMUIFormElementString.h"
#include "amdialogs/AMUIFormElementSelectBox.h"


namespace AMDialogs {

    template<typename TStringType>
    AMUIDialogExpressionView<TStringType>::AMUIDialogExpressionView()
        : AMUIDialogView()
    {

    }

    template<typename TStringType>
    void AMUIDialogExpressionView<TStringType>::render()
    {
        if (renderBegin()) {

            bool grayed = renderNullable();

            ImGuiContext& g = *GImGui;
            ImGuiStyle& style = g.Style;

            float total_width = ImGui::GetWindowWidth() - 2 * style.WindowPadding.x;


            AMUIDialogExpression<TStringType> *dlg = static_cast<AMUIDialogExpression<TStringType> *>(m_dialog);
            for(int i = 0, j = 0; i < dlg->elements().size() - 1; i += 3, j++) {
                auto elb = static_cast<AMDialogs::AMUIFormElementButton *>(dlg->elements()[i]);
                elb->setSizeX(ImGui::GetFrameHeight());
                dlg->elements()[i]->render(false, i);
                ImVec2 pos = ImGui::GetCursorPos();
                ImGui::SetCursorPos(ImVec2(pos.x + dlg->tabs()[j] * 1.5 * ImGui::GetTextLineHeightWithSpacing() + ImGui::GetFrameHeight() + style.ItemInnerSpacing.x, pos.y - ImGui::GetFrameHeight() / 2));
                dlg->elements()[i+1]->render(false, i + 1);
                if (dlg->isValues()[j]) {
                    ImGui::SameLine();
                    dlg->elements()[i + 2]->setSizeX(total_width - ImGui::GetCursorPosX() + pos.x);
                    dlg->elements()[i + 2]->render(false, i + 2);
                }
                ImGui::SetCursorPos(pos);
            }
            auto elb = static_cast<AMDialogs::AMUIFormElementButton *>(dlg->elements()[dlg->elements().size() -1]);
            elb->setSizeX(ImGui::GetFrameHeight());
            elb->render(false, dlg->elements().size() -1);


            for(int i = 0; i < dlg->elements().size(); i++) {
                if (dlg->elements()[i]->needUpdateState()) {
                    int row = i / 3;
                    dlg->setCausedByLine(row);
                    if (i % 3 == 0) {
                        dlg->setCausedByButton(true);
                        dlg->setCausedOperation(AMUIParseExpressionElement<TStringType>::Operations::NOTHING);
                        dlg->setCausedString(nullptr);
                    } else {
                        dlg->setCausedByButton(false);
                        int selNdx = row * 3 + 1;
                        auto sel = static_cast<AMUIFormElementSelectBox *>(dlg->elements()[selNdx]);
                        dlg->setCausedOperation(dlg->backtable()[row][sel->value()]);
                        if (dlg->isValues()[row]) {
                            int inpNdx = row * 3 + 2;
                            switch (dlg->type()) {
                                case AMCore::AMDataType_integer_nullable:
                                case AMCore::AMDataType_integer: {
                                    auto el = static_cast<AMUIFormElementInteger *>(dlg->elements()[inpNdx]);
                                    dlg->setCausedString(el->buffer());
                                    break;
                                }
                                case AMCore::AMDataType_natural_nullable:
                                case AMCore::AMDataType_natural: {
                                    auto el = static_cast<AMUIFormElementNatural *>(dlg->elements()[inpNdx]);
                                    dlg->setCausedString(el->buffer());
                                    break;
                                }
                                case AMCore::AMDataType_address_nullable:
                                case AMCore::AMDataType_string_nullable:
                                case AMCore::AMDataType_address:
                                case AMCore::AMDataType_string: {
                                    auto el = static_cast<AMUIFormElementString *>(dlg->elements()[inpNdx]);
                                    dlg->setCausedString(el->buffer());
                                    break;
                                }
                                case AMCore::AMDataType_time_nullable:
                                case AMCore::AMDataType_date_nullable:
                                case AMCore::AMDataType_datetimeus_nullable:
                                case AMCore::AMDataType_datetime_nullable:
                                case AMCore::AMDataType_time:
                                case AMCore::AMDataType_date:
                                case AMCore::AMDataType_datetimeus:
                                case AMCore::AMDataType_datetime: {
                                    auto el = static_cast<AMUIFormElementDatetime *>(dlg->elements()[inpNdx]);
                                    dlg->setCausedString(el->buffer());
                                    break;
                                }
                                default:
                                    dlg->setCausedString(nullptr);
                                    break;
                            }
                        } else {
                            dlg->setCausedString(nullptr);
                        }

                    }
                }
            }

            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }

    template<typename TStringType>
    bool AMUIDialogExpressionView<TStringType>::renderNullable()
    {
        return false;
    }
} // AMDialogs