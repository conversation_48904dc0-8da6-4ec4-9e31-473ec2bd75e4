//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//
#define IMGUI_DEFINE_MATH_OPERATORS
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

#include "amdialogs/AMUIForm.h"

#include "amcore/AMAssert.h"
#include <sstream>
#include <tuple>
#include <chrono>
#include <memory>
#include "amui/AMUIController.h"
#include "amui/AMUIApp.h"
#include "amui/AMUIConfig.h"

namespace AMDialogs {



    AMUIForm::AMUIForm()
        : m_controller(nullptr),
          m_endpoint(),
          m_idElement(nullptr),
          m_isNew(false),
          m_modified(false),
          m_requests(),
          m_timeout(5000),
          m_loadingId(AMUI::DEFAULT_INT_ID),
          m_grayed(false),
          m_destroyed(true),
          m_caller(this),
          m_forceGrayed(false),
          m_forceRefreshAfterLoad(false)
    {

    }

    void AMUIForm::init(AMUI::AMUIController *controller, std::string endpoint) {
        m_controller = controller;
        m_endpoint = endpoint;
        AMAssert(m_controller);
        AMUI::AMUIApp::instance().addPerformCallback(this, &AMUIForm::perform);
        m_idElement = nullptr;
        m_destroyed = false;
    }

    AMUIForm::~AMUIForm() {
        AMUI::AMUIApp::instance().removePerformCallback(this);
    }

    std::string AMUIForm::recordTitle() {
        return std::string();
    }

    void AMUIForm::perform() {
        if (m_destroyed) {
            acceptRecords();
            return;
        }
        int secs = sections();
        bool uschange = false;
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                if (el->needUpdateState()) {
                    uschange = true;
                    break;
                }
            }
        }
        if (uschange) {
            onUpdateState();
        }
        bool change = false;
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                if (el->needUpdateState()) {
                    el->updateState();
                    if (el->modified()) {
                        m_modified = true;
                        change = true;
                    }
                }
            }
        }
        if (change) {
            onChange();
        }
        acceptRecords();
    }

    void AMUIForm::acceptRecords() {
        std::chrono::time_point<std::chrono::steady_clock> now = std::chrono::steady_clock::now();
        for (std::set<_AMUIFormRequest>::iterator it = m_requests.begin(); it != m_requests.end();) {
            if (it->future.wait_for(std::chrono::seconds(0)) == AMUI::AMFutureStatus::ready) {
                AMLog::AMLogger::Log(AMLog::ELogMode::Info, "FORM: loaded data");
                int id = -1;
                if (it->mode == ERowUpdateMode::DELETE) {
                    id = (m_idElement ? m_idElement->value() : AMUI::DEFAULT_INT_ID);
                }
                bool rv = false;
                if (!m_destroyed) {
                    rv = (it->callback)(std::move(it->future.get()));
                }
                if (it->mode != ERowUpdateMode::DELETE) {
                    id = (m_idElement ? m_idElement->value() : AMUI::DEFAULT_INT_ID);
                }
                if (rv) {
                    clearModified();

                    if (it->mode != ERowUpdateMode::INVALID_OPERATION) {
                        m_controller->setHistory(id, it->mode, m_caller);
                        m_controller->highlightMainRow(id, m_caller);//TODO zkontrolovat highlihtmainrow
                        m_controller->refresh(false);
                    } else {
                        if (m_forceRefreshAfterLoad) {
                            m_forceRefreshAfterLoad = false;
                            m_controller->highlightMainRow(id, m_caller);//TODO zkontrolovat highlihtmainrow
                        }
                    }
                    m_controller->setAppUpdateStatusFlag();
                }

                if (it->customCallback) {
                    it->customCallback();
                }
                m_requests.extract(it++).value();
            } else {
                if (it->deadline < now) {
                    AMLog::AMLogger::Log(AMLog::Error, "FORM: timeout");
                    it = m_requests.erase(it);
                } else {
                    AMLog::AMLogger::Log(AMLog::Debug, "FORM: still running");
                    it++;
                }
            }
        }
    }

    void AMUIForm::reset() {
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->reset();
            }
        }
        m_loadingId = AMUI::DEFAULT_INT_ID;
    }

    void AMUIForm::loadDefaults()
    {
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->loadDefault();
            }
        }
    }

    void AMUIForm::clearModified() {
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->clearModified();
            }
        }
        m_modified = false;
    }

    bool AMUIForm::grayed() {
        return m_grayed || !m_idElement || m_idElement->value() == AMUI::DEFAULT_INT_ID;
    }

#define saveToPostEveryItem \
    if (!all && !el->modified()) {break;}  \
    if (first) {first = false;} else {os<<'&';}

    std::string AMUIForm::saveToPost(bool all) {
        std::ostringstream os;
        int secs = sections();
        bool first = true;
        if (m_idElement && !m_idElement->modified() && !all) {
            os << m_idElement->key() << '=' << m_idElement->value();
            first = false;
        }
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                std::string key = el->key();
                if (key.empty()) {
                    continue;
                }
                if (!all && !el->modified()) {
                    continue;
                }
                if (!canSendKey(el->key(), all)) {
                    continue;
                }
                if (first) {
                    first = false;
                } else {
                    os<<'&';
                }
                std::map<int, std::string> *addKeys = additionalKeys(key);
                if (!addKeys) {
                    os << key << '=' << el->valueAsString();
                } else {
                    os << el->valueAsString(addKeys);
                }
            }
        }
        return os.str();
    }


    void AMUIForm::onInsert() {
        if (!m_idElement) {
            return;
        }
        reset();
        m_idElement->setValue(AMUI::AMUIApp::instance().nextDbId());
        m_idElement->wasModified();
        m_isNew = true;
        loadDefaults();
    }

    bool AMUIForm::canInsert() const {
        return true;
    }

    void AMUIForm::onSave(std::function<void()> onDone) {
        if (m_isNew) {
            std::string post;
            bool important;
            std::string url = insertRecordUrl(important, post);
            _AMUIFormRequest r(
                std::chrono::steady_clock::now() + std::chrono::milliseconds(m_timeout),
                AMUI::AMAsync(
                    AMUI::AMLaunch::async,
                    &AMUIForm::getData,
                    &AMUIForm::isDataAvail,
                    &AMUIForm::putData,
                    this,
                    url,
                    important,
                    post,
                    ""
                    ),
                std::bind(&AMUIForm::acceptInserted, this, std::placeholders::_1),
                ERowUpdateMode::INSERT,
                onDone
                );
            m_requests.insert(std::move(r));
        } else {
            std::string post;
            bool important;
            std::string url = updateRecordUrl(important, post);
            _AMUIFormRequest r(
                std::chrono::steady_clock::now() + std::chrono::milliseconds(m_timeout),
                AMUI::AMAsync(
                    AMUI::AMLaunch::async,
                    &AMUIForm::getData,
                    &AMUIForm::isDataAvail,
                    &AMUIForm::putData,
                    this,
                    url,
                    important,
                    post,
                    ""
                    ),
                std::bind(&AMUIForm::acceptUpdated, this, std::placeholders::_1),
                ERowUpdateMode::UPDATE,
                onDone
                );
            m_requests.insert(std::move(r));
        };
    }

    bool AMUIForm::canSave() const {
        if (!m_grayed && m_idElement && m_idElement->value() != AMUI::DEFAULT_INT_ID && m_modified) {
            return true;
        }
        return false;
    }

    void AMUIForm::onDelete(std::function<void()> onDone) {
        std::string post;
        bool important;
        std::string url = deleteRecordUrl(important, post);
        _AMUIFormRequest r(
            std::chrono::steady_clock::now() + std::chrono::milliseconds(m_timeout),
            AMUI::AMAsync(
                AMUI::AMLaunch::async,
                &AMUIForm::getData,
                &AMUIForm::isDataAvail,
                &AMUIForm::putData,
                this,
                url,
                important,
                post,
                "DELETE"
                ),
            std::bind(&AMUIForm::acceptDeleted, this, std::placeholders::_1),
            ERowUpdateMode::DELETE,
            onDone
            );
        m_requests.insert(std::move(r));
    }

    bool AMUIForm::canDelete() const {
        return !m_grayed && m_idElement && m_idElement->value() != AMUI::DEFAULT_INT_ID;
    }

    void AMUIForm::onCopy() {
        if (!m_idElement) {
            return;
        }
        m_idElement->setValue(AMUI::AMUIApp::instance().nextDbId());
        m_idElement->wasModified();
        m_isNew = true;
    }

    bool AMUIForm::canCopy() const {
        return m_idElement && m_idElement->value() != AMUI::DEFAULT_INT_ID;
    }

    void AMUIForm::onLoad(int id, std::function<void()> onDone) {
        std::string post;
        bool important;
        std::string url = loadRecordUrl(id, important, post);
        _AMUIFormRequest r(
            std::chrono::steady_clock::now() + std::chrono::milliseconds(m_timeout),
            AMUI::AMAsync(
                AMUI::AMLaunch::async,
                &AMUIForm::getData,
                &AMUIForm::isDataAvail,
                &AMUIForm::putData,
                this,
                url,
                important,
                post,
                ""
                ),
            std::bind(&AMUIForm::acceptLoaded, this, std::placeholders::_1),
            ERowUpdateMode::INVALID_OPERATION,
            onDone
            );
        m_requests.insert(std::move(r));
        m_loadingId = id;
    }

    std::string AMUIForm::insertRecordUrl(bool &important, std::string &post)
    {
        AMAssert(m_idElement->value() < 0);
        return "";
    }

    std::string AMUIForm::updateRecordUrl(bool &important, std::string &post)
    {
        AMAssert(m_idElement->value() < 0);
        return "";
    }

    std::string AMUIForm::loadRecordUrl(int id, bool &important, std::string &post)
    {
        AMAssert(m_idElement->value() < 0);
        return "";
    }

    std::string AMUIForm::deleteRecordUrl(bool &important, std::string &post)
    {
        AMAssert(m_idElement->value() < 0);
        return "";
    }

    bool AMUIForm::acceptInserted(std::tuple<std::unique_ptr<char>, int>)
    {
        //m_isNew = false;
        m_loadingId = (m_idElement ? m_idElement->value() : AMUI::DEFAULT_INT_ID);
        return false;
    }

    bool AMUIForm::acceptUpdated(std::tuple<std::unique_ptr<char>, int>)
    {
        m_loadingId = (m_idElement ? m_idElement->value() : AMUI::DEFAULT_INT_ID);
        return false;
    }

    bool AMUIForm::acceptLoaded(std::tuple<std::unique_ptr<char>, int>)
    {
        reset();
        return false;
    }

    bool AMUIForm::acceptDeleted(std::tuple<std::unique_ptr<char>, int>)
    {
        return false;
    }

    bool AMUIForm::modified() const {
        return m_modified;
    }

    std::tuple<std::unique_ptr<char>, int> AMUIForm::getData(void *mem) {
        return std::make_tuple(std::unique_ptr<char>(nullptr), 0);
    }

    void *AMUIForm::putData(std::string url, bool important, std::string post, std::string method) {
        return nullptr;
    }

    bool AMUIForm::isDataAvail(void *) {
        return false;
    }

    int AMUIForm::loadingId() {
        return m_loadingId;
    }

    void AMUIForm::setGrayed(bool grayed)
    {
        m_grayed = grayed || m_forceGrayed;
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->setFormGrayed(m_grayed);
            }
        }
    }

    void AMUIForm::setForceGrayed(bool grayed)
    {
        m_forceGrayed = grayed;
        AMUIForm::setGrayed(m_forceGrayed);
    }

    std::map<int, std::string> *AMUIForm::additionalKeys(std::string key)
    {
        return nullptr;
    }

    void AMUIForm::onChange()
    {
    }

    void AMUIForm::onUpdateState()
    {
    }

    bool AMUIForm::canSendKey(std::string key, bool all)
    {
        return true;
    }

    bool AMUIForm::isNew()
    {
        return m_isNew;
    }

    bool AMUIForm::canDestroyFinish()
    {
        return m_requests.empty();
    }

    void AMUIForm::destroy()
    {
        m_destroyed = true;
        //reset();
        AMUI::AMUIGarbageManager::instance().template deleteObject<AMUIForm>(this);
    }

    void AMUIForm::setFlashbackTimepoint(std::string flashbackTimepoint)
    {
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->setFlashbackTimepoint(flashbackTimepoint);
            }
        }
    }

    int AMUIForm::recordId()
    {
        return m_idElement ? m_idElement->value() : AMUI::DEFAULT_INT_ID;
    }

    void AMUIForm::setIsNew(bool isNew)
    {
        m_isNew = isNew;
    }

    void AMUIForm::setCaller(AMUIForm *caller)
    {
        m_caller = caller;
    }

    void AMUIForm::onRefresh(int id, std::function<void()> onDone)
    {
        if (!m_modified) {
            onLoad(id, onDone);
        }
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                el->refresh();
            }
        }
    }

    void AMUIForm::onRenderStart()
    {
        //ImGui::SetCursorPosY(ImGui::GetCursorPosY()/* + 5.0f*/);
    }

    void AMUIForm::onRenderEnd()
    {
        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 5.0f);
    }

    void AMUIForm::printInfoLine(const char *msg, GLuint icon)
    {
        ImGuiContext& g = *ImGui::GetCurrentContext();
        ImGuiWindow* window = g.CurrentWindow;
        ImVec2 pos = ImGui::GetCursorPos();
        AMUI::AMUIRect<float> r = AMUI::AMUIRect<float>(0.0f, 0.0f, 24.0f, 24.0f);
        auto posw = window->Rect().Min;
        window->DrawList->AddImage(
            (ImTextureID)(long)icon,
            posw + pos + ImVec2(r.left, r.top) + g.Style.FramePadding,
            posw + pos + ImVec2(r.right, r.bottom) + g.Style.FramePadding
                                  );
        ImGui::SetCursorPos(ImVec2(pos.x + r.right + g.Style.FramePadding.y + 1.0f, pos.y + g.Style.FramePadding.y + 5.0f));
        ImGui::TextWrapped("%s", msg);
        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 10.0f);
    }

    AMUIFormElement *AMUIForm::findElement(std::string key)
    {
        int secs = sections();
        for (int i = 0; i < secs; i++) {
            std::vector<AMUIFormElement *> &elems = elementsForSection(i);
            for (AMUIFormElement *el: elems) {
                if (el->key() == key) {
                    return el;
                }
            }
        }
        return nullptr;
    }

    _AMUIFormRequest::_AMUIFormRequest(_AMUIFormRequest &&right)
        : deadline(right.deadline),
          future(std::move(right.future)),
          callback(right.callback),
          mode(right.mode),
          customCallback(right.customCallback){
    }

    bool _AMUIFormRequest::operator<(const _AMUIFormRequest &right) const {
        return deadline < right.deadline;
    }

    _AMUIFormRequest::~_AMUIFormRequest() {

    }

    _AMUIFormRequest::_AMUIFormRequest(
        std::chrono::time_point<std::chrono::steady_clock> _deadline,
        AMUI::AMFuture <std::tuple<std::unique_ptr<char>, int>> &&_future,
        std::function<bool(std::tuple<std::unique_ptr<char>, int>)> _callback,
        ERowUpdateMode _mode,
        std::function<void()> _customCallback
    )
        : deadline(_deadline),
          future(std::move(_future)),
          callback(_callback),
          mode(_mode),
          customCallback(_customCallback)
    {

    }
}