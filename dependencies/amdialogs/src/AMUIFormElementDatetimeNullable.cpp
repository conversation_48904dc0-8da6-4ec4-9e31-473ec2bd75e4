//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//
//TODO sloučit s AMUIFormElementDatetime
#include "../AMUIFormElementDatetimeNullable.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"

namespace AMDialogs {

    const int FORM_ELEMENT_DATETIME_BUFFER_SIZE = 48;

    AMUIFormElementDatetimeNullable::AMUIFormElementDatetimeNullable(const std::string key)
        : AMUIFormElementDatetime(key),
          m_isNotNull(false),
          m_nullType(AMCore::AMDataType_datetime_nullable)
    {
        m_type = AMCore::AMDataType_datetime_nullable;
    }

    void AMUIFormElementDatetimeNullable::init(AMCore::AMDataType type) {
        switch(type) {
            case AMCore::AMDataType_datetime_nullable: m_nullType = type; m_type = AMCore::AMDataType_datetime; m_format = AMDialogs::datetimeFormatText;break;
            case AMCore::AMDataType_date_nullable: m_nullType = type; m_type = AMCore::AMDataType_date; m_format = AMDialogs::dateFormatText;break;
            case AMCore::AMDataType_time_nullable: m_nullType = type; m_type = AMCore::AMDataType_time; m_format = AMDialogs::timeFormatText;break;
            case AMCore::AMDataType_datetimeus_nullable: m_nullType = type; m_type = AMCore::AMDataType_datetimeus; m_format = AMDialogs::datetimeusFormatText;break;
            default:
                break;
        }
        AMUIFormElementDatetime::init(m_type);
    }

    AMUIFormElementDatetimeNullable::~AMUIFormElementDatetimeNullable()
    {
    }

    void AMUIFormElementDatetimeNullable::reset() {
        AMUIFormElementDatetime::reset();
        m_isNotNull = false;
    }

    void AMUIFormElementDatetimeNullable::setValue(AMCore::AMNullable<std::tm> &value)
    {
        AMAssert(m_buffer);
        if (value.isNull()) {
            m_buffer[0] = '\0';
        } else {
            m_valueMin = value.get();
            m_valueMax = value.get();
            strftime(m_buffer, FORM_ELEMENT_DATETIME_BUFFER_SIZE, m_format, &m_valueMin);
        }
        updateState();
    }

    void AMUIFormElementDatetimeNullable::setValue(AMCore::AMNullable<AMCore::AMDatetimeus> &value)
    {
        AMAssert(m_buffer);
        if (value.isNull()) {
            m_buffer[0] = '\0';
        } else {
            m_valueMin = value.get();
            m_valueMax = value.get();
            std::string tdms = AMDialogs::AMUISynthesizeDatetime<std::string>(m_format, &m_valueMin, &m_valueMax, m_wildcard);
            ::strncpy(m_buffer, tdms.c_str(), m_length);
            m_buffer[m_length - 1] = '\0';
        }
        updateState();
    }

    std::string AMUIFormElementDatetimeNullable::valueAsString()
    {
        if (m_buffer[0] == '\0') {
            return "null";
        }
        return AMUIFormElementDatetime::valueAsString();
    }

    AMCore::AMNullable<std::tm> AMUIFormElementDatetimeNullable::valueNullable() const
    {
        if (m_buffer[0] == '\0') {
            return AMCore::AMNullable<std::tm>();
        }
        return AMCore::AMNullable<std::tm>(m_valueMin);
    }

    AMCore::AMNullable<AMCore::AMDatetimeus> AMUIFormElementDatetimeNullable::valueusNullable() const
    {
        if (m_buffer[0] == '\0') {
            return AMCore::AMNullable<AMCore::AMDatetimeus>();
        }
        return AMCore::AMNullable<AMCore::AMDatetimeus>(m_valueMin);
    }

    void AMUIFormElementDatetimeNullable::setValue(const char *buffer)
    {
        if (strcmp(m_buffer, "null") == 0) {
            m_buffer[0] = '\0';
        } else {
            ::strncpy(m_buffer, buffer, m_length);
            m_buffer[m_length - 1] = '\0';
        }
        AMUIFormElementDatetime::updateState();
    }

    void AMUIFormElementDatetimeNullable::updateState()
    {
        AMUIFormElementDatetime::updateState();
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::EMPTY;
        }
    }
}