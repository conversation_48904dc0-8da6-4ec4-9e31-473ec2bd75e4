//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormView.h"
#include "../AMUIForm.h"
#include "amui/AMUIConfig.h"
#include "../AMUIFormConfig.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {

    struct _AMUIFormViewColumn {
        float yOffset;
    };

    static const ImGuiTableFlags FLAGS = /*ImGuiTableFlags_BordersOuter | ImGuiTableFlags_BordersV*/0;
    static const int COLUMNS = 4;
    static const ImU32 TITLE_COLOR = 0xFF000000;
    static const ImU32 TITLE_MODIFIED_COLOR = 0xFFFF5050;

    AMUIFormView::AMUIFormView()
        : m_form(nullptr) {

    }

    void AMUIFormView::render() {

        m_form->onRenderStart();

        _AMUIFormViewColumn columns[COLUMNS];
        float wMax = ImGui::GetContentRegionAvail().x;
        int sections = m_form->sections();
        int ccolumns = (int) (wMax / AMUI::MIN_AGENDA_DETAIL_WIDTH);
        if (ccolumns > COLUMNS) {
            ccolumns = COLUMNS;
        }
        if (ccolumns > sections) {
            ccolumns = sections;
        }
        if (ccolumns < 1) {
            ccolumns = 1;
        }
        wMax /= ccolumns;
        wMax -= ImGui::GetStyle().DisplayWindowPadding.x / 2.0f;
        for (int i = 0; i < ccolumns; i++) {
            columns[i].yOffset = ImGui::GetCursorPosY();
        }
        float posX = ImGui::GetCursorPosX();

        // When using ScrollX or ScrollY we need to specify a size for our table container!
        // Otherwise by default the table will fit all available space, like a BeginChild() call.
        ImVec2 outer_size = ImVec2(wMax, 0.0f/*TEXT_BASE_HEIGHT * 8*/);
        float maxYOffset = 0.0f;
        for (int sec = 0; sec < sections; sec++) {
            char tableName[32];
            snprintf(tableName, sizeof(tableName) - 1, "##form_table_%i", sec);
            int cIndex = (sec) % ccolumns;
            ImGui::SetCursorPos(
                ImVec2(
                    posX + cIndex * (wMax + ImGui::GetStyle().DisplayWindowPadding.x / 2.0f), columns[cIndex].yOffset
                      ));
            ImGui::BeginTable(tableName, 2, FLAGS, outer_size);
            ImGuiContext& g = *GImGui;
            ImGuiTable* table = g.CurrentTable;
            if (!table) {
                //ImGui::EndTable();
                //m_form->onRenderEnd();
                continue;
            }
            ImGui::TableSetupColumn("Vlastnost", ImGuiTableColumnFlags_WidthFixed, 220.0f);
            ImGui::TableSetupColumn("Hodnota", ImGuiTableColumnFlags_WidthStretch);
            //ImGui::TableNextRow(ImGuiTableRowFlags_Headers);

            std::vector<AMUIFormElement *> &elements = m_form->elementsForSection(sec);

            int element_it = 0;
            for (AMUIFormElement *el: elements) {
                if (el->hidden()) {
                    continue;
                }
                if (
                    dynamic_cast<AMUIFormElementNameHidden<int, AMCore::AMStringCZ> *>(el) != nullptr //TODO dodělat hidden do bázzové třídy
                    ) {
                    continue;
                }
                ImGui::TableNextRow();
                ImGui::TableSetColumnIndex(0);
                ImGui::PushStyleColor(ImGuiCol_Text, el->modified() ? TITLE_MODIFIED_COLOR : TITLE_COLOR);
                float title_size = ImGui::CalcTextSize(el->label().c_str()).x;
                float cellWidth = ImGui::GetColumnWidth();
                if (cellWidth > title_size) {
                    ImGui::SetCursorPosX(ImGui::GetCursorPosX() + cellWidth - title_size);
                }
                float posy = ImGui::GetCursorPosY();
                ImGui::SetCursorPosY(posy + 5);
                ImGui::Text("%s", el->label().c_str());
                ImGui::SetCursorPosY(posy);
                ImGui::PopStyleColor();
                ImGui::TableSetColumnIndex(1);
                ImGui::PushItemWidth(-FLT_MIN);
                char elemName[32];
                snprintf(elemName, sizeof(elemName) - 1, "##el_%i_%i", sec, element_it);
                el->render(m_form->grayed(), element_it);
                ImGui::PopItemWidth();
                element_it++;
            }
            ImGui::EndTable();
            columns[cIndex].yOffset = ImGui::GetCursorPosY();
            if (columns[cIndex].yOffset > maxYOffset) {
                maxYOffset = columns[cIndex].yOffset;
            }

            m_form->onRenderEnd();
        }
        ImGui::SetCursorPosY(maxYOffset);
    }

    void AMUIFormView::init(AMUIForm *form) {
        m_form = form;
    }

}

