////
//// Created by <PERSON><PERSON><PERSON> on 9.2.23.
////
//
//#include "amdialogs/AMUIDialogEnumMapLineView.h"
//#include "imgui/imgui.h"
//#include "amdialogs/AMUIDialogEnumMapLine.h"
//
//namespace AMDialogs {
//
//    template<typename TEnumType, typename TStringType>
//    AMUIDialogEnumMapLineView<TEnumType, TStringType>::AMUIDialogEnumMapLineView()
//        : AMUIDialogView()
//    {
//
//    }
//
//    template<typename TEnumType, typename TStringType>
//    void AMUIDialogEnumMapLineView<TEnumType, TStringType>::render()
//    {
//        if (renderBegin()) {
//
//            AMUIDialogEnumMapLine<TEnumType, TStringType> *dlg = static_cast<AMUIDialogEnumMapLine<TEnumType, TStringType> *>(m_dialog);
//            bool b_any_old_value = dlg->any();
//            dlg->elementAny().render(false, dlg->id() * 1000 - 1);
//            if (b_any_old_value != dlg->elementAny().value()) {
//                dlg->setAny(dlg->elementAny().value());
//            }
//            for(auto &el: dlg->elementsStorCommon()) {
//                bool oldValue = el.element.value();
//                el.element.render(false, dlg->id() * 1000);
//                if (el.element.value() != oldValue) {
//                    if (el.element.value()) {
//                        dlg->selectedCommon()->insert({el.value, dlg->selectedCommon()->size()});
//                    } else {
//                        auto itd = dlg->selectedCommon()->find(el.value);
//                        int pos = itd->second;
//                        dlg->selectedCommon()->erase(itd);
//                        for(auto &it: *dlg->selectedCommon()) {
//                            if (it.second > pos) {
//                                it.second--;
//                            }
//                        }
//                    }
//                }
//            }
//            for(auto &el: dlg->elementsStorExtended()) {
//                el.elementRemoveButton.render(false, dlg->id() * 1601);
//                el.elementEnumMap.render(false, dlg->id() * 1500);
//            }
//
//            renderEnd(true);
//        } else {
//            renderEnd(false);
//        }
//    }
//} // AMDialogs