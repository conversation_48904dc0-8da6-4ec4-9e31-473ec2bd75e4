////
//// Created by <PERSON><PERSON><PERSON> on 9.2.23.
////
//
//#include "amdialogs/AMUIDialogEnumMapLine.h"
//#include "amdialogs/AMUIDialogEnumMapLineView.h"
//
//namespace AMDialogs {
//
//    template<typename TEnumType, typename TStringType>
//    void AMUIDialogEnumMapLine<TEnumType, TStringType>::init(
//        std::string name,
//        std::function<void(AMUIDialog *)> onClose,
//        std::function<void(AMUIDialog *)> onChange,
//        int enumNo,
//        std::string strVoidParam,
//        std::map<TEnumType, int> *selectedCommon,
//        std::map<int, std::map<TEnumType, int> > *selectedExtended
//
//        )
//    {
//        m_strVoidParam = strVoidParam;
//        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
//        auto *v = new AMUIDialogEnumMapLineView<TEnumType, TStringType>();
//        v->init(this);
//        m_view = v;
//        m_enumNo = enumNo;
//        m_selectedCommon = selectedCommon;
//        m_selectedExtended = selectedExtended;
//        m_table = table();
//    }
//
//    template<typename TEnumType, typename TStringType>
//    AMUIDialogEnumMapLine<TEnumType, TStringType>::AMUIDialogEnumMapLine()
//        : AMUIDialog(),
//        m_enumNo(-1),
//        m_selectedCommon(nullptr),
//        m_selectedExtended(nullptr),
//        m_table(),
//        m_valueAny(false),
//        m_elementAny(""),
//        m_timePoint(),
//        m_elements{},
//        m_elementsStorCommon{},
//        m_elementsStorExtended{},
//        m_strVoidParam()
//    {
//    }
//
//    template<typename TEnumType, typename TStringType>
//    const AMCore::AMTwoWayTable<TEnumType, TStringType> *AMUIDialogEnumMapLine<TEnumType, TStringType>::table()
//    {
//        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumNo);
//        std::chrono::time_point<std::chrono::steady_clock> tPoint;
//        auto tbl = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumNo, m_strVoidParam, &tPoint);
//        if (m_table != tbl || m_timePoint != tPoint) {
//            m_table = tbl;
//            m_timePoint = tPoint;
//
//            m_valueAny = false;
//            int extendes_cnt = 0;
//            for(auto it: *m_selectedExtended) {
//                if (it.second.size() == 0 ) {
//                    m_valueAny = true;
//                //} else if (it.second.size() == 1 ) {
//
//                } else {
//                    extendes_cnt++;
//                }
//            }
//            m_elements.resize(tbl->setB().size() + extendes_cnt);
//            m_elementsStorCommon.resize(tbl->setB().size());
//            m_elementsStorExtended.resize(extendes_cnt);
//            int i = 0;
//            for(auto &it: tbl->setB()) {
//                m_elementsStorCommon[i].element = AMUIFormElementCheckBox<TStringType>("");
//                m_elementsStorCommon[i].element.initBase(32, "");
//                m_elementsStorCommon[i].element.init(it.first);
//                m_elementsStorCommon[i].element.setState(AMUIWidgetState::FILLED);
//                m_elementsStorCommon[i].value = *it.second;
//                m_elements[i] = &m_elementsStorCommon[i].element;
//                i++;
//            }
//            for(auto its = m_selectedCommon->begin(); its != m_selectedCommon->end(); its ++) {
//                if (!tbl->findB(its->first)) {
//                    its = m_selectedCommon->erase(its);
//                }
//            }
//            int j = 0;
//            for(auto it: *m_selectedExtended) {
//                if (it.second.size() >= 1) {
//                    m_elementsStorExtended[j].elementRemoveButton = AMUIFormElementButton("");
//                    m_elementsStorExtended[j].elementRemoveButton.initBase(0,"");
//                    m_elementsStorExtended[j].elementRemoveButton.init(std::bind(&AMUIDialogEnumMapLine<TEnumType, TStringType>::onDeleteButton, this, std::placeholders::_1), j);
//                    m_elementsStorExtended[j].elementRemoveButton.setValueString("-");
//                    m_elementsStorExtended[j].elementEnumMap = AMUIFormElementEnumMap<TEnumType, TStringType>("");
//                    m_elementsStorExtended[j].elementEnumMap.initBase(0, "");
//                    m_elementsStorExtended[j].elementEnumMap.init(m_enumNo, m_strVoidParam);
//                    m_elementsStorExtended[j].elementEnumMap.setValue(it.second);
//                    m_elementsStorExtended[j].value = it.second;
//                    m_elements[i] = &m_elementsStorExtended[j].elementEnumMap;
//                    i++;
//                    j++;
//                }
//            }
//            update();
//        }
//        return m_table;
//    }
//
//    template<typename TEnumType, typename TStringType>
//    std::map<TEnumType, int> *AMUIDialogEnumMapLine<TEnumType, TStringType>::selectedCommon()
//    {
//        return m_selectedCommon;
//    }
//
//    template<typename TEnumType, typename TStringType>
//    void AMUIDialogEnumMapLine<TEnumType, TStringType>::update()
//    {
//        m_elementAny.setValue(m_valueAny);
//        for(auto &el: m_elementsStorCommon) {
//            auto it = m_selectedCommon->find(el.value);
//            el.element.setValue(it != m_selectedCommon->end());
//        }
//    }
//
//    template<typename TEnumType, typename TStringType>
//    void AMUIDialogEnumMapLine<TEnumType, TStringType>::onDeleteButton(int ndx)
//    {
//            printf("DDD button del %i\n", ndx);
//    }
//
//    template<typename TEnumType, typename TStringType>
//    void AMUIDialogEnumMapLine<TEnumType, TStringType>::setAny(bool any)
//    {
//
//    }
//
//} // AMDialogs