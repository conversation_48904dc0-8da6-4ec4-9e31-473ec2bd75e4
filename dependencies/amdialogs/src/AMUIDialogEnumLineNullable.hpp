//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogEnumLineNullable.h"
#include "amdialogs/AMUIDialogEnumLineNullableView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    void AMUIDialogEnumLineNullable<TEnumType, TStringType>::init(
        std::string name,
        std::function<void(AMUIDialog *)> onClose,
        std::function<void(AMUIDialog *)> onChange,
        int enumNo,
        std::string strVoidParam,
        std::map<TEnumType, int> *selected,
        bool *nullable,
        std::string flashbackTimepoint
        )
    {
        this->m_strVoidParam = strVoidParam;
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogEnumLineNullableView<TEnumType, TStringType>();
        v->init(this);
        this->m_view = v;
        this->m_enumNo = enumNo;
        this->m_selected = selected;
        this->m_table = this->table();
        this->m_flashbackTimepoint = flashbackTimepoint;
        AMAssert(nullable);
        m_nullable = nullable;
        m_nullableCheckBox.initBase(0, "");
        m_nullableCheckBox.init("Aktivní");
        m_nullableCheckBox.setValue(!(*nullable));
        m_nullableCheckBox.setState(AMUIWidgetState::FILLED);
    }


    template<typename TEnumType, typename TStringType>
    AMUIDialogEnumLineNullable<TEnumType, TStringType>::AMUIDialogEnumLineNullable()
        : AMUIDialogEnumLine<TEnumType, TStringType>(),
        m_nullable(nullptr),
        m_nullableCheckBox("")
    {
    }

    template<typename TEnumType, typename TStringType>
    const AMCore::AMTwoWayTable<TEnumType, TStringType> *AMUIDialogEnumLineNullable<TEnumType, TStringType>::table()
    {
        const AMCore::AMTwoWayTable<TEnumType, TStringType> * rv = AMUIDialogEnumLine<TEnumType, TStringType>::table();
        this->m_elements.push_back(&m_nullableCheckBox);
        return rv;
    }
} // AMDialogs