//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs/AMUIFormElementName.h"
#include <cassert>
#include <cstring>
#include "amdialogs/AMUIFormCallbacks.h"
#include "ameliteui/AMEliteImageManager.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIDialogName.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogNameView.h"
#include "amdialogs/AMUINameFlashback.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIFormElementName<TEnumType, TStringType>::AMUIFormElementName(const char *key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElement(key, mandatory, nullable, hidden),
          m_buffer(nullptr),
          m_dialog(nullptr),
          m_wildcard('\0'),
          m_preventRefreshDialog(0),
          m_strVoidParam(),
          m_flashbackTimepoint(),
          m_icon(-1)
    {
          m_callback = AMUIInputCallbackName<TEnumType, TStringType>;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::init(const std::string &strVoidParam, AMUI::AMUIController *controller)
    {
        AMAssert(m_length > 0);
        m_controller = controller;
        m_strVoidParam = strVoidParam;

        if (m_buffer) {
            delete[] m_buffer;
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam, m_flashbackTimepoint);
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_buffer = new char[m_length + 1];
        AMAssert(m_buffer);
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam, m_flashbackTimepoint);
        AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam, m_flashbackTimepoint);
        reset();

        if (m_icon == -1) {
            bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
                MEMIMG(Login_png), &m_icon, nullptr, nullptr
                );
            AMAssert(rv);
        }
    }

    template<typename TEnumType, typename TStringType>
    AMUIFormElementName<TEnumType, TStringType>::~AMUIFormElementName()
    {
        if (m_dialog) {
            delete m_dialog;
        }
        if (m_buffer) {
            delete[] m_buffer;
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam, m_flashbackTimepoint);
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam, m_flashbackTimepoint);
        }
        if (m_icon != -1) {
            AMEliteUI::AMEliteImageManager::unloadTexture(m_icon);
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::reset()
    {
        AMAssert(m_buffer);
        AMAssert(m_length > 0);
        m_buffer[0] = '\0';
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::updateState()
    {
        AMUIFormElement::updateState();
        AMUINameFlashBack<TEnumType, TStringType> nm(m_strVoidParam, m_flashbackTimepoint);
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::EMPTY;
        } else {
            TStringType is(m_buffer);
            AMUIParseResult res = AMUIParseName<TEnumType>(is, (AMUINameFlashBack<TEnumType, TStringType>*)&nm, m_wildcard);
            m_state = res != AMUIParseResult::FAIL ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
        }
        if (m_preventRefreshDialog) {
            m_preventRefreshDialog--;
            return;
        }
        if (m_dialog) {
            //printf("DB setval = %i %i\n", nm.degreeBefore.isNull(), nm.degreeBefore.isNull() ? -1 : nm.degreeBefore.get());
            m_dialog->degreeBefore().setValue(nm.degreeBefore.isNull() ? -1 : nm.degreeBefore.get());
            m_dialog->name().setValue(nm.name.c_str());
            m_dialog->surname().setValue(nm.surname.c_str());
            m_dialog->degreeAfter().setValue(nm.degreeAfter.isNull() ? -1 : nm.degreeAfter.get());
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::setValue(const char *value)
    {
        AMAssert(m_buffer);
        snprintf(m_buffer, m_length, "%s", value);
        m_buffer[m_length] = '\0';
        updateState();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        const float button_size = ImGui::GetFrameHeight();

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elstring_%i", element_id);
        ImGui::SetNextItemWidth(ImMax(1.0f, ImGui::CalcItemWidth() - (button_size + style.ItemInnerSpacing.x)));
        //ImGui::PushItemWidth(-FLT_MIN);
        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        if (ImGui::InputText(
            label, m_buffer, m_length,
            flags | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_CallbackAlways
            , m_callback
            , (void *) this
            )) {
            wasHitEnter();
        }
        ImVec2 rmin = ImGui::GetItemRectMin();

        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y = 0;
        //ImGuiButtonFlags button_flags = ImGuiButtonFlags_Repeat | ImGuiButtonFlags_DontClosePopups;
        if (flags & ImGuiInputTextFlags_ReadOnly)
            ImGui::BeginDisabled();
        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
        ImGui::PushID(label);
        if (ImGui::ImageButton(label, (ImTextureID)(long)m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1)))
        {
            if(!m_dialog) {
                AMUIDialogName<TEnumType, TStringType> *dlg = new AMUIDialogName<TEnumType, TStringType>();
                m_dialog = dlg;
                dlg->init(
                    std::string(m_label),
                    std::bind(&AMUIFormElementName<TEnumType, TStringType>::onDialogClose, this, std::placeholders::_1),
                    std::bind(&AMUIFormElementName<TEnumType, TStringType>::onDialogChange, this, std::placeholders::_1),
                    m_wildcard,
                    m_strVoidParam,
                    m_controller
                    );
                TStringType is(m_buffer);
                AMUINameFlashBack<TEnumType, TStringType> nm(m_strVoidParam, m_flashbackTimepoint);
                AMUIParseName<TEnumType>(is, (AMDialogs::AMUINameFlashBack<TEnumType, TStringType>*)&nm, m_wildcard);
                m_dialog->degreeBefore().setValue(nm.degreeBefore.isNull() ? 0 : nm.degreeBefore.get());
                m_dialog->name().setValue(nm.name.c_str());
                m_dialog->surname().setValue(nm.surname.c_str());
                m_dialog->degreeAfter().setValue(nm.degreeAfter.isNull() ? 0 : nm.degreeAfter.get());
                auto v = static_cast<AMUIDialogAuthorTagView<TStringType> *>(dlg->view());
                ImVec2 rmax = ImGui::GetItemRectMax();
                ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
                ImVec2 sz(std::max(rmax.x - rmin.x + 2 * backup_frame_padding.x, 250.0f), -FLT_MIN);
                v->setOrigin(pos);
                v->setSize(sz);
            }
        }
        ImGui::PopID();

        if (flags & ImGuiInputTextFlags_ReadOnly)
            ImGui::EndDisabled();
        style.FramePadding = backup_frame_padding;
        ImGui::PopStyleColor();
        if (m_dialog) {
            m_dialog->view()->render();
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::onDialogChange(AMUIDialog *dlg)
    {
        AMAssert(dlg == m_dialog);
        m_state = m_dialog->state();

        AMUINameFlashBack<TEnumType, TStringType> nm(m_strVoidParam, m_flashbackTimepoint);
        if (m_dialog->degreeBefore().value() < 0) {
            nm.degreeBefore.set();
        } else {
            nm.degreeBefore.set(m_dialog->degreeBefore().value());
        }
        nm.name = m_dialog->name().buffer();
        nm.surname = m_dialog->surname().buffer();
        if (m_dialog->degreeAfter().value() < 0) {
            nm.degreeAfter.set();
        } else {
            nm.degreeAfter.set(m_dialog->degreeAfter().value());
        }
        TStringType s = AMUISynthesizeName(&nm, m_wildcard == '\0' ? '\0' : '%');
        if (s != m_buffer) {
            strncpy(m_buffer, s.c_str(), m_length - 1);
            m_buffer[m_length - 1] = '\0';
            wasModified();
        }
        m_preventRefreshDialog++;

    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::onDialogClose(AMUIDialog *)
    {
        //onDialogChange(m_dialog);
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
        m_dialog = nullptr;
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUIFormElementName<TEnumType, TStringType>::valueAsString(std::map<int, std::string> *keys)
    {
        AMUINameFlashBack<TEnumType, TStringType> nm(m_strVoidParam, m_flashbackTimepoint);
        TStringType is(m_buffer);
        AMUIParseResult res =AMUIParseName<TEnumType>(is, (AMDialogs::AMUINameFlashBack<TEnumType, TStringType>*)&nm, m_wildcard);
        if (res ==  AMUIParseResult::FAIL) {
            return "";
        }
        if (!keys || keys->size() != 4) {
            return "";
        }
        if (nm.degreeBefore.isNull() || nm.degreeAfter.isNull()) {
            return "";
        }
        std::ostringstream os;
        os.imbue(std::locale("C"));
        os << keys->at(0) << '=' << nm.degreeBefore.get() << '&';
        os << keys->at(1) << '=' << nm.name << '&';
        os << keys->at(2) << '=' << nm.surname << '&';
        os << keys->at(3) << '=' << nm.degreeAfter.get();
        return os.str();
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::setFlashbackTimepoint(std::string flashbackTimepoint)
    {
        if (m_buffer) {
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam, m_flashbackTimepoint);
            AMDialogs::destroyTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam, m_flashbackTimepoint);
        }
        m_flashbackTimepoint = flashbackTimepoint;
        if (m_buffer) {
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeBeforeTableId, m_strVoidParam, m_flashbackTimepoint);
            AMDialogs::prepareTwoWayTable<TEnumType, TStringType>(IDegreeAfterTableId, m_strVoidParam, m_flashbackTimepoint);
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementName<TEnumType, TStringType>::loadDefault() {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }
}
