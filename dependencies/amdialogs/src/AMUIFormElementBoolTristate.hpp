//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

//#include "../AMUIFormElementBool.h"
//#include "amcore/AMAssert.h"
//#include <cstdio>
//#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {

    //const int FORM_ELEMENT_INT_BUFFER_SIZE = 32;

    template<typename TStringType>
    AMUIFormElementBoolTristate<TStringType>::AMUIFormElementBoolTristate(const char *key)
        : AMUIFormElementBool<TStringType>(key)
    {
    }

    template<typename TStringType>
    AMUIFormElementBoolTristate<TStringType>::~AMUIFormElementBoolTristate() {
    }

    template<typename TStringType>
    void AMUIFormElementBoolTristate<TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : this->m_state;

        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
            ImGui::PushStyleColor(ImGuiCol_CheckMark, bgWidgetColors[(int) AMUIWidgetState::READONLY]);
        } else {
            ImGui::PushStyleColor(ImGuiCol_FrameBg, 0xFFFFFFFF);
        }
        char label[128];
        snprintf(
            label, sizeof(label) - 1, "%s##elradio_%i", this->m_trueLabel.c_str(), element_id
                );
        if (ImGui::RadioButton(label, this->valuePtr(), 1)) {
            this->wasModified();
        }
        ImGui::SameLine();
        snprintf(
            label, sizeof(label) - 1, "%s##elradio_%i", this->m_falseLabel.c_str(), element_id
                );
        if (ImGui::RadioButton(label, this->valuePtr(), 0)) {
            this->wasModified();
        }
        ImGui::SameLine();
        snprintf(
            label, sizeof(label) - 1, "%s##elradio_%i", boolUnselectedText, element_id
                );
        if (ImGui::RadioButton(label, this->valuePtr(), -1)) {
            this->wasModified();
        }

        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopStyleColor();
            ImGui::PopItemFlag();
        } else {
            ImGui::PopStyleColor();
        }
    }

    template<typename TStringType>
    void AMUIFormElementBoolTristate<TStringType>::setValue(int value)
    {
        this->m_value = value;
        this->updateState();
    }

}