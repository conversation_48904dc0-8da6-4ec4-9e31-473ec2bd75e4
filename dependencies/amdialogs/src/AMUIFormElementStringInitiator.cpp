//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs//AMUIFormElementStringInitiator.h"

namespace AMDialogs {

    AMUIFormElementStringInitiator::AMUIFormElementStringInitiator(const char *key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElementString(key, mandatory, nullable, hidden),
        m_alreadySet(false)
    {
    }

    AMUIFormElementStringInitiator::~AMUIFormElementStringInitiator()
    {
    }

    void AMDialogs::AMUIFormElementStringInitiator::setValueString(std::string value)
    {
        AMUIFormElementString::setValueString(value);
        m_alreadySet = true;
    }

    void AMDialogs::AMUIFormElementStringInitiator::reset()
    {
        m_alreadySet = false;
        AMUIFormElementString::reset();
    }

    void AMUIFormElementStringInitiator::setValue()
    {
        m_alreadySet = true;
        AMUIFormElementString::setValue();
    }

    void AMUIFormElementStringInitiator::loadDefault()
    {
        m_alreadySet = true;
    }

    void AMUIFormElementStringInitiator::setValue(const char *value)
    {
        m_alreadySet = true;
        AMUIFormElementString::setValue(value);
    }

}
