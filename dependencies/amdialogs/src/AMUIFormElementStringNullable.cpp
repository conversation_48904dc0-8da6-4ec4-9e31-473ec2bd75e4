//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementStringNullable.h"
#include "amcore/AMAssert.h"
#include <cstring>
//#include "../cfg/config.h"
#include "../AMUIFormCallbacks.h"

namespace AMDialogs {

    AMUIFormElementStringNullable::AMUIFormElementStringNullable(const char *key)
        : AMUIFormElementString(key, false, true, false),
          m_notNull(false)
    {
        m_callback = AMUIInputCallbackStringNullable;
    }

    AMUIFormElementStringNullable::~AMUIFormElementStringNullable()
    {
    }

    void AMUIFormElementStringNullable::reset() {
        AMUIFormElementString::reset();
        m_notNull = false;
    }



    bool AMUIFormElementStringNullable::isNull() {
        return !m_notNull;
    }

    void AMUIFormElementStringNullable::setValue()
    {
        m_notNull = false;
        AMUIFormElementString::setValue();
    }

    void AMUIFormElementStringNullable::setValue(const char *newValue)
    {
        m_notNull = true;
        AMUIFormElementString::setValue(newValue);
    }

    std::string AMUIFormElementStringNullable::valueAsString()
    {
        if (!m_notNull) {
            return "";
        }
        return AMUIFormElementString::valueAsString();
    }

    void AMUIFormElementStringNullable::setValueString(std::string value)
    {
        m_notNull = true;
        AMUIFormElementString::setValueString(value);
    }

    void AMUIFormElementStringNullable::render(bool formGrayed, int element_id)
    {
        AMAssert(0 && "otestovat!!");
        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : this->m_state;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        const float button_size = ImGui::GetFrameHeight();

        char chk_label[128];
        snprintf(chk_label, sizeof(chk_label) - 1, "##elcheck_%i", element_id);
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
        }

        if (ImGui::Checkbox(chk_label, &m_notNull)) {
            this->wasModified();
        }
        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopItemFlag();
        }
        ImGui::SameLine();

        AMUIFormElementString::render(formGrayed || !m_notNull, element_id);
    }

}
