//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogEnumLine.h"
#include "amdialogs/AMUIDialogEnumLineView.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    void AMUIDialogEnumLine<TEnumType, TStringType>::init(
        std::string name,
        std::function<void(AMUIDialog *)> onClose,
        std::function<void(AMUIDialog *)> onChange,
        int enumNo,
        std::string strVoidParam,
        std::map<TEnumType, int> *selected,
        std::string flashbackTimepoint
        )
    {
        m_strVoidParam = strVoidParam;
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogEnumLineView<TEnumType, TStringType>();
        v->init(this);
        m_view = v;
        m_enumNo = enumNo;
        m_selected = selected;
        m_table = table();
        m_flashbackTimepoint = flashbackTimepoint;
    }

    template<typename TEnumType, typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogEnumLine<TEnumType, TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TEnumType, typename TStringType>
    AMUIDialogEnumLine<TEnumType, TStringType>::AMUIDialogEnumLine()
        : AMUIDialog(),
        m_enumNo(-1),
        m_selected(nullptr),
        m_table(),
        m_timePoint(),
        m_elements{},
        m_elementsStor{},
        m_strVoidParam(),
        m_hintText("Budou zobrazy záznamy, kde je jedna z položek:"),
        m_flashbackTimepoint()
    {
    }

    template<typename TEnumType, typename TStringType>
    const AMCore::AMTwoWayTable<TEnumType, TStringType> *AMUIDialogEnumLine<TEnumType, TStringType>::table()
    {
        AMDialogs::AMUIFormTwoWayTableLock<TEnumType, TStringType> lock(m_enumNo);
        std::chrono::time_point<std::chrono::steady_clock> tPoint;
        auto tbl = AMDialogs::twoWayTable<TEnumType, TStringType>(m_enumNo, m_strVoidParam, m_flashbackTimepoint, &tPoint);
        if (m_table != tbl || m_timePoint != tPoint) {
            m_table = tbl;
            m_timePoint = tPoint;

            m_elements.resize(tbl->setB().size());
            m_elementsStor.resize(tbl->setB().size());
            int i = 0;
            for(auto &it: tbl->setB()) {
                m_elementsStor[i].element = AMUIFormElementCheckBox<TStringType>("");
                m_elementsStor[i].element.initBase(32, "");
                m_elementsStor[i].element.init(it.first);
                m_elementsStor[i].element.setState(AMUIWidgetState::FILLED);
                m_elementsStor[i].value = *it.second;
                m_elements[i] = &m_elementsStor[i].element;
                i++;
            }
            for(auto its = m_selected->begin(); its != m_selected->end(); its ++) {
                if (!tbl->findB(its->first)) {
                    its = m_selected->erase(its);
                }
            }
            update();
        }
        return m_table;
    }

    template<typename TEnumType, typename TStringType>
    std::map<TEnumType, int> *AMUIDialogEnumLine<TEnumType, TStringType>::selected()
    {
        return m_selected;
    }


    template<typename TEnumType, typename TStringType>
    std::vector<_AMUIDialogEnumLineItem<TEnumType, TStringType>> &
    AMUIDialogEnumLine<TEnumType, TStringType>::elementsStor()
    {
        return m_elementsStor;
    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogEnumLine<TEnumType, TStringType>::update()
    {
        for(auto &el: m_elementsStor) {
            auto it = m_selected->find(el.value);
            el.element.setValue(it != m_selected->end());
        }
    }

} // AMDialogs