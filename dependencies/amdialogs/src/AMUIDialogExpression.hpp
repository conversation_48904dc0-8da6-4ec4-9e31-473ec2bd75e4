//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogExpression.h"
#include "amdialogs/AMUIDialogExpressionView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIFormElementButton.h"
#include "amdialogs/AMUIFormElementInteger.h"
#include "amdialogs/AMUIFormElementNatural.h"
#include "amdialogs/AMUIFormElementDatetime.h"
#include "amdialogs/AMUIFormElementString.h"
#include "amdialogs/AMUIFormElementSelectBox.h"

namespace AMDialogs {

    template<typename TStringType>
    void AMUIDialogExpression<TStringType>::init(
        std::string name,
        std::function<void(AMUIDialog *)> onClose,
        std::function<void(AMUIDialog *)> onChange,
        std::vector<AMUIParseExpressionElement<TStringType> > *expression,
        AMCore::AMDataType type
        )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogExpressionView<TStringType>();
        v->init(this);
        m_view = v;
        //m_expression = expression;
        m_type = type;
        update(expression);
    }

    template<typename TStringType>
    std::vector<AMUIFormElement *> &AMUIDialogExpression<TStringType>::elements()
    {
        return m_elements;
    }

    template<typename TStringType>
    AMUIDialogExpression<TStringType>::AMUIDialogExpression()
        : AMUIDialog(),
        //m_expression(nullptr),
        m_type(AMCore::AMDataType_void),
        m_elements{},
        m_tabs{},
        m_isValues{},
        m_elementsBacktable{},
        m_causedByButton(false),
        m_causedOperation(AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING),
        m_causedString(nullptr),
        m_causedByLine(0)
    {
        AMUIFormElementButton *firstButton = new AMUIFormElementButton("");
        firstButton->initBase(1, "+");
        firstButton->setValueString("+");
        firstButton->init(std::bind(&AMUIDialogExpression<TStringType>::onButtonClicked, this, std::placeholders::_1), 0);
        firstButton->setSizeX(ImGui::GetFrameHeight());
        firstButton->setAlign(AMUIFormElementButton::LEFT);
        firstButton->setState(AMUIWidgetState::EMPTY);
        m_elements.push_back(firstButton);
    }

    static const std::vector<std::string> items_odd = {" ", "A", "NEBO", ")"};
    static const std::vector<std::string> items_even = {" ", "=", "<>", ">", "<", ">=", "<=", "("};
    static const std::vector<std::string> items_evenn = {" ", "=", "<>", ">", "<", ">=", "<=", "Je NULL", "Není NULL", "("};
    static const std::vector<std::string> items_evens = {" ", "Podobný", "Odlišný", "=", "<>", ">", "<", ">=", "<=",  "("};
    static const std::vector<std::string> items_evensn = {" ", "Podobný", "Odlišný", "=", "<>", ">", "<", ">=", "<=", "Je NULL", "Není NULL", "("};
    static const std::vector<std::string> items_inv = {" ", "Nevalidní"};
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_odd[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::AND,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::OR,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED,
    };
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_even[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::EQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MORE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESS,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN
    };
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_evenn[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::EQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MORE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESS,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::ISNULL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTNULL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN
    };
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_evens[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LIKE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::EQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MORE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESS,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN
    };
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_evensn[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LIKE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::EQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MORE,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESS,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::ISNULL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTNULL,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN
    };
    template<typename TStringType>
    static const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations itemIds_inv[12] = {
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::NOTHING,
        AMDialogs::AMUIParseExpressionElement<TStringType>::Operations::INVALID
    };
    static const int itemNdxs_odd[16] = { 0, -1, 3, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 2, -1};
    static const int itemNdxs_even[16] = { 0, 7, -1, 1, 2, -1, -1, 4, 3, 6, 5, -1, -1, -1, -1, -1};
    static const int itemNdxs_evenn[16] = { 0, 9, -1, 1, 2, -1, -1, 4, 3, 6, 5, 7, 8, -1, -1, -1};
    static const int itemNdxs_evens[16] = { 0, 9, -1, 3, 4, 1, 2, 6, 5, 8, 7, -1, -1, -1, -1, -1};
    static const int itemNdxs_evensn[16] = { 0, 11, -1, 3, 4, 1, 2, 6, 5, 8, 7, 9, 10, -1, -1, -1};
    static const int itemNdxs_inv[16] = { 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1};

    template<typename TStringType>
    void AMUIDialogExpression<TStringType>::updateSelectBox(AMUIFormElementSelectBox *sel, int index, std::vector<AMUIParseExpressionElement<TStringType> > *expression)
    {
        const std::vector<std::string> *items;
        const typename AMDialogs::AMUIParseExpressionElement<TStringType>::Operations *itemIds;
        const int *itemNdxs;
        //int itemSz;
        AMUIParseExpressionElement<TStringType> &eel = expression->at(index);
        switch (eel.comboId) {
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::ODD:
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::ODDNULL:
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRING:
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::ODDSTRINGNULL:
                items = &items_odd;
                itemIds = itemIds_odd<TStringType>;
                itemNdxs = itemNdxs_odd;
                //itemSz = 4;
                break;
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::EVEN:
                items = &items_even;
                itemIds = itemIds_even<TStringType>;
                itemNdxs = itemNdxs_even;
                //itemSz = 8;
                break;
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::EVENNULL:
                items = &items_evenn;
                itemIds = itemIds_evenn<TStringType>;
                itemNdxs = itemNdxs_evenn;
                //itemSz = 10;
                break;
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRING:
                items = &items_evens;
                itemIds = itemIds_evens<TStringType>;
                itemNdxs = itemNdxs_evens;
                //itemSz = 10;
                break;
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::EVENSTRINGNULL:
                items = &items_evensn;
                itemIds = itemIds_evensn<TStringType>;
                itemNdxs = itemNdxs_evensn;
                //itemSz = 12;
                break;
            case AMDialogs::AMUIParseExpressionElement<TStringType>::ComboId::INVALID:
                items = &items_inv;
                itemIds = itemIds_inv<TStringType>;
                itemNdxs = itemNdxs_inv;
                //itemSz = 2;
                break;
        }
        int op = itemNdxs[(int)eel.operation];
        sel->init(*items);
        sel->setValue(op);
        m_elementsBacktable[index] = itemIds;
    }
    template<typename TStringType>
    void AMUIDialogExpression<TStringType>::updateInt(int max, std::vector<AMUIParseExpressionElement<TStringType> > *expression)
    {
        for(int i = 0, j = 0; i < max - 1; i += 3, j++) {
            auto sel = static_cast<AMUIFormElementSelectBox *>(m_elements[i + 1]);
            updateSelectBox(sel, j, expression);
            switch(m_type) {
                case AMCore::AMDataType_address_nullable:
                case AMCore::AMDataType_string_nullable:
                case AMCore::AMDataType_address:
                case AMCore::AMDataType_string: {
                    auto el = static_cast<AMUIFormElementString *>(m_elements[i + 2]);
                    el->setValue(expression->at(j).inputValue.c_str());
                    break;
                }
                case AMCore::AMDataType_natural_nullable:
                case AMCore::AMDataType_natural: {
                    auto el = static_cast<AMUIFormElementNatural *>(m_elements[i + 2]);
                    el->setValue(expression->at(j).inputValue.c_str());
                    break;
                }
                case AMCore::AMDataType_integer_nullable:
                case AMCore::AMDataType_integer: {
                    auto el = static_cast<AMUIFormElementInteger *>(m_elements[i + 2]);
                    el->setValue(expression->at(j).inputValue.c_str());
                    break;
                }
                case AMCore::AMDataType_time_nullable:
                case AMCore::AMDataType_date_nullable:
                case AMCore::AMDataType_datetimeus_nullable:
                case AMCore::AMDataType_datetime_nullable:
                case AMCore::AMDataType_time:
                case AMCore::AMDataType_date:
                case AMCore::AMDataType_datetimeus:
                case AMCore::AMDataType_datetime: {
                    auto el = static_cast<AMUIFormElementDatetime *>(m_elements[i + 2]);
                    el->setValue(expression->at(j).inputValue.c_str());
                    break;
                }
                default:
                    AMAssert(0);
                    break;
            }
            m_tabs[j] = expression->at(j).indent;
            m_isValues[j] = AMDialogs::AMUIParseExpressionElement<TStringType>::isValue(expression->at(j).operation);
        }
    }

    template<typename TStringType>
    void AMUIDialogExpression<TStringType>::update(std::vector<AMUIParseExpressionElement<TStringType> > *expression)
    {
        int newSz = expression->size() * 3 + 1;
        if (m_elements.size() < newSz) {

            int oldSz = m_elements.size();
            updateInt(oldSz, expression);
            m_elements.resize(newSz);
            m_tabs.resize(expression->size());
            m_isValues.resize(expression->size());
            m_elementsBacktable.resize(expression->size());
            for(int i = oldSz, j = (oldSz - 1) / 3; i < newSz - 1; i += 3, j++) {
                auto sel = new AMUIFormElementSelectBox("");
                sel->initBase(1, "");
                updateSelectBox(sel, j, expression);
                sel->setSizeX(100.0f);
                sel->setState(AMUIWidgetState::FILLED);
                m_elements[i] = sel;
                switch(m_type) {
                    case AMCore::AMDataType_address_nullable:
                    case AMCore::AMDataType_string_nullable:
                    case AMCore::AMDataType_address:
                    case AMCore::AMDataType_string: {
                        auto el = new AMUIFormElementString("");
                        el->initBase(64, "");
                        el->init();
                        el->setState(AMUIWidgetState::FILLED);
                        el->setValue(expression->at(j).inputValue.c_str());
                        m_elements[i + 1] = el;
                        break;
                    }
                    case AMCore::AMDataType_natural_nullable:
                    case AMCore::AMDataType_natural: {
                        auto el = new AMUIFormElementNatural("");
                        el->initBase(64, "");
                        el->init();
                        el->setState(AMUIWidgetState::FILLED);
                        el->setValue(expression->at(j).inputValue.c_str());
                        m_elements[i + 1] = el;
                        break;
                    }
                    case AMCore::AMDataType_integer_nullable:
                    case AMCore::AMDataType_integer: {
                        auto el = new AMUIFormElementInteger("");
                        el->initBase(64, "");
                        el->init();
                        el->setState(AMUIWidgetState::FILLED);
                        el->setValue(expression->at(j).inputValue.c_str());
                        m_elements[i + 1] = el;
                        break;
                    }
                    case AMCore::AMDataType_date_nullable:
                    case AMCore::AMDataType_time_nullable:
                    case AMCore::AMDataType_datetimeus_nullable:
                    case AMCore::AMDataType_datetime_nullable:
                    case AMCore::AMDataType_date:
                    case AMCore::AMDataType_time:
                    case AMCore::AMDataType_datetimeus:
                    case AMCore::AMDataType_datetime: {
                        auto el = new AMUIFormElementDatetime("");
                        el->initBase(64, "");
                        el->init(m_type);
                        el->setState(AMUIWidgetState::FILLED);
                        el->setValue(expression->at(j).inputValue.c_str());
                        m_elements[i + 1] = el;
                        break;
                    }
                    default:
                        AMAssert(0);
                        break;
                }
                AMUIFormElementButton *button = new AMUIFormElementButton("");
                button->initBase(1, "+");
                button->setValueString("+");
                button->init(std::bind(&AMUIDialogExpression<TStringType>::onButtonClicked, this, std::placeholders::_1), j + 1);
                button->setSizeX(ImGui::GetFrameHeight());
                button->setAlign(AMUIFormElementButton::LEFT);
                button->setState(AMUIWidgetState::EMPTY);
                m_elements[i + 2] = button;
                m_tabs[j] = expression->at(j).indent;
                m_isValues[j] = AMDialogs::AMUIParseExpressionElement<TStringType>::isValue(expression->at(j).operation);
            }

        } else if (m_elements.size() > newSz) {
            updateInt(newSz, expression);
            for(int i = newSz; i < m_elements.size(); i++) {
                delete m_elements[i];
            }
            m_elements.resize(newSz);
            m_tabs.resize(expression->size());
            m_elementsBacktable.resize(expression->size());
        } else {
            updateInt(m_elements.size(), expression);
        }
    }

    template<typename TStringType>
    void AMUIDialogExpression<TStringType>::onButtonClicked(int buttonId)
    {
        setCausedByLine(buttonId);
        setCausedByButton(true);
        setCausedOperation(AMUIParseExpressionElement<TStringType>::Operations::NOTHING);
        setCausedString(nullptr);
        onChange();
    }

    template<typename TStringType>
    AMUIWidgetState AMUIDialogExpression<TStringType>::causedState()
    {
        AMUIWidgetState state = AMUIWidgetState::FILLED;

        std::vector<AMUIFormElement *> &elems = elements();
        int i = 0;
        for (AMUIFormElement *el: elems) {
            if (i % 3 == 2 && m_isValues[i / 3]) {
                state = state + el->state();
            }
            i++;
        }
        return state;
    }

} // AMDialogs