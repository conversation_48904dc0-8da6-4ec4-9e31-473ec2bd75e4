//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#include "../AMUIFormElementDatetime.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"

namespace AMDialogs {

    const int FORM_ELEMENT_DATETIME_BUFFER_SIZE = 48;

    AMUIFormElementDatetime::AMUIFormElementDatetime(const std::string key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElement(key, mandatory, nullable, hidden),
          m_type(AMCore::AMDataType_datetime),
          m_buffer(nullptr),
          m_valueMin(),
          m_valueMax(),
          m_wildcard('\0'),
          m_icon(-1),
          m_format(nullptr),
          m_dialog(nullptr),
          m_focused(false),
          m_preventRefreshDialog(0),
          m_nullType(AMCore::AMDataType_datetime_nullable)
    {
          m_callback = AMUIInputCallbackDatetime;
    }

    void AMUIFormElementDatetime::init(AMCore::AMDataType type) {
        switch(type) {
            case AMCore::AMDataType_datetime: m_type = type;m_type = AMCore::AMDataType_datetime; m_format = AMDialogs::datetimeFormatText;m_nullable = false;break;
            case AMCore::AMDataType_date: m_type = type;m_type = AMCore::AMDataType_date; m_format = AMDialogs::dateFormatText;m_nullable = false;break;
            case AMCore::AMDataType_time: m_type = type;m_type = AMCore::AMDataType_time; m_format = AMDialogs::timeFormatText;m_nullable = false;break;
            case AMCore::AMDataType_datetimeus: m_type = type;m_type = AMCore::AMDataType_datetimeus; m_format = AMDialogs::datetimeusFormatText;m_nullable = false;break;
            case AMCore::AMDataType_datetime_nullable: m_type = AMCore::AMDataType_datetime;m_nullType = type; m_format = AMDialogs::datetimeFormatText;m_nullable = true;break;
            case AMCore::AMDataType_date_nullable: m_type = AMCore::AMDataType_date;m_nullType = type; m_format = AMDialogs::dateFormatText;m_nullable = true;break;
            case AMCore::AMDataType_time_nullable: m_type = AMCore::AMDataType_time;m_nullType = type; m_format = AMDialogs::timeFormatText;m_nullable = true;break;
            case AMCore::AMDataType_datetimeus_nullable: m_type = AMCore::AMDataType_datetimeus;m_nullType = type; m_format = AMDialogs::datetimeusFormatText;m_nullable = true;break;
            default:
                break;
        }
        m_length = FORM_ELEMENT_DATETIME_BUFFER_SIZE;
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[FORM_ELEMENT_DATETIME_BUFFER_SIZE + 1];
        AMAssert(m_buffer);
        reset();

        if (m_icon == -1) {
            bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
                MEMIMG(Calendar_png), &m_icon, nullptr, nullptr
                                                                           );
            AMAssert(rv);
        }

    }

    AMUIFormElementDatetime::~AMUIFormElementDatetime()
    {
        if (m_dialog) {
            delete m_dialog;
        }
        if (m_buffer) {
            delete[] m_buffer;
        }
        if (m_icon != -1) {
            AMEliteUI::AMEliteImageManager::unloadTexture(m_icon);
        }
    }

    void AMUIFormElementDatetime::reset() {
        AMAssert(m_buffer);
        m_valueMin = AMCore::AMDatetimeus();
        m_valueMax = AMCore::AMDatetimeus();
        m_buffer[0] = '\0';
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
    }


    void AMUIFormElementDatetime::updateState()
    {
        AMUIFormElement::updateState();
        AMAssert(m_length > 0);
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = m_nullType ? AMUIWidgetState::EMPTY : AMUIWidgetState::INVALID;
        } else {
            std::istringstream is(m_buffer);
            AMUIParseResult res = AMUIParseDatetime(is, &m_valueMin, &m_valueMax, nullptr, std::string(m_format), m_wildcard);
            unsigned int c = AMCore::AMStreamToLowercaseStripDiaGet(is);
            m_state = res != AMUIParseResult::FAIL && c == UEOF ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
        }
        if (m_preventRefreshDialog) {
            m_preventRefreshDialog--;
            return;
        }
        if (m_dialog) {
            m_dialog->setDatetime(std::string(m_buffer));
        }
    }

    void AMUIFormElementDatetime::setValue(std::tm &value)
    {
        AMAssert(m_buffer);
        m_valueMin = value;
        m_valueMax = value;
        strftime(m_buffer, FORM_ELEMENT_DATETIME_BUFFER_SIZE, m_format, &value);
        updateState();
    }

    void AMUIFormElementDatetime::setValue(AMCore::AMNullable<std::tm> &value)
    {
        AMAssert(m_buffer);
        if (value.isNull()) {
            m_buffer[0] = '\0';
        } else {
            m_valueMin = value.get();
            m_valueMax = value.get();
            strftime(m_buffer, FORM_ELEMENT_DATETIME_BUFFER_SIZE, m_format, &m_valueMin);
        }
        updateState();
    }

    void AMUIFormElementDatetime::setValue(AMCore::AMNullable<AMCore::AMDatetimeus> &value)
    {
        AMAssert(m_buffer);
        if (value.isNull()) {
            m_buffer[0] = '\0';
        } else {
            m_valueMin = value.get();
            m_valueMax = value.get();
            std::string tdms = AMDialogs::AMUISynthesizeDatetime<std::string>(m_format, &m_valueMin, &m_valueMax, m_wildcard);
            ::strncpy(m_buffer, tdms.c_str(), m_length);
            m_buffer[m_length - 1] = '\0';
        }
        updateState();
    }

    void AMUIFormElementDatetime::render(bool formGrayed, int element_id)
    {
        if (m_hidden) {
            return;
        }

        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        const float button_size = ImGui::GetFrameHeight();

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width - button_size - style.ItemInnerSpacing.x));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##eldatetime_%i", element_id);
        if(ImGui::InputText(
            label, m_buffer, m_length,
            flags | ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackAlways
            , m_callback
            , (void *) this
                        )) {
            wasHitEnter();
        }

        ImGui::PopStyleColor();
        bool foc = ImGui::IsItemFocused();
        if (foc != m_focused) {
            m_focused = foc;
            if (foc == false) {
                lostFocus();
            }
        }
        ImVec2 rmin = ImGui::GetItemRectMin();
        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y = 0;
        ImGuiButtonFlags button_flags = 0;

        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        //printf("btn size = %f fp=%f\n", button_size, style.FramePadding.x);
        snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
        ImGui::PushID(label);
        if (ImGui::ImageButton(label, (ImTextureID)(long)m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1)))
        {
            if(!m_dialog) {
                AMUIDialogDatetime *dlg = new AMUIDialogDatetime();
                m_dialog = dlg;
                dlg->init(
                    std::string(m_label),
                    std::bind(&AMUIFormElementDatetime::onDialogClose, this, std::placeholders::_1),
                    std::bind(&AMUIFormElementDatetime::onDialogChange, this, std::placeholders::_1),
                    m_wildcard,
                    m_type
                         );
                dlg->setDatetime(std::string(m_buffer));
                auto v = static_cast<AMUIDialogDatetimeView *>(dlg->view());
                ImVec2 rmax = ImGui::GetItemRectMax();
                ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
                ImVec2 sz(std::max(rmax.x - rmin.x + 2 * backup_frame_padding.x, 500.0f), -FLT_MIN);
                v->setOrigin(pos);
                v->setSize(sz);
            }
        }
        ImGui::PopID();
        /*
        ImVec2 rmax = ImGui::GetItemRectMax();
        if (m_dialog) {
            AMUIDialogDatetime *dlg = static_cast<AMUIDialogDatetime *>(m_dialog);
            auto v = static_cast<AMUIDialogDatetimeView *>(dlg->view());
            ImVec2 pos(rmin.x - backup_frame_padding.x, rmax.y);
            ImVec2 sz(rmax.x - rmin.x + 2 * backup_frame_padding.x, -FLT_MIN);
            v->setOrigin(pos);
            v->setSize(sz);
        }*/
        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }

        style.FramePadding = backup_frame_padding;

        //ImGui::PushStyleColor(ImGuiCol_FrameBg, 0xFF00CCCC);
        //ImGui::PopStyleColor();
        if (m_dialog) {
            m_dialog->view()->render();
        }
//        ImGui::PopStyleColor();
    }

    void AMUIFormElementDatetime::setValue(const char *buffer)
    {
        AMAssert(m_buffer);
        if (strcmp(m_buffer, "null") == 0) {
            m_buffer[0] = '\0';
        } else {
            ::strncpy(m_buffer, buffer, m_length);
            m_buffer[m_length - 1] = '\0';
        }
        updateState();
    }

    void AMUIFormElementDatetime::setValue(AMCore::AMDatetimeus &value)
    {
        AMAssert(m_buffer);
        m_valueMin = value;
        m_valueMax = value;
        std::string tdms = AMDialogs::AMUISynthesizeDatetime<std::string>(m_format, &m_valueMin, &m_valueMax, m_wildcard);
        ::strncpy(m_buffer, tdms.c_str(), m_length);
        m_buffer[m_length - 1] = '\0';
        updateState();
    }

    void AMUIFormElementDatetime::setWildcard(char wildcard)
    {
        m_wildcard = wildcard;
    }

    void AMUIFormElementDatetime::onDialogChange(AMUIDialog *dlg)
    {
        AMAssert(dlg == m_dialog);
        m_state = m_dialog->state();
        strncpy(m_buffer, m_dialog->datetime().c_str(), m_length);
        m_preventRefreshDialog++;
        wasModified();
    }

    void AMUIFormElementDatetime::onDialogClose(AMUIDialog *)
    {
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
        m_dialog = nullptr;
    }

    void AMUIFormElementDatetime::loadDefault() {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }

    std::string AMUIFormElementDatetime::valueAsString()
    {
        if (m_nullable && m_buffer[0] == '\0') {
            return "null";
        }
        switch (m_type) {
            case AMCore::AMDataType_datetime:
                return AMUI::url_encode_string(AMUISynthesizeDatetime<std::string>("%Y-%m-%d %H:%M:%S", &m_valueMin, &m_valueMax, m_wildcard));
            case AMCore::AMDataType_datetimeus:
                return AMUI::url_encode_string(AMUISynthesizeDatetime<std::string>("%Y-%m-%d %H:%M:%S.Q", &m_valueMin, &m_valueMax, m_wildcard));
            case AMCore::AMDataType_date:
                return AMUI::url_encode_string(AMUISynthesizeDatetime<std::string>("%Y-%m-%d", &m_valueMin, &m_valueMax, m_wildcard));
            case AMCore::AMDataType_time:
                return AMUI::url_encode_string(AMUISynthesizeDatetime<std::string>("%H:%M:%S", &m_valueMin, &m_valueMax, m_wildcard));
            default:
                return "";
        }
    }

    AMCore::AMNullable<std::tm> AMUIFormElementDatetime::valueNullable() const
    {
        if (m_nullable && m_buffer[0] == '\0') {
            return AMCore::AMNullable<std::tm>();
        }
        return AMCore::AMNullable<std::tm>(m_valueMin);
    }

    AMCore::AMNullable<AMCore::AMDatetimeus> AMUIFormElementDatetime::valueusNullable() const
    {
        if (m_nullable && m_buffer[0] == '\0') {
            return AMCore::AMNullable<AMCore::AMDatetimeus>();
        }
        return AMCore::AMNullable<AMCore::AMDatetimeus>(m_valueMin);
    }
}