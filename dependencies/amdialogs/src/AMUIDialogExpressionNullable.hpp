//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogExpressionNullable.h"
#include "amdialogs/AMUIDialogExpressionNullableView.h"

namespace AMDialogs {

    template<typename TStringType>
    void AMUIDialogExpressionNullable<TStringType>::init(
        std::string name,
        std::function<void(AMUIDialog *)> onClose,
        std::function<void(AMUIDialog *)> onChange,
        std::vector<AMUIParseExpressionElement<TStringType> > *expression,
        AMCore::AMDataType type,
        bool *nullable
    )
    {
        AMUIDialog::init(std::move(name), std::move(onClose), std::move(onChange));
        auto *v = new AMUIDialogExpressionNullableView<TStringType>();
        v->init(this);
        this->m_view = v;
        this->m_type = type;
        AMAssert(nullable);
        m_nullable = nullable;
        m_nullableCheckBox.initBase(0, "");
        m_nullableCheckBox.init("Aktivní");
        m_nullableCheckBox.setValue(!(*nullable));
        m_nullableCheckBox.setState(AMUIWidgetState::FILLED);
        update(expression);
    }


    template<typename TStringType>
    AMUIDialogExpressionNullable<TStringType>::AMUIDialogExpressionNullable()
        : AMUIDialogExpression<TStringType>(),
        m_nullable(nullptr),
        m_nullableCheckBox("")
    {
    }

    template<typename TStringType>
    void AMUIDialogExpressionNullable<TStringType>::update(std::vector<AMUIParseExpressionElement<TStringType>> *expression)
    {
        AMUIDialogExpression<TStringType>::update(expression);
        //this->m_elements.push_back(&m_nullableCheckBox);
    }

} // AMDialogs