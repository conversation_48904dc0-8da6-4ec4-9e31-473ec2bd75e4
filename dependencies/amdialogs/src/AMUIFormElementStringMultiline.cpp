//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs//AMUIFormElementStringMultiline.h"
#include "amcore/AMAssert.h"
#include <cstring>
//#include "../cfg/config.h"
#include "../AMUIFormCallbacks.h"
#include "amdialogs/AMUIFormConfig.h"

namespace AMDialogs {

    AMUIFormElementStringMultiline::AMUIFormElementStringMultiline(const char *key)
        : AMUIFormElementString(key),
        m_lines(3)
    {
    }

    void AMUIFormElementStringMultiline::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        //int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elstring_%i", element_id);

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);
        if (ImGui::InputTextEx(
            label,
            "Nyní tvořte",
            m_buffer,
            m_length,
            ImVec2(width, ImGui::GetTextLineHeight() * m_lines + ImGui::GetStyle().FramePadding.y * 2.0f),
            ImGuiInputTextFlags_AllowTabInput | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_Multiline,
            m_callback,
            (void *) this
            )) {
            wasHitEnter();
        }
        ImGui::PopStyleColor();
        if (state == AMUIWidgetState::READONLY) {
            ImGui::EndDisabled();
        }
    }

    int AMUIFormElementStringMultiline::lines()
    {
        return m_lines;
    }

    void AMUIFormElementStringMultiline::setLines(int lines)
    {
        m_lines = lines;
    }
}
