//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementRadios.h"
//#include "amcore/AMAssert.h"
//#include <cstdio>
//#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {


    AMUIFormElementRadios::AMUIFormElementRadios(const char *key)
        : AMUIFormElement(key),
        m_value(0),
        m_items(nullptr)
    {
        m_sizeX = FLT_MAX;
    }

    
    AMUIFormElementRadios::~AMUIFormElementRadios() {
    }

    void AMUIFormElementRadios::init(const std::vector<std::string> &items)
    {
        m_items = &items;
    }

    void AMUIFormElementRadios::reset() {
        m_value = 0;
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    void AMUIFormElementRadios::updateState()
    {
        AMUIFormElement::updateState();
    }

    
    void AMUIFormElementRadios::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        if (!m_items) {
            return;
        }

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : this->m_state;

        if (state == AMUIWidgetState::READONLY) {
            ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
            ImGui::PushStyleColor(ImGuiCol_CheckMark, bgWidgetColors[(int) AMUIWidgetState::READONLY]);
        } else {
            ImGui::PushStyleColor(ImGuiCol_FrameBg, 0xFFFFFFFF);
        }
        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;
        char label[128];
        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == FLT_MAX ? max_width : m_sizeX;
        width = m_sizeX == FLT_MAX ? (max_width - (m_items->size() - 1) * style.ItemInnerSpacing.x) / m_items->size() : width;
        float pos_x = ImGui::GetCursorPosX();
        for(int i = 0; i < m_items->size(); i++) {
            snprintf(label, sizeof(label) - 1, "%s##elradios_%i", m_items->at(i).c_str(), element_id);
            ImGui::SetNextItemWidth(ImMax(1.0f, width));
            ImGui::SetCursorPosX(m_sizeX == -FLT_MAX ? pos_x : pos_x + i * (width + style.ItemInnerSpacing.x) );
            if (ImGui::RadioButton(label, &m_value, i)) {
                this->wasModified();
            }
            if ((i != m_items->size() -1) && m_sizeX != -FLT_MAX) {
                ImGui::SameLine();
            }
        }

        if (state == AMUIWidgetState::READONLY) {
            ImGui::PopStyleColor();
            ImGui::PopItemFlag();
        } else {
            ImGui::PopStyleColor();
        }
    }

    void AMUIFormElementRadios::loadDefault() {
        setValue(0);
    }


    void AMUIFormElementRadios::setValue(int value)
    {
        this->m_value = value;
        this->updateState();
    }

}