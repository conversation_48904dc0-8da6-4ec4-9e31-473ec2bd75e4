//
// Created by <PERSON><PERSON><PERSON> on 10.10.22.
//

#include "../AMUIFormElementEnumImage.h"
#include <cassert>
#include <cstdio>
#include <cstring>
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"
#include "ameliteui/AMEliteImageManager.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogDatetime.h"
#include "amdialogs/AMUIDialogDatetimeView.h"
#include "amui/AMUIApp.h"
#include "amdialogs/AMUIEnum.h"
#include "ameliteui/AMEliteResources.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumImage<TEnumType, TStringType>::AMUIFormElementEnumImage(const std::string key, bool mandatory, bool nullable, bool hidden)
        : AMUIFormElementEnum<TEnumType, TStringType>(key, mandatory, nullable, hidden),
        m_icons(nullptr)
    {
    }


    template<typename TEnumType, typename TStringType>
    AMUIFormElementEnumImage<TEnumType, TStringType>::~AMUIFormElementEnumImage()
    {
        destroyImageTable(this->m_enumId);

    }

    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumImage<TEnumType, TStringType>::init(int enumId, const std::string &strVoidParam)
    {
        AMUIFormElementEnum<TEnumType, TStringType>::init(enumId, strVoidParam);
        if (!m_icons) {
            prepareImageTable(enumId);
            m_icons = imageTable(enumId);
        }
    }



    template<typename TEnumType, typename TStringType>
    void AMUIFormElementEnumImage<TEnumType, TStringType>::drawIcon(int n)
    {
        if (m_icons && m_icons->size() >= n && n > 0) {
            ImGui::Image((ImTextureID) (long) m_icons->at(n-1), ImVec2(20.0f, 20.0f), ImVec2(0, 0), ImVec2(1, 1));
            ImGui::SameLine();
        }
    }

    template<typename TEnumType, typename TStringType>
    bool AMUIFormElementEnumImage<TEnumType, TStringType>::drawIconFE(int n, unsigned int color)
    {
        ImGuiWindow* window = ImGui::GetCurrentWindow();
        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;
        if (m_icons && m_icons->size() >= n && n > 0) {
            ImVec2 pos = ImGui::GetCursorPos();
            const ImRect bb(ImVec2(window->DC.CursorPos.x, window->DC.CursorPos.y), ImVec2(ImGui::GetFrameHeight()+ window->DC.CursorPos.x, ImGui::GetFrameHeight() + window->DC.CursorPos.y) );
            window->DrawList->AddRectFilled(bb.Min, bb.Max, color);
            float off = (ImGui::GetFrameHeight() - 20.0) / 2.0;
            window->DrawList->AddImage(
                (ImTextureID) (long)m_icons->at(n-1),
                ImVec2(bb.Min.x + off, bb.Min.y + off),
                ImVec2(bb.Min.x + 20.0f + off, bb.Min.y + 20.0f + off),
                ImVec2(0, 0),
                ImVec2(1, 1),
                0xFF000000);
            ImGui::SetCursorPos(ImVec2(pos.x + 20 + 2 * style.ItemInnerSpacing.x, pos.y));
            const float button_size = ImGui::GetFrameHeight();
            const float max_width = ImGui::CalcItemWidth();
            float width = this->m_sizeX == -FLT_MAX ? max_width : this->m_sizeX;
            ImGui::SetNextItemWidth(ImMax(1.0f, width - 20.0f - 2 * style.ItemInnerSpacing.x + (this->m_controllerFactory ? - button_size - style.ItemInnerSpacing.x : 0.0f)));
            return true;
        }
        return false;
    }



}