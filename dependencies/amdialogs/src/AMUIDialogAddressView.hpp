//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#include "amdialogs/AMUIDialogAddressView.h"
#include "imgui/imgui.h"
#include "amdialogs/AMUIDialogAddress.h"

namespace AMDialogs {

    template<typename TStringType>
    AMUIDialogAddressView<TStringType>::AMUIDialogAddressView()
        : AMUIDialogView()
    {

    }

    template<typename TStringType>
    void AMUIDialogAddressView<TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogAddress<TStringType> *dlg = static_cast<AMUIDialogAddress<TStringType> *>(m_dialog);
            float posx = ImGui::GetCursorPosX();
            float width = ImGui::GetContentRegionAvail().x;
            auto style = ImGui::GetStyle();
            ImGui::Text("Místo:");
            dlg->elements()[0]->render(false, dlg->id() * 6500);
            ImGui::Text("Ulice:");
            ImGui::SameLine();
            ImGui::SetCursorPosX(posx + width - style.ItemSpacing.x - dlg->elements()[2]->sizeX() - dlg->elements()[3]->sizeX());
            ImGui::Text("č.p.");
            ImGui::SameLine();
            ImGui::SetCursorPosX(posx + width - dlg->elements()[3]->sizeX());
            ImGui::Text("č.or.");
            dlg->elements()[1]->setSizeX(width - 2.0f * style.ItemSpacing.x - dlg->elements()[2]->sizeX() - dlg->elements()[3]->sizeX());
            dlg->elements()[1]->render(false, dlg->id() * 6500 + 1);
            ImGui::SameLine();
            ImGui::SetCursorPosX(posx + width - style.ItemSpacing.x - dlg->elements()[2]->sizeX() - dlg->elements()[3]->sizeX());
            dlg->elements()[2]->render(false, dlg->id() * 6500 + 2);
            ImGui::SameLine();
            ImGui::SetCursorPosX(posx + width - dlg->elements()[3]->sizeX());
            dlg->elements()[3]->render(false, dlg->id() * 6500 + 3);
            ImGui::Text("Město:");
            dlg->elements()[4]->render(false, dlg->id() * 6500 + 4);
            ImGui::Text("PSČ:");
            dlg->elements()[5]->render(false, dlg->id() * 6500 + 5);
            ImGui::Text("Stát:");
            dlg->elements()[6]->render(false, dlg->id() * 6500 + 6);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs