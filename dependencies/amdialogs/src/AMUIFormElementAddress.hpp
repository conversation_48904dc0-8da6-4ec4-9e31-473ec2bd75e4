//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "amdialogs/AMUIFormElementAddress.h"
#include "amcore/AMAssert.h"
#include <cstring>
#include "amdialogs/AMUIFormCallbacks.h"
#include "ameliteui/AMEliteImageManager.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIFormResources.h"
#include "amdialogs/AMUIDialogAddress.h"
#include "amdialogs/AMUIFormConfig.h"
#include "amdialogs/AMUIDialogAuthorTagView.h"


namespace AMDialogs {

    template<typename TStringType>
    AMUIFormElementAddress<TStringType>::AMUIFormElementAddress(const char *key)
        : AMUIFormElement(key),
          m_buffer(nullptr),
          m_dialog(nullptr),
          m_wildcard('\0'),
          m_dialogPositionOfffsetX(0.0f),
          m_preventRefreshDialog(0)
    {
          m_callback = AMUIInputCallbackAddress<TStringType>;
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::init()
    {
        AMAssert(m_length > 0);
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[m_length + 1];
        AMAssert(m_buffer);
        reset();

        bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
            MEMIMG(Login_png), &m_icon, nullptr, nullptr
                                                       );
        AMAssert(rv);
    }

    template<typename TStringType>
    AMUIFormElementAddress<TStringType>::~AMUIFormElementAddress()
    {
        if (m_dialog) {
            delete m_dialog;
        }
        if (m_buffer) {
            delete[] m_buffer;
        }
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::reset()
    {
        AMAssert(m_buffer);
        AMAssert(m_length > 0);
        m_buffer[0] = '\0';
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::EMPTY;
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::updateState()
    {
        AMUIFormElement::updateState();
        AMCore::AMAddress<TStringType> a;
        if (m_state == AMUIWidgetState::READONLY) {
            return;
        }
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::EMPTY;
        } else {
            TStringType is(m_buffer);
            AMUIParseResult res = AMUIParseAddress(is, &a, m_wildcard);
            m_state = res != AMUIParseResult::FAIL ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
        }
        if (m_preventRefreshDialog) {
            m_preventRefreshDialog--;
            return;
        }
        if (m_dialog) {
            m_dialog->place().setValue(a.place.c_str());
            m_dialog->street().setValue(a.street.c_str());
            m_dialog->parcelNum().setValue(a.parcelNum);
            m_dialog->orientationNum().setValue(a.orientationNum.c_str());
            m_dialog->city().setValue(a.city.c_str());
            m_dialog->zip().setValue(a.zip.c_str());
            m_dialog->country().setValue(a.country.c_str());
        }
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::setValue(const char *value)
    {
        AMAssert(m_buffer);
        snprintf(m_buffer, m_length, "%s", value);
        m_buffer[m_length] = '\0';
        updateState();
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;

        const float button_size = ImGui::GetFrameHeight();

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width - button_size - style.ItemInnerSpacing.x));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elstring_%i", element_id);

        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);

        if (ImGui::InputText(
            label, m_buffer, m_length,
            flags | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackEdit | ImGuiInputTextFlags_CallbackAlways
            , m_callback
            , (void *) this
            )) {
            wasHitEnter();
        }
        ImVec2 rmin = ImGui::GetItemRectMin();

        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y = 0;

        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "##elicon_%i", element_id);
        ImGui::PushID(label);
        if (ImGui::ImageButton(label, (ImTextureID)(long)m_icon, ImVec2(24.0f, 24.0f),  ImVec2(0,0),  ImVec2(1,1)))
        {
            if(!m_dialog) {
                AMUIDialogAddress<TStringType> *dlg = new AMUIDialogAddress<TStringType>();
                m_dialog = dlg;
                dlg->init(
                    std::string(m_label),
                    std::bind(&AMUIFormElementAddress<TStringType>::onDialogClose, this, std::placeholders::_1),
                    std::bind(&AMUIFormElementAddress<TStringType>::onDialogChange, this, std::placeholders::_1),
                    m_wildcard
                    );
                std::string is(m_buffer);
                AMCore::AMAddress<std::string> a;
                AMUIParseAddress(is, &a, m_wildcard);
                m_dialog->place().setValue(a.place.c_str());
                m_dialog->street().setValue(a.street.c_str());
                m_dialog->parcelNum().setValue(a.parcelNum);
                m_dialog->orientationNum().setValue(a.orientationNum.c_str());
                m_dialog->city().setValue(a.city.c_str());
                m_dialog->zip().setValue(a.zip.c_str());
                m_dialog->country().setValue(a.country.c_str());
                auto v = static_cast<AMUIDialogAddressView<TStringType> *>(dlg->view());
                ImVec2 rmax = ImGui::GetItemRectMax();
                ImVec2 pos(rmin.x - backup_frame_padding.x + m_dialogPositionOfffsetX, rmax.y);
                ImVec2 sz(std::max(rmax.x - rmin.x + 2 * backup_frame_padding.x - m_dialogPositionOfffsetX, 250.0f), -FLT_MIN);
                v->setOrigin(pos);
                v->setSize(sz);
            }
        }
        ImGui::PopID();

        if (state == AMUIWidgetState::READONLY)
            ImGui::EndDisabled();
        style.FramePadding = backup_frame_padding;
        ImGui::PopStyleColor();
        if (m_dialog) {
            m_dialog->view()->render();
        }
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::onDialogChange(AMUIDialog *dlg)
    {
        AMAssert(dlg == m_dialog);
        m_state = m_dialog->state();

        std::ostringstream os;
        os.imbue(std::locale("C"));
        if (m_dialog->place().buffer()[0] != '\0') {
            os << m_dialog->place().buffer() << ": ";
        }
        os << m_dialog->street().buffer() << " ";
        if (strlen(m_dialog->parcelNum().buffer()) != 0) {
            os << m_dialog->parcelNum().value();
        }
        if (strlen(m_dialog->orientationNum().buffer()) != 0) {
            os << "/";
        }
        os << m_dialog->orientationNum().buffer();
        if (strlen(m_dialog->city().buffer()) != 0 || strlen(m_dialog->zip().buffer()) != 0 || strlen(m_dialog->country().buffer()) != 0) {
            os << ", ";
            os << m_dialog->city().buffer();
            if (strlen(m_dialog->zip().buffer()) != 0 || strlen(m_dialog->country().buffer()) != 0) {
                os << ", ";
                os << m_dialog->zip().buffer();
                if (strlen(m_dialog->country().buffer()) != 0) {
                    os << ", ";
                    os << m_dialog->country().buffer();
                }
            }
        }
        strncpy(m_buffer, os.str().c_str(), m_length - 1);
        m_buffer[m_length - 1] = '\0';
        m_preventRefreshDialog++;
        wasModified();
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::onDialogClose(AMUIDialog *)
    {
        if (m_changeRequestByEnter) {
            wasHitEnter();
        }
        m_dialog = nullptr;
    }

    template<typename TStringType>
    void AMUIFormElementAddress<TStringType>::loadDefault() {
        if (m_buffer && m_length > 0) {
            m_buffer[0] = '\0';
        }
        updateState();
    }
}
