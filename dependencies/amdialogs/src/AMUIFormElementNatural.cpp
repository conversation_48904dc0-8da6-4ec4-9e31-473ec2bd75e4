//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#include "../AMUIFormElementNatural.h"
#include "amcore/AMAssert.h"
#include <cstdio>
#include "amui/AMUIConfig.h"
#include "../AMUIFormCallbacks.h"
#include "imgui/imgui_internal.h"

namespace AMDialogs {

    const int FORM_ELEMENT_INT_BUFFER_SIZE = 32;

    AMUIFormElementNatural::AMUIFormElementNatural(const char *key)
        : AMUIFormElement(key),
          m_buffer(nullptr),
          m_value(0),
          m_wildcard('\0')
    {
          m_callback = AMUIInputCallbackNatural;
    }

    void AMUIFormElementNatural::init()
    {
        m_length = FORM_ELEMENT_INT_BUFFER_SIZE;
        if (m_buffer) {
            delete[] m_buffer;
        }
        m_buffer = new char[FORM_ELEMENT_INT_BUFFER_SIZE + 1];
        AMAssert(m_buffer);
        reset();
    }

    AMUIFormElementNatural::~AMUIFormElementNatural()
    {
        if (m_buffer) {
            delete[] m_buffer;
        }
    }

    void AMUIFormElementNatural::reset()
    {
        AMAssert(m_buffer);
        m_value = 0;
        snprintf(m_buffer, FORM_ELEMENT_INT_BUFFER_SIZE, "%i", m_value);
        m_state = m_state == AMUIWidgetState::READONLY ? AMUIWidgetState::READONLY : AMUIWidgetState::FILLED;
    }

    void AMUIFormElementNatural::updateState()
    {
        AMUIFormElement::updateState();
        AMAssert(m_length > 0);
        if (m_buffer[0] == '\0') {
            m_state = AMUIWidgetState::INVALID;
        } else {
            if (m_state == AMUIWidgetState::READONLY) {
                return;
            }
            std::istringstream is(m_buffer);
            AMUIParseResult res = AMUIParseNatural(is, &m_value, (unsigned int*)nullptr, nullptr, m_wildcard);
            m_state = res != AMUIParseResult::FAIL && is.eof() ? AMUIToWidgetState(res) : AMUIWidgetState::INVALID;
        }
    }

    void AMUIFormElementNatural::setValue(unsigned int value) {
        AMAssert(m_buffer);
        m_value = value;
        snprintf(m_buffer, FORM_ELEMENT_INT_BUFFER_SIZE, "%i", m_value);
        updateState();
    }

    void AMUIFormElementNatural::setValue(const char *value)
    {
        AMAssert(m_buffer);
        strncpy(m_buffer, value,m_length);
        m_buffer[m_length - 1] = '\0';
        updateState();
    }

    void AMUIFormElementNatural::render(bool formGrayed, int element_id)
    {
        AMUIFormElement::render(formGrayed, element_id);

        AMUIWidgetState state = formGrayed ? AMUIWidgetState::READONLY : m_state;

        int flags = state == AMUIWidgetState::READONLY ? ImGuiInputTextFlags_ReadOnly : 0;

        ImGuiContext& g = *GImGui;
        ImGuiStyle& style = g.Style;
        const float button_size = ImGui::GetFrameHeight();

        const float max_width = ImGui::CalcItemWidth();//ImGui::GetWindowContentRegionWidth();
        float width = m_sizeX == -FLT_MAX ? max_width : m_sizeX;
        ImGui::SetNextItemWidth(ImMax(1.0f, width - (button_size + style.ItemInnerSpacing.x) * 2.0f));
        if (state == AMUIWidgetState::READONLY) {
            ImGui::BeginDisabled();
        }
        if (m_align == CENTER) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width) / 2);
        } else if (m_align == RIGHT) {
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + (max_width - width));
        }

        char label[128];
        snprintf(label, sizeof(label) - 1, "##elnatural_%i", element_id);
        ImGui::PushStyleColor(ImGuiCol_FrameBg, bgWidgetColors[(int) state]);
        //ImGui::SetNextItemWidth(ImMax(1.0f, ImGui::CalcItemWidth() - (button_size + style.ItemInnerSpacing.x) * 2));
        if (ImGui::InputText(
            label, m_buffer, m_length,
            flags | ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackEdit |
            ImGuiInputTextFlags_CallbackCharFilter | ImGuiInputTextFlags_CallbackAlways
            , m_callback
            , (void *) this
                        )) {
            wasHitEnter();
        }
        ImGui::PopStyleColor();
        // Step buttons

        const ImVec2 backup_frame_padding = style.FramePadding;
        style.FramePadding.x = style.FramePadding.y;
        ImGuiButtonFlags button_flags = 0;

        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "+##elnatural_+%i", element_id);
        if (ImGui::ButtonEx(label, ImVec2(button_size, button_size), button_flags))
        {
            setValue(m_value  + 1);
            wasModified();
        }
        ImGui::SameLine(0, style.ItemInnerSpacing.x);
        snprintf(label, sizeof(label) - 1, "-##elnaturar_-%i", element_id);
        if (ImGui::ButtonEx(label, ImVec2(button_size, button_size), button_flags))
        {
            setValue(m_value  - 1);
            wasModified();
        }
        if (flags & ImGuiInputTextFlags_ReadOnly)
            ImGui::EndDisabled();

        /*
        const char* label_end = FindRenderedTextEnd(label);
        if (label != label_end)
        {
            SameLine(0, style.ItemInnerSpacing.x);
            TextEx(label, label_end);
        }*/
        style.FramePadding = backup_frame_padding;
    }
}
