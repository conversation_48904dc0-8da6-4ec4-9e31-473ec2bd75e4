//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//
#include "amdialogs/AMUIDialogLoginView.h"
#include "imgui/imgui.h"
#include "imgui/imgui_internal.h"
#include "amdialogs/AMUIDialogLogin.h"

namespace AMDialogs {

    template<typename TEnumType, typename TStringType>
    AMUIDialogLoginView<TEnumType, TStringType>::AMUIDialogLoginView()
        : AMUIDialogView()
    {

    }

    template<typename TEnumType, typename TStringType>
    void AMUIDialogLoginView<TEnumType, TStringType>::render()
    {
        if (renderBegin()) {

            AMUIDialogLogin<TEnumType, TStringType> *dlg
                = static_cast<AMUIDialogLogin<TEnumType, TStringType> *>(m_dialog);
            if (dlg->icon() != std::numeric_limits<GLuint>::max()) {
                ImVec2 pos = ImGui::GetCursorPos();
                AMUI::AMUIRect<float> r = dlg->iconRect();
                ImGuiContext& g = *ImGui::GetCurrentContext();
                ImGuiWindow* window = g.CurrentWindow;
                auto posw = window->InnerRect.Min;
                window->DrawList->AddImage(
                    (ImTextureID)(long)dlg->icon(),
                    posw + ImVec2(r.left, r.top) + g.Style.FramePadding,
                    posw + ImVec2(r.right, r.bottom) + g.Style.FramePadding
                                          );
                ImGui::SetCursorPosY(pos.y + r.bottom);
            }
            if (!dlg->errorText().empty()) {
                ImGui::PushFont(AMUI::BoldFont);
                ImGui::PushStyleColor(ImGuiCol_Text, AMUI::foregroundErrorColor);
                ImGui::Text("%s",dlg->errorText().c_str());
                ImGui::PopStyleColor();
                ImGui::PopFont();
            }
            ImGui::Text("Uživatelské jméno:");
            dlg->elements()[0]->render(false, dlg->id() * 6800);
            ImGui::Text("Heslo:");
            dlg->elements()[1]->render(false, dlg->id() * 6800 + 1);
            renderEnd(true);
        } else {
            renderEnd(false);
        }
    }
} // AMDialogs