//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTS_H
#define SAW_ALL_AMUIFORMELEMENTS_H

#include "AMUIFormElementString.h"
#include "AMUIFormElementStringMultiline.h"
#include "AMUIFormElementInteger.h"
#include "AMUIFormElementFloat.h"
#include "AMUIFormElementNatural.h"
#include "AMUIFormElementBool.h"
#include "AMUIFormElementBoolTristate.h"
#include "AMUIFormElementStringNullable.h"
#include "AMUIFormElementDatetime.h"
#include "AMUIFormElementAuthorTag.h"
#include "AMUIFormElementAddress.h"
#include "AMUIFormElementAddressNullable.h"
#include "AMUIFormElementEnum.h"
#include "AMUIFormElementEnumImage.h"
#include "AMUIFormElementEnumMap.h"
#include "AMUIFormElementEnumMapImage.h"
#include "amuitable/AMUIFormElementEnumBig.h"
#include "amuitable/AMUIFormElementEnumName.h"
#include "amuitable/AMUIFormElementEnumDepended.h"
#include "AMUIFormElementName.h"
#include "AMUIFormElementNameHidden.h"

#endif //SAW_ALL_AMUIFORMELEMENTS_H


