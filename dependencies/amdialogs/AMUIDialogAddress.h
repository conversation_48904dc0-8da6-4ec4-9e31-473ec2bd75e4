//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGADDRESS_H
#define SAW_AMUIDIALOGADDRESS_H

#include "amdialogs/AMUIDialog.h"
#include "AMUIFormElementString.h"
#include "AMUIFormElementNatural.h"

namespace AMDialogs {

    template<typename TStringType>
    class AMUIDialogAddress:public  AMUIDialog {
    public:
        AMUIDialogAddress();
        void init(std::string name, std::function<void(AMUIDialog *)>, std::function<void(AMUIDialog *)>, char wildcard);
        std::vector<AMUIFormElement *> &elements() override;
        AMUIFormElementString &place() {return m_place;};
        AMUIFormElementString &street() {return  m_street;};
        AMUIFormElementNatural &parcelNum() {return m_parcelNum;};
        AMUIFormElementString &orientationNum() {return m_orientationNum;};
        AMUIFormElementString &city() {return m_city;};
        AMUIFormElementString &zip() {return m_zip;};
        AMUIFormElementString &country() {return m_country;};

        char wildcard() {return m_wildcard;};
        void setWildcard(char wildcard) {m_wildcard = wildcard;}
    protected:
        AMUIFormElementString m_place;
        AMUIFormElementString m_street;
        AMUIFormElementNatural m_parcelNum;
        AMUIFormElementString m_orientationNum;
        AMUIFormElementString m_city;
        AMUIFormElementString m_zip;
        AMUIFormElementString m_country;
        std::vector<AMUIFormElement *> m_elements;
        char m_wildcard;
    };

} // AMDialogs

#include "src/AMUIDialogAddress.hpp"

#endif //SAW_AMUIDIALOGADDRESS_H
