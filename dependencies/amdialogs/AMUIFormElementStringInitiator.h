//
// Created by <PERSON><PERSON><PERSON> on 14.9.22.
//

#ifndef SAW_ALL_AMUIFORMELEMENTSTRINGINITIATOR_H
#define SAW_ALL_AMUIFORMELEMENTSTRINGINITIATOR_H


#include "AMUIFormElementString.h"

namespace AMDialogs {

    class AMUIFormElementStringInitiator : public AMUIFormElementString {
    public:
        AMUIFormElementStringInitiator(const char *key, bool mandatory = false, bool nullable = false, bool hidden = false);

        ~AMUIFormElementStringInitiator();

        void setValue() override;
        void setValue(const char *) override;

        void setValueString(std::string value) override;

        void reset() override;

        bool alreadySet() {return m_alreadySet;}

        void loadDefault() override;

    protected:
        bool m_alreadySet;
    };

}
#endif //SAW_ALL_AMUIFORMELEMENTSTRINGINITIATOR_H
