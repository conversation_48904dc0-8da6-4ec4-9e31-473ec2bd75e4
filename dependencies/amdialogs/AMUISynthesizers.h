//
// Created by <PERSON><PERSON><PERSON> on 9.1.23.
//

#ifndef AMCORE_AMUISYNTHESIZERS_H
#define AMCORE_AMUISYNTHESIZERS_H

#include <string>
#include <cstring>
#include <sstream>
#include <algorithm>
#include <iomanip>
#include "amcore/AMNullable.h"
#include "AMUIParsers.h"
#include <ctime>
//#include "date/include/date/tz.h"

namespace AMDialogs {

    template<typename A, typename B>
    std::pair<B,A> _flip_pair(const std::pair<A,B> &p)
    {
        return std::pair<B,A>(p.second, p.first);
    }

    template<typename A, typename B>
    std::map<B,A> _flip_map(const std::map<A,B> &src)
    {
        std::map<B,A> dst;
        std::transform(src.begin(), src.end(), std::inserter(dst, dst.begin()), _flip_pair<A,B>);
        return dst;
    }

    template<typename TSourceType>
    std::string AMUISynthesizeNatural(TSourceType natural)
    {
        return std::to_string(natural);
    }
    template<typename TSourceType>
    std::string AMUISynthesizeInteger(TSourceType integer)
    {
        return std::to_string(integer);
    }
    template<typename TSourceType>
    std::string AMUISynthesizeInteger(TSourceType min, TSourceType max, char wildcard)
    {
        int wc = 0;
        TSourceType rest;
        do {
            if (min == 0) {
                break;
            }
            if (max == 0) {
                break;
            }
            if (min == max) {
                break;
            }
            if (min % 10 != 0) {
                break;
            }
            if (max % 10 != 9) {
                break;
            }
            min /= 10;
            max /= 10;
            wc++;
        } while(1);
        if (min == 0 && wc != 0) {
            return std::string(wc, wildcard);
        }
        return std::to_string(min) + std::string(wc, wildcard);
    }
    template<typename TSourceType>
    std::string AMUISynthesizeFloat(TSourceType number)
    {
        return std::to_string(number);
    }
    template<typename TSourceType>
    std::string AMUISynthesizeNatural(AMCore::AMNullable<TSourceType> &natural)
    {
        return natural.isNull() ? AMCore::AMNullString : std::to_string(natural.get());
    }
    template<typename TSourceType>
    std::string AMUISynthesizeInteger(AMCore::AMNullable<TSourceType> &integer)
    {
        return integer.isNull() ? AMCore::AMNullString : std::to_string(integer.get());
    }
    template<typename TSourceType>
    std::string AMUISynthesizeFloat(AMCore::AMNullable<TSourceType> &number)
    {
        return number.isNull() ? AMCore::AMNullString : std::to_string(number.get());
    }
    /*
    template<typename TStringType>
    TStringType AMUISynthesizeDatetime(const char *format, const std::tm *datetime)
    {
        std::string buffer;
        buffer.resize(strlen(format) + 64);
        int len = strftime(&buffer[0], buffer.size(), format, datetime);
        while (len == 0) {
            buffer.resize(buffer.size()*2);
            len = strftime(&buffer[0], buffer.size(), format, datetime);
        }
        buffer.resize(len);
        return TStringType(buffer);
    }*/
    template<typename TStringType>
    TStringType AMUISynthesizeDatetime(const char *format, const AMCore::AMNullable<std::tm> *datetime)
    {
        if (datetime->isNull()) {
            return AMCore::AMNullString;
        }
        std::tm dtm = datetime->get();
        return AMUISynthesizeDatetime<TStringType>(format, &dtm);
    }
    template<typename TStringType>
    TStringType AMUISynthesizeDatetime(std::string format, const std::tm *min, const std::tm *max = nullptr, char wildcard = '\0')
    {
        if (!max) {
            max = min;
        }
        bool wc;
        std::string year = AMUISynthesizeInteger(min->tm_year + 1900, max->tm_year + 1900, wildcard);
        wc = min->tm_mon != max->tm_mon;
        std::string month = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_mon + 1, max->tm_mon + 1, wildcard);
        wc = wc || (min->tm_mday != max->tm_mday);
        std::string day = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_mday, max->tm_mday , wildcard);
        wc = wc || (min->tm_hour != max->tm_hour);
        std::string hour = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_hour, max->tm_hour , wildcard);
        wc = wc || (min->tm_min != max->tm_min);
        std::string mins = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_min, max->tm_min, wildcard);
        wc = wc || (min->tm_sec != max->tm_sec);
        std::string sec = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_sec, max->tm_sec, wildcard);
        std::ostringstream os;
        for (std::string::const_iterator it = format.cbegin(); it != format.cend();  it++) {
            if (*it != '%') {
                os << *it;
                continue;
            }
            it++;
            switch(*it) {
                case 'Y': os << std::setw(4) << std::setfill('0') << year;break;
                case 'm': os << std::setw(2) << std::setfill('0') <<  month;break;
                case 'd': os << std::setw(2) << std::setfill('0') <<  day;break;
                case 'H': os << std::setw(2) << std::setfill('0') <<  hour;break;
                case 'M': os << std::setw(2) << std::setfill('0') <<  mins;break;
                case 'S': os << std::setw(2) << std::setfill('0') <<  sec;break;
                default:
                    os << '%' << *it;
            }
        }
        return os.str();
    }
    template<typename TStringType>
    TStringType AMUISynthesizeDatetime(std::string format, const AMCore::AMDatetimeus *min, const AMCore::AMDatetimeus *max = nullptr, char wildcard = '\0')
    {
        if (!max) {
            max = min;
        }
        bool wc;
        std::string year = AMUISynthesizeInteger(min->tm_year + 1900, max->tm_year + 1900, wildcard);
        wc = min->tm_mon != max->tm_mon;
        std::string month = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_mon + 1, max->tm_mon + 1, wildcard);
        wc = wc || (min->tm_mday != max->tm_mday);
        std::string day = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_mday, max->tm_mday , wildcard);
        wc = wc || (min->tm_hour != max->tm_hour);
        std::string hour = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_hour, max->tm_hour , wildcard);
        wc = wc || (min->tm_min != max->tm_min);
        std::string mins = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_min, max->tm_min, wildcard);
        wc = wc || (min->tm_sec != max->tm_sec);
        std::string sec = wc ? std::string(2, wildcard) : AMUISynthesizeInteger(min->tm_sec, max->tm_sec, wildcard);
        wc = wc || (min->usec != max->usec);
        std::string usec = wc ? std::string(6, wildcard) : AMUISynthesizeInteger(min->usec, max->usec, wildcard);
        std::ostringstream os;
        for (std::string::const_iterator it = format.cbegin(); it != format.cend();  it++) {
            if (*it != '%') {
                os << *it;
                continue;
            }
            it++;
            switch(*it) {
                case 'Y': os << std::setw(4) << std::setfill('0') <<  year;break;
                case 'm': os << std::setw(2) << std::setfill('0') <<  month;break;
                case 'd': os << std::setw(2) << std::setfill('0') <<  day;break;
                case 'H': os << std::setw(2) << std::setfill('0') <<  hour;break;
                case 'M': os << std::setw(2) << std::setfill('0') <<  mins;break;
                case 'S': os << std::setw(2) << std::setfill('0') <<  sec;break;
                case 'Q': os << std::setw(6) << std::setfill('0') <<  usec;break;
                default:
                    os << '%' << *it;
            }
        }
        return os.str();
    }

    template<typename TStringType>
    TStringType AMUISynthesizeDatetime(std::string format, const AMCore::AMDatetimeus *min, int timezoneOffset)
    {
        char s[80];
        AMCore::AMDatetimeus t = *min;
        t.tm_sec -= timezoneOffset;
        mktime(&t);
        return AMUISynthesizeDatetime<TStringType>(format, &t, &t, '\0');
    }
    template<typename TEnumType, typename TStringType>
    TStringType AMUISynthesizeName(const AMUINameFlashBack<TEnumType, TStringType> *src, char wildcardMulti = '\0')
    {
        if (!src) {
            return "";
        }
        std::ostringstream os;
        os.imbue(std::locale("C"));
        if (wildcardMulti != '\0') {
            if (src->degreeBefore.isNull() && src->name.empty() && src->surname.empty() && src->degreeAfter.isNull()) {
                return "";
            }
            if (src->degreeBefore.isNull()) {
                os << wildcardMulti << ' ';
            } else {
                const TStringType *sdb = AMDialogs::twoWayTable<TEnumType, TStringType>(AMDialogs::IDegreeBeforeTableId, src->strVoidParam())->findB(src->degreeBefore.get());
                if (sdb && !sdb->empty()) {
                    os << *sdb << ' ';
                }
            }
            if (src->name.empty()) {
                os << wildcardMulti << ' ';
            } else {
                os << src->name << ' ';
            }
            if (src->name.find(' ') != std::string::npos || src->surname.find(' ') != std::string::npos) {
                os << ' ';
            }
            if (src->surname.empty()) {
                os << wildcardMulti << ' ';
            } else {
                os << src->surname << ' ';
            }
            if (src->degreeAfter.isNull()) {
                os << wildcardMulti;
            } else {
                const TStringType *sda = AMDialogs::twoWayTable<TEnumType, TStringType>(AMDialogs::IDegreeAfterTableId, src->strVoidParam())->findB(src->degreeAfter.get());
                if (sda && !sda->empty()) {
                    os << *sda;
                }
            }
        } else {
            if (!src->degreeBefore.isNull()) {
                const TStringType *sdb = AMDialogs::twoWayTable<TEnumType, TStringType>(AMDialogs::IDegreeBeforeTableId, src->strVoidParam())->findB(src->degreeBefore.get());
                if (sdb && !sdb->empty()) {
                    os << *sdb;
                    if (!src->name.empty()) os << ' ';
                }
            }
            if (!src->name.empty()) os << src->name;
            if (src->name.find(' ') != std::string::npos || src->surname.find(' ') != std::string::npos) {
                os << ' ';
            }
            if (!src->surname.empty()) os << ' ' << src->surname;
            if (!src->degreeAfter.isNull()) {
                const TStringType *sda = AMDialogs::twoWayTable<TEnumType, TStringType>(AMDialogs::IDegreeAfterTableId, src->strVoidParam())->findB(src->degreeAfter.get());
                if (sda && !sda->empty()) {
                    os << ' ' << *sda;
                }
            }
        }
        return os.str();
    }

    AMCore::AMDatetimeus subtractTime(AMCore::AMDatetimeus &t, int secs);
    AMCore::AMDatetimeus addTime(AMCore::AMDatetimeus &t, int secs);

    template<typename TStringType>
    TStringType AMUISynthesizeDatetimeGMT(std::string format, const std::tm *t)
    {
        AMCore::AMDatetimeus tt = *t;
        tt.usec = 0;
        std::tm tl = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};
        std::time_t ttt = mktime(&tt);
        tl = *localtime(&ttt);
        tt = *t;
        char buff [80];
        tt = subtractTime(tt, tl.tm_gmtoff);
        strftime(buff, sizeof(buff) / sizeof(char), format.c_str(), &tt);
        return TStringType(buff);
    }

    template<typename TStringType>
    TStringType AMUISynthesizeDatetimeGMT(std::string format, const AMCore::AMDatetimeus *t)
    {
        AMCore::AMDatetimeus tt = *t;
        std::tm tl = {0,0,0,0,0,0,0, 0, 0,0LL, nullptr};
        std::time_t ttt = mktime(&tt);
        tl = *localtime(&ttt);
        tt = *t;
        tt = subtractTime(tt, tl.tm_gmtoff);
        return AMUISynthesizeDatetime<TStringType>(format, &tt, &tt, '\0');
    }
    inline std::string AMUISynthesizeBool(const std::string trueString, const std::string falseString, bool b)
    {
        return b ? trueString : falseString;
    }
    inline std::string AMUISynthesizeBool(const std::string trueString, const std::string falseString, AMCore::AMNullable<bool> &b)
    {
        if (b.isNull()) {
            return AMCore::AMNullString;
        }
        return b.get() ? trueString : falseString;
    }
    template<typename TSourceType>
    inline std::string AMUISynthesizeEnum(const AMCore::AMTwoWayTable<TSourceType, std::string> &table, TSourceType enumerated)
    {
        return *table.findB(enumerated)->second;
    }
    template<typename TSourceType>
    inline std::string AMUISynthesizeEnum(const AMCore::AMTwoWayTable<TSourceType, std::string> &table, AMCore::AMNullable<TSourceType> enumerated)
    {
        if (enumerated.isNull()) {
            return AMCore::AMNullString;
        }
        return *table.findB(enumerated.get())->second;
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUISynthesizeEnumLine(const AMCore::AMTwoWayTable<TEnumType, TStringType> *table, const std::map<TEnumType, int> &line, bool null = false)
    {
        if (!table) {
            return "";
        }
        std::ostringstream os;
        bool first = true;
        if (null) {
            os<<AMCore::AMNullString;
            first= false;
        }
        std::map<int, TEnumType> rline = _flip_map(line);

        for(const auto &it: rline) {
            const auto itt = table->findB(it.second);
            if (itt == nullptr) {
                return std::string();
            }
            if (first) {
                first = false;
            } else {
                os<<"; ";
            }
            os<< (*itt == TStringType("") ? TStringType("''") : *itt);
        }
        return os.str();
    }

    template<typename TEnumType, typename TStringType>
    std::string AMUISynthesizeEnumMapLine(const AMCore::AMTwoWayTable<TEnumType, TStringType> *table, const std::map<TEnumType, int> &line, const std::map<int, std::map<TEnumType, int>> &lineEx)
    {
        if (!table) {
            return "";
        }
        std::ostringstream os;
        bool first = true;
        std::map<int, TEnumType> rline = _flip_map(line);

        for(const auto &it: rline) {
            const auto itt = table->findB(it.second);
            if (itt == nullptr) {
                return std::string();
            }
            if (first) {
                first = false;
            } else {
                os<<"; ";
            }
            os<< (*itt == TStringType("") ? TStringType("''") : *itt);
        }
        for(const auto &it: lineEx) {
            if (first) {
                first = false;
            } else {
                os<<", ";
            }
            bool firste = true;
            for(const auto &ite: it.second) {
                const auto itt = table->findB(ite.second);
                if (itt == nullptr) {
                    return std::string();
                }
                if (firste) {
                    firste = false;
                } else {
                    os<<"; ";
                }
                os << (*itt == TStringType("") ? TStringType("''") : *itt);
            }
        }
        return os.str();
    }

    template<typename TEnumType, typename TStringType>
    inline TStringType AMUISynthesizeExpression(AMCore::AMDataType _type, typename std::vector<AMUIParseExpressionElement<TStringType> > &parsed)
    {
        if (_type == AMCore::AMDataType_address
            || _type == AMCore::AMDataType_address_nullable
            || _type == AMCore::AMDataType_string_nullable) {
            _type = AMCore::AMDataType_string;
        }
        std::ostringstream os;
        bool first = true;
        for(const auto &it: parsed) {
            if (first) {
                first = false;
            } else {
                os<<' ';
            }
            TStringType s = it.inputValue;
            switch(it.operation) {
                case AMUIParseExpressionElement<TStringType>::Operations::NOTHING: break;
                case AMUIParseExpressionElement<TStringType>::Operations::BRACEOPEN: os<<'(';break;
                case AMUIParseExpressionElement<TStringType>::Operations::BRACECLOSED: os<<')';break;
                case AMUIParseExpressionElement<TStringType>::Operations::EQUAL: _type == AMCore::AMDataType_string ? os<<"'"<<AMCore::AMEscape(s, "'")<<"'" : os<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::NOTEQUAL: _type == AMCore::AMDataType_string ? os<<"<>'"<<AMCore::AMEscape(s, "'")<<"'" : os<<"<>"<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::LIKE: if (!s.empty()) {
                    bool prnQuote = s.find_first_of(" \t\r\n") != std::string::npos;
                    bool prnPercent = s.find_first_of("%") == std::string::npos && prnQuote;
                    if (prnQuote) {os << "'";}
                    if (prnPercent) {os << "%";}
                    os<<s;
                    if (prnPercent) {os << "%";}
                    if (prnQuote) {os << "'";}
                }
                break;
                case AMUIParseExpressionElement<TStringType>::Operations::NOTLIKE: if (!it.inputValue.empty()) {
                        os<<"<>";
                        bool prnQuote = s.find_first_of(" \t\r\n") != std::string::npos;
                        bool prnPercent = s.find_first_of("%") == std::string::npos && prnQuote;
                        if (prnQuote) {os << "'";}
                        if (prnPercent) {os << "%";}
                        os<<s;
                        if (prnPercent) {os << "%";}
                        if (prnQuote) {os << "'";}
                }
                break;
                case AMUIParseExpressionElement<TStringType>::Operations::LESS: _type == AMCore::AMDataType_string ? os<<"<'"<<AMCore::AMEscape(s, "'")<<"'" : os<<"<"<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::MORE: _type == AMCore::AMDataType_string ? os<<">'"<<AMCore::AMEscape(s, "'")<<"'" : os<<">"<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::LESSEQUAL: _type == AMCore::AMDataType_string ? os<<"<='"<<AMCore::AMEscape(s, "'")<<"'" : os<<"<="<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::MOREEQUAL: _type == AMCore::AMDataType_string ? os<<">='"<<AMCore::AMEscape(s, "'")<<"'" : os<<">="<<s;break;
                case AMUIParseExpressionElement<TStringType>::Operations::ISNULL: os<<'='<<AMCore::AMNullString;break;
                case AMUIParseExpressionElement<TStringType>::Operations::NOTNULL: os<<"<>"<<AMCore::AMNullString;break;
                case AMUIParseExpressionElement<TStringType>::Operations::AND: os<<andText;break;
                case AMUIParseExpressionElement<TStringType>::Operations::OR: os<<orText;break;
                case AMUIParseExpressionElement<TStringType>::Operations::INVALID: os<<s;break;
                default: return "";
            }
        }
        return os.str();
    }

    inline std::string si_to_string(std::set<int> &inp)
    {
        std::ostringstream os;
        bool first = true;
        os << "'{";
        for (auto it: inp) {
            if (first) {
                first = false;
            } else {
                os << ',';
            }
            os << it;
        }
        os << "}'";
        return os.str();
    }


}

#endif //AMCORE_AMUISYNTHESIZERS_H
