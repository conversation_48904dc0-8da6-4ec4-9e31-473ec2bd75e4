//
// Created by <PERSON><PERSON><PERSON> on 15.9.22.
//

#ifndef SAW_ALL_AMUIWIDGETSTATE_H
#define SAW_ALL_AMUIWIDGETSTATE_H

#include "AMUIParsers.h"

namespace AMDialogs {

    enum class AMUIWidgetState {
        READONLY,
        EMPTY,
        FILLED,
        WITHMISTAKE,
        INVALID
    };

    inline AMUIWidgetState AMUIToWidgetState(AMUIParseResult res)
    {
        static const AMUIWidgetState tab[3] = {
            AMUIWidgetState::INVALID,//FAIL = 0,
            AMUIWidgetState::WITHMISTAKE,//PARTIAL = 1,
            AMUIWidgetState::FILLED//SUCCESS = 2
        };
        return tab[(int)res];
    }

    inline AMUIParseResult AMUIToParserResult(AMUIWidgetState st)
    {
        static const AMUIParseResult tab[5] = {
            AMUIParseResult::SUCCESS,
            AMUIParseResult::SUCCESS,
            AMUIParseResult::SUCCESS,
            AMUIParseResult::PARTIAL,
            AMUIParseResult::FAIL,
        };
        return tab[(int)st];
    }

    AMUIWidgetState operator + (AMUIWidgetState s1, AMUIWidgetState s2);
    AMUIWidgetState operator * (AMUIWidgetState s1, AMUIWidgetState s2);
    AMUIWidgetState operator - (AMUIWidgetState s1, AMUIWidgetState s2);


}
#endif //SAW_ALL_AMUIWIDGETSTATE_H
