//
// Created by <PERSON><PERSON><PERSON> on 9.2.23.
//

#ifndef SAW_AMUIDIALOGENUMLINENULLABLE_H
#define SAW_AMUIDIALOGENUMLINENULLABLE_H

#include "amdialogs/AMUIDialogEnumLine.h"

namespace AMDialogs {



    template<typename TEnumType, typename TStringType>
    class AMUIDialogEnumLineNullable:public  AMUIDialogEnumLine<TEnumType, TStringType> {
    public:
        AMUIDialogEnumLineNullable();
        void init(
            std::string name,
            std::function<void(AMUIDialog *)>,
            std::function<void(AMUIDialog *)>,
            int enumNo,
            std::string strVoidParam,
            std::map<TEnumType, int> *selected,
            bool *nullable,
            std::string flashbackTimepoint
            );
        const AMCore::AMTwoWayTable<TEnumType, TStringType> *table();
        bool isNull() {return *m_nullable;}
        void setNull(bool newNull) {if (m_nullable) *m_nullable = newNull;};
        AMUIFormElementCheckBox<TStringType> &nullableCheckBox() {return m_nullableCheckBox;};

        //void onChange() override;

    protected:
        bool *m_nullable;
        AMUIFormElementCheckBox<TStringType> m_nullableCheckBox;
    };

} // AMDialogs

#include "src/AMUIDialogEnumLineNullable.hpp"

#endif //SAW_AMUIDIALOGENUMLINENULLABLE_H
