# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.23

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: AMCore
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\zdenek\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\
# =============================================================================
# Object build statements for SHARED_LIBRARY target AMCore


#############################################
# Order-only phony target for AMCore

build cmake_object_order_depends_target_AMCore: phony || CMakeFiles\AMCore.dir

build CMakeFiles\AMCore.dir\src\AMLString.cpp.obj: CXX_COMPILER__AMCore_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\src\AMLString.cpp || cmake_object_order_depends_target_AMCore
  DEFINES = -DAMCore_EXPORTS
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\AMCore.dir
  OBJECT_FILE_DIR = CMakeFiles\AMCore.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\AMCore.dir\
  TARGET_PDB = AMCore.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target AMCore


#############################################
# Link the shared library AMCore.dll

build AMCore.dll AMCore.lib: CXX_SHARED_LIBRARY_LINKER__AMCore_Debug CMakeFiles\AMCore.dir\src\AMLString.cpp.obj
  LANGUAGE_COMPILE_FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL
  LINK_LIBRARIES = kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\AMCore.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  RESTAT = 1
  TARGET_COMPILE_PDB = CMakeFiles\AMCore.dir\
  TARGET_FILE = AMCore.dll
  TARGET_IMPLIB = AMCore.lib
  TARGET_PDB = AMCore.pdb

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMFNV1a


#############################################
# Order-only phony target for TEST_AMFNV1a

build cmake_object_order_depends_target_TEST_AMFNV1a: phony || CMakeFiles\TEST_AMFNV1a.dir

build CMakeFiles\TEST_AMFNV1a.dir\test\Hash\test_AMFNV1a.cpp.obj: CXX_COMPILER__TEST_AMFNV1a_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Hash\test_AMFNV1a.cpp || cmake_object_order_depends_target_TEST_AMFNV1a
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMFNV1a.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMFNV1a.dir\test\Hash
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMFNV1a.dir\
  TARGET_PDB = TEST_AMFNV1a.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMFNV1a


#############################################
# Link the executable TEST_AMFNV1a.exe

build TEST_AMFNV1a.exe: CXX_EXECUTABLE_LINKER__TEST_AMFNV1a_Debug CMakeFiles\TEST_AMFNV1a.dir\test\Hash\test_AMFNV1a.cpp.obj
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMFNV1a.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMFNV1a.dir\
  TARGET_FILE = TEST_AMFNV1a.exe
  TARGET_IMPLIB = TEST_AMFNV1a.lib
  TARGET_PDB = TEST_AMFNV1a.pdb


#############################################
# Utility command for Tests_3

build Tests_3: phony CMakeFiles\Tests_3 TEST_AMFNV1a.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMRange


#############################################
# Order-only phony target for TEST_AMRange

build cmake_object_order_depends_target_TEST_AMRange: phony || CMakeFiles\TEST_AMRange.dir

build CMakeFiles\TEST_AMRange.dir\test\Range\test_AMRange.cpp.obj: CXX_COMPILER__TEST_AMRange_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Range\test_AMRange.cpp || cmake_object_order_depends_target_TEST_AMRange
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMRange.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMRange.dir\test\Range
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMRange.dir\
  TARGET_PDB = TEST_AMRange.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMRange


#############################################
# Link the executable TEST_AMRange.exe

build TEST_AMRange.exe: CXX_EXECUTABLE_LINKER__TEST_AMRange_Debug CMakeFiles\TEST_AMRange.dir\test\Range\test_AMRange.cpp.obj
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMRange.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMRange.dir\
  TARGET_FILE = TEST_AMRange.exe
  TARGET_IMPLIB = TEST_AMRange.lib
  TARGET_PDB = TEST_AMRange.pdb


#############################################
# Utility command for Tests_8

build Tests_8: phony CMakeFiles\Tests_8 TEST_AMRange.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMInteger


#############################################
# Order-only phony target for TEST_AMInteger

build cmake_object_order_depends_target_TEST_AMInteger: phony || CMakeFiles\TEST_AMInteger.dir

build CMakeFiles\TEST_AMInteger.dir\test\Integer\test_AMInteger.cpp.obj: CXX_COMPILER__TEST_AMInteger_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Integer\test_AMInteger.cpp || cmake_object_order_depends_target_TEST_AMInteger
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMInteger.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMInteger.dir\test\Integer
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMInteger.dir\
  TARGET_PDB = TEST_AMInteger.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMInteger


#############################################
# Link the executable TEST_AMInteger.exe

build TEST_AMInteger.exe: CXX_EXECUTABLE_LINKER__TEST_AMInteger_Debug CMakeFiles\TEST_AMInteger.dir\test\Integer\test_AMInteger.cpp.obj
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMInteger.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMInteger.dir\
  TARGET_FILE = TEST_AMInteger.exe
  TARGET_IMPLIB = TEST_AMInteger.lib
  TARGET_PDB = TEST_AMInteger.pdb


#############################################
# Utility command for Tests_9

build Tests_9: phony CMakeFiles\Tests_9 TEST_AMInteger.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMTypeId


#############################################
# Order-only phony target for TEST_AMTypeId

build cmake_object_order_depends_target_TEST_AMTypeId: phony || CMakeFiles\TEST_AMTypeId.dir

build CMakeFiles\TEST_AMTypeId.dir\test\TypeId\test_AMCETypeId.cpp.obj: CXX_COMPILER__TEST_AMTypeId_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\TypeId\test_AMCETypeId.cpp || cmake_object_order_depends_target_TEST_AMTypeId
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMTypeId.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMTypeId.dir\test\TypeId
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMTypeId.dir\
  TARGET_PDB = TEST_AMTypeId.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMTypeId


#############################################
# Link the executable TEST_AMTypeId.exe

build TEST_AMTypeId.exe: CXX_EXECUTABLE_LINKER__TEST_AMTypeId_Debug CMakeFiles\TEST_AMTypeId.dir\test\TypeId\test_AMCETypeId.cpp.obj
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMTypeId.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMTypeId.dir\
  TARGET_FILE = TEST_AMTypeId.exe
  TARGET_IMPLIB = TEST_AMTypeId.lib
  TARGET_PDB = TEST_AMTypeId.pdb


#############################################
# Utility command for Tests_4

build Tests_4: phony CMakeFiles\Tests_4 TEST_AMTypeId.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMLString


#############################################
# Order-only phony target for TEST_AMLString

build cmake_object_order_depends_target_TEST_AMLString: phony || cmake_object_order_depends_target_AMCore

build CMakeFiles\TEST_AMLString.dir\test\LString\test_AMLString.cpp.obj: CXX_COMPILER__TEST_AMLString_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\LString\test_AMLString.cpp || cmake_object_order_depends_target_TEST_AMLString
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMLString.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMLString.dir\test\LString
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMLString.dir\
  TARGET_PDB = TEST_AMLString.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMLString


#############################################
# Link the executable TEST_AMLString.exe

build TEST_AMLString.exe: CXX_EXECUTABLE_LINKER__TEST_AMLString_Debug CMakeFiles\TEST_AMLString.dir\test\LString\test_AMLString.cpp.obj | AMCore.lib || AMCore.dll
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  AMCore.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMLString.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMLString.dir\
  TARGET_FILE = TEST_AMLString.exe
  TARGET_IMPLIB = TEST_AMLString.lib
  TARGET_PDB = TEST_AMLString.pdb


#############################################
# Utility command for Tests_5

build Tests_5: phony CMakeFiles\Tests_5 TEST_AMLString.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMException


#############################################
# Order-only phony target for TEST_AMException

build cmake_object_order_depends_target_TEST_AMException: phony || cmake_object_order_depends_target_AMCore

build CMakeFiles\TEST_AMException.dir\test\Exception\test_AMException.cpp.obj: CXX_COMPILER__TEST_AMException_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Exception\test_AMException.cpp || cmake_object_order_depends_target_TEST_AMException
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMException.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMException.dir\test\Exception
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMException.dir\
  TARGET_PDB = TEST_AMException.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMException


#############################################
# Link the executable TEST_AMException.exe

build TEST_AMException.exe: CXX_EXECUTABLE_LINKER__TEST_AMException_Debug CMakeFiles\TEST_AMException.dir\test\Exception\test_AMException.cpp.obj | AMCore.lib || AMCore.dll
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  AMCore.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMException.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMException.dir\
  TARGET_FILE = TEST_AMException.exe
  TARGET_IMPLIB = TEST_AMException.lib
  TARGET_PDB = TEST_AMException.pdb


#############################################
# Utility command for Tests_6

build Tests_6: phony CMakeFiles\Tests_6 TEST_AMException.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMVariant


#############################################
# Order-only phony target for TEST_AMVariant

build cmake_object_order_depends_target_TEST_AMVariant: phony || cmake_object_order_depends_target_AMCore

build CMakeFiles\TEST_AMVariant.dir\test\Variant\test_AMVariant.cpp.obj: CXX_COMPILER__TEST_AMVariant_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Variant\test_AMVariant.cpp || cmake_object_order_depends_target_TEST_AMVariant
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMVariant.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMVariant.dir\test\Variant
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMVariant.dir\
  TARGET_PDB = TEST_AMVariant.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMVariant


#############################################
# Link the executable TEST_AMVariant.exe

build TEST_AMVariant.exe: CXX_EXECUTABLE_LINKER__TEST_AMVariant_Debug CMakeFiles\TEST_AMVariant.dir\test\Variant\test_AMVariant.cpp.obj | AMCore.lib || AMCore.dll
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  AMCore.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMVariant.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMVariant.dir\
  TARGET_FILE = TEST_AMVariant.exe
  TARGET_IMPLIB = TEST_AMVariant.lib
  TARGET_PDB = TEST_AMVariant.pdb


#############################################
# Utility command for Tests_7

build Tests_7: phony CMakeFiles\Tests_7 TEST_AMVariant.exe

# =============================================================================
# Object build statements for EXECUTABLE target TEST_AMParsers


#############################################
# Order-only phony target for TEST_AMParsers

build cmake_object_order_depends_target_TEST_AMParsers: phony || CMakeFiles\TEST_AMParsers.dir

build CMakeFiles\TEST_AMParsers.dir\test\Parsers\test_AMParsers.cpp.obj: CXX_COMPILER__TEST_AMParsers_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\test\Parsers\test_AMParsers.cpp || cmake_object_order_depends_target_TEST_AMParsers
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMParsers.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMParsers.dir\test\Parsers
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMParsers.dir\
  TARGET_PDB = TEST_AMParsers.pdb

build CMakeFiles\TEST_AMParsers.dir\src\AMParsers.cpp.obj: CXX_COMPILER__TEST_AMParsers_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\src\AMParsers.cpp || cmake_object_order_depends_target_TEST_AMParsers
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMParsers.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMParsers.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMParsers.dir\
  TARGET_PDB = TEST_AMParsers.pdb

build CMakeFiles\TEST_AMParsers.dir\src\AMNullable.cpp.obj: CXX_COMPILER__TEST_AMParsers_Debug C$:\Users\zdenek\Documents\andromeda\AMCore\src\AMNullable.cpp || cmake_object_order_depends_target_TEST_AMParsers
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IC:\Users\<USER>\Documents\andromeda\AMCore\include -IC:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\include
  OBJECT_DIR = CMakeFiles\TEST_AMParsers.dir
  OBJECT_FILE_DIR = CMakeFiles\TEST_AMParsers.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMParsers.dir\
  TARGET_PDB = TEST_AMParsers.pdb


# =============================================================================
# Link build statements for EXECUTABLE target TEST_AMParsers


#############################################
# Link the executable TEST_AMParsers.exe

build TEST_AMParsers.exe: CXX_EXECUTABLE_LINKER__TEST_AMParsers_Debug CMakeFiles\TEST_AMParsers.dir\test\Parsers\test_AMParsers.cpp.obj CMakeFiles\TEST_AMParsers.dir\src\AMParsers.cpp.obj CMakeFiles\TEST_AMParsers.dir\src\AMNullable.cpp.obj
  FLAGS = -fexceptions /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = gtest.a.lib  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\Documents\andromeda\AMCore\..\3rdparty\lib
  OBJECT_DIR = CMakeFiles\TEST_AMParsers.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\TEST_AMParsers.dir\
  TARGET_FILE = TEST_AMParsers.exe
  TARGET_IMPLIB = TEST_AMParsers.lib
  TARGET_PDB = TEST_AMParsers.pdb


#############################################
# Utility command for Tests_10

build Tests_10: phony CMakeFiles\Tests_10 TEST_AMParsers.exe


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Documents\andromeda\AMCore -BC:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Custom command for CMakeFiles\Tests_3

build CMakeFiles\Tests_3 | ${cmake_ninja_workdir}CMakeFiles\Tests_3: CUSTOM_COMMAND || TEST_AMFNV1a.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMFNV1a.exe"


#############################################
# Custom command for CMakeFiles\Tests_8

build CMakeFiles\Tests_8 | ${cmake_ninja_workdir}CMakeFiles\Tests_8: CUSTOM_COMMAND || TEST_AMRange.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMRange.exe"


#############################################
# Custom command for CMakeFiles\Tests_9

build CMakeFiles\Tests_9 | ${cmake_ninja_workdir}CMakeFiles\Tests_9: CUSTOM_COMMAND || TEST_AMInteger.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMInteger.exe"


#############################################
# Custom command for CMakeFiles\Tests_4

build CMakeFiles\Tests_4 | ${cmake_ninja_workdir}CMakeFiles\Tests_4: CUSTOM_COMMAND || TEST_AMTypeId.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMTypeId.exe"


#############################################
# Custom command for CMakeFiles\Tests_5

build CMakeFiles\Tests_5 | ${cmake_ninja_workdir}CMakeFiles\Tests_5: CUSTOM_COMMAND || AMCore.dll TEST_AMLString.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMLString.exe"


#############################################
# Custom command for CMakeFiles\Tests_6

build CMakeFiles\Tests_6 | ${cmake_ninja_workdir}CMakeFiles\Tests_6: CUSTOM_COMMAND || AMCore.dll TEST_AMException.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMException.exe"


#############################################
# Custom command for CMakeFiles\Tests_7

build CMakeFiles\Tests_7 | ${cmake_ninja_workdir}CMakeFiles\Tests_7: CUSTOM_COMMAND || AMCore.dll TEST_AMVariant.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMVariant.exe"


#############################################
# Custom command for CMakeFiles\Tests_10

build CMakeFiles\Tests_10 | ${cmake_ninja_workdir}CMakeFiles\Tests_10: CUSTOM_COMMAND || TEST_AMParsers.exe
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang && C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang\TEST_AMParsers.exe"

# =============================================================================
# Target aliases.

build AMCore: phony AMCore.dll

build TEST_AMException: phony TEST_AMException.exe

build TEST_AMFNV1a: phony TEST_AMFNV1a.exe

build TEST_AMInteger: phony TEST_AMInteger.exe

build TEST_AMLString: phony TEST_AMLString.exe

build TEST_AMParsers: phony TEST_AMParsers.exe

build TEST_AMRange: phony TEST_AMRange.exe

build TEST_AMTypeId: phony TEST_AMTypeId.exe

build TEST_AMVariant: phony TEST_AMVariant.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang

build all: phony AMCore.dll TEST_AMFNV1a.exe Tests_3 TEST_AMRange.exe Tests_8 TEST_AMInteger.exe Tests_9 TEST_AMTypeId.exe Tests_4 TEST_AMLString.exe Tests_5 TEST_AMException.exe Tests_6 TEST_AMVariant.exe Tests_7 TEST_AMParsers.exe Tests_10

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ..\CMakeLists.txt ..\src\AMCoreConfig.h.in C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCXXInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeGenericSystem.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeRCInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang-C.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang-CXX.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindDoxygen.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindPackageMessage.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang-C.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang-CXX.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.23.2\CMakeCCompiler.cmake CMakeFiles\3.23.2\CMakeCXXCompiler.cmake CMakeFiles\3.23.2\CMakeRCCompiler.cmake CMakeFiles\3.23.2\CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ..\CMakeLists.txt ..\src\AMCoreConfig.h.in C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCXXInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeGenericSystem.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeRCInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang-C.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang-CXX.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Compiler\Clang.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindDoxygen.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\FindPackageMessage.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang-C.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang-CXX.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-Clang.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\Windows.cmake C$:\Users\zdenek\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.23.2\CMakeCCompiler.cmake CMakeFiles\3.23.2\CMakeCXXCompiler.cmake CMakeFiles\3.23.2\CMakeRCCompiler.cmake CMakeFiles\3.23.2\CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
