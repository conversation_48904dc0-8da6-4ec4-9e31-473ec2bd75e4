C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\apps\CLion\ch-0\222.4167.35\bin\cmake\win\bin\cmake.exe -DCMAKE_BUILD_TYPE=Debug -DCMAKE_MAKE_PROGRAM=C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/ninja/win/ninja.exe "-DCMAKE_C_COMPILER=C:/Program Files/LLVM/bin/clang-cl.exe" "-DCMAKE_CXX_COMPILER=C:/Program Files/LLVM/bin/clang-cl.exe" -DCMAKE_C_FLAGS=-m64 -DCMAKE_CXX_FLAGS=-m64 -G Ninja -S C:\Users\<USER>\Documents\andromeda\AMCore -B C:\Users\<USER>\Documents\andromeda\AMCore\cmake-build-debug-visual-studio-clang
-- The C compiler identification is Clang 15.0.2 with MSVC-like command-line
-- The CXX compiler identification is Clang 15.0.2 with MSVC-like command-line
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Program Files/LLVM/bin/clang-cl.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: C:/Program Files/LLVM/bin/clang-cl.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Could NOT find Doxygen (missing: DOXYGEN_EXECUTABLE) 
Doxygen need to be installed to generateIndex the doxygen documentation
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang
