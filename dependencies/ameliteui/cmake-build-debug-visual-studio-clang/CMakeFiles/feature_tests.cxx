
  const char features[] = {"\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 199711L
"1"
#else
"0"
#endif
"cxx_template_template_parameters\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_alias_templates)
"1"
#else
"0"
#endif
"cxx_alias_templates\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_alignas)
"1"
#else
"0"
#endif
"cxx_alignas\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_alignas)
"1"
#else
"0"
#endif
"cxx_alignof\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_attributes)
"1"
#else
"0"
#endif
"cxx_attributes\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_auto_type)
"1"
#else
"0"
#endif
"cxx_auto_type\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_constexpr)
"1"
#else
"0"
#endif
"cxx_constexpr\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_decltype)
"1"
#else
"0"
#endif
"cxx_decltype\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_default_function_template_args)
"1"
#else
"0"
#endif
"cxx_default_function_template_args\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_defaulted_functions)
"1"
#else
"0"
#endif
"cxx_defaulted_functions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_defaulted_functions)
"1"
#else
"0"
#endif
"cxx_defaulted_move_initializers\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_delegating_constructors)
"1"
#else
"0"
#endif
"cxx_delegating_constructors\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_deleted_functions)
"1"
#else
"0"
#endif
"cxx_deleted_functions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_enum_forward_declarations\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_explicit_conversions)
"1"
#else
"0"
#endif
"cxx_explicit_conversions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_extended_friend_declarations\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_extern_templates\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_override_control)
"1"
#else
"0"
#endif
"cxx_final\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_func_identifier\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_generalized_initializers)
"1"
#else
"0"
#endif
"cxx_generalized_initializers\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_inheriting_constructors)
"1"
#else
"0"
#endif
"cxx_inheriting_constructors\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_inline_namespaces\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_lambdas)
"1"
#else
"0"
#endif
"cxx_lambdas\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_local_type_template_args)
"1"
#else
"0"
#endif
"cxx_local_type_template_args\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_long_long_type\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_noexcept)
"1"
#else
"0"
#endif
"cxx_noexcept\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_nonstatic_member_init)
"1"
#else
"0"
#endif
"cxx_nonstatic_member_init\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_nullptr)
"1"
#else
"0"
#endif
"cxx_nullptr\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_override_control)
"1"
#else
"0"
#endif
"cxx_override\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_range_for)
"1"
#else
"0"
#endif
"cxx_range_for\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_raw_string_literals)
"1"
#else
"0"
#endif
"cxx_raw_string_literals\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_reference_qualified_functions)
"1"
#else
"0"
#endif
"cxx_reference_qualified_functions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_right_angle_brackets\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_rvalue_references)
"1"
#else
"0"
#endif
"cxx_rvalue_references\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_sizeof_member\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_static_assert)
"1"
#else
"0"
#endif
"cxx_static_assert\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_strong_enums)
"1"
#else
"0"
#endif
"cxx_strong_enums\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_thread_local)
"1"
#else
"0"
#endif
"cxx_thread_local\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_trailing_return)
"1"
#else
"0"
#endif
"cxx_trailing_return_types\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_unicode_literals)
"1"
#else
"0"
#endif
"cxx_unicode_literals\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_generalized_initializers)
"1"
#else
"0"
#endif
"cxx_uniform_initialization\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_unrestricted_unions)
"1"
#else
"0"
#endif
"cxx_unrestricted_unions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_user_literals)
"1"
#else
"0"
#endif
"cxx_user_literals\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __cplusplus >= 201103L
"1"
#else
"0"
#endif
"cxx_variadic_macros\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_variadic_templates)
"1"
#else
"0"
#endif
"cxx_variadic_templates\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_aggregate_nsdmi)
"1"
#else
"0"
#endif
"cxx_aggregate_default_initializers\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 304 && __cplusplus > 201103L
"1"
#else
"0"
#endif
"cxx_attribute_deprecated\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_binary_literals)
"1"
#else
"0"
#endif
"cxx_binary_literals\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_contextual_conversions)
"1"
#else
"0"
#endif
"cxx_contextual_conversions\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 304 && __cplusplus > 201103L
"1"
#else
"0"
#endif
"cxx_decltype_auto\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 304 && __cplusplus > 201103L
"1"
#else
"0"
#endif
"cxx_digit_separators\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 304 && __cplusplus > 201103L
"1"
#else
"0"
#endif
"cxx_generic_lambdas\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_init_captures)
"1"
#else
"0"
#endif
"cxx_lambda_init_captures\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_relaxed_constexpr)
"1"
#else
"0"
#endif
"cxx_relaxed_constexpr\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_return_type_deduction)
"1"
#else
"0"
#endif
"cxx_return_type_deduction\n"
"CXX_FEATURE:"
#if ((__clang_major__ * 100) + __clang_minor__) >= 301 && __has_feature(cxx_variable_templates)
"1"
#else
"0"
#endif
"cxx_variable_templates\n"

};

int main(int argc, char** argv) { (void)argv; return features[argc]; }
