{"artifacts": [{"path": "AMCore.dll"}, {"path": "AMCore.lib"}, {"path": "AMCore.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "link_directories", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 1, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fexceptions /MDd /Zi /Ob0 /Od /RTC1"}, {"fragment": "-std:c++17"}], "defines": [{"define": "AMCore_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/include"}, {"backtrace": 3, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/../3rdparty/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "id": "AMCore::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Documents\\andromeda\\AMCore\\..\\3rdparty\\lib", "role": "libraryPath"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "AMCore", "nameOnDisk": "AMCore.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/AMLString.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}