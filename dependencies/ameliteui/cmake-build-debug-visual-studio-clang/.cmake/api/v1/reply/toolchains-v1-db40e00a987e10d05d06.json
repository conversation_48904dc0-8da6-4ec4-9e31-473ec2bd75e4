{"kind": "toolchains", "toolchains": [{"compiler": {"id": "Clang", "implicit": {"includeDirectories": [], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "C:/Program Files/LLVM/bin/clang-cl.exe", "version": "15.0.2"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "Clang", "implicit": {"includeDirectories": [], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "C:/Program Files/LLVM/bin/clang-cl.exe", "version": "15.0.2"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm"]}, {"compiler": {"implicit": {}, "path": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.19041.0/x86/rc.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}