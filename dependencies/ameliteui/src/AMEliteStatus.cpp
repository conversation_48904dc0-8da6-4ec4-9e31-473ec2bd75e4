//
// Created by <PERSON><PERSON><PERSON> on 19.12.21.
//

#include "../AMEliteStatus.h"

namespace AMEliteUI {

    void AMEliteStatus::Perform() {
        std::chrono::time_point<std::chrono::steady_clock> now = std::chrono::steady_clock::now();
        if (now < m_messageValidTo) {
            return;
        }
        if (m_queue.empty()) {
            m_text = m_defaultText;
            m_level = m_defaultLevel;
        } else {
            _AMEliteStatusItem item = m_queue.front();
            m_queue.pop();
            m_messageValidTo = now + std::chrono::milliseconds(item.duration);
            m_text = item.text;
            m_level = item.level;
        }
    }

    void AMEliteStatus::PushMessage(std::string text, AMEliteStatusLevel level, int duration) {
        _AMEliteStatusItem item = {text, level, duration};
        m_queue.push(item);
    }

    void AMEliteStatus::PushNetworkMessage(std::string text, AMEliteStatusLevel level) {
        m_networkText = text;
        m_networkLevel = level;
    }

    AMEliteStatus::AMEliteStatus() :
        m_queue(),
        m_text(""),
        m_level(EStatusLevelInfo),
        m_networkText(""),
        m_networkLevel(EStatusLevelInfo),
        m_defaultText(),
        m_defaultLevel(EStatusLevelInfo),
        m_messageValidTo(std::chrono::steady_clock::now()) {

    }

    void AMEliteStatus::PushDefaultMessage(std::string text, AMEliteStatusLevel level) {
        m_defaultLevel = level;
        m_defaultText = text;
    }

}