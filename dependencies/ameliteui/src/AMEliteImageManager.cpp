//
// Created by <PERSON><PERSON><PERSON> on 13.9.22.
//

#include "../AMEliteImageManager.h"
#include "amcore/AMAssert.h"
#include "stb/stb_image.h"

namespace AMEliteUI {

    bool
    AMEliteImageManager::loadTextureFromFile(const char *filename, GLuint *outTexture, int *outWidth, int *outHeight) {
        int image_width = 0;
        int image_height = 0;
        unsigned char *image_data = stbi_load(filename, &image_width, &image_height, NULL, 4);
        return _loadTexture(image_data, image_width, image_height, outTexture, outWidth, outHeight);
    }

    bool
    AMEliteImageManager::loadTextureFromMemory(const unsigned char *buffer, int length, AMEliteImageManagerImage *var,
                                               GLuint *outTexture, int *outWidth, int *outHeight) {
        if (var->texture != -1) {
            var->count++;
            *outTexture = var->texture;
            if (outWidth) {
                *outWidth = var->width;
            }
            if (outHeight) {
                *outHeight = var->height;
            }
            return true;
        }
        int image_width = 0;
        int image_height = 0;
        unsigned char *image_data = stbi_load_from_memory(buffer, length, &image_width, &image_height, NULL, 4);
        bool rv = _loadTexture(image_data, image_width, image_height, outTexture, outWidth, outHeight);
        if (rv && *outTexture != -1) {
            s_existingTextures.insert({*outTexture, var});
            var->texture = *outTexture;
            var->count = 1;
            var->width = image_width;
            var->height = image_height;
        }
        return rv;
    }

    bool AMEliteImageManager::_loadTexture(unsigned char *imageData, int width, int height, GLuint *outTexture,
                                           int *outWidth, int *outHeight) {
        if (imageData == NULL)
            return false;

        // Create a OpenGL texture identifier
        GLuint image_texture;
        glGenTextures(1, &image_texture);
        glBindTexture(GL_TEXTURE_2D, image_texture);

        // Setup filtering parameters for display
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(
            GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE
                       ); // This is required on WebGL for non power-of-two textures
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE); // Same

        // Upload pixels into texture
#if defined(GL_UNPACK_ROW_LENGTH) && !defined(__EMSCRIPTEN__)
        glPixelStorei(GL_UNPACK_ROW_LENGTH, 0);
#endif
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, imageData);
        stbi_image_free(imageData);

        *outTexture = image_texture;
        if (outWidth) {
            *outWidth = width;
        }
        if (outHeight) {
            *outHeight = height;
        }
        return true;
    }

    void AMEliteImageManager::unloadTexture(GLuint texture) {
        auto it = s_existingTextures.find(texture);
        AMAssert(it != s_existingTextures.end());
        if (it->second->count > 1) {
            it->second->count--;
            return;
        }
        AMAssert(it->second->count == 1);
        it->second->count = 0;
        it->second->texture = -1;
        s_existingTextures.erase(it);
        glDeleteTextures(1, &texture);
    }

    std::map<GLuint, AMEliteImageManagerImage *> AMEliteImageManager::s_existingTextures;
/*
void AMEliteImageManager::refresh()
{
    for(auto it: s_existingTextures) {

        int image_width = 0;
        int image_height = 0;
        unsigned char* image_data = stbi_load_from_memory(it.second->data, it.second->length, &image_width, &image_height, NULL, 4);

        glBindTexture(GL_TEXTURE_2D, it.second->texture);

        // Setup filtering parameters for display
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE); // This is required on WebGL for non power-of-two textures
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE); // Same

        // Upload pixels into texture
        #if defined(GL_UNPACK_ROW_LENGTH) && !defined(__EMSCRIPTEN__)
                glPixelStorei(GL_UNPACK_ROW_LENGTH, 0);
        #endif

        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, image_width, image_height, 0, GL_RGBA, GL_UNSIGNED_BYTE, image_data);
        stbi_image_free(image_data);

    }
}
*/
}