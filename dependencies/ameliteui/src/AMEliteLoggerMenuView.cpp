//
// Created by <PERSON><PERSON><PERSON> on 16.9.22.
//

#include "../AMEliteLoggerMenuView.h"
#include "amcore/AMAssert.h"
#include "imgui/imgui.h"
#include "../AMEliteLoggerController.h"

namespace AMEliteUI {

    void AMEliteLoggerMenuView::init(AMEliteLoggerController *controller) {
        m_controller = controller;
    }

    void AMEliteLoggerMenuView::render(bool *opened) {
        AMAssert(m_controller);
        if (ImGui::BeginMenu("Operace")) {
            if (ImGui::MenuItem("Stáhnout log", nullptr)) {
                m_controller->saveLog();
            }
            ImGui::EndMenu();
            *opened = true;
        } else {
            *opened = false;
        }
    }

    AMEliteLoggerMenuView::AMEliteLoggerMenuView()
        : m_controller(nullptr) {
    }

}
