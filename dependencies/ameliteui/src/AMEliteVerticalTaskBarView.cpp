//
// Created by <PERSON><PERSON><PERSON> on 1.9.22.
//

#include "../AMEliteVerticalTaskBarView.h"
#include "imgui/imgui.h"
#include "../AMEliteApp.h"
#include "amui/AMUIFonts.h"
#include "../AMEliteConfig.h"

namespace AMEliteUI {

    void AMEliteVerticalTaskBarView::render() {
        ImGuiTableFlags flags =
            ImGuiTableFlags_ScrollY | ImGuiTableFlags_RowBg | ImGuiTableFlags_BordersOuter | ImGuiTableFlags_BordersH |
            ImGuiTableFlags_BordersV;

        ImGuiStyle style = ImGui::GetStyle();
        if (m_width < 0.0f) {
            m_width = TASKBAR_TAB_WIDTH + 3 * style.CellPadding.x + style.ScrollbarSize;
        }
        AMUI::AMUIRect<float> rect = AMEliteApp::instance().getMainViewPort();
        ImGui::SetNextWindowPos(ImVec2(rect.right + style.DisplayWindowPadding.x / 2.0f, rect.top));
        ImGui::SetNextWindowSize(ImVec2(m_width, rect.bottom - rect.top));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0, 0.0));
        ImGui::PushStyleColor(ImGuiCol_WindowBg, ImGui::GetColorU32(ImGuiCol_TableHeaderBg));
        //ImGui::PushStyleColor(ImGuiCol_TableHeaderBg, TASKBAR_HEADER_ACTIVE_COLOR);
        ImGui::PushStyleColor(ImGuiCol_HeaderActive, TASKBAR_HEADER_ACTIVE_COLOR);
        int controller_count = m_app->controllers().size();
        float tab_size_x = m_width;
        if (tab_size_x > ImGui::GetMainViewport()->Size.x - style.DisplayWindowPadding.x) {
            tab_size_x = ImGui::GetMainViewport()->Size.x - style.DisplayWindowPadding.x;
        }
        ImVec2 outer_size = ImVec2(
            tab_size_x, 0
                                  );
        if (ImGui::Begin("AppTaskBarWnd", nullptr,
            ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoCollapse |
            ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoFocusOnAppearing
                        )) {
            if (controller_count > 0) {
                AMAssert(controller_count > 0);
                AMAssert(m_width > 0.0);
                if (ImGui::BeginTable("AppVerticalTaskBarTab", 1, flags, outer_size)) {
                    ImGui::TableSetupColumn("AppVerticalTaskBarTitle", ImGuiTableColumnFlags_WidthFixed, tab_size_x);
                    ImGuiListClipper clipper;
                    clipper.Begin(controller_count);
                    while (clipper.Step()) {

                        ImGui::TableSetBgColor(ImGuiTableBgTarget_RowBg0, ImGui::GetColorU32(ImGuiCol_TableHeaderBg));
                        ImGui::PushStyleColor(ImGuiCol_Text, 0xFF000000);

                        int column = 0;
                        std::list<AMUI::AMUIController *>::iterator it = m_app->controllers().begin();
                        for (int i = 0; i < clipper.DisplayStart; i++) {
                            it++;
                        }
                        for (int row = clipper.DisplayStart; row < clipper.DisplayEnd; row++, it++) {
                            ImGui::TableNextRow();
                            ImGui::TableSetColumnIndex(0);
                            ImGui::PushFont(AMUI::BoldFont);
                            AMUI::AMUIController *c = *it;
                            if (c->isFocused()) {
                                ImGui::TableSetBgColor(
                                    ImGuiTableBgTarget_CellBg, ImGui::GetColorU32(ImGuiCol_HeaderActive), column
                                                      );
                            } else {
                                ImGui::TableSetBgColor(
                                    ImGuiTableBgTarget_CellBg, ImGui::GetColorU32(ImGuiCol_TableHeaderBg), column
                                                      );
/*
                                if (ImGui::IsItemHovered()) {
                                    ImGui::TableSetBgColor(
                                        ImGuiTableBgTarget_CellBg, ImGui::GetColorU32(ImGuiCol_HeaderHovered), column
                                                          );
                                } else {
                                    ImGui::TableSetBgColor(
                                        ImGuiTableBgTarget_CellBg, ImGui::GetColorU32(ImGuiCol_TableHeaderBg), column
                                                          );
                                }*/
                            }

                            char label[64];
                            snprintf(label, sizeof(label) / sizeof(char), "%s\n ###VTB_%i", c->title().c_str(), row);
                            if (ImGui::Selectable(label, false, 0, ImVec2(0, 0))) {
                                AMUI::AMUIController *mc = AMEliteApp::instance().maximizedController();
                                if (mc && mc->isMaximized()) {
                                    c->bringToForeground();
                                    c->setMaximized(true);
                                } else {
                                    c->bringToForeground();
                                }
                            }
                            ImGui::PopFont();
                            ImGui::SetCursorPosY(ImGui::GetCursorPosY() - ImGui::GetFontSize());
                            ImGui::Text("%s", c->subTitle().c_str());
                        }
                        ImGui::PopStyleColor();
                    }
                    ImGui::EndTable();
                }
            }
        }
        ImGui::End();
        ImGui::PopStyleColor(2);
        ImGui::PopStyleVar();
    }

    AMEliteVerticalTaskBarView::AMEliteVerticalTaskBarView()
        : m_app(nullptr),
          m_width(-1.0f) {
    }

    void AMEliteVerticalTaskBarView::Init(AMUI::AMUIApp *app) {
        m_app = app;
    }

}
