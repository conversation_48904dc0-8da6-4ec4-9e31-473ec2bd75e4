//
// Created by <PERSON><PERSON><PERSON> on 6.9.22.
//

#include "ameliteui/AMEliteLoggerController.h"
#include <ctime>
#include <sstream>
#include <iomanip>
#include "amui/AMUIDirectoryUtils.h"
#include "ameliteui/AMEliteResources.h"

namespace AMEliteUI {

    AMUI::AMUIIView &AMEliteLoggerController::getMainView() {
        return m_view;
    }

    AMEliteLoggerController::AMEliteLoggerController()
        : AMEliteController("Logger"),
          m_view(),
          m_menuView() {
        m_view.init(AMLog::AMLogger::getInstance());
        m_menuView.init(this);
    }

    AMEliteLoggerController &AMEliteLoggerController::instance() {
        if (!m_instance) {
            m_instance = new AMEliteLoggerController();
            m_instance->init();
        }
        return *m_instance;
    }

    AMEliteLoggerController *AMEliteLoggerController::m_instance = nullptr;

    AMEliteLoggerController::~AMEliteLoggerController() {
        m_instance = nullptr;
    }

    void AMEliteLoggerController::onResume() {
        AMEliteController::onResume();
        bool rv = AMEliteImageManager::loadTextureFromMemory(
            MEMIMG(icon_log_png), &m_main_icon, nullptr, nullptr
                                                            );
        AMAssert(rv);
        rv = AMEliteImageManager::loadTextureFromMemory(
            MEMIMG(icon_log_download_png), &m_download_icon, nullptr, nullptr
                                                       );
        AMAssert(rv);
    }

    void AMEliteLoggerController::onPause() {
        AMUIController::onPause();
        AMEliteImageManager::unloadTexture(m_main_icon);
        AMEliteImageManager::unloadTexture(m_download_icon);
        m_main_icon = -1;
    }

    GLuint AMEliteLoggerController::mainIcon() {
        return m_main_icon;
    }

    int AMEliteLoggerController::titlebarIcons() {
        return 5;
    }

    GLuint AMEliteLoggerController::titlebarIcon(int _n) {
        if (_n == 0) return m_main_icon;
        if (_n == 2) return -1;
        return m_download_icon;
    }

    void AMEliteLoggerController::onTitlebarIconClicked(int _n) {
        //AMEliteController::onTitlebarIconClicked(_n);
        //printf("click titlebar icon %i\n", _n);
        if (_n == 0) {
            saveLog();
        }
    }

    void AMEliteLoggerController::saveLog() {
        std::string text = AMLog::AMLogger::getInstance().dump();
        auto t = std::time(nullptr);
        auto tm = *std::localtime(&t);

        char buffer[128];
        strftime(buffer, sizeof(buffer) / sizeof(char), "LOG_%d_%m_%Y_%H_%M_%S.log", &tm);
        AMUI::AMUIDirectoryUtils::saveToDownloadDir(std::string(buffer), text);
    }

    bool AMEliteLoggerController::onMenuOperation(bool *opened) {
        m_menuView.render(opened);
        return true;
    }

    bool AMEliteLoggerController::onMenuRecord(bool *opened) {
        return false;
    }

}
