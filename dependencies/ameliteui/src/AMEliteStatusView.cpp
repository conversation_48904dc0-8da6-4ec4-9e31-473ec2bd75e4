//
// Created by <PERSON><PERSON><PERSON> on 19.12.21.
//

#include "../AMEliteStatusView.h"
#include "imgui/imgui.h"
#include "amui/AMUIFonts.h"
#include "amui/AMUIConfig.h"

namespace AMEliteUI {

    void AMEliteStatusView::Init(AMEliteStatus *_status) {
        m_status = _status;
    }

    void AMEliteStatusView::Render() {

        ImGuiStyle style = ImGui::GetStyle();
        ImU32 bgColor = AMUI::backgroundStatusColors[(int) m_status->Level()];
        ImU32 fgColor = AMUI::foregroundStatusColors[(int) m_status->Level()];
        bool bold = AMUI::statusboldFonts[(int) m_status->Level()];
        auto mvp = ImGui::GetMainViewport();
        ImGui::SetNextWindowPos(ImVec2(style.DisplayWindowPadding.x / 2.0, -style.DisplayWindowPadding.y / 2.0 + mvp->Size.y - 32));
        ImGui::SetNextWindowSize(
            ImVec2(mvp->Size.x - style.DisplayWindowPadding.x * 1.5 - 180.0, 32.0));
        if (bgColor) {
            ImGui::PushStyleColor(ImGuiCol_WindowBg, bgColor);
        }
        ImGui::Begin(
            "StatusBar general", nullptr,
            ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoCollapse
                    );
        ImGui::PushStyleColor(ImGuiCol_Text, fgColor);
        if (bold) {
            ImGui::PushFont(AMUI::BoldFont);
        }
        ImGui::Text("%s", m_status->Text().c_str());
        if (bold) {
            ImGui::PopFont();
        }
        ImGui::PopStyleColor();
        ImGui::End();
        if (bgColor) {
            ImGui::PopStyleColor();
        }
        ImU32 nbgColor = AMUI::backgroundStatusColors[(int) m_status->NetworkLevel()];
        ImU32 nfgColor = AMUI::foregroundStatusColors[(int) m_status->NetworkLevel()];
        bool nbold = AMUI::statusboldFonts[(int) m_status->NetworkLevel()];
        ImGui::SetNextWindowPos(
            ImVec2(
                ImGui::GetMainViewport()->Size.x - style.DisplayWindowPadding.x / 2.0 - 180.0,
                -style.DisplayWindowPadding.y / 2.0 /*+ 20.0*/ + mvp->Size.y - 32
                  ));
        ImGui::SetNextWindowSize(ImVec2(180.0, 32.0));
        if (nbgColor) {
            ImGui::PushStyleColor(ImGuiCol_WindowBg, nbgColor);
        }
        ImGui::Begin(
            "StatusBar network", nullptr,
            ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoCollapse
                    );
        ImGui::PushStyleColor(ImGuiCol_Text, nfgColor);
        if (nbold) {
            ImGui::PushFont(AMUI::BoldFont);
        }
        ImGui::Text("%s", m_status->NetworkText().c_str());
        if (nbold) {
            ImGui::PopFont();
        }
        ImGui::PopStyleColor();
        ImGui::End();
        if (nbgColor) {
            ImGui::PopStyleColor();
        }
    }

}
