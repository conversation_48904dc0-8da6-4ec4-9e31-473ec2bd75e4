/**
 * @file: wordsize.h
 * Information about word size of curent target
 *
 * <AUTHOR>  &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;
 */
#ifndef AMCORE_WORDSIZE_H
#define AMCORE_WORDSIZE_H

#ifdef DOXYGEN
   /**
	*  @ingroup Common
	*  @brief If CPU word width is 64 bit, AMCPU64BIT is defined
	*/
   #define AMCPU64BIT
   /**
	*  @ingroup Common
	*  @brief If CPU word width is 32 bit, AMCPU32BIT is defined
	*/
   #define AMCPU32BIT
#endif
/**
 *  @ingroup Common
 *  @{
 */


namespace AMCore {

	/**
	 *  @ingroup Common
	 *  @brief Indicates the word size of target platform
	 *  If CPu is 64 bit machine, AMCore::wordsize::cpuwordsize equals AMCore::wordsize::cpu64bit.<br/>
	 *  If CPu is 32 bit machine, AMCore::wordsize::cpuwordsize equals AMCore::wordsize::cpu32bit.<br/>
	 *  Other type of  machines are not suported.<br/>
	 *  <br/>
	 *  If CPU word width is 64 bit, AMCPU64BIT is defined.<br/>
	 *  If CPU word width is 32 bit, AMCPU32BIT is defined.<br/>
	 *  Example:
	 *	@code{.cpp}
	 *  #include <AMCore/wordsize.h>
	 *  #include <iostream>
	 *
	 *  int main() {
	 *
	 *  if constexpr (AMCore::wordsize::cpuwordsize == AMCore::wordsize::cpu64bit) {
	 *       std::cout << "CPu 64 bit" << '\n';
	 *  }
	 *  else if constexpr (AMCore::wordsize::cpuwordsize == AMCore::wordsize::cpu32bit) {
	 *       std::cout << "CPU 32 bit"  << '\n';
	 *  }
	 *  @endcode
	 */
	enum class wordsize
	{
		cpu64bit = 0,
		cpu32bit = 1,


#if    defined(_WIN64)\
	|| defined(__x86_64__)\
	|| defined(__ppc64__)

	    cpuwordsize = 0
		#define AMCPU64BIT

#elif  defined(_WIN32)\
    || (defined(__i386__) && !defined(__x86_64__))

	    cpuwordsize = 1
		#define AMCPU32BIT

#else

# error The Andromeda library could not determine word size of this platform.

#endif

	};
}//namespace

/** @} */

#endif //AMCORE_WORDSIZE_H
