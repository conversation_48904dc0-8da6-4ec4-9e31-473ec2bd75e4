//
// Created by <PERSON><PERSON><PERSON> on 22.1.23.
//

#ifndef AMCORE_AMTWOWAYTABLE_H
#define AMCORE_AMTWOWAYTABLE_H

#include <set>
#include "amcore/AMAssert.h"

namespace AMCore {

    template<class T1, class T2>
    struct AMTwoWayPair
    {
        T1 first;
        mutable T2 second;

        template<class s1, class s2>
        bool operator < (const AMTwoWayPair<s1, s2>& other) const
        {
            return first < other.first;
        }
        template<class s1, class s2>
        bool operator == (const AMTwoWayPair<s1, s2>& other) const
        {
            return first == other.first;
        }
    };

    template<typename ClassA, typename ClassB>
    class AMTwoWayTable
    {
    public:
        //void insert(const ClassA &a, const ClassB &b);
        const ClassA *findA(ClassB key) const {
            auto itr = m_reverse.find({key, nullptr});
            if (itr != m_reverse.end()) {
                return itr->second;
            }
            return nullptr;
        }
        const ClassB *findB(ClassA key) const {
            auto itr = m_set.find({key, nullptr});
            if (itr != m_set.end()) {
                return itr->second;
            }
            return nullptr;
        }
        /*
        const ClassA *findA(ClassB& key) const {
            auto itr = m_reverse.find({key, nullptr});
            if (itr != m_reverse.end()) {
                return itr->second;
            }
            return nullptr;
        }
        const ClassB *findB(ClassA& key) const {
            auto itr = m_set.find({key, nullptr});
            if (itr != m_set.end()) {
                return itr->second;
            }
            return nullptr;
        }*/

        //AMTwoWayTable();
        void insert(const ClassA &a, const ClassB &b) {
            auto bitr = m_reverse.insert({b, nullptr}).first; // creates first pair
            const ClassB *bp = &(bitr->first);  // get pointer of our stored copy of b
            auto aitr = m_set.insert({a, bp}).first;
            // insert second pair {a, pointer_to_b}
            const ClassA *ap = &(aitr->first);  // update pointer in mapA to point to a
            bitr->second = ap;
        }
        void changeB(const ClassB &from, const ClassB &to)
        {
            auto bit = m_reverse.find({from, nullptr});
            if (bit == m_reverse.end()) {
                return;
            }
            auto ait = m_set.find({*bit->second, nullptr});
            AMAssert(ait != m_set.end());
            const ClassA a = *bit->second;
            m_set.erase(ait);
            m_reverse.erase(bit);
            insert(a, to);
        }
        const std::set<AMTwoWayPair<ClassA, const ClassB *>, std::less</*AMTwoWayPair<ClassA, const ClassB *>*/> > &setA() const
        {
            return m_set;
        }
        const std::set<AMTwoWayPair<ClassB, const ClassA *>, std::less</*AMTwoWayPair<ClassB, const ClassA *>*/> > &setB() const
        {
            return m_reverse;
        }

        void clear()
        {
            m_set.clear();
            m_reverse.clear();
        }

        AMTwoWayTable()
            :m_set(),
             m_reverse()
        {
        }

    protected:
         std::set<AMTwoWayPair<ClassA, const ClassB *>, std::less</*AMTwoWayPair<ClassA, const ClassB *>*/ > > m_set;
         std::set<AMTwoWayPair<ClassB, const ClassA *>, std::less</*AMTwoWayPair<ClassB, const ClassA *>*/ > > m_reverse;
    };

} // AMCore

#endif //AMCORE_AMTWOWAYTABLE_H
