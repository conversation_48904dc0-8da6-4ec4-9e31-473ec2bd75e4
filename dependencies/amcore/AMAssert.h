//
// Created by <PERSON><PERSON><PERSON> on 27.4.24.
//

#ifndef SAW_AMASSERT_H
#define SAW_AMASSERT_H

#ifdef NDEBUG
#undef NDEBUG
#include <cassert>
#define NDEBUG
#else
#include <cassert>
#endif

#define AMAssert assert

/*
#include <cassert>
#include <exception>
#include <string>
#include <cstdio>
//#define AMAssert assert

class AMAssertException: public std::exception
{
public:
    AMAssertException(char* msg): std::exception()
    {
        m_what = msg;
    };
    const char *what() const noexcept override
    {
        return m_what.c_str();
    }
protected:
    std::string m_what;
};

#define AMAssert(condition) \
    if (!(condition)) {              \
        char msg[1024];\
        snprintf(msg, sizeof(msg)/ sizeof(char), "AMAssertion failed %s at %s:%d", #condition, __FILE__, __LINE__); \
        printf("\n\n%s\n", msg);     \
        throw AMAssertException(msg);                         \
    }
    */
#endif //SAW_AMASSERT_H
