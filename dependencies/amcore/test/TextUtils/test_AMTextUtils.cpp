#include "amcore/AMTextUtils.h"
#include <sstream>
#include "gtest/gtest.h"
#include "../googlemock/include/gmock/gmock-matchers.h"

using namespace AMCore;
using namespace testing;



#define CINT(value) (unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -2 + 4]) | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -3 + 4])) << 8 | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -4 + 4])) << 16 | \
                    ((unsigned int)((unsigned char)"\0\0\0\0"#value"\0\0\0\0"[sizeof(#value) -5 + 4])) << 24

TEST(AMTextUtils, stripDia)
{
    std::string s01("m<PERSON><PERSON><PERSON><PERSON>\xca\xb0.");
    EXPECT_STREQ(AMStringToLowercaseStripDia(s01).c_str(), "muzes zit\xca\xb0.");

    std::istringstream ss02("můžeš Žít\xca\xb0.");
    unsigned int orig;
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'm');
    EXPECT_EQ(orig, 'm');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'u');
    EXPECT_EQ(orig, CINT(ů));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'z');
    EXPECT_EQ(orig, CINT(ž));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'e');
    EXPECT_EQ(orig, 'e');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 's');
    EXPECT_EQ(orig, CINT(š));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'z');
    EXPECT_EQ(orig, CINT(Ž));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 'i');
    EXPECT_EQ(orig, CINT(í));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 't');
    EXPECT_EQ(orig, 't');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss02, &orig), 0xcab0);
    EXPECT_EQ(orig, 0xcab0);

    std::istringstream ss03("můžeš Žít\xca\xb0 \xE7\x8C\xAB \xF0\x9D\x8D\x8C .");
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'm');
    EXPECT_EQ(orig, 'm');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'm');
    EXPECT_EQ(orig, 'm');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'u');
    EXPECT_EQ(orig, CINT(ů));
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'u');
    EXPECT_EQ(orig, CINT(ů));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'z');
    EXPECT_EQ(orig, CINT(ž));
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'z');
    EXPECT_EQ(orig, CINT(ž));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'e');
    EXPECT_EQ(orig, 'e');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'e');
    EXPECT_EQ(orig, 'e');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 's');
    EXPECT_EQ(orig, CINT(š));
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 's');
    EXPECT_EQ(orig, CINT(š));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'z');
    EXPECT_EQ(orig, CINT(Ž));
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'z');
    EXPECT_EQ(orig, CINT(Ž));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'i');
    EXPECT_EQ(orig, CINT(í));
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 'i');
    EXPECT_EQ(orig, CINT(í));
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 't');
    EXPECT_EQ(orig, 't');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 't');
    EXPECT_EQ(orig, 't');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xcab0);
    EXPECT_EQ(orig, 0xcab0);
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xcab0);
    EXPECT_EQ(orig, 0xcab0);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xE78CAB);
    EXPECT_EQ(orig, 0xE78CAB);
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xE78CAB);
    EXPECT_EQ(orig, 0xE78CAB);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xF09D8D8C);
    EXPECT_EQ(orig, 0xF09D8D8C);
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), 0xF09D8D8C);
    EXPECT_EQ(orig, 0xF09D8D8C);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), ' ');
    EXPECT_EQ(orig, ' ');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), '.');
    EXPECT_EQ(orig, '.');
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), '.');
    EXPECT_EQ(orig, '.');
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), EOF);
    EXPECT_EQ(orig, EOF);
    AMStreamToLowercaseStripDiaUnget(ss03);
    AMStreamToLowercaseStripDiaUnget(ss03);
    EXPECT_EQ(AMStreamToLowercaseStripDiaGet(ss03, &orig), '.');
    EXPECT_EQ(orig, '.');
    std::string ss04("můžeš Žít\xca\xb0 \xE7\x8C\xAB \xF0\x9D\x8D\x8C .");
    std::string::const_iterator it = ss04.begin();
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'm');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'u');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'z');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'e');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 's');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'z');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 'i');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 't');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 0xCAB0);
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 0xE78CAB);
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), 0xF09D8D8C);
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaGet(it, ss04), '.');
    EXPECT_EQ(it, ss04.end());
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), '.');
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), 0xF09D8D8C);
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), 0xE78CAB);
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), ' ');
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), 0xCAB0);
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), 't');
    EXPECT_EQ(AMStringToLowercaseStripDiaUnget(it, ss04), 'i');



    std::string s20("Ahoj ");
    AMStringPushback(s20, 'Z');
    EXPECT_STREQ(s20.c_str(), "Ahoj Z");
    AMStringPushback(s20, CINT(Ž));
    EXPECT_STREQ(s20.c_str(), "Ahoj ZŽ");



}

TEST(AMTextUtils, explodeImplode)
{
    std::string line1("red, green  , blue , yellow");
    std::map<int, std::string> rv1 = AMExplode(",", line1);
    EXPECT_THAT(rv1, ElementsAre(Pair(0, "red"), Pair(1,"green"), Pair(2, "blue"), Pair(3, "yellow")));
    std::map<int, std::string> rv2 = AMExplode(",", line1, false);
    EXPECT_THAT(rv2, ElementsAre(Pair(0, "red"), Pair(1," green  "), Pair(2, " blue "), Pair(3, " yellow")));
    std::string rv3 = AMImplode(", ", rv1);
    EXPECT_STREQ(rv3.c_str(), "red, green, blue, yellow");
    std::string s1 = "red";
    EXPECT_STREQ(AMStringStripSpaces(s1).c_str(), "red");
    std::string s2 = " green";
    EXPECT_STREQ(AMStringStripSpaces(s2).c_str(), "green");
    std::string s3 = " blue ";
    EXPECT_STREQ(AMStringStripSpaces(s3).c_str(), "blue");


    std::string line4("red, , , yellow");
    rv1 = AMExplode(",", line4);
    EXPECT_THAT(rv1, ElementsAre(Pair(0, "red"), Pair(1,""), Pair(2, ""), Pair(3, "yellow")));

    std::string line5("");
    rv1 = AMExplode(",", line5);
    EXPECT_EQ(rv1.size(), 0);

    std::string line6("mamma");
    rv1 = AMExplode(",", line6);
    EXPECT_EQ(rv1.size(), 1);
    EXPECT_STREQ(rv1[0].c_str(), "mamma");

    std::string line7("h");
    rv1 = AMExplode(",", line7);
    EXPECT_EQ(rv1.size(), 1);
    EXPECT_STREQ(rv1[0].c_str(), "h");
}

TEST(AMTextUtils, containsCharacters)
{
    std::string s1("ahoj!");
    EXPECT_TRUE(AMStringContainsOnlyCharacters("%a!", s1));
    std::string s2("aHoj!");
    EXPECT_FALSE(AMStringContainsOnlyCharacters("%a!", s2));
    std::string s3("ahoj+");
    EXPECT_FALSE(AMStringContainsOnlyCharacters("%a!", s3));
    std::string s4("aHoj!");
    EXPECT_TRUE(AMStringContainsOnlyCharacters("%a%A!", s4));
    std::string s5("aHój");
    EXPECT_TRUE(AMStringContainsOnlyCharacters("%l", s5));
    std::string s6("<a href=\"gg\">ÁÁhóójj!!!</a>");
    EXPECT_TRUE(AMStringContainsOnlyCharacters("%l <>/=\"!", s6));
}

TEST(AMTextUtils, escapes)
{
    std::string s1("ahoj @ Andy@");
    EXPECT_STREQ(AMEscape(s1, "@", '@').c_str(), "ahoj @@ Andy@@");

    std::string s2("ahoj @@ Andy");
    EXPECT_STREQ(AMEscape(s2, "@", '@').c_str(), "ahoj @@@@ Andy");

    std::string s3("ahoj @ Andy@@@");
    EXPECT_STREQ(AMEscape(s3, "@", '@').c_str(), "ahoj @@ Andy@@@@@@");

    std::string s4("ahoj Andy");
    EXPECT_STREQ(AMEscape(s4, "@", '@').c_str(), "ahoj Andy");

    std::string s1b("ahoj @ Andy@");
    EXPECT_STREQ(AMUnEscape(s1b, '@').c_str(), "ahoj @ Andy@");

    std::string s2b("ahoj @@ Andy");
    EXPECT_STREQ(AMUnEscape(s2b, '@').c_str(), "ahoj @ Andy");

    std::string s3b("ahoj @@ Andy@@@@@@");
    EXPECT_STREQ(AMUnEscape(s3b, '@').c_str(), "ahoj @ Andy@@@");

    std::string s4b("ahoj Andy");
    EXPECT_STREQ(AMUnEscape(s4b, '@').c_str(), "ahoj Andy");
}

TEST(AMTextUtils, stringStripped) {
    AMStringStripped st0("Řidič");
    std::string sn0("Řidič");
    EXPECT_FALSE(sn0 < st0);
    EXPECT_FALSE(st0 < sn0);

    AMStringStripped st1("Řidič");
    AMStringStripped sn1("Řidič");
    EXPECT_FALSE(sn1 < st1);
    EXPECT_FALSE(st1 < sn1);

    std::string st2("Řidič");
    AMStringStripped sn2("Řidič");
    EXPECT_FALSE(sn2 < st2);
    EXPECT_FALSE(st2 < sn2);


    AMStringStripped st3("Řidič");
    std::string sn3("ridic");
    EXPECT_FALSE(sn3 < st3);
    EXPECT_FALSE(st3 < sn3);

    AMStringStripped st4("Řidič");
    AMStringStripped sn4("ridic");
    EXPECT_FALSE(sn4 < st4);
    EXPECT_FALSE(st4 < sn4);

    std::string st5("Řidič");
    AMStringStripped sn5("ridic");
    EXPECT_FALSE(sn5 < st5);
    EXPECT_FALSE(st5 < sn5);


    AMStringStripped st6("Řidič");
    std::string sn6("r");
    EXPECT_TRUE(sn6 < st6);
    EXPECT_FALSE(st6 < sn6);

    AMStringStripped st7("Řidič");
    AMStringStripped sn7("r");
    EXPECT_TRUE(sn7 < st7);
    EXPECT_FALSE(st7 < sn7);

    std::string st8("Řidič");
    AMStringStripped sn8("r");
    EXPECT_FALSE(sn8 < st8);
    EXPECT_FALSE(st8 < sn8);
}


const char* wordstab[]={ "", "Agnes", "agnes", "Ágnes", "ágnes", "Ridic", "ridic", "Řidič", "řidič", "Smouha",
                        "smouha", "Šmouha", "šmouha", "Hůl", "hůl", "Chlad", "chlad", "Internet", "internet"};
const char* chartab[]={"A", "a", "Á", "á", "B", "b", "C", "c", "Č", "č", "D", "d", "Ď", "ď", "E", "e", "É", "é",
                       "Ě", "ě", "F", "f", "G", "g", "H", "h", "Ch", "ch", "I", "i", "Í", "í", "J", "j", "K", "k",
                       "L", "l", "M", "m", "N", "n", "Ň", "ň", "O", "o", "Ó", "ó", "P", "p", "Q", "q", "R", "r",
                       "Ř", "ř", "S", "s", "Š", "š", "T", "t", "Ť", "ť", "U", "u", "Ú", "ú", "Ů", "ů", "V", "v",
                       "W", "w", "X", "x", "Y", "y", "Ý", "ý", "Z", "z", "Ž," "ž"};
TEST(AMTextUtils, stringCZ) {
    std::set<std::string> ss;
    for(int i = 0; i < sizeof(wordstab) / sizeof(const char*); i++ ) {
        ss.insert(wordstab[i]);
    }
    std::ostringstream oss;
    for(auto it: ss) {
        oss<<'-'<<it;
    }
    oss<<'-';
    EXPECT_STREQ(oss.str().c_str(),"--Agnes-Chlad-Hůl-Internet-Ridic-Smouha-agnes-chlad-hůl-internet-ridic-smouha-Ágnes-ágnes-Řidič-řidič-Šmouha-šmouha-");

    std::set<AMStringCZ> sc;
    for(int i = 0; i < sizeof(wordstab) / sizeof(const char*); i++ ) {
        sc.insert(wordstab[i]);
    }
    std::ostringstream osc;
    for(auto it: sc) {
        osc<<'-'<<it;
    }
    osc<<'-';
    EXPECT_STREQ(osc.str().c_str(),"--Agnes-agnes-Ágnes-ágnes-Hůl-hůl-Chlad-chlad-Internet-internet-Ridic-ridic-Řidič-řidič-Smouha-smouha-Šmouha-šmouha-");

    std::set<AMStringCZ> sx;
    for(int i = 0; i < sizeof(chartab) / sizeof(const char*); i++ ) {
        sx.insert(chartab[i]);
    }
    std::ostringstream osx;
    for(auto it: sx) {
        osx<<'-'<<it;
    }
    osx<<'-';
    EXPECT_STREQ(osx.str().c_str(),"-A-a-Á-á-B-b-C-c-Č-č-D-d-Ď-ď-E-e-É-é-Ě-ě-F-f-G-g-H-h-Ch-ch-I-i-Í-í-J-j-K-k-L-l-M-m-N-n-Ň-ň-O-o-Ó-ó-P-p-Q-q-R-r-Ř-ř-S-s-Š-š-T-t-Ť-ť-U-u-Ú-ú-Ů-ů-V-v-W-w-X-x-Y-y-Ý-ý-Z-z-Ž,ž-");
}


int main(int argc, char **argv) {

    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
