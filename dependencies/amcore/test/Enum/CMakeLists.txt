cmake_minimum_required(VERSION 3.10)
set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "../../build/tests/Enum")
project(enum)

set(CMAKE_CXX_STANDARD 17)
set(SOURCE_FILES test_AMEnum.cpp ../../AMEnum.h)
include_directories(../../include ../../../3rdparty/include)
link_directories("../../../3rdparty/lib" "../../build/bin/")

add_executable(enum test_AMEnum.cpp)
target_link_libraries(enum libgtest.a pthread AMCore)
