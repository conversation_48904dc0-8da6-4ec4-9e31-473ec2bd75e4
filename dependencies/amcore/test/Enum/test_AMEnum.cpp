#include "../../AMEnum.h"
#include "gtest/gtest.h"


using namespace std;
using namespace AMCore;


class AMEnumerationTypeBaseTest: public AMEnumerationTypeBase
{
public:
    static constexpr const char* readName(const char* src)
    {
        return AMEnumerationTypeBase::readName(src);
    }
    static constexpr const char* readExpression(const char* src, char endChar, int nBraces = 0, int nTempl = 0)
    {
        return AMEnumerationTypeBase::readExpression(src, endChar, nBraces, nTempl);
    }
};


TEST(AMEnum, Parse) {

	const char* s01 = "green  ,  fda";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readName(s01), &s01[5]);
	const char* s02 = "pink";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readName(s02), &s02[4]);
	const char* s03 = "red=const_int_a";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readName(s03), &s03[3]);
	const char* s04 = " ,kk";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s04, ','), &s04[2]);
	const char* s05 = "=const_int_a";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s05, ','), nullptr);
	const char* s06 = "=const_int_a , l";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s06, ','), &s06[14]);
	const char* s07 = " = ll , ";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s07, ','), &s07[7]);
	const char* s08 = "=1(hgdf,gfd(gsd,tt)) ,ll";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s08, ','), &s08[22]);
	const char* s09 = " = gsdf<klkl,klklkk<hgfdfg> > ,b";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s09, ','), &s09[31]);
	const char* s10 = "=gsdf<klkl,klklkk<hgfdfg>> +5,";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s10, ','), &s10[30]);
	const char* s11 = "=gsdf<klkl,klklkk<hgfdfg>> klk > > +5, nn";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s11, ','), &s11[38]);
	const char* s12 = " = gsdf<klkl,klklkk<hgfdfg>>jjkllk , mm";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s12, ','), &s12[36]);
	const char* s13 = " = gsdf<klkl,klklkk<hgfdfg>>jjkllk  mm";
	EXPECT_EQ(AMEnumerationTypeBaseTest::readExpression(s13, ','), nullptr);
}

const int const_int_a=0x89;


ENUM( colors , Normal ,
	green ,
	blue ,
	pink ,
	red=const_int_a
)

ENUM( longenum , Binary ,
	bit_one = 1 ,
	bit_high = 0x400000000LL ,
 	bit_byte = 0xF0
)

ENUM( numbers , AllDefined,
	one = 1,
	two = 2,
	ten = 10
)

ENUM( states , Normal,
	PREPARE = 1,
	START = 2,
	WORK,
	FINISHING = 6,
	DONE = 5
)

/*
ENUM( states , Normal,
__(PREPARE)=1 ,
__(START)=2 ,
__(WORK),
__(FINISHING),
__(DONE))
*/
TEST(AMEnum, Indexes) {
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex("green"), 0);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex("blue"), 1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex("pink"), 2);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex("red"), 3);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex("brown"), -1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(""), -1);

	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex("bit_one"), 0);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex("bit_high"), 1);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex("bit_byte"), 2);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex("multibit"), -1);

	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex("one"), 0);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex("two"), 1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex("ten"), 2);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex("seven"), -1);

	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("PREPARE"), 0);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("START"), 1);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("WORK"), 2);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("FINISHING"), 3);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("DONE"), 4);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex("NOTWORK"), -1);

	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("green")), 0);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("blue")), 1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("pink")), 2);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("red")), 3);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("brown")), -1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(_("")), -1);

	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(_("bit_one")), 0);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(_("bit_high")), 1);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(_("bit_byte")), 2);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(_("multibit")), -1);

	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(_("one")), 0);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(_("two")), 1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(_("ten")), 2);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(_("seven")), -1);

	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("PREPARE")), 0);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("START")), 1);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("WORK")), 2);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("FINISHING")), 3);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("DONE")), 4);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(_("NOTWORK")), -1);

	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(0), 0);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(1), 1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(2), 2);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(0x89), 3);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(0x55), -1);
	EXPECT_EQ(AMEnumerationType_colors<int>::findIndex(-55), -1);

	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(1LL), 0);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(0x400000000LL), 1);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex(0xF0LL), 2);
	EXPECT_EQ(AMEnumerationType_longenum<int>::findIndex((long)0LL), -1);

	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(1), 0);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(2), 1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(10), 2);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(7), -1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::findIndex(-1), -1);

	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(1), 0);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(2), 1);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(3), 2);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(6), 3);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(5), 4);
	EXPECT_EQ(AMEnumerationType_states<int>::findIndex(4), -1);

	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex("green"), 0);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex("blue"), 1);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex("pink"), 2);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex("red"), 3);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex("brown"), -1);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(""), -1);

	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex("bit_one"), 0);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex("bit_high"), 1);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex("bit_byte"), 2);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex("multibit"), -1);

	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex("one"), 0);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex("two"), 1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex("ten"), 2);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex("seven"), -1);

	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("PREPARE"), 0);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("START"), 1);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("WORK"), 2);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("FINISHING"), 3);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("DONE"), 4);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex("NOTWORK"), -1);

	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(0), 0);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(1), 1);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(2), 2);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(0x89), 3);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(0x55), -1);
	EXPECT_EQ(AMEnumerationType_colors<int>::ceFindIndex(-55), -1);

	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex(1LL), 0);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex(0x400000000LL), 1);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex(0xF0LL), 2);
	EXPECT_EQ(AMEnumerationType_longenum<int>::ceFindIndex((long)0LL), -1);

	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex(1), 0);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex(2), 1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex(10), 2);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex(7), -1);
	EXPECT_EQ(AMEnumerationType_numbers<int>::ceFindIndex(-1), -1);

	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(1), 0);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(2), 1);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(3), 2);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(6), 3);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(5), 4);
	EXPECT_EQ(AMEnumerationType_states<int>::ceFindIndex(4), -1);

	switch (7) {
		case AMEnumerationType_numbers<int>::ceFindIndex("two"): break;
		case AMEnumerationType_numbers<int>::ceFindIndex(10): break;
		default: break;
	}
};

TEST(AMEnum, Main) {

	EXPECT_STREQ(AMEnumToString(colors::green),"green");
	EXPECT_STREQ(AMEnumToString(colors::blue),"blue");
	EXPECT_STREQ(AMEnumToString(colors::pink),"pink");
	EXPECT_STREQ(AMEnumToString(colors::red),"red");
	try {
		const char* str =AMEnumToString(static_cast<colors>(77));
		FAIL() << "Undefined enumeration conversion should throw exception";
	}
	catch(AMEnumException& e) {
		EXPECT_STREQ(e.what(), "AMEnum: colors::AMEnumToString (Normal): value 77 out of range.\n");
	}
	catch(...) {
		FAIL() << "Enueration conversion throws and unknown exception type.";
	}

	EXPECT_STREQ(AMEnumToString(states::PREPARE),"PREPARE");
	EXPECT_STREQ(AMEnumToString(states::START),"START");
	EXPECT_STREQ(AMEnumToString(states::WORK),"WORK");
	EXPECT_STREQ(AMEnumToString(states::FINISHING),"FINISHING");
	EXPECT_STREQ(AMEnumToString(states::DONE),"DONE");
	try {
		const char* str =AMEnumToString(static_cast<states>(75));
		FAIL() << "Undefined enumeration conversion should throw exception";
	}
	catch(AMEnumException& e) {
		EXPECT_STREQ(e.what(), "AMEnum: states::AMEnumToString (Normal): value 75 out of range.\n");
	}
	catch(...) {
		FAIL() << "Enueration conversion throws and unknown exception type.";
	}

	char buff[64];
	EXPECT_STREQ(AMEnumToString(numbers::one, buff, 64),"one");
	EXPECT_STREQ(AMEnumToString(numbers::two, buff, 64),"two");
	EXPECT_STREQ(AMEnumToString(numbers::ten, buff, 64),"ten");
	EXPECT_STREQ(AMEnumToString(static_cast<numbers>(73), buff, 64),"73");
}

TEST(AMEnum, Pokus) {

	auto tab1 = AMEnumerationType_colors<int>::_M_string_enum;
	for(int i=0;i < tab1.size() ; i++) {
		std::cout<<tab1[i]._M_name_item._M_original_str<<"="<<tab1[i]._M_value<<std::endl;
	}
	auto tab1b = AMEnumerationType_colors<int>::_M_string_index;
	for(int i=0;i < tab1b.size() ; i++) {
		std::cout<<"SI "<<i<<"="<<tab1b[i]<<std::endl;
	}
	auto tab1c = AMEnumerationType_colors<int>::_M_enum_index;
	for(int i=0;i < tab1c.size() ; i++) {
		std::cout<<"EI "<<i<<"="<<tab1c[i]<<std::endl;
	}

	auto tab2 = AMEnumerationType_longenum<int>::_M_string_enum;
	for(int i=0;i < tab2.size() ; i++) {
		std::cout<<tab2[i]._M_name_item._M_original_str<<"="<<tab2[i]._M_value<<std::endl;
	}

	auto tab2b = AMEnumerationType_longenum<int>::_M_string_index;
	for(int i=0;i < tab2b.size() ; i++) {
		std::cout<<"SI "<<i<<"="<<tab2b[i]<<std::endl;
	}
	auto tab2c = AMEnumerationType_longenum<int>::_M_enum_index;
	for(int i=0;i < tab2c.size() ; i++) {
		std::cout<<"EI "<<i<<"="<<tab2c[i]<<std::endl;
	}

	auto tab3 = AMEnumerationType_numbers<int>::_M_string_enum;
	for(int i=0;i < tab3.size() ; i++) {
		std::cout<<tab3[i]._M_name_item._M_original_str<<"="<<tab3[i]._M_value<<std::endl;
	}

	auto tab3b = AMEnumerationType_numbers<int>::_M_string_index;
	for(int i=0;i < tab3b.size() ; i++) {
		std::cout<<"SI "<<i<<"="<<tab3b[i]<<std::endl;
	}
	auto tab3c = AMEnumerationType_numbers<int>::_M_enum_index;
	for(int i=0;i < tab3c.size() ; i++) {
		std::cout<<"EI "<<i<<"="<<tab3c[i]<<std::endl;
	}


	auto tab4 = AMEnumerationType_states<int>::_M_string_enum;
	for(int i=0;i < tab4.size() ; i++) {
		std::cout<<tab4[i]._M_name_item._M_original_str<<"="<<tab4[i]._M_value<<std::endl;
	}

	auto tab4b = AMEnumerationType_states<int>::_M_string_index;
	for(int i=0;i < tab4b.size() ; i++) {
		std::cout<<"SI "<<i<<"="<<tab4b[i]<<std::endl;
	}
	auto tab4c = AMEnumerationType_states<int>::_M_enum_index;
	for(int i=0;i < tab4c.size() ; i++) {
		std::cout<<"EI "<<i<<"="<<tab4c[i]<<std::endl;
	}
	std::cout<<AMEnumerationType_states<int>::_M_ce_value_min<<"..."<<AMEnumerationType_states<int>::_M_ce_value_length<<std::endl;

}


int main(int argc, char **argv) {

     ::testing::InitGoogleTest(&argc, argv);
     return RUN_ALL_TESTS();
}
