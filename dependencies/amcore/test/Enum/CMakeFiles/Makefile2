# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/Enum

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/Enum

#=============================================================================
# Target rules for target CMakeFiles/enum.dir

# All Build rule for target.
CMakeFiles/enum.dir/all:
	$(MAKE) -f CMakeFiles/enum.dir/build.make CMakeFiles/enum.dir/depend
	$(MAKE) -f CMakeFiles/enum.dir/build.make CMakeFiles/enum.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles --progress-num=1,2 "Built target enum"
.PHONY : CMakeFiles/enum.dir/all

# Include target in all.
all: CMakeFiles/enum.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
CMakeFiles/enum.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/enum.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles 0
.PHONY : CMakeFiles/enum.dir/rule

# Convenience name for target.
enum: CMakeFiles/enum.dir/rule

.PHONY : enum

# clean rule for target.
CMakeFiles/enum.dir/clean:
	$(MAKE) -f CMakeFiles/enum.dir/build.make CMakeFiles/enum.dir/clean
.PHONY : CMakeFiles/enum.dir/clean

# clean rule for target.
clean: CMakeFiles/enum.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

