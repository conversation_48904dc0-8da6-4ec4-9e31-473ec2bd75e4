# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/Enum

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/Enum

# Include any dependencies generated for this target.
include CMakeFiles/enum.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/enum.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/enum.dir/flags.make

CMakeFiles/enum.dir/test_AMEnum.cpp.o: CMakeFiles/enum.dir/flags.make
CMakeFiles/enum.dir/test_AMEnum.cpp.o: test_AMEnum.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/enum.dir/test_AMEnum.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/enum.dir/test_AMEnum.cpp.o -c /home/<USER>/dev/andromeda/amcore/test/Enum/test_AMEnum.cpp

CMakeFiles/enum.dir/test_AMEnum.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/enum.dir/test_AMEnum.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/dev/andromeda/amcore/test/Enum/test_AMEnum.cpp > CMakeFiles/enum.dir/test_AMEnum.cpp.i

CMakeFiles/enum.dir/test_AMEnum.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/enum.dir/test_AMEnum.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/dev/andromeda/amcore/test/Enum/test_AMEnum.cpp -o CMakeFiles/enum.dir/test_AMEnum.cpp.s

CMakeFiles/enum.dir/test_AMEnum.cpp.o.requires:

.PHONY : CMakeFiles/enum.dir/test_AMEnum.cpp.o.requires

CMakeFiles/enum.dir/test_AMEnum.cpp.o.provides: CMakeFiles/enum.dir/test_AMEnum.cpp.o.requires
	$(MAKE) -f CMakeFiles/enum.dir/build.make CMakeFiles/enum.dir/test_AMEnum.cpp.o.provides.build
.PHONY : CMakeFiles/enum.dir/test_AMEnum.cpp.o.provides

CMakeFiles/enum.dir/test_AMEnum.cpp.o.provides.build: CMakeFiles/enum.dir/test_AMEnum.cpp.o


# Object files for target enum
enum_OBJECTS = \
"CMakeFiles/enum.dir/test_AMEnum.cpp.o"

# External object files for target enum
enum_EXTERNAL_OBJECTS =

enum: CMakeFiles/enum.dir/test_AMEnum.cpp.o
enum: CMakeFiles/enum.dir/build.make
enum: CMakeFiles/enum.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable enum"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/enum.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/enum.dir/build: enum

.PHONY : CMakeFiles/enum.dir/build

CMakeFiles/enum.dir/requires: CMakeFiles/enum.dir/test_AMEnum.cpp.o.requires

.PHONY : CMakeFiles/enum.dir/requires

CMakeFiles/enum.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/enum.dir/cmake_clean.cmake
.PHONY : CMakeFiles/enum.dir/clean

CMakeFiles/enum.dir/depend:
	cd /home/<USER>/dev/andromeda/amcore/test/Enum && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/andromeda/amcore/test/Enum /home/<USER>/dev/andromeda/amcore/test/Enum /home/<USER>/dev/andromeda/amcore/test/Enum /home/<USER>/dev/andromeda/amcore/test/Enum /home/<USER>/dev/andromeda/amcore/test/Enum/CMakeFiles/enum.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/enum.dir/depend

