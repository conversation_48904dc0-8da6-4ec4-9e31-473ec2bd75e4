#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../../../3rdparty/include/gtest/gtest-death-test.h
gtest/internal/gtest-death-test-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-death-test-internal.h

../../../../3rdparty/include/gtest/gtest-matchers.h
memory
-
ostream
-
string
-
type_traits
-
gtest/gtest-printers.h
../../../../3rdparty/include/gtest/gtest/gtest-printers.h
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-port.h

../../../../3rdparty/include/gtest/gtest-message.h
limits
-
memory
-
sstream
-
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-port.h

../../../../3rdparty/include/gtest/gtest-param-test.h
iterator
-
utility
-
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-param-util.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-param-util.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-port.h

../../../../3rdparty/include/gtest/gtest-printers.h
functional
-
ostream
-
sstream
-
string
-
tuple
-
type_traits
-
utility
-
vector
-
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-port.h
absl/strings/string_view.h
../../../../3rdparty/include/gtest/absl/strings/string_view.h
absl/types/optional.h
../../../../3rdparty/include/gtest/absl/types/optional.h
absl/types/variant.h
../../../../3rdparty/include/gtest/absl/types/variant.h
gtest/internal/custom/gtest-printers.h
../../../../3rdparty/include/gtest/gtest/internal/custom/gtest-printers.h

../../../../3rdparty/include/gtest/gtest-test-part.h
iosfwd
-
vector
-
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-string.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-string.h

../../../../3rdparty/include/gtest/gtest-typed-test.h
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-port.h
gtest/internal/gtest-type-util.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-type-util.h

../../../../3rdparty/include/gtest/gtest.h
cstddef
-
limits
-
memory
-
ostream
-
type_traits
-
vector
-
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-internal.h
gtest/internal/gtest-string.h
../../../../3rdparty/include/gtest/gtest/internal/gtest-string.h
gtest/gtest-death-test.h
../../../../3rdparty/include/gtest/gtest/gtest-death-test.h
gtest/gtest-matchers.h
../../../../3rdparty/include/gtest/gtest/gtest-matchers.h
gtest/gtest-message.h
../../../../3rdparty/include/gtest/gtest/gtest-message.h
gtest/gtest-param-test.h
../../../../3rdparty/include/gtest/gtest/gtest-param-test.h
gtest/gtest-printers.h
../../../../3rdparty/include/gtest/gtest/gtest-printers.h
gtest/gtest_prod.h
../../../../3rdparty/include/gtest/gtest/gtest_prod.h
gtest/gtest-test-part.h
../../../../3rdparty/include/gtest/gtest/gtest-test-part.h
gtest/gtest-typed-test.h
../../../../3rdparty/include/gtest/gtest/gtest-typed-test.h
gtest/gtest_pred_impl.h
../../../../3rdparty/include/gtest/gtest/gtest_pred_impl.h

../../../../3rdparty/include/gtest/gtest_pred_impl.h
gtest/gtest.h
../../../../3rdparty/include/gtest/gtest/gtest.h

../../../../3rdparty/include/gtest/gtest_prod.h

../../../../3rdparty/include/gtest/internal/custom/gtest-port.h

../../../../3rdparty/include/gtest/internal/custom/gtest-printers.h

../../../../3rdparty/include/gtest/internal/gtest-death-test-internal.h
gtest/gtest-matchers.h
../../../../3rdparty/include/gtest/internal/gtest/gtest-matchers.h
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-internal.h
stdio.h
-
memory
-

../../../../3rdparty/include/gtest/internal/gtest-filepath.h
gtest/internal/gtest-string.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-string.h

../../../../3rdparty/include/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-port.h
stdlib.h
-
sys/types.h
-
sys/wait.h
-
unistd.h
-
stdexcept
-
ctype.h
-
float.h
-
string.h
-
cstdint
-
iomanip
-
limits
-
map
-
set
-
string
-
type_traits
-
vector
-
gtest/gtest-message.h
../../../../3rdparty/include/gtest/internal/gtest/gtest-message.h
gtest/internal/gtest-filepath.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-filepath.h
gtest/internal/gtest-string.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-string.h
gtest/internal/gtest-type-util.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-type-util.h

../../../../3rdparty/include/gtest/internal/gtest-param-util.h
ctype.h
-
cassert
-
iterator
-
memory
-
set
-
tuple
-
type_traits
-
utility
-
vector
-
gtest/internal/gtest-internal.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-internal.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-port.h
gtest/gtest-printers.h
../../../../3rdparty/include/gtest/internal/gtest/gtest-printers.h
gtest/gtest-test-part.h
../../../../3rdparty/include/gtest/internal/gtest/gtest-test-part.h

../../../../3rdparty/include/gtest/internal/gtest-port-arch.h
winapifamily.h
-

../../../../3rdparty/include/gtest/internal/gtest-port.h
ctype.h
-
stddef.h
-
stdio.h
-
stdlib.h
-
string.h
-
cstdint
-
limits
-
type_traits
-
sys/types.h
-
sys/stat.h
-
AvailabilityMacros.h
-
TargetConditionals.h
-
iostream
-
memory
-
string
-
tuple
-
vector
-
gtest/internal/custom/gtest-port.h
../../../../3rdparty/include/gtest/internal/gtest/internal/custom/gtest-port.h
gtest/internal/gtest-port-arch.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-port-arch.h
direct.h
-
io.h
-
unistd.h
-
strings.h
-
android/api-level.h
-
regex.h
-
typeinfo
-
pthread.h
-
time.h
-
absl/strings/string_view.h
../../../../3rdparty/include/gtest/internal/absl/strings/string_view.h
string_view
-

../../../../3rdparty/include/gtest/internal/gtest-string.h
mem.h
-
string.h
-
cstdint
-
string
-
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-port.h

../../../../3rdparty/include/gtest/internal/gtest-type-util.h
gtest/internal/gtest-port.h
../../../../3rdparty/include/gtest/internal/gtest/internal/gtest-port.h
cxxabi.h
-
acxx_demangle.h
-

../../../include/AMCore/AMCEFNV1a.h
cstdint
-

../../../include/AMCore/AMEnum.h
limits
-
array
-
cstring
-
stdexcept
-
AMCore/AMLString.h
../../../include/AMCore/AMCore/AMLString.h

../../../include/AMCore/AMLString.h
utility
-
AMCore/AMCEFNV1a.h
../../../include/AMCore/AMCore/AMCEFNV1a.h

/home/<USER>/dev/andromeda/amcore/test/Enum/test_AMEnum.cpp
AMCore/AMEnum.h
-
gtest/gtest.h
/home/<USER>/dev/andromeda/amcore/test/Enum/gtest/gtest.h

