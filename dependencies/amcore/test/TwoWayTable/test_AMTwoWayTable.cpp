#include "../../AMTwoWayTable.h"
#include <string>
#include "gtest/gtest.h"

using namespace AMCore;


TEST(AMTwoWayTable, basicTest)
{
    AMTwoWayTable<std::string, std::string> t;
    t.insert("banana", "yellow");
    t.insert("tomato", "red");
    t.insert("cucumber", "green");

    EXPECT_NE(t.findA("yellow"), nullptr);
    EXPECT_STREQ(t.findA("yellow")->c_str(), "banana");
    EXPECT_STREQ(t.findA("red")->c_str(), "tomato");
    EXPECT_STREQ(t.findA("green")->c_str(), "cucumber");
    EXPECT_EQ(t.findA("pink"), nullptr);

    EXPECT_STREQ(t.findB("banana")->c_str(), "yellow");
    EXPECT_STREQ(t.findB("tomato")->c_str(), "red");
    EXPECT_STREQ(t.findB("cucumber")->c_str(), "green");
    EXPECT_EQ(t.findB("apple"), nullptr);
}


int main(int argc, char **argv) {

     ::testing::InitGoogleTest(&argc, argv);
     return RUN_ALL_TESTS();
}
