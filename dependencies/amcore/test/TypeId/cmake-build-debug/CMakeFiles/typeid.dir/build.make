# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.17

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake

# The command to remove a file.
RM = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/TypeId

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug

# Include any dependencies generated for this target.
include CMakeFiles/typeid.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/typeid.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/typeid.dir/flags.make

CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o: CMakeFiles/typeid.dir/flags.make
CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o: ../test_AMCETypeId.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o -c /home/<USER>/dev/andromeda/amcore/test/TypeId/test_AMCETypeId.cpp

CMakeFiles/typeid.dir/test_AMCETypeId.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/typeid.dir/test_AMCETypeId.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/dev/andromeda/amcore/test/TypeId/test_AMCETypeId.cpp > CMakeFiles/typeid.dir/test_AMCETypeId.cpp.i

CMakeFiles/typeid.dir/test_AMCETypeId.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/typeid.dir/test_AMCETypeId.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/dev/andromeda/amcore/test/TypeId/test_AMCETypeId.cpp -o CMakeFiles/typeid.dir/test_AMCETypeId.cpp.s

# Object files for target typeid
typeid_OBJECTS = \
"CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o"

# External object files for target typeid
typeid_EXTERNAL_OBJECTS =

typeid: CMakeFiles/typeid.dir/test_AMCETypeId.cpp.o
typeid: CMakeFiles/typeid.dir/build.make
typeid: CMakeFiles/typeid.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable typeid"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/typeid.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/typeid.dir/build: typeid

.PHONY : CMakeFiles/typeid.dir/build

CMakeFiles/typeid.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/typeid.dir/cmake_clean.cmake
.PHONY : CMakeFiles/typeid.dir/clean

CMakeFiles/typeid.dir/depend:
	cd /home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/andromeda/amcore/test/TypeId /home/<USER>/dev/andromeda/amcore/test/TypeId /home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/TypeId/cmake-build-debug/CMakeFiles/typeid.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/typeid.dir/depend

