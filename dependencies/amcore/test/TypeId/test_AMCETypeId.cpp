/*
 * File:   main.cpp
 * Author: z<PERSON><PERSON>
 *
 * Created on 3. září 2019, 16:59
 */

#define COLLECTING_DATA

#include "../../AMCETypeId.h"
#include "gtest/gtest.h"
#include <algorithm>


using namespace std;
using namespace AMCore;


TEST(AMCETypeId, basicTest) {

	uint64_t sw = AMCETypeId<uint64_t>::hash_code();

    switch(sw)
    {
		case AMCETypeId<uint32_t>::hash_code(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<int64_t>::hash_code(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<uint64_t>::hash_code(): ;break;
		case AMCETypeId<char*>::hash_code(): FAIL() << "Switch: bad branch selected";break;
    }

	uint32_t sw32 = AMCETypeId<uint64_t>::hash_code32();

    switch(sw32)
    {
		case AMCETypeId<uint32_t>::hash_code32(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<int64_t>::hash_code32(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<uint64_t>::hash_code32(): ;break;
		case AMCETypeId<char*>::hash_code32(): FAIL() << "Switch: bad branch selected";break;
    }
	#if defined(__clang__)
		EXPECT_STREQ(AMCETypeId<uint64_t>::name(), "unsigned long");
		EXPECT_STREQ(AMCETypeId<int64_t>::name(), "long");
		EXPECT_STREQ(AMCETypeId<uint32_t>::name(), "unsigned int");
		EXPECT_STREQ(AMCETypeId<int32_t>::name(), "int");
		EXPECT_STREQ(AMCETypeId<char*>::name(), "char *");
		EXPECT_STREQ(AMCETypeId<std::string>::name(), "std::basic_string<char>");
	#elif defined(__GNUC__) && !defined(__clang__)
		EXPECT_STREQ(AMCETypeId<uint64_t>::name(), "long unsigned int");
		EXPECT_STREQ(AMCETypeId<int64_t>::name(), "long int");
		EXPECT_STREQ(AMCETypeId<uint32_t>::name(), "unsigned int");
		EXPECT_STREQ(AMCETypeId<int32_t>::name(), "int");
		EXPECT_STREQ(AMCETypeId<char*>::name(), "char*");
		EXPECT_STREQ(AMCETypeId<std::string>::name(), "std::__cxx11::basic_string<char>");
	#elif defined(_MSC_VER)
	    #error test it!!!
	#else
	    #error "Andromeda Library do not support this compiler."
	#endif


}

namespace AMCore {

    template<> inline constexpr uint64_t AMCETypeId<uint64_t>::id() { return 100;}
    template<> inline constexpr uint64_t AMCETypeId<int64_t>::id() { return 101;}
    template<> inline constexpr uint64_t AMCETypeId<const char*>::id() { return 102;}
    template<> inline constexpr uint64_t AMCETypeId<uint32_t>::id() { return 104;}
}

TEST(AMCETypeId, basicId) {

	uint64_t sw = AMCETypeId<uint64_t>::id();

    switch(sw)
    {
		case AMCETypeId<uint32_t>::id(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<int64_t>::id(): FAIL() << "Switch: bad branch selected";break;
		case AMCETypeId<uint64_t>::id(): ;break;
		case AMCETypeId<char*>::id(): FAIL() << "Switch: bad branch selected";break;
    }

    EXPECT_EQ(AMCETypeId<uint64_t>::id(), 100);
    EXPECT_EQ(AMCETypeId<int64_t>::id(), 101);
    EXPECT_EQ(AMCETypeId<uint32_t>::id(), 104);
    EXPECT_EQ(AMCETypeId<int32_t>::id(), 0x2B9FFF192BD4C83EULL);
    EXPECT_EQ(AMCETypeId<const char*>::id(), 102);
#if defined(__clang__)
////    EXPECT_EQ(AMCETypeId<std::string>::id(), 0xD5388B9D127E7868ULL);
    EXPECT_EQ(AMCETypeId<std::string>::id(), 0xDD402F4DE8D36956ULL);
#elif defined(__GNUC__) && !defined(__clang__)
    EXPECT_EQ(AMCETypeId<std::string>::id(), 0x5FE0D2AC503758FBULL);
#elif defined(_MSC_VER)
    #error test it!!!
#else
    #error "Andromeda Library do not support this compiler."
#endif

    EXPECT_EQ(AMCETypeId<uint64_t>::id32(), 100);
    EXPECT_EQ(AMCETypeId<int64_t>::id32(), 101);
    EXPECT_EQ(AMCETypeId<uint32_t>::id32(), 104);
    EXPECT_EQ(AMCETypeId<int32_t>::id32(), 0x2BD4C83EULL);

}

TEST(AMCETypeId, collecting) {

	uint64_t sw = AMCETypeId<uint64_t>::id();
	uint64_t sw2 = AMCETypeId<int64_t>::id();
	uint64_t sw3 = AMCETypeId<const char *>::id();
	uint64_t sw4 = AMCETypeId<char>::id();

    _T_AMTypeId_list_leaf<true>* leaf = _T_AMTypeId_list_leaf<true>::_M_first;
	//cout<<"TYPEID list:"<<endl;
	int i=1;
	while(leaf) {
		//cout<<i<<" : "<<leaf->_M_name<<" : "<<leaf->_M_hash<<endl;
		i++;
		leaf = leaf->_M_next;
	}
	EXPECT_EQ(i, 7+1);
	i=0;
	leaf = _T_AMTypeId_list_leaf<true>::_M_first;
	std::array<uint64_t,7> hashes;
	while(leaf) {
		hashes[i] = leaf->_M_hash;
		i++;
		leaf = leaf->_M_next;
	}
	//cout<<"TYPEID end:"<<endl;
	std::sort(hashes.begin(), hashes.end());
	#if defined(__clang__)
		EXPECT_EQ(hashes[0], AMCETypeId<char*>::hash_code());
		EXPECT_EQ(hashes[1], AMCETypeId<int32_t>::hash_code());
		EXPECT_EQ(hashes[2], AMCETypeId<uint64_t>::hash_code());
		EXPECT_EQ(hashes[3], AMCETypeId<uint32_t>::hash_code());
		EXPECT_EQ(hashes[4], AMCETypeId<int64_t>::hash_code());
		EXPECT_EQ(hashes[5], AMCETypeId<std::string>::hash_code());
		EXPECT_EQ(hashes[6], AMCETypeId<char>::hash_code());
	#elif defined(__GNUC__) && !defined(__clang__)
		EXPECT_EQ(hashes[0], AMCETypeId<uint64_t>::hash_code());
		EXPECT_EQ(hashes[1], AMCETypeId<int32_t>::hash_code());
		EXPECT_EQ(hashes[2], AMCETypeId<std::string>::hash_code());
		EXPECT_EQ(hashes[3], AMCETypeId<char*>::hash_code());
		EXPECT_EQ(hashes[4], AMCETypeId<uint32_t>::hash_code());
		EXPECT_EQ(hashes[5], AMCETypeId<int64_t>::hash_code());
		EXPECT_EQ(hashes[6], AMCETypeId<char>::hash_code());
	#elif defined(_MSC_VER)
	    #error test it!!!
	#else
	    #error "Andromeda Library do not support this compiler."
	#endif
}

int main(int argc, char **argv) {

     ::testing::InitGoogleTest(&argc, argv);
     return RUN_ALL_TESTS();
}
