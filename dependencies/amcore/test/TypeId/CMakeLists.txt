cmake_minimum_required(VERSION 3.10)
set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "../../build/tests/TypeId")
project(typeid)

set(CMAKE_CXX_STANDARD 14)
set(SOURCE_FILES test_AMCETypeId.cpp ../../AMCETypeId.h)
include_directories(../../include ../../../3rdparty/include)
link_directories("../../../3rdparty/lib" "../../build/bin/")

add_executable(typeid test_AMCETypeId.cpp)
target_link_libraries(typeid libgtest.a pthread AMCore)
