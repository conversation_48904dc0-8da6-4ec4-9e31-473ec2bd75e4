# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.17

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake

# The command to remove a file.
RM = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/Variant

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug

# Include any dependencies generated for this target.
include CMakeFiles/variant.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/variant.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/variant.dir/flags.make

CMakeFiles/variant.dir/test_AMVariant.cpp.o: CMakeFiles/variant.dir/flags.make
CMakeFiles/variant.dir/test_AMVariant.cpp.o: ../test_AMVariant.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/variant.dir/test_AMVariant.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/variant.dir/test_AMVariant.cpp.o -c /home/<USER>/dev/andromeda/amcore/test/Variant/test_AMVariant.cpp

CMakeFiles/variant.dir/test_AMVariant.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/variant.dir/test_AMVariant.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/dev/andromeda/amcore/test/Variant/test_AMVariant.cpp > CMakeFiles/variant.dir/test_AMVariant.cpp.i

CMakeFiles/variant.dir/test_AMVariant.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/variant.dir/test_AMVariant.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/dev/andromeda/amcore/test/Variant/test_AMVariant.cpp -o CMakeFiles/variant.dir/test_AMVariant.cpp.s

# Object files for target variant
variant_OBJECTS = \
"CMakeFiles/variant.dir/test_AMVariant.cpp.o"

# External object files for target variant
variant_EXTERNAL_OBJECTS =

variant: CMakeFiles/variant.dir/test_AMVariant.cpp.o
variant: CMakeFiles/variant.dir/build.make
variant: CMakeFiles/variant.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable variant"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/variant.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/variant.dir/build: variant

.PHONY : CMakeFiles/variant.dir/build

CMakeFiles/variant.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/variant.dir/cmake_clean.cmake
.PHONY : CMakeFiles/variant.dir/clean

CMakeFiles/variant.dir/depend:
	cd /home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/andromeda/amcore/test/Variant /home/<USER>/dev/andromeda/amcore/test/Variant /home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/Variant/cmake-build-debug/CMakeFiles/variant.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/variant.dir/depend

