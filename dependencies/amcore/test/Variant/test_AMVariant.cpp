/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/*
 * File:   main.cpp
 * Author: <PERSON><PERSON><PERSON>
 *
 * Created on 3. září 2019, 16:59
 */

//using namespace std;

#include "../../AMVariant.h"
#include "../../AMLString.h"
#include "gtest/gtest.h"

#include <any>

using namespace AMCore;

class test_t
{
public:
    test_t() : n_(0) { std::cout << "def_ctr " << __FUNCTION__ << " " << n_ << " " << this << std::endl; }
    test_t(int n) : n_(n) { std::cout << __FUNCTION__ << " " << n_ << " " << this << std::endl; }
    ~test_t() { std::cout << __FUNCTION__ << " " << n_ << " " << " " << this << std::endl; }
//    test_t(const test_t&) = default;
    test_t(const test_t& r) { n_ = r.n_; std::cout << "copy " << __FUNCTION__ << " " << n_ << " " << this << std::endl; }

    int get_n() const {return n_;}
private:
//    test_t();
    int n_;
//    char b_[256];
};

class test_c_t
{
public:
    test_c_t(int n) : n_(n) { std::cout << __FUNCTION__ << " " << n_ << " " << std::endl; }
    void operator()() { std::cout << __FUNCTION__ << " " << n_ << std::endl; }

private:
    int n_;
};

TEST(AMVariant, ConstructDestruct) {
//    std::any at(std::in_place_type<test_t>, 153);
//    const std::any& rat = at;
//    const test_t& pt = std::any_cast<const test_t>(rat);

    AMVariant vt(std::in_place_type<test_t>, 153);
    std::cout << "**" << std::endl;
    const AMVariant vt1(vt);
    std::cout << "***" << std::endl;

    const test_t& rtc = vt;
    std::cout << "****" << std::endl;
    test_t& rt = static_cast<test_t&>(vt);
    std::cout << "*****" << std::endl;
    const test_t& crt = static_cast<const test_t&>(vt1);
    std::cout << "******" << std::endl;

    vt.emplace<test_c_t>(111);
    const test_c_t* rct = &static_cast<const test_c_t&>(vt);
    std::cout << "---" << std::endl;

    const AMVariant cv(2.6f);
    EXPECT_EQ(2.6f, static_cast<const float&>(cv));
    std::cout << "----" << std::endl;

    int i = static_cast<const float&>(cv);
    EXPECT_EQ(2, i);
    std::cout << "-----" << std::endl;

    AMVariant v(cv);
    float f = v;
    EXPECT_EQ(2.6f, f);
    std::cout << "------" << std::endl;

    AMVariant mv(std::move(vt));
    std::cout << "mv created" << std::endl;

    vt = vt1;

    mv = std::move(vt);

    AMVariant fv = 123.e10;
    double d = fv;
    EXPECT_EQ(123.e10, d);

    fv = test_t(123);
#if 0
    AMVariant vt(std::in_place_type<test_t>, 153);
    const test_t* rt = &static_cast<const test_t&>(vt);

    vt.emplace<test_c_t>(111);
    vt.operator test_c_t()();

//      v = test_c_t(123);
      test_c_t tc(123);
      v = tc;
      v.operator test_c_t()();
#endif
    std::string s("***123");

    test_t t(321);
    v = t;
    test_t t1(static_cast<test_t&>(v));

    try
    {
        s = static_cast<std::string&>(v);
    }
    catch (std::bad_cast)
    {
        std::cout << "*** bad_cast ***" << std::endl;
    }

    v = s;
    s = static_cast<std::string&>(v);
}
#if 0
TEST(AMVariant, ConstructDestruct_copyable) {

std::cout << "<<<" << std::endl;
    AMVariant v(2.6f);
std::cout << ">>>" << std::endl;
//    std::any av(2.6f);
    std::string s("***123");

    std::cout << "size v " << sizeof(v) << " " << sizeof(AMVariant) << " " << sizeof(void*) << std::endl;
    std::cout << "size s " << sizeof(s) << " " << sizeof(std::string) << " " << sizeof(AMLString) << std::endl;
    std::cout << "copyable " << _T_AMVAR_is_location_invariant<AMVariant>::value << " " << std::is_nothrow_move_constructible<AMVariant>::value << std::endl;
    std::cout << "copyable " << _T_AMVAR_is_location_invariant<test_t>::value << " " << std::is_nothrow_move_constructible<test_t>::value << std::endl;
    std::cout << "copyable " << _T_AMVAR_is_location_invariant<AMCore::AMLString>::value << " " << std::is_nothrow_move_constructible<AMCore::AMLString>::value << std::endl;

//    int i = std::any_cast<float>(v);
    int i = static_cast<float>(v);

    test_t t(1);
    std::cout << "<<<" << std::endl;
    v = t;
    std::cout << ">>>" << std::endl;
//    t = std::any_cast<test_t>(av);
//    t = std::static_cast<test_t>(av);
    std::cout << "before t" << std::endl;
    test_t t1(static_cast<test_t>(v));

    std::cout << "before s" << std::endl;
    try
    {
        s = (std::string)v;
    }
    catch (std::bad_cast)
    {
        std::cout << "*** bad_cast ***" << std::endl;
    }
    std::cout << "print s " << s << std::endl;

    std::cout << "assign s" << std::endl;
    v = s;
//    s = std::any_cast<std::string>(v);
    s = static_cast<std::string>(v);
//    std::shared_ptr<void> sv(std::make_shared<test_t>(2));

//    std::cout << "before t" << std::endl;
//    test_t t1 = static_cast<test_t>(v);

    std::cout << s << std::endl;
}
#endif
int main(int argc, char **argv) {

    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
