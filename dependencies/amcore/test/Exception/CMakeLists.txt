cmake_minimum_required(VERSION 3.10)
set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "../../build/tests/Exception")
project(exception)

set(CMAKE_CXX_STANDARD 17)
set(SOURCE_FILES test_AMException.cpp ../../AMException.h)
include_directories(../../include ../../../3rdparty/include)
link_directories("../../../3rdparty/lib" "../../build/bin/")

add_executable(exception test_AMException.cpp)
target_link_libraries(exception libgtest.a pthread AMCore)
