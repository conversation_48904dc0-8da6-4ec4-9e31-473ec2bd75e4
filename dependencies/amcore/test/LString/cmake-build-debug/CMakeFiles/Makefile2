# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.17

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake

# The command to remove a file.
RM = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/LString

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/lstring.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/lstring.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/lstring.dir

# All Build rule for target.
CMakeFiles/lstring.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lstring.dir/build.make CMakeFiles/lstring.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lstring.dir/build.make CMakeFiles/lstring.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles --progress-num=1,2 "Built target lstring"
.PHONY : CMakeFiles/lstring.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/lstring.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/lstring.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles 0
.PHONY : CMakeFiles/lstring.dir/rule

# Convenience name for target.
lstring: CMakeFiles/lstring.dir/rule

.PHONY : lstring

# clean rule for target.
CMakeFiles/lstring.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/lstring.dir/build.make CMakeFiles/lstring.dir/clean
.PHONY : CMakeFiles/lstring.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

