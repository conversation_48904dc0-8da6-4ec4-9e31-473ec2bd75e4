# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.17

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake

# The command to remove a file.
RM = /home/<USER>/clion-2020.2.4/bin/cmake/linux/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/dev/andromeda/amcore/test/LString

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug

# Include any dependencies generated for this target.
include CMakeFiles/lstring.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/lstring.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/lstring.dir/flags.make

CMakeFiles/lstring.dir/test_AMLString.cpp.o: CMakeFiles/lstring.dir/flags.make
CMakeFiles/lstring.dir/test_AMLString.cpp.o: ../test_AMLString.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/lstring.dir/test_AMLString.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/lstring.dir/test_AMLString.cpp.o -c /home/<USER>/dev/andromeda/amcore/test/LString/test_AMLString.cpp

CMakeFiles/lstring.dir/test_AMLString.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/lstring.dir/test_AMLString.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/dev/andromeda/amcore/test/LString/test_AMLString.cpp > CMakeFiles/lstring.dir/test_AMLString.cpp.i

CMakeFiles/lstring.dir/test_AMLString.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/lstring.dir/test_AMLString.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/dev/andromeda/amcore/test/LString/test_AMLString.cpp -o CMakeFiles/lstring.dir/test_AMLString.cpp.s

# Object files for target lstring
lstring_OBJECTS = \
"CMakeFiles/lstring.dir/test_AMLString.cpp.o"

# External object files for target lstring
lstring_EXTERNAL_OBJECTS =

lstring: CMakeFiles/lstring.dir/test_AMLString.cpp.o
lstring: CMakeFiles/lstring.dir/build.make
lstring: CMakeFiles/lstring.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable lstring"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/lstring.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/lstring.dir/build: lstring

.PHONY : CMakeFiles/lstring.dir/build

CMakeFiles/lstring.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/lstring.dir/cmake_clean.cmake
.PHONY : CMakeFiles/lstring.dir/clean

CMakeFiles/lstring.dir/depend:
	cd /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/dev/andromeda/amcore/test/LString /home/<USER>/dev/andromeda/amcore/test/LString /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug /home/<USER>/dev/andromeda/amcore/test/LString/cmake-build-debug/CMakeFiles/lstring.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/lstring.dir/depend

