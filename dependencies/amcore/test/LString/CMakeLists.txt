cmake_minimum_required(VERSION 3.10)
set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "../../build/tests/LString")
project(lstring)

set(CMAKE_CXX_STANDARD 17)
set(SOURCE_FILES test_AMLString.cpp ../../AMLString.h)
include_directories(../../include ../../../3rdparty/include)
link_directories("../../../3rdparty/lib" "../../build/bin/")

add_executable(lstring test_AMLString.cpp)
target_link_libraries(lstring libgtest.a pthread AMCore)
