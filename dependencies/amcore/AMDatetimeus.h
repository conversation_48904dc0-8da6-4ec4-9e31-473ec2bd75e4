//
// Created by <PERSON><PERSON><PERSON> on 25.3.23.
//

#ifndef SAW_AMDATETIMEUS_H
#define SAW_AMDATETIMEUS_H

#include <ctime>

namespace AMCore
{
    struct AMDatetimeus: public std::tm
    {
        int usec;
        AMDatetimeus()
            : std::tm(),
            usec(0)
        {
            tm_year = 0;
            tm_mon = 0;
            tm_mday = 0;
            tm_wday = 0;
            tm_hour = 0;
            tm_min = 0;
            tm_sec = 0;
            tm_isdst = 0;
            tm_gmtoff = 0;
            tm_zone = nullptr;
        }
        AMDatetimeus(int years, int months, int days, int hours, int mins, int secs, long usecs = 0LL)
            : std::tm(),
            usec(usecs)
        {
            tm_year = years - 1900;
            tm_mon = months - 1;
            tm_mday = days;
            tm_wday = 0;
            tm_hour = hours;
            tm_min = mins;
            tm_sec = secs;
            tm_isdst = 0;
            tm_gmtoff = 0;
            tm_zone = nullptr;
        }
        AMDatetimeus(const std::tm &other)
            : std::tm(other),
            usec(0)
        {}
        AMDatetimeus &operator=(const std::tm &other)
        {
            tm_year = other.tm_year;
            tm_mon = other.tm_mon;
            tm_mday = other.tm_mday;
            tm_wday = other.tm_wday;
            tm_yday = other.tm_yday;
            tm_hour = other.tm_hour;
            tm_min = other.tm_min;
            tm_sec = other.tm_sec;
            tm_zone = other.tm_zone;
            tm_gmtoff = other.tm_gmtoff;
            tm_isdst = other.tm_isdst;
            usec = 0;
            return *this;
        }
    };
}

#endif //SAW_AMDATETIMEUS_H
