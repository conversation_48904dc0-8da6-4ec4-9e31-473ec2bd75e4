/**  @file AMVariant.h
*    This file is function wrapper like std::function but optimized for Smart properties.
*    <AUTHOR>  &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;
*/

/**
 *  @ingroup SmartProperties
 * @{
 */

#ifndef AMCore_AMVariant_H
#define AMCore_AMVariant_H

//#include <functional>
#include <iostream>
#include <type_traits>
#include <utility>

namespace AMCore {

#ifdef AMVARIANT_DEFAULT_DATA_SIZE
    static const int _AMVARIANT_DEFAULT_DATA_SIZE = AMVARIANT_DEFAULT_DATA_SIZE;
#else
    static const int _AMVARIANT_DEFAULT_DATA_SIZE = sizeof(void*);
#endif

    enum _E_AMVAR_manager_operation {
        _get_type_info,
        _clone_object,
        _clone_object_move,
        _destroy_object,
//        _convert_object
    };

#ifdef COLLECTING_DATA
#include <stdio.h>

    struct _T_AMVAR_size_base {
        static unsigned long _M_max;
    };
    unsigned long _T_AMVAR_size_base::_M_max = 0;

    template<typename _T>
    struct _T_AMVAR_size : _T_AMVAR_size_base {

        unsigned long _M_get_size() {return _M_size;}

        static  int _M_register_size() {
            if (_M_max < sizeof(_T)) {
                _M_max = sizeof(_T);
            }
            return sizeof(_T);
        }
        static unsigned long _M_size;
    };
    template<typename _T>
    unsigned long _T_AMVAR_size<_T>::_M_size = _T_AMVAR_size<_T>::_M_register_size();
#endif

    /*
     *  Trait identifying "location-invariant" types, meaning that the
     *  address of the object (or any of its members) will not escape.
     *  Trivially copyable types are location-invariant and users can
     *  specialize this trait for other types.
     */
    template<typename _Tp>
    struct _T_AMVAR_is_location_invariant : std::is_trivially_copyable<_Tp>::type {
    };

    //template<typename _From, typename _To>
    //using _T_AMVAR_check_objunc_return_type = std::__objr_<std::is_void<_To>, std::is_same<_From, _To>/*, std::is_convertible<_From, _To>*/ >;

    template<int _size = _AMVARIANT_DEFAULT_DATA_SIZE>
    union _T_AMVAR_nocopy_types {
        void *_M_object;
        const void *_M_const_object;
        char _M_data[_size];
    };

    template<typename _T, typename _U> constexpr size_t _T_AMVAR_objffset_objf(_U _T::*member)
    {
        return (char*)&((_T*)nullptr->*member) - (char*)nullptr;
    }

    class _T_AMVAR_any_data {
    public:
        _T_AMVAR_any_data() {}

        void *_M_access() { return &_M_data._M_data[0]; }

        const void *_M_access() const { return &_M_data._M_data[0]; }

        void *_M_access_ptr() { return _M_data._M_object; }

        const void *_M_access_ptr() const { return _M_data._M_object; }

        void _M_set_access_ptr(void *ptr) { _M_data._M_object = ptr; }

        template<typename _Tp>
        _Tp& _M_get_object_ptr() {
            return *static_cast<_Tp*>(_M_data._M_object); }

        template<typename _Tp>
        const _Tp& _M_get_object_ptr() const {
            return *static_cast<const _Tp*>(_M_data._M_object); }

        template<typename _Tp>
        _Tp &_M_get_object() {
            return *static_cast<_Tp *>(static_cast<void *>(&_M_data._M_data[0])); }

        template<typename _Tp>
        const _Tp &
        _M_get_object() const {
            return *static_cast<const _Tp *>(static_cast<const void *>(&_M_data._M_data[0])); }

        _T_AMVAR_nocopy_types<_AMVARIANT_DEFAULT_DATA_SIZE> _M_data;
    };

/*
    template<typename _Signature>
    class _T_AMVAR_generic_objunctor;

    template<typename _T>
    class _T_AMVAR_generic_objunctor<_T> {
        _Res operator ()(_ArgTypes ...);
    };*/

    class _T_AMVAR_kernel_base {
friend class AMVariant;
    protected:
        _T_AMVAR_any_data _M_object;

        typedef const std::type_info *(*_T_AMVAR_kernel_op_ptr)(_T_AMVAR_kernel_base *_kernel_ptr, const _T_AMVAR_any_data &_src,
                                                          _E_AMVAR_manager_operation _op);

        _T_AMVAR_kernel_op_ptr _M_kernel_op_ptr;

    public:
        _T_AMVAR_kernel_base(_T_AMVAR_kernel_op_ptr _kernel_op_ptr) noexcept:
                _M_object(),
                _M_kernel_op_ptr(_kernel_op_ptr) {};

        _T_AMVAR_kernel_base() noexcept:
                _M_object(),
                _M_kernel_op_ptr(nullptr) {};

        _T_AMVAR_kernel_base(const _T_AMVAR_kernel_base &_obj) :
                _M_object(),
                _M_kernel_op_ptr(_obj._M_kernel_op_ptr) {
                    //if (_M_kernel_op_ptr) {
                        _M_kernel_op_ptr(this, _obj._M_object, _clone_object);
                    //}
        }

        _T_AMVAR_kernel_base(_T_AMVAR_kernel_base &&_obj) :
                _M_object(),
                _M_kernel_op_ptr(_obj._M_kernel_op_ptr) {
                    //if (_M_kernel_op_ptr) {
                        _M_kernel_op_ptr(this, _obj._M_object, _clone_object_move);
                    //}
                    _obj._M_destroy();
        }

        ~_T_AMVAR_kernel_base() {
            if (_M_kernel_op_ptr) {
                _M_kernel_op_ptr(this, _M_object, _destroy_object);
            }
        }

        _T_AMVAR_kernel_base &operator=(const _T_AMVAR_kernel_base &_obj) {
            if (_M_kernel_op_ptr) {
                _M_kernel_op_ptr(this, _M_object, _destroy_object);
            }
            _M_kernel_op_ptr = _obj._M_kernel_op_ptr;
            if (_M_kernel_op_ptr) {
                _M_kernel_op_ptr(this, _obj._M_object, _clone_object);
            }
            return *this;
        }

        const _T_AMVAR_any_data &_M_get_object() const { return _M_object; }


        void _M_destroy() {
            if (_M_kernel_op_ptr) {
                _M_kernel_op_ptr(this, _M_object, _destroy_object);
            }
            _M_kernel_op_ptr = nullptr;
        }



        bool _M_empty() const noexcept {
            return _M_kernel_op_ptr == nullptr;
        }

#if __cpp_rtti

        const std::type_info &_M_get_type_info() const noexcept {
            //if (_M_empty()) {
            //    return typeid(void);
            //}
            _T_AMVAR_kernel_base *nonconstthis = const_cast<_T_AMVAR_kernel_base *>(this);
            return *_M_kernel_op_ptr(nonconstthis, _M_object, _get_type_info);
        }

#endif
    };

//    template<typename _T>
//    class _T_AMVAR_kernel_internal;

    template<typename _T>
    class _AMFunction_handler;

    template<typename _T>
    class _T_AMVAR_kernel_internal
            : public _T_AMVAR_kernel_base {
friend class AMVariant;
        typedef _T_AMVAR_kernel_base _T_Base;

        void _M_clone(const _T_AMVAR_any_data &_object) {
            _T *vp = &_T_Base::_M_object.template _M_get_object<_T>();
            ::new(vp) _T(_object.template _M_get_object<_T>());
        }

        void _M_move(const _T_AMVAR_any_data &_object) {
            _T *vp = &_T_Base::_M_object.template _M_get_object<_T>();
            ::new(vp) _T(std::move(_object.template _M_get_object<_T>()));
        }

        void _M_init(_T && _source) {
            _T *vp = &_T_Base::_M_object.template _M_get_object<_T>();
            ::new(vp) _T(std::forward<_T>(_source));
        }

        template<typename... _Args>
        void _M_init(_Args&&... __args)
        {
            _T *vp = &_T_Base::_M_object.template _M_get_object<_T>();
            ::new (vp) _T(std::forward<_Args>(__args)...);
        }

        void _M_destroy() {
            _T_Base::_M_object.template _M_get_object<_T>().~_T();
        }

        _T& _M_access() {
            return _T_Base::_M_object.template _M_get_object<_T>();
        }

        const _T& _M_access() const {
            return _T_Base::_M_object.template _M_get_object<_T>();
        }

        static const std::type_info *
        _M_manage_op(_T_AMVAR_kernel_base*_me, const _T_AMVAR_any_data &_object,
                     _E_AMVAR_manager_operation _op) {
            switch (_op) {
                case _destroy_object:
                    static_cast<_T_AMVAR_kernel_internal*>(_me)->_M_destroy();
                    break;
                case _clone_object:
                    static_cast<_T_AMVAR_kernel_internal*>(_me)->_M_clone(_object);
                    break;
                case _clone_object_move:
                    static_cast<_T_AMVAR_kernel_internal*>(_me)->_M_move(_object);
                    break;
//                case _convert_object:
//                    static_cast<_T_AMVAR_kernel_internal*>(_me)->_M_convert(_object);
//                    break;
#if __cpp_rtti
                case _get_type_info:
                    return &typeid(_T);
                    break;
#endif
            }
            return nullptr;
        }

    public:
        typedef _T _My_Type;

        template<typename _NewT>
        static _NewT _M_convert(const _T_AMVAR_any_data& _source) {
            _T& tsource = _source._M_get_object<_T>();
            if (std::is_same<_NewT,_T>::value) {
                return tsource;
            }
            return static_cast<_NewT>(tsource);
        }

        _T_AMVAR_kernel_internal(const _T &_object) :
                _T_AMVAR_kernel_base(
                        &_M_manage_op
                ) {
            _M_init(_object);
        };
    };

    template<typename _T>
    class _T_AMVAR_kernel_external
            : public _T_AMVAR_kernel_base {
friend class AMVariant;
        typedef _T_AMVAR_kernel_base _T_Base;

        void _M_clone(const _T_AMVAR_any_data &_object) {
            void *rv;
            rv = static_cast<void *>(new _T(_object.template _M_get_object_ptr<_T>()));
            _T_Base::_M_object._M_set_access_ptr(rv);
        }

        void _M_move(const _T_AMVAR_any_data &_object) {
            void *rv;
            rv = static_cast<void *>(new _T(std::move(_object.template _M_get_object_ptr<_T>())));
            _T_Base::_M_object._M_set_access_ptr(rv);
        }

        void _M_init(_T &&_source) {
            void* const rv = static_cast<void *>(new _T(std::forward<_T>(_source)));
            _T_Base::_M_object._M_set_access_ptr(rv);
        }

        template<typename... _Args>
        void _M_init(_Args&&... __args)
        {
            void* const rv = static_cast<void *>(new _T(std::forward<_Args>(__args)...));
            _T_Base::_M_object._M_set_access_ptr(rv);
        }

        void _M_destroy() {
            delete &_T_Base::_M_object.template _M_get_object_ptr<_T>();
        }

        _T& _M_access() {
            return _T_Base::_M_object.template _M_get_object_ptr<_T>();
        }

        const _T& _M_access() const {
            return _T_Base::_M_object.template _M_get_object_ptr<_T>();
        }

        static const std::type_info *
        _M_manage_op(_T_AMVAR_kernel_base*_me, const _T_AMVAR_any_data &_object,
                     _E_AMVAR_manager_operation _op) {
            switch (_op) {
                case _destroy_object:
                    static_cast<_T_AMVAR_kernel_external*>(_me)->_M_destroy();
                    break;
                case _clone_object:
                    static_cast<_T_AMVAR_kernel_external*>(_me)->_M_clone(_object);
                    break;
                case _clone_object_move:
                    static_cast<_T_AMVAR_kernel_external*>(_me)->_M_move(_object);
                    break;
//                case _convert_object:
//                    static_cast<_T_AMVAR_kernel_external*>(_me)->_M_convert(_object);
//                    break;
#if __cpp_rtti
                case _get_type_info:
                    return &typeid(_T);
                    break;
#endif
            }
            return nullptr;
        }

    public:
        typedef _T _My_Type;

        template<typename _NewT>
        static _NewT _M_convert(const _T_AMVAR_any_data& _source) {
            _T& tsource = _source._M_get_object_ptr<_T>();
            if (std::is_same<_NewT,_T>::value) {
                return tsource;
            }
            return static_cast<_NewT>(tsource);
        }

        _T_AMVAR_kernel_external(const _T &_object) :
                _T_AMVAR_kernel_base(
                        &_M_manage_op
                ) {
            _M_init(_object);
        };
    };

/*
    template<typename _Res, typename _Functor, typename... _ArgTypes>
    class _AMFunction_handler<_T, _Functor>
            : public _T_AMVAR_kernel<_T, _Functor> {
        typedef _T_AMVAR_kernel<_T, _Functor> _T_Base;

    public:
        _AMFunction_handler(_Functor _object) : _T_Base(_object, &_AMFunction_handler<_T, _Functor>::_M_invoke) {}

        _Res _M_invoke(_ArgTypes ... __args) {
            const void* newvfunctor = _T_Base::_M_get_object()._M_access_ptr();
            const _Functor& newfunctor = *static_cast<const _Functor*>(newvfunctor);
            return newfunctor(std::forward<_ArgTypes>(__args)...);
        }
    };

    template<typename _Functor, typename... _ArgTypes>
    class _AMFunction_handler<void(_ArgTypes...), _Functor>
            : public _T_AMVAR_kernel<void(_ArgTypes...), _Functor> {
        typedef _T_AMVAR_kernel<void(_ArgTypes...), _Functor> _T_Base;

    public:
        _AMFunction_handler(_Functor _object) : _T_Base(_object, &_AMFunction_handler<void(_ArgTypes...), _Functor>::_M_invoke) {}

        void _M_invoke(_ArgTypes ... __args) {
            const void* newvfunctor = _T_Base::_M_get_object()._M_access_ptr();
            const _Functor& newfunctor = *static_cast<const _Functor*>(newvfunctor);
            newfunctor(std::forward<_ArgTypes>(__args)...);
        }
    };*/



/**
 *  @ingroup SmartProperties
 *  @brief AMCore::AMVariant is a function wrapper for smart properties.
 *
 *  Class template AMCore::AMfunction is function wrapper, basically intended for smart properties.
 *  Instances of AMCore::AMVariant can store, copy, and invoke Functor type callable target -- lambda expressions.
 *  Rather than dynamically alocation, stores functors itself. Its optimalisation for real-time operations.
 *  Class compute the most bigger memory footprint of all stored Functors. It completelly removes need of
 *  dynamic memory allocation.
 *
 *  <h3>Terminology taken from std:function</h3>
 *  The stored callable object is called the target of AMCore::AMVariant. If a AMCore::AMVariant
 *  contains no target, it is called empty. Invoking the target of an empty AMCore::AMVariant
 *  results in std::bad_objunction_call exception being thrown.
 *
 *  <h3>Comparison between AMCore::AMVariant and std::function</h3>
 *  <table>
 *  <tr><th>Feature</th><th>AMCore::AMVariant</th><th>std::function</th></tr>
 *  <tr><td>Object size</td><td>3&times;sizeof(void*) + local storage</td><td>4&times;sizeof(void*)</td></tr>
 *  <tr><td>Minimum local storage size</td><td>sizeof(void*)</td><td>2&times;sizeof(void*)</td></tr>
 *  <tr><td>Maximum local storage size</td><td>_data_size parameter</td><td>2&times;sizeof(void*)</td></tr>
 *  <tr><td>Invocation speed</td><td>one call</td><td>two calls</td></tr>
 *  <tr><td>Invocation memory constiption</td><td>one call stack space</td><td>two calls stack space</td></tr>
 *  <tr><td>Supports functors</td><td>YES</td><td>types</td></tr>
 *  <tr><td>Supports pointer to functions</td><td>NO</td><td>YES</td></tr>
 *  <tr><td>Supports pointers to member functions</td><td>NO</td><td>YES</td></tr>
 *  <tr><td>Supports realtime operations</td><td>YES</td><td>YES, but with restrictions</td></tr>
 *  </table>
 */

    class AMVariant {

        _T_AMVAR_kernel_base _M_kernel;

        //template<typename _Func,
        //        typename _Res2 = typename std::result_objf<_Func &(_ArgTypes ...)>::type>
        //struct _Callable : _T_AMVAR_check_objunc_return_type<_Res2, _Res> {
        //};

        //template<typename _Tp>
        //struct _Callable<AMVariant, _Tp> : std::false_type {
        //};

        template<typename _Cond, typename _Tp>
        using _Requires = typename std::enable_if<_Cond::value, _Tp>::type;

        static const std::size_t _M_max_size = sizeof(_T_AMVAR_nocopy_types<_AMVARIANT_DEFAULT_DATA_SIZE>);
        static const std::size_t _M_max_align = __alignof__(_T_AMVAR_nocopy_types<_AMVARIANT_DEFAULT_DATA_SIZE>);

        template<typename _T>
            using _Local_storage = std::integral_constant<bool,
                (_T_AMVAR_is_location_invariant<_T>::value
                 && (sizeof(_T) <= _M_max_size)
                 && (__alignof__(_T) <= _M_max_align)
                 && (_M_max_align % __alignof__(_T) == 0))
                 && (_AMVARIANT_DEFAULT_DATA_SIZE > 0)>;

    public:

        /**
         *  @brief Default construct creates an empty AMVariant.
         *  @throw This @c function will not throw an exception.
         */
        AMVariant() noexcept: _M_kernel() {}

        /**
         *  @brief Creates an empty AMVariant.
         *  @throw This @c function will not throw an exception.
         */
//        AMVariant(nullptr_t) noexcept: _M_kernel() {}

        /**
         *  @brief %AMVariant copy constructor.
         *  @param _obj A %AMVariant object with identical call signature.
         *
         *  The newly-created %AMVariant contains a copy of the target of @a
         *  _obj (if it has one).
         *
         *  @throw Constructor of target can throw an exception.
         */
        AMVariant(const AMVariant& _obj) : _M_kernel(_obj._M_kernel) {}


        /**
         *  @brief %AMVariant move constructor.
         *  @param _obj A %AMVariant object rvalue with identical call signature.
         *
         *  The newly-created %AMVariant contains the target of @a _obj
         *  (if it has one).
         *
         *  @throw Constructor of target can throw an exception.
         */
        AMVariant(AMVariant&& _obj) : _M_kernel(std::move(_obj._M_kernel)) {}

        /**
         *  @brief Builds a %AMVariant that targets to a copy of Functor object.
         *  @param _obj A %AMVariant object that is callable with parameters of
         *  type @c T1, @c T2, ..., @c TN and returns a value convertible
         *  to @c Res.
         *
         *  @throw Constructor of target can throw an exception.
         */
        template<typename _T,
                typename = _Requires<std::__not_<std::is_same<_T, AMVariant> >, void> >
        AMVariant(_T _obj);

        /**
         *  @ingroup SmartProperties
         *  @brief Construct with an object created from @p __args as
         *         the contained object.
         *
         *  @throw Constructor of a target can throw an exception.
         */
        template <typename _T, typename... _Args>
        AMVariant(std::in_place_type_t<_T>, _Args&&... __args);

        /**
         *  @brief %AMVariant assignment operator.
         *  @param _obj A %AMVariant with identical call signature.
         *  @returns @c *this
         *
         *  The target of @a _obj is copied to @c *this. If @a _obj has no
         *  target, then @c *this will be empty.
         *
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */
        AMVariant &operator=(const AMVariant &_obj) {
            if (!_M_kernel._M_empty()) {
                _M_kernel._M_destroy();
            }
            _M_kernel = _obj._M_kernel;
            return *this;
        }

        /**
         *  @brief %AMVariant move-assignment operator.
         *  @param _obj A %AMVariant rvalue with identical call signature.
         *  @returns @c *this
         *
         *  The target of @a _obj is moved to @c *this. If @a _obj has no
         *  target, then @c *this will be empty.
         *
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */
        AMVariant &operator=(AMVariant &&_obj) {
            if (!_M_kernel._M_empty()) {
                _M_kernel._M_destroy();
            }
            _M_kernel = std::move(_obj._M_kernel);
            return *this;
        }

        /**
         *  @brief %AMVariant assignment to zero.
         *  @returns @c *this
         *
         *  The target of @c *this is deallocated, leaving it empty.
         *
         *  @throw Destructor of old target can throw an exception.
         */
        /*AMVariant &operator=(nullptr_t) {
            if (!_M_kernel._M_empty()) {
                _M_kernel._M_destroy();
            }
            return *this;
        }*/

        /**
         *  @brief %AMVariant assignment to a new target.
         *  @param _obj A %AMVariant object that is callable with parameters of
         *  type @c T1, @c T2, ..., @c TN and returns a value convertible
         *  to @c Res.
         *  @return @c *this
         *
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */

        template<typename _T>
        AMVariant& operator=(const _T &_obj) {
#ifdef COLLECTING_DATA
            static _T_AMVAR_size<_T> _object_size;
            _object_size._M_get_size();
#endif
            if (!_M_kernel._M_empty()) {
                _M_kernel._M_destroy();
            }
            ::new(&_M_kernel) Kernel_t<_T>(_obj);
            return *this;
        }

        /**
         *  @brief Determine if the %AMVariant has a target.
         *
         *  @return @c true when this %AMVariant object contains a target,
         *  or @c false when it is empty.
         *
         *  @throw This @c function will not throw an exception.
         */
        /*explicit operator bool() const noexcept {
            return !_M_kernel._M_empty();
        }*/

        template<typename _T>
            using Kernel_t = std::conditional_t<_Local_storage<_T>::value,
                                                _T_AMVAR_kernel_internal<_T>,
                                                _T_AMVAR_kernel_external<_T>>;

        /**
         *  @brief Invokes the AMVariant targeted by @c *this.
         *  @returns the result of the target.
         *
         *  The AMVariant call operator invokes the target AMVariant object
         *  stored by @c this.
         *  @throws bad_objunction_call when @c !(bool)*this
         */
#if 0
        template<typename _T>
        operator _T () {
            using _UT = std::__remove_cvref_t<_T>;
            if (_M_kernel._M_kernel_op_ptr != &Kernel_t<_UT>::_M_manage_op) {
                throw std::bad_cast();
            }
            return static_cast<Kernel_t<_UT>&>(_M_kernel)._M_access();
        }
        template<typename _T>
        explicit operator _T () const {
            using _UT = std::__remove_cvref_t<_T>;
            if (_M_kernel._M_kernel_op_ptr != &Kernel_t<_UT>::_M_manage_op) {
                throw std::bad_cast();
            }
            return static_cast<const Kernel_t<_UT>&>(_M_kernel)._M_access();
        }
#endif
        template<typename _T>
        operator _T& () {
            using _UT = std::__remove_cvref_t<_T>;

            if (_M_kernel._M_kernel_op_ptr != &Kernel_t<_UT>::_M_manage_op) {
                throw std::bad_cast();
            }
            return static_cast<Kernel_t<_T>&>(_M_kernel)._M_access();
        }

        template<typename _T>
        explicit operator const _T& () const {
            using _UT = std::__remove_cvref_t<_T>;
            if (_M_kernel._M_kernel_op_ptr != &Kernel_t<_UT>::_M_manage_op) {
                throw std::bad_cast();
            }
            return static_cast<const Kernel_t<_T>&>(_M_kernel)._M_access();
        }

#if __cpp_rtti

        /**
         *  @brief Determine the type of the target of this AMVariant object
         *  @returns the type identifier of the target AMVariant object, or
         *  @c typeid(void) if @c !(bool)*this.
         *
         *  @throw This @c function will not throw an exception.
         */
        const std::type_info &target_type() const noexcept {
            const std::type_info &_typeinfo_result = _M_kernel._M_get_type_info();
            return _typeinfo_result;
        }

#endif

        /**
         *  @brief Determines maximum functor size used in program.
         *  @returns maximum functor size
         *
         *  Determines maximum functor size used in program,
         *  respectively, only functors that were passed to constructor of
         *  AMCore::AMVariant, or AMCore::AMVariant::operator=.
         *  Value is intended to next compile for real-time systems.
         *  You must pass COLLECTING_DATA macto to compiling. In case of
         *  macro not passed, returns simply -1.
         *  This @c function will not throw an %exception.
         *
         *  @throw This @c function will not throw an exception.
         */
        static unsigned long getMaximumFunctorSize() noexcept {

            unsigned long _return_value = -1;
#ifdef COLLECTING_DATA
            _return_value = _T_AMVAR_size_base::_M_max;
#endif
            return _return_value;
        }

// TODO: private
        /**
         *  @brief Emplace with an object created from @p __args as the
         *         contained object.
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */
        template <typename _T, typename... _Args, typename _Kernel = Kernel_t<_T>>
        void _emplace(_Args&&... __args)
        {
            _M_kernel._M_destroy();
            reinterpret_cast<Kernel_t<_T>&>(_M_kernel)._M_init(std::forward<_Args>(__args)...);
            _M_kernel._M_kernel_op_ptr = &Kernel_t<_T>::_M_manage_op;
        }

        /**
         *  @ingroup SmartProperties
         *  @brief Emplace with an object created from @p __args as the
         *         contained object.
         *
         *  @returns A reference to the new contained object.
         *
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */
        template <typename _T, typename... _Args>
        std::decay_t<_T>& emplace(_Args&&... __args)
        {
            _emplace<std::decay_t<_T>>(std::forward<_Args>(__args)...);
            return reinterpret_cast<Kernel_t<_T>&>(_M_kernel)._M_access();
        }

        /**
         *  @ingroup SmartProperties
         *  @brief Emplace with an object created from @p __il and @p
         *         __args as the contained object.
         *
         *  @returns A reference to the new contained object.
         *
         *  @throw Constructor of new target of destructor of old target can throw an exception.
         */
        template <typename _T, typename _U, typename... _Args>
        std::decay_t<_T> emplace(std::initializer_list<_U> __il, _Args&&... __args)
        {
    //       using _VTp = decay_t<_Tp>;
    //       __do_emplace<_VTp, _Up>(__il, std::forward<_Args>(__args)...);
    //       return *any::_Manager<_VTp>::_S_access(_M_storage);
        }

    };

    template<typename _T, typename>
    AMVariant::AMVariant(_T _obj) {
#ifdef COLLECTING_DATA
        static _T_AMVAR_size<_T> _object_size;
        _object_size._M_get_size();
#endif
        ::new(&_M_kernel) Kernel_t<_T>(_obj);
    }

    /**
     *  @ingroup SmartProperties
     *  @brief Construct with an object created from @p __args as
     *         the contained object.
     *
     *  @throw Constructor of a target can throw an exception.
     */
/*
    template <typename _Tp, typename... _Args, typename _VTp = decay_t<_Tp>,
    typename _Mgr = _Manager<_VTp>,
    __any_constructible_t<_VTp, _Args&&...> = false>
    explicit
*/
    template <typename _T, typename... _Args>
    AMVariant::AMVariant(std::in_place_type_t<_T>, _Args&&... __args)
    {
#ifdef COLLECTING_DATA
        static _T_AMVAR_size<_T> _object_size;
        _object_size._M_get_size();
#endif
        reinterpret_cast<Kernel_t<_T>&>(_M_kernel)._M_init(std::forward<_Args>(__args)...);
        _M_kernel._M_kernel_op_ptr = &Kernel_t<_T>::_M_manage_op;
    }

    /**
     *  @ingroup SmartProperties
     *  @brief Compares an AMVariant target with nullptr
     *
     *  @returns @c true if the wrapper has no target, @c false otherwise
     *
     *  @throw This @c function will not throw an exception.
     */
    /*
    template<typename _Res, typename ... _Args>
    inline bool
    operator==(const AMVariant<_Res(_Args ...)> &_obj, nullptr_t) noexcept {
        return !static_cast<bool>(_obj);
    }*/

    /**
     *  @ingroup SmartProperties
     *  @brief Compares an AMVariant target with nullptr
     *
     *  @returns @c true if the wrapper has no target, @c false otherwise
     *
     *  @throw This @c function will not throw an exception.
     */
    /*
    template<typename _Res, typename ... _Args>
    inline bool
    operator==(nullptr_t, const AMVariant<_Res(_Args ...)> &_obj) noexcept {
        return !static_cast<bool>(_obj);
    }*/

    /**
     *  @ingroup SmartProperties
     *  @brief Compares an AMVariant target with nullptr
     *  @returns @c false if the wrapper has no target, @c true otherwise
     *
     *  @throw This @c function will not throw an exception.
     */
    /*
    template<typename _Res, typename ... _Args>
    inline bool
    operator!=(const AMVariant<_Res(_Args ...)> &_obj, nullptr_t) noexcept {
        return static_cast<bool>(_obj);
    }*/

    /**
     *  @ingroup SmartProperties
     *  @brief Compares an AMVariant target with nullptr
     *  @returns @c false if the wrapper has no target, @c true otherwise
     *
     *  @throw This @c function will not throw an exception.
     */
    /*
    template<typename _Res, typename ... _Args>
    inline bool
    operator!=(nullptr_t, const AMVariant<_Res(_Args ...)> &_obj) noexcept {
        return static_cast<bool>(_obj);
    }*/

}//namespace AMCore
/** @} */
#endif //AMCore_AMVariant_H
