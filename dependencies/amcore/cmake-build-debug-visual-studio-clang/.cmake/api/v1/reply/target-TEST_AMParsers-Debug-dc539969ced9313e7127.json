{"artifacts": [{"path": "TEST_AMParsers.exe"}, {"path": "TEST_AMParsers.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 57, "parent": 0}, {"command": 1, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 58, "parent": 0}, {"command": 3, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fexceptions /MDd /Zi /Ob0 /Od /RTC1"}, {"fragment": "-std:c++17"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/include"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/../3rdparty/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "id": "TEST_AMParsers::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-fexceptions /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Documents\\andromeda\\AMCore\\..\\3rdparty\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "gtest.a.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "pthread.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "TEST_AMParsers", "nameOnDisk": "TEST_AMParsers.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/Parsers/test_AMParsers.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/AMParsers.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/AMNullable.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}