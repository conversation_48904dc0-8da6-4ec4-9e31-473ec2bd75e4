{"artifacts": [{"path": "TEST_AMLString.exe"}, {"path": "TEST_AMLString.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 45, "parent": 0}, {"command": 1, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 46, "parent": 0}, {"command": 3, "file": 0, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fexceptions /MDd /Zi /Ob0 /Od /RTC1"}, {"fragment": "-std:c++17"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/include"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/andromeda/AMCore/../3rdparty/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "AMCore::@6890427a1f51a3e7e1df"}], "id": "TEST_AMLString::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-fexceptions /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:C:\\Users\\<USER>\\Documents\\andromeda\\AMCore\\..\\3rdparty\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "gtest.a.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "pthread.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "AMCore.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "TEST_AMLString", "nameOnDisk": "TEST_AMLString.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/LString/test_AMLString.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}