{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/cmake/win/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/cmake/win/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/cmake/win/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/cmake/win/share/cmake-3.23"}, "version": {"isDirty": false, "major": 3, "minor": 23, "patch": 2, "string": "3.23.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-9ad04a180d48adac57f3.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "cache-v2-b8bd9c4ce75e0fe0d28c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5ac8f2afd0bb8afe1614.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-db40e00a987e10d05d06.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-b8bd9c4ce75e0fe0d28c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5ac8f2afd0bb8afe1614.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9ad04a180d48adac57f3.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, "toolchains-v1": {"jsonFile": "toolchains-v1-db40e00a987e10d05d06.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}