The system is: Windows - 10.0.22000 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/LLVM/bin/clang-cl.exe 
Build flags: -m64
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.exe"

The C compiler identification is Clang, found in "C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/3.23.2/CompilerIdC/CMakeCCompilerId.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/LLVM/bin/clang-cl.exe 
Build flags: -m64
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"

The CXX compiler identification is Clang, found in "C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/3.23.2/CompilerIdCXX/CMakeCXXCompilerId.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/ninja/win/ninja.exe cmTC_75be9 && [1/2] Building C object CMakeFiles\cmTC_75be9.dir\CMakeCCompilerABI.c.obj

[2/2] Linking C executable cmTC_75be9.exe




Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/ninja/win/ninja.exe cmTC_44ebf && [1/2] Building CXX object CMakeFiles\cmTC_44ebf.dir\CMakeCXXCompilerABI.cpp.obj

[2/2] Linking CXX executable cmTC_44ebf.exe






Detecting CXX [-std:c++14] compiler features compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/ninja/win/ninja.exe cmTC_ea1f3 && [1/2] Building CXX object CMakeFiles\cmTC_ea1f3.dir\feature_tests.cxx.obj

[2/2] Linking CXX executable cmTC_ea1f3.exe




    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates
    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_variable_templates


Detecting CXX [] compiler features compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/andromeda/AMCore/cmake-build-debug-visual-studio-clang/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Users/<USER>/AppData/Local/JetBrains/Toolbox/apps/CLion/ch-0/222.4167.35/bin/ninja/win/ninja.exe cmTC_17a70 && [1/2] Building CXX object CMakeFiles\cmTC_17a70.dir\feature_tests.cxx.obj

[2/2] Linking CXX executable cmTC_17a70.exe




    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates
    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_variable_templates
