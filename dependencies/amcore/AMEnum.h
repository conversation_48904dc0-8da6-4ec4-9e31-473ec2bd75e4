/*!
*	@file AMEnum.h
*   This file is interface for definition enumaretions, tha can be converter to its string
*   representation, even, can be translated as Localizes strings.
*   For more informations, see @ref Strings.
*   <AUTHOR> Skulínek  &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;
*/
#ifndef AMENUM_H
#define AMENUM_H

#include <limits>
#include <array>
#include <cstring>
#include <stdexcept>
#include "AMLString.h"

/**
 *  @ingroup Typing
 *  @{
 */

namespace AMCore {

	template<typename T, std::size_t N>
	constexpr void AMCEQuickSort(std::array<T, N> &array, int low, int high)
	{
	    T pivot = array[(low + high) / 2];
	    int left = low;
		int right = high;
	    do {
			while (left < high && array[left] < pivot ) left++;
	        while (right > low && pivot < array[right]) right--;
			if (left <= right) {
	        	T tmp = array[left];
	        	array[left++] = array[right];
	        	array[right--] = tmp;
			}
	    } while (left < right);

		if (right > low) AMCEQuickSort(array, low, right);
		if (left < high) AMCEQuickSort(array, left, high);
	}

	template<typename T, std::size_t N>
	constexpr std::array<T, N> AMCEQuickSort(std::array<T, N>& array)
	{
	    AMCEQuickSort(array, 0, N - 1);
	    return array;
	}

	constexpr int AMCECompareStrings(char const * a, char const * b)
	{
		if (*a == *b) {
			if (*a == '\0') {
				return 0;
			}
			return AMCECompareStrings(a + 1, b + 1);
		}
    	return (*a < *b) ? -1 : 1;
	}

	template<typename T, std::size_t N, typename TLess>
	constexpr int AMCEBinarySearchThroughIndex(const std::array<T, N> &array,const std::array<int, N>& index,T value, TLess fn)
	{
		int low = 0;
		int high = N - 1;
		while(low <= high) {
			int middle = (low + high) / 2;
			if (fn(value, array[index[middle]])) {
				high = middle - 1;
			}
			else if (fn(array[index[middle]], value)) {
				low = middle + 1;
			}
			else {
				int lowmid = middle - 1;
				while(lowmid > 0 && !fn(array[index[lowmid]], value) && !fn(value, array[index[lowmid]])) {
					lowmid--;
				}
				return index[lowmid + 1];
			}
		}
		return -1;
	}

	/*! \enum AM_EEnumerationModes
	* Possibilities of conversion functions behavior.
	*
	*/
	enum class AMEnumerationModes{
		/*!
		* Every item has it's own text image. In case of conversion enum to string or string to enum,
		* it's directly item from table. In case of undefined value,
		* error string AMconst_string_Undefined is returned.*/
		Normal,
		/*!  Every item has it's own text image. In case of item does not exists in table, a conversion
		*    between int <--> string is returned. It's a int type with by word defined some values.
		*/
		AllDefined,
		/*!  Every item has it's own text image. It's possible to add item by operator |.
		*    In case of item has combination of two or more items, or sets more bits,
		*    functions work properly(it tries to work from most singnificant bit), but this operation may not be unique.
		*    Text is in itema|itemb|....itemN format.
		*/
		Binary
	};

    class AMEnumException: public AMException
    {
    public:
        AMEnumException(const AMLString& what): AMException(what)
        {
        }
    };

	constexpr void _T_AM_EnumerationTypesParameterTestFunctionMode(const char* s, AMEnumerationModes e) {}
	inline void AM_EnumerationTypesparameterTestFunction_item(const char* s, int i) {}
	inline void AM_EnumerationTypesparameterTestFunction_item2(const char* s) {}

	struct _T_AM_EnumerationValuesCounter
  	    {
  	    constexpr _T_AM_EnumerationValuesCounter() {}
		constexpr _T_AM_EnumerationValuesCounter(const int i) {}
		constexpr _T_AM_EnumerationValuesCounter(const unsigned int i) {}
		constexpr _T_AM_EnumerationValuesCounter(const long i) {}
		constexpr _T_AM_EnumerationValuesCounter(const unsigned long i) {}
		constexpr _T_AM_EnumerationValuesCounter(const long long i) {}
		constexpr _T_AM_EnumerationValuesCounter(const unsigned long long i) {}
  	    constexpr _T_AM_EnumerationValuesCounter(const _T_AM_EnumerationValuesCounter& c) {}
  	    };


	enum class _T_AM_EnumerationValuesTyperResult
		{
		typeFail,
		typeS64,
		typeU64,
		typeS32,
		typeU32,
		};


	struct _T_AM_EnumerationValuesTyper
  	    {
		enum _T_AM_EnumerationValuesTyperFlags
		{
			_T_AM_EnumerationValuesTyper_Empty = 0,
			_T_AM_EnumerationValuesTyper_32U = 1,
			_T_AM_EnumerationValuesTyper_32S = 2,
			_T_AM_EnumerationValuesTyper_32SU = 4,
			_T_AM_EnumerationValuesTyper_64U = 8,
			_T_AM_EnumerationValuesTyper_64S = 16,
			_T_AM_EnumerationValuesTyper_64SU = 32,
		};
		_T_AM_EnumerationValuesTyperFlags flags;

  	    constexpr _T_AM_EnumerationValuesTyper()
			:flags(_T_AM_EnumerationValuesTyper_32SU){}
		constexpr _T_AM_EnumerationValuesTyper(const int i)
			:flags(i < 0 ? _T_AM_EnumerationValuesTyper_32S : _T_AM_EnumerationValuesTyper_32SU) {}
		constexpr _T_AM_EnumerationValuesTyper(const unsigned int i)
			:flags(i & 0x80000000U ? _T_AM_EnumerationValuesTyper_32U : _T_AM_EnumerationValuesTyper_32SU) {}
		constexpr _T_AM_EnumerationValuesTyper(const long long i)
			:flags(
				i < 0
				?
				(i & 0xFFFFFFFF00000000ULL) != 0xFFFFFFFF00000000ULL
					?
					_T_AM_EnumerationValuesTyper_64S
					:
					_T_AM_EnumerationValuesTyper_32S
				:
				(i & 0xFFFFFFFF00000000ULL) != 0
				 	?
					_T_AM_EnumerationValuesTyper_64SU
					:
					_T_AM_EnumerationValuesTyper_32SU
			){}
		constexpr _T_AM_EnumerationValuesTyper(const unsigned long long i)
			:flags(
				i & 0x8000000000000000ULL
				?
				(i & 0xFFFFFFFF00000000ULL) != 0
					?
					_T_AM_EnumerationValuesTyper_64U
					:
					_T_AM_EnumerationValuesTyper_32U
				:
				(i & 0xFFFFFFFF00000000ULL) != 0
					?
					_T_AM_EnumerationValuesTyper_64SU
					:
					_T_AM_EnumerationValuesTyper_32SU
			){}
  	    constexpr _T_AM_EnumerationValuesTyper(const _T_AM_EnumerationValuesTyper& c)
			:flags(c.flags) {}
  	    };
	template<typename T>
		struct _T_AM_EnumerationValuesHolder
	  	    {
				T _M_value;
				bool _M_set;
		  	    constexpr _T_AM_EnumerationValuesHolder()
					:_M_value(0), _M_set(false) {}
				constexpr _T_AM_EnumerationValuesHolder(const int i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
				constexpr _T_AM_EnumerationValuesHolder(const unsigned int i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
				constexpr _T_AM_EnumerationValuesHolder(const long i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
				constexpr _T_AM_EnumerationValuesHolder(const unsigned long i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
				constexpr _T_AM_EnumerationValuesHolder(const long long i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
				constexpr _T_AM_EnumerationValuesHolder(const unsigned long long i)
					:_M_value(static_cast<T>(i)), _M_set(true) {}
		  	    constexpr _T_AM_EnumerationValuesHolder(const _T_AM_EnumerationValuesHolder<T>& c)
					:_M_value(c._M_value), _M_set(c._M_set) {}
	  	    };
	template<typename T>
	class _T_AM_EnumStringItem
	{
	public:
		constexpr _T_AM_EnumStringItem(T value, _T_AM_StringItemBase name_item):_M_value(value),_M_name_item(name_item) {}
		constexpr _T_AM_EnumStringItem():_M_value(0),_M_name_item() {}
		T _M_value;
		_T_AM_StringItemBase _M_name_item;
	};
	class AMEnumerationTypeBase
	{
	protected:
		static constexpr bool ceIsspace(const char c)
		{
			return (c == ' ')
				|| (c == '\t')
				|| (c == '\n')
				|| (c == '\v')
				|| (c == '\f')
				|| (c == '\r');
		}
		static constexpr bool ceIsalpha(const char c)
		{
			return ((c >= 'a')
				&& (c <= 'z'))
				|| ((c >= 'A')
				&& (c <= 'Z'));
		}
		static constexpr bool ceIsalnum(const char c)
		{
			return ((c >= 'a')
				&& (c <= 'z'))
				|| ((c >= 'A')
				&& (c <= 'Z'))
				|| ((c > '0')
				&& (c <= '9'));
		}
		static constexpr const char* skipws(const char* src)
		{
			const char* p = src;
			while(*p != '\0') {
				if(ceIsspace(*p)) {
					p++;
					continue;
				}
				return p;
			}
			return p;
		}
		static constexpr const char* readName(const char* src)
		{
			const char* p = src;
			if (!p || !(ceIsalpha(*src) || (*src=='_'))) {
				return src;
			}
			p++;
			while(*p && (ceIsalnum(*p) || (*p == '_'))) {
				p++;
			}
			return p;
		}
		static constexpr const char* readExpression(const char* src, char endChar, int nBraces = 0, int nTempl = 0)
		{
			const char* p = src;
			while(*p) {
				if ((*p == '<') && (p[1] != '<')) {
					p = readExpression(p+1 , '>', nBraces, nTempl+1);
					if (!p) {
						return nullptr;
					}
				}
				if ((*p == '>') && (p[1] != '>') && (endChar == '>')) {
					if (nTempl == 0) {
						return nullptr;
					}
					return p+1;
				}
				if ((*p == '>') && (p[1] == '>') && (endChar == '>')) {
					if (nTempl < 2) {
						return nullptr;
					}
					const char* hypo = readExpression(p+2, endChar, nBraces, nTempl);
					if (hypo) {
						p = hypo;
					}
					return p+1;
				}
				if (*p == '(') {
					//try it as shift operator
					p = readExpression(p+1 , ')', nBraces + 1, nTempl);
					if (!p) {
						return nullptr;
					}
				}
				if (*p == ')' && endChar == ')') {
					if (nBraces == 0) {
						return nullptr;
					}
					return p+1;
				}
				if ((*p == ',') && (endChar == ',')) {
					//nbraces and ntempl must be zero
					return p+1;
				}
				p++;
			}
			return nullptr;
		}
		static constexpr int computeBufferLength(const char* src, int nItems)
		{
			const char* p = src;
			int bufferLength = 0;
			for(int i = 0; i < nItems; i++) {
					p = skipws(p);
					const char* afterName = readName(p);
					bufferLength += (afterName - p) + 1;
					p = readExpression(afterName, ',');
					if (!p) {
						return bufferLength;
					}
			}
			return bufferLength;
		}
		static constexpr _T_AM_EnumerationValuesTyperResult simplifyResult(
			_T_AM_EnumerationValuesTyper items[],
			int length
		)
		{
			int flags = 0;
			for(int i=0; i < length; i++)
			{
				flags |= items[i].flags;
			}
			if ((flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_64U)
				&& (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_64S)) {
				return _T_AM_EnumerationValuesTyperResult::typeFail;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_64U) {
				return _T_AM_EnumerationValuesTyperResult::typeU64;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_64S) {
				return _T_AM_EnumerationValuesTyperResult::typeS64;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_64SU) {
				return _T_AM_EnumerationValuesTyperResult::typeS64;
			}
			if ((flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_32U)
				&& (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_32S)) {
				return _T_AM_EnumerationValuesTyperResult::typeS64;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_32U) {
				return _T_AM_EnumerationValuesTyperResult::typeU32;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_32S) {
				return _T_AM_EnumerationValuesTyperResult::typeS32;
			}
			if (flags & _T_AM_EnumerationValuesTyper::_T_AM_EnumerationValuesTyper_32SU) {
				return _T_AM_EnumerationValuesTyperResult::typeS32;
			}
			return _T_AM_EnumerationValuesTyperResult::typeFail;
		}
		template<typename Tbuffer>
		static constexpr Tbuffer fillBuffer(const char* src, int nItems)
		{
			Tbuffer rv {};
			const char* p = src;
			int d = 0;
			for(int i = 0; i < nItems; i++) {
					p = skipws(p);
					const char* afterName = readName(p);
					for(const char* s = p; s < afterName; s++) {
						rv[d] = *s;
						d++;
					}
					rv[d] = '\0';
					d++;
					p = readExpression(afterName, ',');
			}
			return rv;
		}
		template<typename Titem, typename Tbuffer, typename T>
		static constexpr Tbuffer fillStringToEnum(const char* src, _T_AM_EnumerationValuesHolder<T> items[], int nItems)
		{
			Tbuffer b {};
			const char *s = src;
			valuesAssign(items, nItems);
			for(int i =0; i < nItems; i++) {
				b[i] = Titem(
					 items[i]._M_value
					,_T_AM_StringItemBase(s)
				);
				while((*s) != '\0') {
					s++;
				}
				if (i + 1 < nItems) {
					s++;
				}
			}
			return b;
		}
		template<typename T, std::size_t size>
		static constexpr std::array<int, size> fillStringIndex(const std::array<T, size>& src)
		{
			std::array<int, size> rv{};
			struct si
			{
				int index;
				const char* name;
				constexpr bool operator < (const si& right) {return AMCECompareStrings(name, right.name) < 0;}
			};
			std::array<si, size> sia{};
			for(int i = 0; i < size; i++) {
				sia[i].index = i;
				sia[i].name = src[i]._M_name_item._M_str;
			}
			AMCEQuickSort(sia);
			for(int i = 0; i < size; i++) {
				rv[i] = sia[i].index;
			}
			return rv;
		}
		template<typename T, std::size_t size, typename Tenum, int newSize, Tenum min>
		static constexpr std::array<int, newSize < 0 ? -newSize : newSize> fillEnumIndex(const std::array<T, size>& src)
		{
			std::array<int, newSize < 0 ? -newSize : newSize> rv{};
			if (newSize < 0) {
				for(int i = 0; i < -newSize; i++) {
					rv[i] = -1;
				}
				for(int i = 0; i < size; i++) {
					Tenum index = -min + (Tenum)src[i]._M_value;
					//static_assert(index >= 0 && index < (Tenum)-newSize);
					rv[index] = i;
					//rv[i] = index*10000+i+size*10000000;
				}

			}
			else {
				struct ei
				{
					int index;
					Tenum enumval;
					constexpr bool operator < (const ei& right) {return enumval < right.enumval;}
				};
				std::array<ei, size> eia{};
				for(int i = 0; i < newSize; i++) {
					eia[i].index = i;
					eia[i].enumval = src[i]._M_value;
				}
				AMCEQuickSort(eia);
				for(int i = 0; i < newSize; i++) {
					rv[i] = eia[i].index;
				}
			}

			return rv;
		}
		template<typename T>
		static constexpr void valuesAssign(
			_T_AM_EnumerationValuesHolder<T> items[],
			int length
		)
		{
			T last = 0;
			for(int i = 0; i < length; i++) {
				if (items[i]._M_set) {
					last = items[i]._M_value;
				}
				else {
					items[i]._M_value = last;
					items[i]._M_set = true;
				}
				last++;
			}
		}
		template<typename T>
		static constexpr int valuesLength(
			_T_AM_EnumerationValuesHolder<T> items[],
			int length
		)
		{
			valuesAssign(items, length);
			T min = std::numeric_limits<T>::max();
			T max = std::numeric_limits<T>::min();
			for(int i = 0; i < length; i++) {
				if (items[i]._M_value < min) {
					min = items[i]._M_value;
				}
				if (items[i]._M_value > max) {
					max = items[i]._M_value;
				}
			}
			T vRange = max - min + 1;
			T iRange = (length & 0x1F)*2 + (length >> 5)*1.25*32;
			if (vRange < iRange) {
				return -vRange;
			}
			return (T)length;
		}
		template<typename T>
		static constexpr T valuesMin(
			_T_AM_EnumerationValuesHolder<T> items[],
			int length
		)
		{
			valuesAssign(items, length);
			T min = std::numeric_limits<T>::max();
			for(int i = 0; i < length; i++) {
				if (items[i]._M_value < min) {
					min = items[i]._M_value;
				}
			}
			return min;
		}
		template<typename TEnumType, std::size_t length>
		static constexpr int ceFindIndex(
			const std::array<_T_AM_EnumStringItem<TEnumType>, length>& stringEnum,
			const std::array<int, length>& stringIndex,
			const char* name)
		{
			_T_AM_EnumStringItem<TEnumType> needle(0, name);
			return  AMCEBinarySearchThroughIndex(
				stringEnum,
				stringIndex,
				needle,
				[](const _T_AM_EnumStringItem<TEnumType>& left, const _T_AM_EnumStringItem<TEnumType>& right) constexpr
					{return AMCECompareStrings(left._M_name_item._M_str, right._M_name_item._M_str) < 0;}
				);
		}
		template<typename TEnumType, bool bBSearch, TEnumType min, std::size_t length, std::size_t enumIndexLength>
		static constexpr int ceFindIndex(
			const std::array<_T_AM_EnumStringItem<TEnumType>, length>& stringEnum,
			const std::array<int, enumIndexLength>& enumIndex,
			TEnumType value)
		{
			if constexpr (bBSearch) {
				_T_AM_EnumStringItem<TEnumType> needle(value, "");
				return  AMCEBinarySearchThroughIndex(
					stringEnum,
					enumIndex,
					needle,
					[](const _T_AM_EnumStringItem<TEnumType>& left, const  _T_AM_EnumStringItem<TEnumType>& right) constexpr
						{return left._M_value < right._M_value;}
					);
			}
			else {
				int ndx = int(value - min);
				if (ndx < 0 || ndx >= enumIndexLength) {
					return -1;
				}
				return enumIndex[ndx];
			}
		}
		template<typename TEnumType, std::size_t length>
		static inline int findIndex(
			std::array<_T_AM_EnumStringItem<TEnumType>, length>& stringEnum,
			std::array<int, length>& stringIndex,
			const char* name)
		{
			_T_AM_EnumStringItem<TEnumType> needle(0, name);
			return  AMCEBinarySearchThroughIndex(
				stringEnum,
				stringIndex,
				needle,
				[](const _T_AM_EnumStringItem<TEnumType>& left, const _T_AM_EnumStringItem<TEnumType>& right)
					{return strcmp(left._M_name_item._M_str, right._M_name_item._M_str) < 0;}
				);
		}
		template<typename TEnumType, bool bBSearch, TEnumType min, std::size_t length, std::size_t enumIndexLength>
		static inline int findIndex(
			std::array<_T_AM_EnumStringItem<TEnumType>, length>& stringEnum,
			std::array<int, enumIndexLength>& enumIndex,
			TEnumType value)
		{
			return ceFindIndex<TEnumType, bBSearch, min>(stringEnum, enumIndex, value);
		}
		template<typename TEnumType>
		inline static const char* sprintval(char* buffer, TEnumType value)
		{
			if constexpr (std::is_same<uint32_t, TEnumType>::value) {
				sprintf(buffer, "%u", value);
			}
			else if constexpr (std::is_same<int32_t, TEnumType>::value) {
				sprintf(buffer, "%i", value);
			}
			else if constexpr (std::is_same<uint64_t, TEnumType>::value) {
				sprintf(buffer, "%lu", value);
			}
			else{
				sprintf(buffer, "%li", value);
			}
			return buffer;
		}
	};





	/*!
	 *    Enumeration type definition, macro.
	 *    \param enumname     Name of new enumeration type(set enum enumname {...})
	 *    \param enummode     AM_EEnumerationModes mode.
	 *    \param enumdef      Items. Definition as any enum item, may contain obsahovat constants etc. Instead of ',' are items
	 *                        separated by macro ,
	 */
	#define ENUM( enumname , enummode , ... )\
	    template<typename T>\
			class AMEnumerationType_##enumname: public AMEnumerationTypeBase\
			{\
				constexpr void testMode()\
				{\
					_T_AM_EnumerationTypesParameterTestFunctionMode(#enumname, AMEnumerationModes::enummode);\
				}\
				static constexpr int length()\
				{\
					_T_AM_EnumerationValuesCounter __VA_ARGS__;\
					_T_AM_EnumerationValuesCounter items[] = {__VA_ARGS__};\
					return sizeof(items) / sizeof(_T_AM_EnumerationValuesCounter);\
				}\
				static constexpr int _M_ce_length = AMEnumerationType_##enumname::length();\
				static constexpr _T_AM_EnumerationValuesTyperResult type()\
				{\
					_T_AM_EnumerationValuesTyper __VA_ARGS__;\
					_T_AM_EnumerationValuesTyper items[] = {__VA_ARGS__};\
					return AMEnumerationTypeBase::simplifyResult(items, AMEnumerationType_##enumname::_M_ce_length);\
				}\
				static constexpr _T_AM_EnumerationValuesTyperResult _M_type_maybe_fails = AMEnumerationType_##enumname::type();\
				static constexpr _T_AM_EnumerationValuesTyperResult throwIfTypeFails()\
				{\
					static_assert(AMEnumerationType_##enumname::_M_type_maybe_fails != _T_AM_EnumerationValuesTyperResult::typeFail, "Enumeration values out of range");\
					return AMEnumerationType_##enumname::_M_type_maybe_fails;\
				}\
				static constexpr _T_AM_EnumerationValuesTyperResult _M_type = AMEnumerationType_##enumname::throwIfTypeFails();\
				static constexpr int _M_string_buffer_length_maybe_fail = AMEnumerationTypeBase::computeBufferLength(#__VA_ARGS__, _M_ce_length);\
				static constexpr int throwIfParseFails()\
				{\
					static_assert(AMEnumerationType_##enumname::_M_string_buffer_length_maybe_fail > 0, "Enumaration values fails parsing");\
					return AMEnumerationType_##enumname::_M_string_buffer_length_maybe_fail;\
				}\
				static constexpr int _M_string_buffer_length = AMEnumerationType_##enumname::throwIfParseFails();\
				static constexpr std::array<char, _M_string_buffer_length> _M_ce_buffer = AMCore::AMEnumerationTypeBase::template fillBuffer<std::array<char, _M_string_buffer_length> >(#__VA_ARGS__, _M_ce_length);\
				static std::array<char, _M_string_buffer_length> _M_buffer;\
			public:\
				typedef typename std::conditional<_M_type == _T_AM_EnumerationValuesTyperResult::typeS32, std::int32_t,\
						typename std::conditional<_M_type == _T_AM_EnumerationValuesTyperResult::typeU32, std::uint32_t,\
						typename std::conditional<_M_type == _T_AM_EnumerationValuesTyperResult::typeS64, std::int64_t,\
						std::uint64_t>::type>::type>::type _T_AM_enum_type;\
			/*protected:*/\
				static constexpr int valuesLength()\
				{\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> __VA_ARGS__;\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> items[] = {__VA_ARGS__};\
					return AMEnumerationTypeBase::valuesLength(items, AMEnumerationType_##enumname::_M_ce_length);\
				}\
				static constexpr int _M_ce_value_length = AMEnumerationType_##enumname::valuesLength();\
				static constexpr std::array<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>, _M_ce_length> fillStringEnum()\
				{\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> __VA_ARGS__;\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> items[] = {__VA_ARGS__};\
					return AMCore::AMEnumerationTypeBase::template fillStringToEnum<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>, std::array<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>, _M_ce_length>, _T_AM_enum_type>(_M_ce_buffer.data(), items, AMEnumerationType_##enumname::_M_ce_length);\
				}\
				static constexpr std::array<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>, _M_ce_length> _M_ce_string_enum = AMEnumerationType_##enumname::fillStringEnum();\
				static constexpr std::array<int, _M_ce_length> _M_ce_string_index = AMEnumerationTypeBase::fillStringIndex(_M_ce_string_enum);\
				static constexpr _T_AM_enum_type valuesMin()\
				{\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> __VA_ARGS__;\
					_T_AM_EnumerationValuesHolder<_T_AM_enum_type> items[] = {__VA_ARGS__};\
					return AMEnumerationTypeBase::valuesMin(items, AMEnumerationType_##enumname::_M_ce_length);\
				}\
				static constexpr int _M_ce_value_min = AMEnumerationType_##enumname::valuesMin();\
				static constexpr std::array<int, _M_ce_value_length < 0 ? -_M_ce_value_length : _M_ce_value_length> _M_ce_enum_index = AMEnumerationTypeBase::template fillEnumIndex<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>,_M_ce_length,_T_AM_enum_type, _M_ce_value_length, _M_ce_value_min>(_M_ce_string_enum);\
			public:\
				static std::array<AMCore::_T_AM_EnumStringItem<AMEnumerationType_##enumname::_T_AM_enum_type>, _M_ce_length> _M_string_enum;\
				static std::array<int, _M_ce_length> _M_string_index;\
				static std::array<int, _M_ce_value_length < 0 ? -_M_ce_value_length : _M_ce_value_length> _M_enum_index;\
				static inline int findIndex(const char* name) { return AMEnumerationTypeBase::findIndex(_M_string_enum, _M_string_index, name);}\
				static inline int findIndex(AMCore::AMLString lstring) { return AMEnumerationTypeBase::findIndex(_M_string_enum, _M_string_index, lstring.c_str());}\
				static inline int findIndex(_T_AM_enum_type value) { return AMEnumerationTypeBase::template findIndex<_T_AM_enum_type, (_M_ce_value_length > 0), _M_ce_value_min>(_M_string_enum, _M_enum_index, (_T_AM_enum_type)value);}\
				static constexpr int ceFindIndex(const char* name) { return AMEnumerationTypeBase::ceFindIndex(_M_ce_string_enum, _M_ce_string_index, name);}\
				static constexpr int ceFindIndex(AMEnumerationType_##enumname::_T_AM_enum_type value) { return AMEnumerationTypeBase::template ceFindIndex<_T_AM_enum_type, (_M_ce_value_length > 0), _M_ce_value_min>(_M_ce_string_enum, _M_ce_enum_index, (_T_AM_enum_type)value);}\
				static inline const char* enumToStringNormal(_T_AM_enum_type value, char* buffer, std::size_t bufferLength)\
				{\
					int ndx = findIndex((_T_AM_enum_type)value);\
					if (ndx == -1) {\
						char buff[1024];\
						char nbuff[40];\
						sprintf(buff, "AMEnum: "#enumname"::AMEnumToString ("#enummode"): value %s out of range.\n", AMEnumerationTypeBase::sprintval(nbuff,value));\
						throw AMEnumException(buff);\
					}\
					if (!buffer || bufferLength < 32) {\
						return _M_string_enum[ndx]._M_name_item._M_str;\
					}\
					strncpy(buffer, _M_string_enum[ndx]._M_name_item._M_str, bufferLength - 1);\
					buffer[bufferLength - 1] = '\0';\
					return buffer;\
				}\
				static inline const char* enumToStringBinary(_T_AM_enum_type value, char* buffer, std::size_t bufferLength)\
				{\
					int ndx = findIndex((_T_AM_enum_type)value);\
					if (ndx == -1) {\
						char buff[1024];\
						char nbuff[40];\
						sprintf(buff, "AMEnum: "#enumname"::AMEnumToString ("#enummode"): value %s out of range.\n", AMEnumerationTypeBase::sprintval(nbuff,value));\
						throw AMEnumException(buff);\
					}\
					return _M_string_enum[ndx]._M_name_item._M_str;\
				}\
				static inline const char* enumToStringAllDefined(_T_AM_enum_type value, char* buffer, std::size_t bufferLength)\
				{\
					int ndx = findIndex((_T_AM_enum_type)value);\
					if (ndx == -1) {\
						if (!buffer || bufferLength < 32) {\
							throw AMEnumException("AMEnum: "#enumname"::AMEnumToString ("#enummode"): provided buffer is less than 32 chracters.\n");\
						}\
						AMEnumerationTypeBase::sprintval(buffer,value);\
						return buffer;\
					}\
					if (!buffer || bufferLength < 32) {\
						return _M_string_enum[ndx]._M_name_item._M_str;\
					}\
					strncpy(buffer, _M_string_enum[ndx]._M_name_item._M_str, bufferLength - 1);\
					buffer[bufferLength - 1] = '\0';\
					return buffer;\
				}\
			};\
		template<typename T>\
			std::array<char, AMEnumerationType_##enumname<T>::_M_string_buffer_length>  AMEnumerationType_##enumname<T>::_M_buffer = AMEnumerationType_##enumname<T>::_M_ce_buffer;\
		template<typename T>\
			std::array<typename AMCore::_T_AM_EnumStringItem<typename AMEnumerationType_##enumname<T>::_T_AM_enum_type>, AMEnumerationType_##enumname<T>::_M_ce_length>  AMEnumerationType_##enumname<T>::_M_string_enum = AMEnumerationType_##enumname<T>::_M_ce_string_enum;\
		template<typename T>\
			std::array<int, AMEnumerationType_##enumname<T>::_M_ce_length>  AMEnumerationType_##enumname<T>::_M_string_index = AMEnumerationType_##enumname<T>::_M_ce_string_index;\
		template<typename T>\
			std::array<int, AMEnumerationType_##enumname<T>::_M_ce_value_length < 0 ? -AMEnumerationType_##enumname<T>::_M_ce_value_length : AMEnumerationType_##enumname<T>::_M_ce_value_length>  AMEnumerationType_##enumname<T>::_M_enum_index = AMEnumerationType_##enumname<T>::_M_ce_enum_index;\
		enum class enumname: AMEnumerationType_##enumname<int>::_T_AM_enum_type  {__VA_ARGS__};\
		inline const char* AMEnumToString(enumname value, char* buffer = nullptr, std::size_t bufferLength = 0) {return AMEnumerationType_##enumname<int>::enumToString##enummode(static_cast<AMEnumerationType_##enumname<int>::_T_AM_enum_type>(value), buffer, bufferLength);}\
		inline const char* AMCEEnumToString(enumname value);


}

/** @} */

#endif // AMENUM_H
