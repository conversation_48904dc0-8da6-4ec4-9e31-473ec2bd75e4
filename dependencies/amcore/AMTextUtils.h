//
// Created by <PERSON><PERSON><PERSON> on 24.11.22.
//

#ifndef AMCORE_AMTEXTUTILS_H
#define AMCORE_AMTEXTUTILS_H

#include <string>
#include <iostream>
#include <map>
#include "AMTwoWayTable.h"

namespace AMCore {

#define UEOF ((unsigned int)0xFFFFFFFF)

    std::string& AMStringToLowercaseStripDia(std::string &string);
    template<typename TStringType>
    TStringType AMStringStripSpaces(TStringType &s);
    bool AMStringContainsOnlyCharacters(std::string characters, std::string& str);
    template<typename TIter>
    unsigned int AMStringToLowercaseStripDiaGet(TIter &it, const std::string &s);
    template<typename TIter>
    unsigned int AMStringGet(TIter &it, const std::string &s);
    extern template unsigned int AMStringToLowercaseStripDiaGet(std::string::const_iterator &it, const std::string &s);
    extern template unsigned int AMStringToLowercaseStripDiaGet(std::string::const_reverse_iterator &it, const std::string &s);
    template<typename TIter>
    unsigned int AMStringToLowercaseStripDiaUnget(TIter &it, const std::string &s);
    extern template unsigned int AMStringToLowercaseStripDiaUnget(std::string::const_iterator &it, const std::string &s);
    extern template unsigned int AMStringToLowercaseStripDiaUnget(std::string::const_reverse_iterator &it, const std::string &s);
    unsigned int AMStreamToLowercaseStripDiaGet(std::istream &s, unsigned int *original = nullptr);
    void AMStreamResetContext(std::istream &s);
    void AMStreamToLowercaseStripDiaUnget(std::istream &s);
    void AMStreamPut(std::ostream &os, unsigned int c);
    void AMStringPushback(std::string &s, unsigned int c);
    class AMStringCZ;
    class AMStringStripped: public std::string
    {
    public:
        AMStringStripped(const std::string& s): std::string(s) {};
        AMStringStripped(const AMStringCZ& s);
        AMStringStripped(): std::string() {};
        AMStringStripped& operator=(const AMStringCZ& s) {std::string::operator=((std::string &)s);AMStringToLowercaseStripDia(*this);return *this;};
    };
    class AMStringCZ: public std::string
    {
    public:
        AMStringCZ(): std::string() {};
        AMStringCZ(const std::string& s): std::string(s) {};
        AMStringCZ(const char *s): std::string(s) {};
        template< class InputIt >
        AMStringCZ(InputIt bg, InputIt en): std::string(bg, en) {}
        //bool operator==(const AMStringCZ &other) const {return (static_cast<const std::string&>(*this) == static_cast<const std::string&>(other));};
    };

    bool operator<( const AMStringCZ& lhs, const AMStringCZ& rhs);
    bool operator<( const AMStringStripped& lhs, const AMStringStripped& rhs );
    bool operator<( const std::string& lhs, const AMStringStripped& rhs );
    bool operator<( const AMStringStripped& lhs, const std::string& rhs );
    bool operator<( const AMStringCZ& lhs, const AMStringStripped& rhs );
    bool operator<( const AMStringStripped& lhs, const AMStringCZ& rhs );
    inline bool operator==(const AMStringCZ &a,const AMStringCZ &b) {return (static_cast<const std::string&>(a) == static_cast<const std::string&>(b));};
    inline bool operator==(const AMStringCZ &a, const AMStringStripped &b) {return (static_cast<const std::string&>(a) == static_cast<const std::string&>(b));};
    inline bool operator==(const AMStringStripped &a, const AMStringCZ &b) {return (static_cast<const std::string&>(a) == static_cast<const std::string&>(b));};
    inline bool operator==(const AMStringCZ &a, const std::string &b) {return (static_cast<const std::string&>(a) == static_cast<const std::string&>(b));};
    inline bool operator==(const std::string &a, const AMStringCZ &b) {return (static_cast<const std::string&>(a) == static_cast<const std::string&>(b));};
    inline bool operator==(const AMStringCZ &a, const char *b) {return (static_cast<const std::string&>(a) == b);};
    inline bool operator==(const char *a, const AMStringCZ &b) {return (a == static_cast<const std::string&>(b));};

    inline bool operator<( const AMTwoWayPair<AMStringStripped , const int *>& lhs, const AMTwoWayPair<AMStringStripped , const int *>& rhs ) {return lhs.first < rhs.first;};
    inline bool operator<( const AMTwoWayPair<std::string , const int *>& lhs, const AMTwoWayPair<AMStringStripped , const int *>& rhs ) {return lhs.first < rhs.first;};
    inline bool operator<( const AMTwoWayPair<AMStringStripped , const int *>& lhs, const AMTwoWayPair<std::string , const int *>& rhs ) {return lhs.first < rhs.first;};
    inline bool operator<( const AMTwoWayPair<AMStringCZ , const int *>& lhs, const AMTwoWayPair<AMStringCZ , const int *>& rhs ) {return lhs.first < rhs.first;};
    inline bool operator<( const AMTwoWayPair<AMStringCZ , const int *>& lhs, const AMTwoWayPair<AMStringStripped , const int *>& rhs ) {return lhs.first < rhs.first;};
    inline bool operator<( const AMTwoWayPair<AMStringStripped , const int *>& lhs, const AMTwoWayPair<AMStringCZ , const int *>& rhs ) {return lhs.first < rhs.first;};

    /*
    class AMStringStrippedStart: public std::string
    {
    public:
        AMStringStrippedStart(const std::string& s): std::string(s) {};
    };
    bool operator<( const AMStringStrippedStart& lhs, const AMStringStrippedStart& rhs );
    bool operator<( const std::string& lhs, const AMStringStrippedStart& rhs );
    bool operator<( const AMStringStrippedStart& lhs, const std::string& rhs );
    class AMStringStrippedStartReverse: public std::string
    {
    public:
        AMStringStrippedStartReverse(const std::string& s): std::string(s) {};
    };

    bool operator<( const AMStringStrippedStartReverse& lhs, const AMStringStrippedStartReverse& rhs );
    bool operator<( const std::string& lhs, const AMStringStrippedStartReverse& rhs );
    bool operator<( const AMStringStrippedStartReverse& lhs, const std::string& rhs );*/
    template<typename TStringType>
    std::map<int, TStringType> AMExplode(TStringType delimiter, TStringType s, bool strip = true);
    template<typename TStringType>
    inline std::map<int, TStringType> AMExplode(const char *delimiter, TStringType s, bool strip = true);
    std::string AMImplode(std::string delimiter, const std::map<int, std::string>& map);
    std::string AMEscape(const std::string &s, const std::string escapables, char escape = '\\');
    std::string AMUnEscape(const std::string &s, char escape);


    template<typename TStringType>
    TStringType AMStringStripSpaces(TStringType &s)
    {
        typename TStringType::size_type substrPosBeg = 0;
        typename TStringType::size_type substrPosEnd = s.length();
        while(substrPosBeg < substrPosEnd && std::isspace(s[substrPosBeg])) {
            substrPosBeg++;
        }
        while(substrPosEnd > substrPosBeg && std::isspace(s[substrPosEnd - 1])) {
            substrPosEnd--;
        }
        if (substrPosBeg > 0 || substrPosEnd < s.length()) {
            return s.substr(substrPosBeg, substrPosEnd - substrPosBeg);
        }
        return s;
    }

    template<typename TStringType>
    std::map<int, TStringType> AMExplode(TStringType delimiter, TStringType s, bool strip)
    {
        std::map<int, TStringType> rv;
        typename TStringType::size_type substrPosBeg = 0;
        typename TStringType::size_type substrPosEnd = TStringType::npos;
        int ndx = 0;
        do {
            substrPosEnd = s.find(delimiter, substrPosBeg);
            typename TStringType::size_type bg = substrPosBeg;
            typename TStringType::size_type en = substrPosEnd == TStringType::npos ? s.length() : substrPosEnd;
            if (strip) {
                while(bg < en && std::isspace(s[bg])) {
                    bg++;
                }
                while(en > bg && std::isspace(s[en - 1])) {
                    en--;
                }
            }
            if (en >= bg) {
                if (bg != en || ndx > 0) {
                    rv.insert({ndx, s.substr(bg, en - bg)});
                    ndx++;
                }
            }
            substrPosBeg = substrPosEnd + delimiter.length();
        } while(substrPosEnd != TStringType::npos);
        return rv;
    }

    template<typename TStringType>
    inline std::map<int, TStringType> AMExplode(const char *delimiter, TStringType s, bool strip)
    {
        return AMExplode(TStringType(delimiter), s, strip);
    }

};


#endif //AMCORE_AMTEXTUTILS_H
