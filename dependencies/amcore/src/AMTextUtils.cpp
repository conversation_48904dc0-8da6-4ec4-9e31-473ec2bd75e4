//
// Created by <PERSON><PERSON><PERSON> on 24.11.22.
//

#include "../AMTextUtils.h"
#include "amcore/AMAssert.h"
#include <mutex> //for context
#include <sstream>

namespace AMCore {

    std::mutex AMTextUtils_Mutex;

    //https://www.utf8-chartable.de/unicode-utf8-table.pl?number=1024
    static unsigned short StringToLowercaseStripDia_tab_base[] = {
        '\0', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' '
        , ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', '!', '"', '#', '$', '%', '&', '\'', '(', ')', '*', '+'
        , ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'a'
        , 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w'
        , 'x', 'y', 'z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm'
        , 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~', 0x7F
    };

    static unsigned short StringToLowercaseStripDia_tab_ext[7][128] = {
        {
            'a',//U+00C0	À	c3 80	LATIN CAPITAL LETTER A WITH GRAVE
            'a',//U+00C1	Á	c3 81	LATIN CAPITAL LETTER A WITH ACUTE
            'a',//U+00C2	Â	c3 82	LATIN CAPITAL LETTER A WITH CIRCUMFLEX
            'a',//U+00C3	Ã	c3 83	LATIN CAPITAL LETTER A WITH TILDE
            'a',//U+00C4	Ä	c3 84	LATIN CAPITAL LETTER A WITH DIAERESIS
            'a',//U+00C5	Å	c3 85	LATIN CAPITAL LETTER A WITH RING ABOVE
            0xc3a6,//U+00C6	Æ	c3 86	LATIN CAPITAL LETTER AE
            'c',//U+00C7	Ç	c3 87	LATIN CAPITAL LETTER C WITH CEDILLA
            'e',//U+00C8	È	c3 88	LATIN CAPITAL LETTER E WITH GRAVE
            'e',//U+00C9	É	c3 89	LATIN CAPITAL LETTER E WITH ACUTE
            'e',//U+00CA	Ê	c3 8a	LATIN CAPITAL LETTER E WITH CIRCUMFLEX
            'e',//U+00CB	Ë	c3 8b	LATIN CAPITAL LETTER E WITH DIAERESIS
            'i',//U+00CC	Ì	c3 8c	LATIN CAPITAL LETTER I WITH GRAVE
            'i',//U+00CD	Í	c3 8d	LATIN CAPITAL LETTER I WITH ACUTE
            'i',//U+00CE	Î	c3 8e	LATIN CAPITAL LETTER I WITH CIRCUMFLEX
            'i',//U+00CF	Ï	c3 8f	LATIN CAPITAL LETTER I WITH DIAERESIS
            0xc3b0,//U+00D0	Ð	c3 90	LATIN CAPITAL LETTER ETH
            'n',//U+00D1	Ñ	c3 91	LATIN CAPITAL LETTER N WITH TILDE
            'o',//U+00D2	Ò	c3 92	LATIN CAPITAL LETTER O WITH GRAVE
            'o',//U+00D3	Ó	c3 93	LATIN CAPITAL LETTER O WITH ACUTE
            'o',//U+00D4	Ô	c3 94	LATIN CAPITAL LETTER O WITH CIRCUMFLEX
            'o',//U+00D5	Õ	c3 95	LATIN CAPITAL LETTER O WITH TILDE
            'o',//U+00D6	Ö	c3 96	LATIN CAPITAL LETTER O WITH DIAERESIS
            0xC397,//U+00D7	×	c3 97	MULTIPLICATION SIGN
            'o',//U+00D8	Ø	c3 98	LATIN CAPITAL LETTER O WITH STROKE
            'u',//U+00D9	Ù	c3 99	LATIN CAPITAL LETTER U WITH GRAVE
            'u',//U+00DA	Ú	c3 9a	LATIN CAPITAL LETTER U WITH ACUTE
            'u',//U+00DB	Û	c3 9b	LATIN CAPITAL LETTER U WITH CIRCUMFLEX
            'u',//U+00DC	Ü	c3 9c	LATIN CAPITAL LETTER U WITH DIAERESIS
            'y',//U+00DD	Ý	c3 9d	LATIN CAPITAL LETTER Y WITH ACUTE
            0xc3be,//U+00DE	Þ	c3 9e	LATIN CAPITAL LETTER THORN
            's',//U+00DF	ß	c3 9f	LATIN SMALL LETTER SHARP S
            'a',//U+00E0	à	c3 a0	LATIN SMALL LETTER A WITH GRAVE
            'a',//U+00E1	á	c3 a1	LATIN SMALL LETTER A WITH ACUTE
            'a',//U+00E2	â	c3 a2	LATIN SMALL LETTER A WITH CIRCUMFLEX
            'a',//U+00E3	ã	c3 a3	LATIN SMALL LETTER A WITH TILDE
            'a',//U+00E4	ä	c3 a4	LATIN SMALL LETTER A WITH DIAERESIS
            'a',//U+00E5	å	c3 a5	LATIN SMALL LETTER A WITH RING ABOVE
            0xc3a6, //U+00E6	æ	c3 a6	LATIN SMALL LETTER AE
            'c',//U+00E7	ç	c3 a7	LATIN SMALL LETTER C WITH CEDILLA
            'e',//U+00E8	è	c3 a8	LATIN SMALL LETTER E WITH GRAVE
            'e',//U+00E9	é	c3 a9	LATIN SMALL LETTER E WITH ACUTE
            'e',//U+00EA	ê	c3 aa	LATIN SMALL LETTER E WITH CIRCUMFLEX
            'e',//U+00EB	ë	c3 ab	LATIN SMALL LETTER E WITH DIAERESIS
            'i',//U+00EC	ì	c3 ac	LATIN SMALL LETTER I WITH GRAVE
            'i',//U+00ED	í	c3 ad	LATIN SMALL LETTER I WITH ACUTE
            'i',//U+00EE	î	c3 ae	LATIN SMALL LETTER I WITH CIRCUMFLEX
            'i',//U+00EF	ï	c3 af	LATIN SMALL LETTER I WITH DIAERESIS
            0xc3b0,//U+00F0	ð	c3 b0	LATIN SMALL LETTER ETH
            'n',//U+00F1	ñ	c3 b1	LATIN SMALL LETTER N WITH TILDE
            'o',//U+00F2	ò	c3 b2	LATIN SMALL LETTER O WITH GRAVE
            'o',//U+00F3	ó	c3 b3	LATIN SMALL LETTER O WITH ACUTE
            'o',//U+00F4	ô	c3 b4	LATIN SMALL LETTER O WITH CIRCUMFLEX
            'o',//U+00F5	õ	c3 b5	LATIN SMALL LETTER O WITH TILDE
            'o',//U+00F6	ö	c3 b6	LATIN SMALL LETTER O WITH DIAERESIS
            0xC3B7,//U+00F7	÷	c3 b7	DIVISION SIGN
            'o',//U+00F8	ø	c3 b8	LATIN SMALL LETTER O WITH STROKE
            'u',//U+00F9	ù	c3 b9	LATIN SMALL LETTER U WITH GRAVE
            'u',//U+00FA	ú	c3 ba	LATIN SMALL LETTER U WITH ACUTE
            'u',//U+00FB	û	c3 bb	LATIN SMALL LETTER U WITH CIRCUMFLEX
            'u',//U+00FC	ü	c3 bc	LATIN SMALL LETTER U WITH DIAERESIS
            'y',//U+00FD	ý	c3 bd	LATIN SMALL LETTER Y WITH ACUTE
            0xc3be,//U+00FE	þ	c3 be	LATIN SMALL LETTER THORN
            'y',//U+00FF	ÿ	c3 bf	LATIN SMALL LETTER Y WITH DIAERESIS
        }, {
            'a',//U+0100	Ā	c4 80	LATIN CAPITAL LETTER A WITH MACRON
            'a',//U+0101	ā	c4 81	LATIN SMALL LETTER A WITH MACRON
            'a',//U+0102	Ă	c4 82	LATIN CAPITAL LETTER A WITH BREVE
            'a',//U+0103	ă	c4 83	LATIN SMALL LETTER A WITH BREVE
            'a',//U+0104	Ą	c4 84	LATIN CAPITAL LETTER A WITH OGONEK
            'a',//U+0105	ą	c4 85	LATIN SMALL LETTER A WITH OGONEK
            'c',//U+0106	Ć	c4 86	LATIN CAPITAL LETTER C WITH ACUTE
            'c',//U+0107	ć	c4 87	LATIN SMALL LETTER C WITH ACUTE
            'c',//U+0108	Ĉ	c4 88	LATIN CAPITAL LETTER C WITH CIRCUMFLEX
            'c',//U+0109	ĉ	c4 89	LATIN SMALL LETTER C WITH CIRCUMFLEX
            'c',//U+010A	Ċ	c4 8a	LATIN CAPITAL LETTER C WITH DOT ABOVE
            'c',//U+010B	ċ	c4 8b	LATIN SMALL LETTER C WITH DOT ABOVE
            'c',//U+010C	Č	c4 8c	LATIN CAPITAL LETTER C WITH CARON
            'c',//U+010D	č	c4 8d	LATIN SMALL LETTER C WITH CARON
            'd',//U+010E	Ď	c4 8e	LATIN CAPITAL LETTER D WITH CARON
            'd',//U+010F	ď	c4 8f	LATIN SMALL LETTER D WITH CARON
            'd',//U+0110	Đ	c4 90	LATIN CAPITAL LETTER D WITH STROKE
            'd',//U+0111	đ	c4 91	LATIN SMALL LETTER D WITH STROKE
            'e',//U+0112	Ē	c4 92	LATIN CAPITAL LETTER E WITH MACRON
            'e',//U+0113	ē	c4 93	LATIN SMALL LETTER E WITH MACRON
            'e',//U+0114	Ĕ	c4 94	LATIN CAPITAL LETTER E WITH BREVE
            'e',//U+0115	ĕ	c4 95	LATIN SMALL LETTER E WITH BREVE
            'e',//U+0116	Ė	c4 96	LATIN CAPITAL LETTER E WITH DOT ABOVE
            'e',//U+0117	ė	c4 97	LATIN SMALL LETTER E WITH DOT ABOVE
            'e',//U+0118	Ę	c4 98	LATIN CAPITAL LETTER E WITH OGONEK
            'e',//U+0119	ę	c4 99	LATIN SMALL LETTER E WITH OGONEK
            'e',//U+011A	Ě	c4 9a	LATIN CAPITAL LETTER E WITH CARON
            'e',//U+011B	ě	c4 9b	LATIN SMALL LETTER E WITH CARON
            'g',//U+011C	Ĝ	c4 9c	LATIN CAPITAL LETTER G WITH CIRCUMFLEX
            'g',//U+011D	ĝ	c4 9d	LATIN SMALL LETTER G WITH CIRCUMFLEX
            'g',//U+011E	Ğ	c4 9e	LATIN CAPITAL LETTER G WITH BREVE
            'g',//U+011F	ğ	c4 9f	LATIN SMALL LETTER G WITH BREVE
            'g',//U+0120	Ġ	c4 a0	LATIN CAPITAL LETTER G WITH DOT ABOVE
            'g',//U+0121	ġ	c4 a1	LATIN SMALL LETTER G WITH DOT ABOVE
            'g',//U+0122	Ģ	c4 a2	LATIN CAPITAL LETTER G WITH CEDILLA
            'g',//U+0123	ģ	c4 a3	LATIN SMALL LETTER G WITH CEDILLA
            'h',//U+0124	Ĥ	c4 a4	LATIN CAPITAL LETTER H WITH CIRCUMFLEX
            'h',//U+0125	ĥ	c4 a5	LATIN SMALL LETTER H WITH CIRCUMFLEX
            'h',//U+0126	Ħ	c4 a6	LATIN CAPITAL LETTER H WITH STROKE
            'h',//U+0127	ħ	c4 a7	LATIN SMALL LETTER H WITH STROKE
            'i',//U+0128	Ĩ	c4 a8	LATIN CAPITAL LETTER I WITH TILDE
            'i',//U+0129	ĩ	c4 a9	LATIN SMALL LETTER I WITH TILDE
            'i',//U+012A	Ī	c4 aa	LATIN CAPITAL LETTER I WITH MACRON
            'i',//U+012B	ī	c4 ab	LATIN SMALL LETTER I WITH MACRON
            'i',//U+012C	Ĭ	c4 ac	LATIN CAPITAL LETTER I WITH BREVE
            'i',//U+012D	ĭ	c4 ad	LATIN SMALL LETTER I WITH BREVE
            'i',//U+012E	Į	c4 ae	LATIN CAPITAL LETTER I WITH OGONEK
            'i',//U+012F	į	c4 af	LATIN SMALL LETTER I WITH OGONEK
            'i',//U+0130	İ	c4 b0	LATIN CAPITAL LETTER I WITH DOT ABOVE
            0xc4b1,//U+0131	ı	c4 b1	LATIN SMALL LETTER DOTLESS I
            0xc4b3,//U+0132	Ĳ	c4 b2	LATIN CAPITAL LIGATURE IJ
            0xc4b3,//U+0133	ĳ	c4 b3	LATIN SMALL LIGATURE IJ
            'j',//U+0134	Ĵ	c4 b4	LATIN CAPITAL LETTER J WITH CIRCUMFLEX
            'j',//U+0135	ĵ	c4 b5	LATIN SMALL LETTER J WITH CIRCUMFLEX
            'k',//U+0136	Ķ	c4 b6	LATIN CAPITAL LETTER K WITH CEDILLA
            'k',//U+0137	ķ	c4 b7	LATIN SMALL LETTER K WITH CEDILLA
            0xc4b8,//U+0138	ĸ	c4 b8	LATIN SMALL LETTER KRA
            'l',//U+0139	Ĺ	c4 b9	LATIN CAPITAL LETTER L WITH ACUTE
            'l',//U+013A	ĺ	c4 ba	LATIN SMALL LETTER L WITH ACUTE
            'l',//U+013B	Ļ	c4 bb	LATIN CAPITAL LETTER L WITH CEDILLA
            'l',//U+013C	ļ	c4 bc	LATIN SMALL LETTER L WITH CEDILLA
            'l',//U+013D	Ľ	c4 bd	LATIN CAPITAL LETTER L WITH CARON
            'l',//U+013E	ľ	c4 be	LATIN SMALL LETTER L WITH CARON
            'l',//U+013F	Ŀ	c4 bf	LATIN CAPITAL LETTER L WITH MIDDLE DOT
        }, {
            'l',//U+0140	ŀ	c5 80	LATIN SMALL LETTER L WITH MIDDLE DOT
            'l',//U+0141	Ł	c5 81	LATIN CAPITAL LETTER L WITH STROKE
            'l',//U+0142	ł	c5 82	LATIN SMALL LETTER L WITH STROKE
            'n',//U+0143	Ń	c5 83	LATIN CAPITAL LETTER N WITH ACUTE
            'n',//U+0144	ń	c5 84	LATIN SMALL LETTER N WITH ACUTE
            'n',//U+0145	Ņ	c5 85	LATIN CAPITAL LETTER N WITH CEDILLA
            'n',//U+0146	ņ	c5 86	LATIN SMALL LETTER N WITH CEDILLA
            'n',//U+0147	Ň	c5 87	LATIN CAPITAL LETTER N WITH CARON
            'n',//U+0148	ň	c5 88	LATIN SMALL LETTER N WITH CARON
            'n',//U+0149	ŉ	c5 89	LATIN SMALL LETTER N PRECEDED BY APOSTROPHE
            0xc58b,//U+014A	Ŋ	c5 8a	LATIN CAPITAL LETTER ENG
            0xc58b,//U+014B	ŋ	c5 8b	LATIN SMALL LETTER ENG
            'o',//U+014C	Ō	c5 8c	LATIN CAPITAL LETTER O WITH MACRON
            'o',//U+014D	ō	c5 8d	LATIN SMALL LETTER O WITH MACRON
            'o',//U+014E	Ŏ	c5 8e	LATIN CAPITAL LETTER O WITH BREVE
            'o',//U+014F	ŏ	c5 8f	LATIN SMALL LETTER O WITH BREVE
            'o',//U+0150	Ő	c5 90	LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
            'o',//U+0151	ő	c5 91	LATIN SMALL LETTER O WITH DOUBLE ACUTE
            0xc593,//U+0152	Œ	c5 92	LATIN CAPITAL LIGATURE OE
            0xc593,//U+0153	œ	c5 93	LATIN SMALL LIGATURE OE
            'r',//U+0154	Ŕ	c5 94	LATIN CAPITAL LETTER R WITH ACUTE
            'r',//U+0155	ŕ	c5 95	LATIN SMALL LETTER R WITH ACUTE
            'r',//U+0156	Ŗ	c5 96	LATIN CAPITAL LETTER R WITH CEDILLA
            'r',//U+0157	ŗ	c5 97	LATIN SMALL LETTER R WITH CEDILLA
            'r',//U+0158	Ř	c5 98	LATIN CAPITAL LETTER R WITH CARON
            'r',//U+0159	ř	c5 99	LATIN SMALL LETTER R WITH CARON
            's',//U+015A	Ś	c5 9a	LATIN CAPITAL LETTER S WITH ACUTE
            's',//U+015B	ś	c5 9b	LATIN SMALL LETTER S WITH ACUTE
            's',//U+015C	Ŝ	c5 9c	LATIN CAPITAL LETTER S WITH CIRCUMFLEX
            's',//U+015D	ŝ	c5 9d	LATIN SMALL LETTER S WITH CIRCUMFLEX
            's',//U+015E	Ş	c5 9e	LATIN CAPITAL LETTER S WITH CEDILLA
            's',//U+015F	ş	c5 9f	LATIN SMALL LETTER S WITH CEDILLA
            's',//U+0160	Š	c5 a0	LATIN CAPITAL LETTER S WITH CARON
            's',//U+0161	š	c5 a1	LATIN SMALL LETTER S WITH CARON
            't',//U+0162	Ţ	c5 a2	LATIN CAPITAL LETTER T WITH CEDILLA
            't',//U+0163	ţ	c5 a3	LATIN SMALL LETTER T WITH CEDILLA
            't',//U+0164	Ť	c5 a4	LATIN CAPITAL LETTER T WITH CARON
            't',//U+0165	ť	c5 a5	LATIN SMALL LETTER T WITH CARON
            't',//U+0166	Ŧ	c5 a6	LATIN CAPITAL LETTER T WITH STROKE
            't',//U+0167	ŧ	c5 a7	LATIN SMALL LETTER T WITH STROKE
            'u',//U+0168	Ũ	c5 a8	LATIN CAPITAL LETTER U WITH TILDE
            'u',//U+0169	ũ	c5 a9	LATIN SMALL LETTER U WITH TILDE
            'u',//U+016A	Ū	c5 aa	LATIN CAPITAL LETTER U WITH MACRON
            'u',//U+016B	ū	c5 ab	LATIN SMALL LETTER U WITH MACRON
            'u',//U+016C	Ŭ	c5 ac	LATIN CAPITAL LETTER U WITH BREVE
            'u',//U+016D	ŭ	c5 ad	LATIN SMALL LETTER U WITH BREVE
            'u',//U+016E	Ů	c5 ae	LATIN CAPITAL LETTER U WITH RING ABOVE
            'u',//U+016F	ů	c5 af	LATIN SMALL LETTER U WITH RING ABOVE
            'u',//U+0170	Ű	c5 b0	LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
            'u',//U+0171	ű	c5 b1	LATIN SMALL LETTER U WITH DOUBLE ACUTE
            'u',//U+0172	Ų	c5 b2	LATIN CAPITAL LETTER U WITH OGONEK
            'u',//U+0173	ų	c5 b3	LATIN SMALL LETTER U WITH OGONEK
            'w',//U+0174	Ŵ	c5 b4	LATIN CAPITAL LETTER W WITH CIRCUMFLEX
            'w',//U+0175	ŵ	c5 b5	LATIN SMALL LETTER W WITH CIRCUMFLEX
            'y',//U+0176	Ŷ	c5 b6	LATIN CAPITAL LETTER Y WITH CIRCUMFLEX
            'y',//U+0177	ŷ	c5 b7	LATIN SMALL LETTER Y WITH CIRCUMFLEX
            'y',//U+0178	Ÿ	c5 b8	LATIN CAPITAL LETTER Y WITH DIAERESIS
            'z',//U+0179	Ź	c5 b9	LATIN CAPITAL LETTER Z WITH ACUTE
            'z',//U+017A	ź	c5 ba	LATIN SMALL LETTER Z WITH ACUTE
            'z',//U+017B	Ż	c5 bb	LATIN CAPITAL LETTER Z WITH DOT ABOVE
            'z',//U+017C	ż	c5 bc	LATIN SMALL LETTER Z WITH DOT ABOVE
            'z',//U+017D	Ž	c5 bd	LATIN CAPITAL LETTER Z WITH CARON
            'z',//U+017E	ž	c5 be	LATIN SMALL LETTER Z WITH CARON
            's',//U+017F	ſ	c5 bf	LATIN SMALL LETTER LONG S
        }, {
            'b',//U+0180	ƀ	c6 80	LATIN SMALL LETTER B WITH STROKE
            'b',//U+0181	Ɓ	c6 81	LATIN CAPITAL LETTER B WITH HOOK
            'b',//U+0182	Ƃ	c6 82	LATIN CAPITAL LETTER B WITH TOPBAR
            'b',//U+0183	ƃ	c6 83	LATIN SMALL LETTER B WITH TOPBAR
            '6',//U+0184	Ƅ	c6 84	LATIN CAPITAL LETTER TONE SIX
            '6',//U+0185	ƅ	c6 85	LATIN SMALL LETTER TONE SIX
            'o',//U+0186	Ɔ	c6 86	LATIN CAPITAL LETTER OPEN O
            'c',//U+0187	Ƈ	c6 87	LATIN CAPITAL LETTER C WITH HOOK
            'c',//U+0188	ƈ	c6 88	LATIN SMALL LETTER C WITH HOOK
            'd',//U+0189	Ɖ	c6 89	LATIN CAPITAL LETTER AFRICAN D
            'd',//U+018A	Ɗ	c6 8a	LATIN CAPITAL LETTER D WITH HOOK
            'd',//U+018B	Ƌ	c6 8b	LATIN CAPITAL LETTER D WITH TOPBAR
            'd',//U+018C	ƌ	c6 8c	LATIN SMALL LETTER D WITH TOPBAR
            0xc68d,//U+018D	ƍ	c6 8d	LATIN SMALL LETTER TURNED DELTA
            0xc998,//U+018E	Ǝ	c6 8e	LATIN CAPITAL LETTER REVERSED E
            0xc68f,//U+018F	Ə	c6 8f	LATIN CAPITAL LETTER SCHWA
            0xc99b,//U+0190	Ɛ	c6 90	LATIN CAPITAL LETTER OPEN E
            'f',//U+0191	Ƒ	c6 91	LATIN CAPITAL LETTER F WITH HOOK
            'f',//U+0192	ƒ	c6 92	LATIN SMALL LETTER F WITH HOOK
            'g',//U+0193	Ɠ	c6 93	LATIN CAPITAL LETTER G WITH HOOK
            0xc9a3,//U+0194	Ɣ	c6 94	LATIN CAPITAL LETTER GAMMA
            0xc695,//U+0195	ƕ	c6 95	LATIN SMALL LETTER HV
            0xc9a9,//U+0196	Ɩ	c6 96	LATIN CAPITAL LETTER IOTA
            'i',//U+0197	Ɨ	c6 97	LATIN CAPITAL LETTER I WITH STROKE
            'k',//U+0198	Ƙ	c6 98	LATIN CAPITAL LETTER K WITH HOOK
            'k',//U+0199	ƙ	c6 99	LATIN SMALL LETTER K WITH HOOK
            'l',//U+019A	ƚ	c6 9a	LATIN SMALL LETTER L WITH BAR
            0xc69b,//U+019B	ƛ	c6 9b	LATIN SMALL LETTER LAMBDA WITH STROKE
            0xc9af,//U+019C	Ɯ	c6 9c	LATIN CAPITAL LETTER TURNED M
            'n',//U+019D	Ɲ	c6 9d	LATIN CAPITAL LETTER N WITH LEFT HOOK
            'n',//U+019E	ƞ	c6 9e	LATIN SMALL LETTER N WITH LONG RIGHT LEG
            'o',//U+019F	Ɵ	c6 9f	LATIN CAPITAL LETTER O WITH MIDDLE TILDE
            'o',//U+01A0	Ơ	c6 a0	LATIN CAPITAL LETTER O WITH HORN
            'o',//U+01A1	ơ	c6 a1	LATIN SMALL LETTER O WITH HORN
            0xc6a3,//U+01A2	Ƣ	c6 a2	LATIN CAPITAL LETTER OI
            0xc6a3,//U+01A3	ƣ	c6 a3	LATIN SMALL LETTER OI
            'p',//U+01A4	Ƥ	c6 a4	LATIN CAPITAL LETTER P WITH HOOK
            'p',//U+01A5	ƥ	c6 a5	LATIN SMALL LETTER P WITH HOOK
            0xc6a6,//U+01A6	Ʀ	c6 a6	LATIN LETTER YR
            '2',//U+01A7	Ƨ	c6 a7	LATIN CAPITAL LETTER TONE TWO
            '2',//U+01A8	ƨ	c6 a8	LATIN SMALL LETTER TONE TWO
            0xca83,//U+01A9	Ʃ	c6 a9	LATIN CAPITAL LETTER ESH
            0xc6aa,//U+01AA	ƪ	c6 aa	LATIN LETTER REVERSED ESH LOOP
            't',//U+01AB	ƫ	c6 ab	LATIN SMALL LETTER T WITH PALATAL HOOK
            't',//U+01AC	Ƭ	c6 ac	LATIN CAPITAL LETTER T WITH HOOK
            't',//U+01AD	ƭ	c6 ad	LATIN SMALL LETTER T WITH HOOK
            't',//U+01AE	Ʈ	c6 ae	LATIN CAPITAL LETTER T WITH RETROFLEX HOOK
            'u',//U+01AF	Ư	c6 af	LATIN CAPITAL LETTER U WITH HORN
            'u',//U+01B0	ư	c6 b0	LATIN SMALL LETTER U WITH HORN
            0xca8a,//U+01B1	Ʊ	c6 b1	LATIN CAPITAL LETTER UPSILON
            'v',//U+01B2	Ʋ	c6 b2	LATIN CAPITAL LETTER V WITH HOOK
            'y',//U+01B3	Ƴ	c6 b3	LATIN CAPITAL LETTER Y WITH HOOK
            'y',//U+01B4	ƴ	c6 b4	LATIN SMALL LETTER Y WITH HOOK
            'z',//U+01B5	Ƶ	c6 b5	LATIN CAPITAL LETTER Z WITH STROKE
            'z',//U+01B6	ƶ	c6 b6	LATIN SMALL LETTER Z WITH STROKE
            0xca92,//U+01B7	Ʒ	c6 b7	LATIN CAPITAL LETTER EZH
            0xc6b9,//U+01B8	Ƹ	c6 b8	LATIN CAPITAL LETTER EZH REVERSED
            0xc6b9,//U+01B9	ƹ	c6 b9	LATIN SMALL LETTER EZH REVERSED
            0xca92,//U+01BA	ƺ	c6 ba	LATIN SMALL LETTER EZH WITH TAIL
            '2',//U+01BB	ƻ	c6 bb	LATIN LETTER TWO WITH STROKE
            '5',//U+01BC	Ƽ	c6 bc	LATIN CAPITAL LETTER TONE FIVE
            '5',//U+01BD	ƽ	c6 bd	LATIN SMALL LETTER TONE FIVE
            0xc6be,//U+01BE	ƾ	c6 be	LATIN LETTER INVERTED GLOTTAL STOP WITH STROKE
            0xc6bf,//U+01BF	ƿ	c6 bf	LATIN LETTER WYNN
        }, {
            0xc780,//U+01C0	ǀ	c7 80	LATIN LETTER DENTAL CLICK
            0xc781,//U+01C1	ǁ	c7 81	LATIN LETTER LATERAL CLICK
            0xc782,//U+01C2	ǂ	c7 82	LATIN LETTER ALVEOLAR CLICK
            0xc783,//U+01C3	ǃ	c7 83	LATIN LETTER RETROFLEX CLICK
            0xc7b3,//U+01C4	Ǆ	c7 84	LATIN CAPITAL LETTER DZ WITH CARON
            0xc7b3,//U+01C5	ǅ	c7 85	LATIN CAPITAL LETTER D WITH SMALL LETTER Z WITH CARON
            0xc7b3,//U+01C6	ǆ	c7 86	LATIN SMALL LETTER DZ WITH CARON
            0xc789,//U+01C7	Ǉ	c7 87	LATIN CAPITAL LETTER LJ
            0xc789,//U+01C8	ǈ	c7 88	LATIN CAPITAL LETTER L WITH SMALL LETTER J
            0xc789,//U+01C9	ǉ	c7 89	LATIN SMALL LETTER LJ
            0xc78c,//U+01CA	Ǌ	c7 8a	LATIN CAPITAL LETTER NJ
            0xc78c,//U+01CB	ǋ	c7 8b	LATIN CAPITAL LETTER N WITH SMALL LETTER J
            0xc78c,//U+01CC	ǌ	c7 8c	LATIN SMALL LETTER NJ
            'a',//U+01CD	Ǎ	c7 8d	LATIN CAPITAL LETTER A WITH CARON
            'a',//U+01CE	ǎ	c7 8e	LATIN SMALL LETTER A WITH CARON
            'i',//U+01CF	Ǐ	c7 8f	LATIN CAPITAL LETTER I WITH CARON
            'i',//U+01D0	ǐ	c7 90	LATIN SMALL LETTER I WITH CARON
            'o',//U+01D1	Ǒ	c7 91	LATIN CAPITAL LETTER O WITH CARON
            'o',//U+01D2	ǒ	c7 92	LATIN SMALL LETTER O WITH CARON
            'u',//U+01D3	Ǔ	c7 93	LATIN CAPITAL LETTER U WITH CARON
            'u',//U+01D4	ǔ	c7 94	LATIN SMALL LETTER U WITH CARON
            'u',//U+01D5	Ǖ	c7 95	LATIN CAPITAL LETTER U WITH DIAERESIS AND MACRON
            'u',//U+01D6	ǖ	c7 96	LATIN SMALL LETTER U WITH DIAERESIS AND MACRON
            'u',//U+01D7	Ǘ	c7 97	LATIN CAPITAL LETTER U WITH DIAERESIS AND ACUTE
            'u',//U+01D8	ǘ	c7 98	LATIN SMALL LETTER U WITH DIAERESIS AND ACUTE
            'u',//U+01D9	Ǚ	c7 99	LATIN CAPITAL LETTER U WITH DIAERESIS AND CARON
            'u',//U+01DA	ǚ	c7 9a	LATIN SMALL LETTER U WITH DIAERESIS AND CARON
            'u',//U+01DB	Ǜ	c7 9b	LATIN CAPITAL LETTER U WITH DIAERESIS AND GRAVE
            'u',//U+01DC	ǜ	c7 9c	LATIN SMALL LETTER U WITH DIAERESIS AND GRAVE
            'e',//U+01DD	ǝ	c7 9d	LATIN SMALL LETTER TURNED E
            'a',//U+01DE	Ǟ	c7 9e	LATIN CAPITAL LETTER A WITH DIAERESIS AND MACRON
            'a',//U+01DF	ǟ	c7 9f	LATIN SMALL LETTER A WITH DIAERESIS AND MACRON
            'a',//U+01E0	Ǡ	c7 a0	LATIN CAPITAL LETTER A WITH DOT ABOVE AND MACRON
            'a',//U+01E1	ǡ	c7 a1	LATIN SMALL LETTER A WITH DOT ABOVE AND MACRON
            0xc3a6,//U+01E2	Ǣ	c7 a2	LATIN CAPITAL LETTER AE WITH MACRON
            0xc3a6,//U+01E3	ǣ	c7 a3	LATIN SMALL LETTER AE WITH MACRON
            'g',//U+01E4	Ǥ	c7 a4	LATIN CAPITAL LETTER G WITH STROKE
            'g',//U+01E5	ǥ	c7 a5	LATIN SMALL LETTER G WITH STROKE
            'g',//U+01E6	Ǧ	c7 a6	LATIN CAPITAL LETTER G WITH CARON
            'g',//U+01E7	ǧ	c7 a7	LATIN SMALL LETTER G WITH CARON
            'k',//U+01E8	Ǩ	c7 a8	LATIN CAPITAL LETTER K WITH CARON
            'k',//U+01E9	ǩ	c7 a9	LATIN SMALL LETTER K WITH CARON
            'o',//U+01EA	Ǫ	c7 aa	LATIN CAPITAL LETTER O WITH OGONEK
            'o',//U+01EB	ǫ	c7 ab	LATIN SMALL LETTER O WITH OGONEK
            'o',//U+01EC	Ǭ	c7 ac	LATIN CAPITAL LETTER O WITH OGONEK AND MACRON
            'o',//U+01ED	ǭ	c7 ad	LATIN SMALL LETTER O WITH OGONEK AND MACRON
            0xca92,//U+01EE	Ǯ	c7 ae	LATIN CAPITAL LETTER EZH WITH CARON
            0xca92,//U+01EF	ǯ	c7 af	LATIN SMALL LETTER EZH WITH CARON
            'j',//U+01F0	ǰ	c7 b0	LATIN SMALL LETTER J WITH CARON
            0xc7b3,//U+01F1	Ǳ	c7 b1	LATIN CAPITAL LETTER DZ
            0xc7b3,//U+01F2	ǲ	c7 b2	LATIN CAPITAL LETTER D WITH SMALL LETTER Z
            0xc7b3,//U+01F3	ǳ	c7 b3	LATIN SMALL LETTER DZ
            'g',//U+01F4	Ǵ	c7 b4	LATIN CAPITAL LETTER G WITH ACUTE
            'g',//U+01F5	ǵ	c7 b5	LATIN SMALL LETTER G WITH ACUTE
            0xc7b6,//U+01F6	Ƕ	c7 b6	LATIN CAPITAL LETTER HWAIR
            0xc6bf,//U+01F7	Ƿ	c7 b7	LATIN CAPITAL LETTER WYNN
            'n',//U+01F8	Ǹ	c7 b8	LATIN CAPITAL LETTER N WITH GRAVE
            'n',//U+01F9	ǹ	c7 b9	LATIN SMALL LETTER N WITH GRAVE
            'a',//U+01FA	Ǻ	c7 ba	LATIN CAPITAL LETTER A WITH RING ABOVE AND ACUTE
            'a',//U+01FB	ǻ	c7 bb	LATIN SMALL LETTER A WITH RING ABOVE AND ACUTE
            0xc3a6,//U+01FC	Ǽ	c7 bc	LATIN CAPITAL LETTER AE WITH ACUTE
            0xc3a6,//U+01FD	ǽ	c7 bd	LATIN SMALL LETTER AE WITH ACUTE
            'o',//U+01FE	Ǿ	c7 be	LATIN CAPITAL LETTER O WITH STROKE AND ACUTE
            'o',//U+01FF	ǿ	c7 bf	LATIN SMALL LETTER O WITH STROKE AND ACUTE
        }, {
            'a',//U+0200	Ȁ	c8 80	LATIN CAPITAL LETTER A WITH DOUBLE GRAVE
            'a',//U+0201	ȁ	c8 81	LATIN SMALL LETTER A WITH DOUBLE GRAVE
            'a',//U+0202	Ȃ	c8 82	LATIN CAPITAL LETTER A WITH INVERTED BREVE
            'a',//U+0203	ȃ	c8 83	LATIN SMALL LETTER A WITH INVERTED BREVE
            'e',//U+0204	Ȅ	c8 84	LATIN CAPITAL LETTER E WITH DOUBLE GRAVE
            'e',//U+0205	ȅ	c8 85	LATIN SMALL LETTER E WITH DOUBLE GRAVE
            'e',//U+0206	Ȇ	c8 86	LATIN CAPITAL LETTER E WITH INVERTED BREVE
            'e',//U+0207	ȇ	c8 87	LATIN SMALL LETTER E WITH INVERTED BREVE
            'i',//U+0208	Ȉ	c8 88	LATIN CAPITAL LETTER I WITH DOUBLE GRAVE
            'i',//U+0209	ȉ	c8 89	LATIN SMALL LETTER I WITH DOUBLE GRAVE
            'i',//U+020A	Ȋ	c8 8a	LATIN CAPITAL LETTER I WITH INVERTED BREVE
            'i',//U+020B	ȋ	c8 8b	LATIN SMALL LETTER I WITH INVERTED BREVE
            'o',//U+020C	Ȍ	c8 8c	LATIN CAPITAL LETTER O WITH DOUBLE GRAVE
            'o',//U+020D	ȍ	c8 8d	LATIN SMALL LETTER O WITH DOUBLE GRAVE
            'o',//U+020E	Ȏ	c8 8e	LATIN CAPITAL LETTER O WITH INVERTED BREVE
            'o',//U+020F	ȏ	c8 8f	LATIN SMALL LETTER O WITH INVERTED BREVE
            'r',//U+0210	Ȑ	c8 90	LATIN CAPITAL LETTER R WITH DOUBLE GRAVE
            'r',//U+0211	ȑ	c8 91	LATIN SMALL LETTER R WITH DOUBLE GRAVE
            'r',//U+0212	Ȓ	c8 92	LATIN CAPITAL LETTER R WITH INVERTED BREVE
            'r',//U+0213	ȓ	c8 93	LATIN SMALL LETTER R WITH INVERTED BREVE
            'u',//U+0214	Ȕ	c8 94	LATIN CAPITAL LETTER U WITH DOUBLE GRAVE
            'u',//U+0215	ȕ	c8 95	LATIN SMALL LETTER U WITH DOUBLE GRAVE
            'u',//U+0216	Ȗ	c8 96	LATIN CAPITAL LETTER U WITH INVERTED BREVE
            'u',//U+0217	ȗ	c8 97	LATIN SMALL LETTER U WITH INVERTED BREVE
            's',//U+0218	Ș	c8 98	LATIN CAPITAL LETTER S WITH COMMA BELOW
            's',//U+0219	ș	c8 99	LATIN SMALL LETTER S WITH COMMA BELOW
            't',//U+021A	Ț	c8 9a	LATIN CAPITAL LETTER T WITH COMMA BELOW
            't',//U+021B	ț	c8 9b	LATIN SMALL LETTER T WITH COMMA BELOW
            0xc89d,//U+021C	Ȝ	c8 9c	LATIN CAPITAL LETTER YOGH
            0xc89d,//U+021D	ȝ	c8 9d	LATIN SMALL LETTER YOGH
            'h',//U+021E	Ȟ	c8 9e	LATIN CAPITAL LETTER H WITH CARON
            'h',//U+021F	ȟ	c8 9f	LATIN SMALL LETTER H WITH CARON
            'n',//U+0220	Ƞ	c8 a0	LATIN CAPITAL LETTER N WITH LONG RIGHT LEG
            'd',//U+0221	ȡ	c8 a1	LATIN SMALL LETTER D WITH CURL
            0xc8a3,//U+0222	Ȣ	c8 a2	LATIN CAPITAL LETTER OU
            0xc8a3,//U+0223	ȣ	c8 a3	LATIN SMALL LETTER OU
            'z',//U+0224	Ȥ	c8 a4	LATIN CAPITAL LETTER Z WITH HOOK
            'z',//U+0225	ȥ	c8 a5	LATIN SMALL LETTER Z WITH HOOK
            'a',//U+0226	Ȧ	c8 a6	LATIN CAPITAL LETTER A WITH DOT ABOVE
            'a',//U+0227	ȧ	c8 a7	LATIN SMALL LETTER A WITH DOT ABOVE
            'e',//U+0228	Ȩ	c8 a8	LATIN CAPITAL LETTER E WITH CEDILLA
            'e',//U+0229	ȩ	c8 a9	LATIN SMALL LETTER E WITH CEDILLA
            'o',//U+022A	Ȫ	c8 aa	LATIN CAPITAL LETTER O WITH DIAERESIS AND MACRON
            'o',//U+022B	ȫ	c8 ab	LATIN SMALL LETTER O WITH DIAERESIS AND MACRON
            'o',//U+022C	Ȭ	c8 ac	LATIN CAPITAL LETTER O WITH TILDE AND MACRON
            'o',//U+022D	ȭ	c8 ad	LATIN SMALL LETTER O WITH TILDE AND MACRON
            'o',//U+022E	Ȯ	c8 ae	LATIN CAPITAL LETTER O WITH DOT ABOVE
            'o',//U+022F	ȯ	c8 af	LATIN SMALL LETTER O WITH DOT ABOVE
            'o',//U+0230	Ȱ	c8 b0	LATIN CAPITAL LETTER O WITH DOT ABOVE AND MACRON
            'o',//U+0231	ȱ	c8 b1	LATIN SMALL LETTER O WITH DOT ABOVE AND MACRON
            'y',//U+0232	Ȳ	c8 b2	LATIN CAPITAL LETTER Y WITH MACRON
            'y',//U+0233	ȳ	c8 b3	LATIN SMALL LETTER Y WITH MACRON
            'l',//U+0234	ȴ	c8 b4	LATIN SMALL LETTER L WITH CURL
            'n',//U+0235	ȵ	c8 b5	LATIN SMALL LETTER N WITH CURL
            't',//U+0236	ȶ	c8 b6	LATIN SMALL LETTER T WITH CURL
            0xc8b7,//U+0237	ȷ	c8 b7	LATIN SMALL LETTER DOTLESS J
            0xc8b8,//U+0238	ȸ	c8 b8	LATIN SMALL LETTER DB DIGRAPH
            0xc8b9,//U+0239	ȹ	c8 b9	LATIN SMALL LETTER QP DIGRAPH
            'a',//U+023A	Ⱥ	c8 ba	LATIN CAPITAL LETTER A WITH STROKE
            'c',//U+023B	Ȼ	c8 bb	LATIN CAPITAL LETTER C WITH STROKE
            'c',//U+023C	ȼ	c8 bc	LATIN SMALL LETTER C WITH STROKE
            'l',//U+023D	Ƚ	c8 bd	LATIN CAPITAL LETTER L WITH BAR
            't',//U+023E	Ⱦ	c8 be	LATIN CAPITAL LETTER T WITH DIAGONAL STROKE
            's',//U+023F	ȿ	c8 bf	LATIN SMALL LETTER S WITH SWASH TAIL
        }, {
            'z',//U+0240	ɀ	c9 80	LATIN SMALL LETTER Z WITH SWASH TAIL
            0xc982,//U+0241	Ɂ	c9 81	LATIN CAPITAL LETTER GLOTTAL STOP
            0xc98a,//U+0242	ɂ	c9 82	LATIN SMALL LETTER GLOTTAL STOP
            'b',//U+0243	Ƀ	c9 83	LATIN CAPITAL LETTER B WITH STROKE
            'u',//U+0244	Ʉ	c9 84	LATIN CAPITAL LETTER U BAR
            0xca8c,//U+0245	Ʌ	c9 85	LATIN CAPITAL LETTER TURNED V
            'e',//U+0246	Ɇ	c9 86	LATIN CAPITAL LETTER E WITH STROKE
            'e',//U+0247	ɇ	c9 87	LATIN SMALL LETTER E WITH STROKE
            'j',//U+0248	Ɉ	c9 88	LATIN CAPITAL LETTER J WITH STROKE
            'j',//U+0249	ɉ	c9 89	LATIN SMALL LETTER J WITH STROKE
            'q',//U+024A	Ɋ	c9 8a	LATIN CAPITAL LETTER SMALL Q WITH HOOK TAIL
            'q',//U+024B	ɋ	c9 8b	LATIN SMALL LETTER Q WITH HOOK TAIL
            'r',//U+024C	Ɍ	c9 8c	LATIN CAPITAL LETTER R WITH STROKE
            'r',//U+024D	ɍ	c9 8d	LATIN SMALL LETTER R WITH STROKE
            'y',//U+024E	Ɏ	c9 8e	LATIN CAPITAL LETTER Y WITH STROKE
            'y',//U+024F	ɏ	c9 8f	LATIN SMALL LETTER Y WITH STROKE
            0xc990,//U+0250	ɐ	c9 90	LATIN SMALL LETTER TURNED A
            0xc991,//U+0251	ɑ	c9 91	LATIN SMALL LETTER ALPHA
            0xc992,//U+0252	ɒ	c9 92	LATIN SMALL LETTER TURNED ALPHA
            'b',//U+0253	ɓ	c9 93	LATIN SMALL LETTER B WITH HOOK
            0xc994,//U+0254	ɔ	c9 94	LATIN SMALL LETTER OPEN O
            'c',//U+0255	ɕ	c9 95	LATIN SMALL LETTER C WITH CURL
            'd',//U+0256	ɖ	c9 96	LATIN SMALL LETTER D WITH TAIL
            'd',//U+0257	ɗ	c9 97	LATIN SMALL LETTER D WITH HOOK
            0xc998,//U+0258	ɘ	c9 98	LATIN SMALL LETTER REVERSED E
            0xc999,//U+0259	ə	c9 99	LATIN SMALL LETTER SCHWA
            0xc999,//U+025A	ɚ	c9 9a	LATIN SMALL LETTER SCHWA WITH HOOK
            0xc99b,//U+025B	ɛ	c9 9b	LATIN SMALL LETTER OPEN E
            0xc99c,//U+025C	ɜ	c9 9c	LATIN SMALL LETTER REVERSED OPEN E
            0xc99c,//U+025D	ɝ	c9 9d	LATIN SMALL LETTER REVERSED OPEN E WITH HOOK
            0xc99c,//U+025E	ɞ	c9 9e	LATIN SMALL LETTER CLOSED REVERSED OPEN E
            0xc8b7,//U+025F	ɟ	c9 9f	LATIN SMALL LETTER DOTLESS J WITH STROKE
            'g',//U+0260	ɠ	c9 a0	LATIN SMALL LETTER G WITH HOOK
            0xc9a1,//U+0261	ɡ	c9 a1	LATIN SMALL LETTER SCRIPT G
            'g',//U+0262	ɢ	c9 a2	LATIN LETTER SMALL CAPITAL G
            0xc9a3,//U+0263	ɣ	c9 a3	LATIN SMALL LETTER GAMMA
            0xc9a4,//U+0264	ɤ	c9 a4	LATIN SMALL LETTER RAMS HORN
            0xc9a5,//U+0265	ɥ	c9 a5	LATIN SMALL LETTER TURNED H
            'h',//U+0266	ɦ	c9 a6	LATIN SMALL LETTER H WITH HOOK
            0xc9a7,//U+0267	ɧ	c9 a7	LATIN SMALL LETTER HENG WITH HOOK
            'i',//U+0268	ɨ	c9 a8	LATIN SMALL LETTER I WITH STROKE
            0xc9a9,//U+0269	ɩ	c9 a9	LATIN SMALL LETTER IOTA
            'i',//U+026A	ɪ	c9 aa	LATIN LETTER SMALL CAPITAL I
            'l',//U+026B	ɫ	c9 ab	LATIN SMALL LETTER L WITH MIDDLE TILDE
            'l',//U+026C	ɬ	c9 ac	LATIN SMALL LETTER L WITH BELT
            'l',//U+026D	ɭ	c9 ad	LATIN SMALL LETTER L WITH RETROFLEX HOOK
            0xc9ae,//U+026E	ɮ	c9 ae	LATIN SMALL LETTER LEZH
            0xc9af,//U+026F	ɯ	c9 af	LATIN SMALL LETTER TURNED M
            0xc9af,//U+0270	ɰ	c9 b0	LATIN SMALL LETTER TURNED M WITH LONG LEG
            'm',//U+0271	ɱ	c9 b1	LATIN SMALL LETTER M WITH HOOK
            'n',//U+0272	ɲ	c9 b2	LATIN SMALL LETTER N WITH LEFT HOOK
            'n',//U+0273	ɳ	c9 b3	LATIN SMALL LETTER N WITH RETROFLEX HOOK
            'n',//U+0274	ɴ	c9 b4	LATIN LETTER SMALL CAPITAL N
            'o',//U+0275	ɵ	c9 b5	LATIN SMALL LETTER BARRED O
            0xc9b6,//U+0276	ɶ	c9 b6	LATIN LETTER SMALL CAPITAL OE
            0xc9b7,//U+0277	ɷ	c9 b7	LATIN SMALL LETTER CLOSED OMEGA
            0xc9b8,//U+0278	ɸ	c9 b8	LATIN SMALL LETTER PHI
            0xc9b9,//U+0279	ɹ	c9 b9	LATIN SMALL LETTER TURNED R
            0xc9b9,//U+027A	ɺ	c9 ba	LATIN SMALL LETTER TURNED R WITH LONG LEG
            0xc9b9,//U+027B	ɻ	c9 bb	LATIN SMALL LETTER TURNED R WITH HOOK
            'r',//U+027C	ɼ	c9 bc	LATIN SMALL LETTER R WITH LONG LEG
            'r',//U+027D	ɽ	c9 bd	LATIN SMALL LETTER R WITH TAIL
            'r',//U+027E	ɾ	c9 be	LATIN SMALL LETTER R WITH FISHHOOK
            0xc9bf,//U+027F	ɿ	c9 bf	LATIN SMALL LETTER REVERSED R WITH FISHHOOK
        }
    };

    std::string& AMStringToLowercaseStripDia(std::string &_string) {
        std::string::iterator its = _string.begin();
        std::string::iterator itd = _string.begin();
        while (its != _string.end()) {
            unsigned int c = (unsigned char) *its;
            if (c >= 0 && c <= 0x7F) {
                *itd = StringToLowercaseStripDia_tab_base[c];
                itd++;
                its++;
            } else if (c >= 0xC3 && c <= 0xC9) {
                its++;
                if (its != _string.end()) {
                    unsigned int next = (unsigned char) *its;
                    if (next >= 0x80 && next <= 0xBF) {
                        unsigned short d = StringToLowercaseStripDia_tab_ext[c - 0xC3][next - 0x80];
                        if (d >= 0x100) {
                            *itd = (char) (d >> 8);
                            itd++;
                        }
                        *itd = (char) d & 0xFF;
                    } else {
                        *itd = (char) c;
                        itd++;
                        *itd = next;
                    }
                    itd++;
                    its++;
                }
            } else if (c >= 0xC0 && c <= 0xCF) {
                its++;
                if (its != _string.end()) {
                    *itd = c;
                    itd++;
                    *itd = *its;
                    itd++;
                    its++;
                }
            } else {
                *itd = *its;
                its++;
                itd++;
            }
        }
        _string.erase(itd, _string.end());
        return _string;
    }

    struct _AMStreamToLowercaseContext
    {
    protected:
        unsigned int m_preQueue[3];
        int m_preQueueSize;
        unsigned int m_lasts[5];
        int m_lastNdx;
        bool m_alreadyUngetToStream;
    public:
        _AMStreamToLowercaseContext()
            : m_preQueue{UEOF, UEOF, UEOF},
              m_preQueueSize(0),
              m_lasts{UEOF, UEOF, UEOF, UEOF, UEOF},
              m_lastNdx(0),
              m_alreadyUngetToStream(false)
        {};

        unsigned int get(std::istream &s)
        {
            if (m_preQueueSize == 0) {
                int tc = m_lasts[m_lastNdx] = s.get();
                m_lastNdx++;
                if (m_lastNdx >= 5) {
                    m_lastNdx = 0;
                }
                m_alreadyUngetToStream = false;
                return tc;
            } else {
                int tc = m_lasts[m_lastNdx] = m_preQueue[m_preQueueSize - 1];
                m_lastNdx++;
                if (m_lastNdx >= 5) {
                    m_lastNdx = 0;
                }
                m_preQueueSize--;
                return tc;
            }
        }

        void unget(std::istream &s)
        {
            AMAssert(m_preQueueSize < 3);
            if (m_alreadyUngetToStream) {
                m_preQueue[m_preQueueSize] = m_lasts[m_lastNdx -1 < 0 ? m_lastNdx - 1 + 5: m_lastNdx - 1];
                m_preQueueSize++;
            } else {
                s.unget();
                m_alreadyUngetToStream = true;
            }
            m_lastNdx--;
            if (m_lastNdx < 0) {
                m_lastNdx = 4;
            }
            m_lasts[m_lastNdx] = UEOF;
        }
        unsigned int lastChar()
        {
            unsigned int lastChar = m_lasts[(m_lastNdx - 1 < 0) ? m_lastNdx - 1 + 5 : m_lastNdx - 1];
            if (lastChar >= 0 && lastChar <= 0x7F) {
                return lastChar;
            }
            unsigned int lastChar2 = m_lasts[(m_lastNdx - 2 < 0) ? m_lastNdx - 2 + 5 : m_lastNdx - 2];
            if (lastChar2 >= 0xc0 && lastChar2 <= 0xDF) {
                return (lastChar2 << 8) | lastChar;
            }
            unsigned int lastChar3 = m_lasts[(m_lastNdx - 3 < 0) ? m_lastNdx - 3 + 5 : m_lastNdx - 3];
            if (lastChar3 >= 0xE0 && lastChar2 <= 0xEF) {
                return (lastChar3 << 16) | (lastChar2 << 8) | lastChar;
            }
            unsigned int lastChar4 = m_lasts[(m_lastNdx - 4 < 0) ? m_lastNdx - 4 + 5 : m_lastNdx - 4];
            if (lastChar4 >= 0xF0 && lastChar4 <= 0xF7) {
                return (lastChar4 << 24) | (lastChar3 << 16) | (lastChar2 << 8) | lastChar;
            }
            return lastChar;
        }
    };

    static std::map<std::istream *, _AMStreamToLowercaseContext> _AMStreamToLowercaseContexts;

    void AMStreamResetContext(std::istream &s) {
        std::lock_guard<std::mutex> lock(AMTextUtils_Mutex);
        auto it = _AMStreamToLowercaseContexts.find(&s);
        if (it != _AMStreamToLowercaseContexts.end()) {
            _AMStreamToLowercaseContexts.erase(it);
        }
    }

    void AMStreamToLowercaseStripDiaUnget(std::istream &s)
    {
        std::lock_guard<std::mutex> lock(AMTextUtils_Mutex);
        std::map<std::istream *, _AMStreamToLowercaseContext>::iterator cit = _AMStreamToLowercaseContexts.find(&s);
        AMAssert(cit != _AMStreamToLowercaseContexts.end());
        unsigned int c = cit->second.lastChar();
        if (c >= 0x100 && c!= 0xFFFFFFFF) {
            if (c >= 0x10000) {
                if (c >= 0x1000000) {
                    cit->second.unget(s);
                }
                cit->second.unget(s);
            }
            cit->second.unget(s);
        }
        cit->second.unget(s);
    }

    unsigned int AMStreamToLowercaseStripDiaGet(std::istream &s, unsigned int *original)
    {
        std::lock_guard<std::mutex> lock(AMTextUtils_Mutex);
        std::map<std::istream *, _AMStreamToLowercaseContext>::iterator cit = _AMStreamToLowercaseContexts.find(&s);
        if (cit == _AMStreamToLowercaseContexts.end()) {
            _AMStreamToLowercaseContexts.insert({&s, _AMStreamToLowercaseContext()});
            cit = _AMStreamToLowercaseContexts.find(&s);
        }
        _AMStreamToLowercaseContext& context = cit->second;
        unsigned int c = context.get(s);
        if (c == UEOF) {
            if (original) {
                *original = UEOF;
            }
            return UEOF;
        } else if (c >= 0 && c <= 0x7F) {
            if (original) {
                *original = c;
            }
            c = StringToLowercaseStripDia_tab_base[c];
            return c;
        } else if (c >= 0xC3 && c <= 0xC9) {
            unsigned int next = context.get(s);
            if (next == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            if (next >= 0x80 && next <= 0xBF) {
                unsigned short d = StringToLowercaseStripDia_tab_ext[c - 0xC3][next - 0x80];
                if (original) {
                    *original = (c << 8 | next);
                }
                return d;
            } else {
                c = (c << 8) | next;
                if (original) {
                    *original = c;
                }
                return c;
            }
        } else if (c >= 0xC0 && c <= 0xDF) {
            unsigned int next = context.get(s);
            if (next == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            c = (c << 8) | next;
            if (original) {
                *original = c;
            }
            return (c);
        } else if (c >= 0xE0 && c <= 0xEF) {
            unsigned int next = context.get(s);
            if (next == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            unsigned int next2 = context.get(s);
            if (next2 == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            c = (c << 16) | (next << 8) | next2;
            if (original) {
                *original = c;
            }
            return (c);
        } else if (c >= 0xF0 && c <= 0xF7) {
            unsigned int next = context.get(s);
            if (next == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            unsigned int next2 = context.get(s);
            if (next2 == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            unsigned int next3 = context.get(s);
            if (next3 == UEOF) {
                if (original) {
                    *original = UEOF;
                }
                return UEOF;
            }
            c = (c << 24) | (next << 16) | (next2 << 8) | next3;
            if (original) {
                *original = c;
            }
            return (c);
        } else {
            if (original) {
                *original = c;
            }
            return c;
        }
    }

    template<typename TIter>
    unsigned int AMStringToLowercaseStripDiaGet(TIter &it, const std::string &s)
    {
        unsigned int c = *it++ & 0xFF;
        if (c >= 0 && c <= 0x7F) {
            return StringToLowercaseStripDia_tab_base[c];
        }
        if (c >= 0xC3 && c <= 0xC9) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            int next = *it++ & 0xFF;
            if (next >= 0x80 && next <= 0xBF) {
                return StringToLowercaseStripDia_tab_ext[c - 0xC3][next - 0x80];
            }
            return (c << 8) & (next);
        } else if (c >= 0xC0 && c <= 0xDF) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            return (c << 8) | (*it++ & 0xFF);
        } else if (c >= 0xE0 && c <= 0xEF) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            return c;
        } else if (c >= 0xF0 && c <= 0xF7) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            return c;
        } else {
            return c;
        }
    }

    template<typename TIter>
    unsigned int AMStringGet(TIter &it, const std::string &s)
    {
        unsigned int c = *it++ & 0xFF;
        if (c >= 0 && c <= 0x7F) {
            return c;
        }
        if (c >= 0xC3 && c <= 0xC9) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            int next = *it++ & 0xFF;
            if (next >= 0x80 && next <= 0xBF) {
                return c<<8 | next;
            }
            return (c << 8) & (next);
        } else if (c >= 0xC0 && c <= 0xDF) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            return (c << 8) | (*it++ & 0xFF);
        } else if (c >= 0xE0 && c <= 0xEF) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            return c;
        } else if (c >= 0xF0 && c <= 0xF7) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cend()) {
                    return UEOF;
                }
            } else {
                if (it == s.crend()) {
                    return UEOF;
                }
            }
            c = (c << 8) | (*it++ & 0xFF);
            return c;
        } else {
            return c;
        }
    }

    template<typename TIter>
    unsigned int AMStringToLowercaseStripDiaUnget(TIter &it, const std::string &s)
    {
        if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
            AMAssert(it >= s.cbegin());
            if (it == s.cbegin()) {
                return UEOF;
            }
        } else {
            AMAssert(it >= s.crbegin());
            if (it == s.crbegin()) {
                return UEOF;
            }
        }
        unsigned int c = *--it & 0xFF;
        if (c >= 0 && c <= 0x7F) {
            return StringToLowercaseStripDia_tab_base[c];
        }
        if (c >= 0x80 && c <= 0xBF) {
            if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                if (it == s.cbegin()) {
                    return UEOF;
                }
            } else {
                if (it == s.crbegin()) {
                    return UEOF;
                }
            }
            int next = *--it & 0xFF;
            if (next >= 0xC3 && next <= 0xC9) {
                return StringToLowercaseStripDia_tab_ext[next - 0xC3][c - 0x80];
            } else if (next >= 0xC0 && next <= 0xDF) {
                return (next << 8) | (c);
            } else if (next >= 0x80 && next <= 0xBF) {
                if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                    if (it == s.cbegin()) {
                        return UEOF;
                    }
                } else {
                    if (it == s.crbegin()) {
                        return UEOF;
                    }
                }
                int next2 = *--it & 0xFF;
                if (next2 >= 0xE0 && next2 <= 0xEF) {
                    return (next2 << 16) | (next << 8) | (c);
                } else if (next2 >= 0x80 && next2 <= 0xBF) {
                    if constexpr (std::is_same<TIter, std::string::const_iterator>::value) {
                        if (it == s.cbegin()) {
                            return UEOF;
                        }
                    } else {
                        if (it == s.crbegin()) {
                            return UEOF;
                        }
                    }
                    int next3 = *--it & 0xFF;
                    if (next3 >= 0xF0 && next2 <= 0xF7) {
                        return (next3 << 24) | (next2 << 16) | (next << 8) | (c);
                    }
                }
            }
        }
        return UEOF;
    }

    void AMStreamPut(std::ostream &os, unsigned int c)
    {
        if (c >= 0x1000000) {
            os.put(c >> 24);
        }
        if (c >= 0x10000) {
            os.put((c >> 16) & 0xFF);
        }
        if (c >= 0x100) {
            os.put((c >> 8) & 0xFF);
        }
        os.put(c & 0xFF);
    }

    void AMStringPushback(std::string &s, unsigned int c)
    {
        int sz = 1;
        int cap = 0;
        if (c >= 0x100) {
            sz++;
            if (c >= 0x10000) {
                sz++;
                if (sz >= 1000000) {
                    sz++;
                }
            }
        }
        int len = s.length();
        s.append(sz, ' ');
        switch (sz) {
            case 4: s[len] = (c >> 24) & 0xFF;len++;
            case 3: s[len] = (c >> 16) & 0xFF;len++;
            case 2: s[len] = (c >> 8) & 0xFF;len++;
            case 1: s[len] = c & 0xFF;
        }
    }
#include <cstdio>
    bool operator<( const AMCore::AMStringCZ& lhs, const AMCore::AMStringCZ& rhs )
    {
        //printf("HHHH CC =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringCZ::const_iterator lit = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator rit = rhs.cbegin();
        AMCore::AMStringCZ::const_iterator litl = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator ritl = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            unsigned int lcl = AMStringGet(litl, lhs);
            unsigned int rcl = AMStringGet(ritl, rhs);
            if (lc == 'c' || rc == 'c') {
                AMCore::AMStringCZ::const_iterator litt = litl;
                AMCore::AMStringCZ::const_iterator ritt = ritl;
                unsigned int lct = litt == lhs.cend() ? '\0' : AMStringGet(litt, lhs);
                unsigned int rct = ritt == rhs.cend() ? '\0' : AMStringGet(ritt, rhs);
                if (((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                     && !((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (rc <= 'h') {
                        //printf(" -> false ch nch rc<=h\n");
                        return false;
                    } else if (rc >= 'i') {
                        //printf(" -> true ch nch rc>=i\n");
                        return true;
                    }
                } else if (!((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                           && ((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (lc <= 'h') {
                        //printf(" -> true nch ch lc<=h\n");
                        return true;
                    } else if (rc >= 'i') {
                        //printf(" -> false nch ch lc>=i\n");
                        return false;
                    }
                }
            }
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
            if (lcl < rcl) {
                //printf(" -> true lcl<rcl\n");
                return true;
            }
            if (lcl > rcl) {
                //printf(" -> false lcl>rcl\n");
                return false;
            }
        };
        //printf(" -> false def\n");
        if (lit == lhs.cend() && rit != rhs.cend()) {
            return true;
        }
        return false;
    }

    bool operator<( const AMCore::AMStringStripped& lhs, const AMCore::AMStringStripped& rhs )
    {
        //printf("HHHH SS =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringStripped::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStripped::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
        };
        //printf(" -> false def\n");
        if (lit == lhs.cend() && rit != rhs.cend()) {
            return true;
        }
        return false;
    }
    bool operator<( const std::string& lhs, const AMCore::AMStringStripped& rhs )
    {
        //printf("HHHH NS =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringStripped::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStripped::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
        };
        if (lit == lhs.cend() && rit != rhs.cend()) {
            //printf(" -> true part\n");
            return true;
        }
        //printf(" -> false def\n");
        if (lit == lhs.cend() && rit != rhs.cend()) {
            return true;
        }
        return false;
    }
    bool operator<( const AMCore::AMStringStripped& lhs, const std::string& rhs )
    {
        //printf("HHHH SN =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringStripped::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStripped::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
        };
        //printf(" -> false def\n");
        return false;
    }
    bool operator<( const AMCore::AMStringCZ& lhs, const AMCore::AMStringStripped& rhs )
    {
        //printf("HHHH CS =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringCZ::const_iterator lit = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator rit = rhs.cbegin();
        AMCore::AMStringCZ::const_iterator litl = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator ritl = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            unsigned int lcl = AMStringGet(litl, lhs);
            unsigned int rcl = AMStringGet(ritl, rhs);
            if (lc == 'c' || rc == 'c') {
                AMCore::AMStringCZ::const_iterator litt = litl;
                AMCore::AMStringCZ::const_iterator ritt = ritl;
                unsigned int lct = litt == lhs.cend() ? '\0' : AMStringGet(litt, lhs);
                unsigned int rct = ritt == rhs.cend() ? '\0' : AMStringGet(ritt, rhs);
                if (((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                    && !((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (rc <= 'h') {
                        //printf(" -> false ch nch rc<=h\n");
                        return false;
                    } else if (rc >= 'i') {
                        //printf(" -> true ch nch rc>=i\n");
                        return true;
                    }
                } else if (!((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                           && ((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (lc <= 'h') {
                        //printf(" -> true nch ch lc<=h\n");
                        return true;
                    } else if (rc >= 'i') {
                        //printf(" -> false nch ch lc>=i\n");
                        return false;
                    }
                }
            }
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
        };
        if (lit == lhs.cend() && rit != rhs.cend()) {
            //printf(" -> true part\n");
            return true;
        }
        //printf(" -> false def\n");
        return false;
    }
    bool operator<( const AMCore::AMStringStripped& lhs, const AMCore::AMStringCZ& rhs )
    {
        //printf("HHHH SC =%s= =%s=", lhs.c_str(), rhs.c_str());
        AMCore::AMStringCZ::const_iterator lit = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator rit = rhs.cbegin();
        AMCore::AMStringCZ::const_iterator litl = lhs.cbegin();
        AMCore::AMStringCZ::const_iterator ritl = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            unsigned int lcl = AMStringGet(litl, lhs);
            unsigned int rcl = AMStringGet(ritl, rhs);
            if (lc == 'c' || rc == 'c') {
                AMCore::AMStringCZ::const_iterator litt = litl;
                AMCore::AMStringCZ::const_iterator ritt = ritl;
                unsigned int lct = litt == lhs.cend() ? '\0' : AMStringGet(litt, lhs);
                unsigned int rct = ritt == rhs.cend() ? '\0' : AMStringGet(ritt, rhs);
                if (((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                    && !((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (rc <= 'h') {
                        //printf(" -> false ch nch rc<=h\n");
                        return false;
                    } else if (rc >= 'i') {
                        //printf(" -> true ch nch rc>=i\n");
                        return true;
                    }
                } else if (!((lcl == 'c' || lcl == 'C') && (lct == 'h' || lct == 'H'))
                           && ((rcl == 'c' || rcl == 'C') && (rct == 'h' || rct == 'H'))){
                    if (lc <= 'h') {
                        //printf(" -> true nch ch lc<=h\n");
                        return true;
                    } else if (rc >= 'i') {
                        //printf(" -> false nch ch lc>=i\n");
                        return false;
                    }
                }
            }
            if (lc < rc) {
                //printf(" -> true lc<rc\n");
                return true;
            }
            if (lc > rc) {
                //printf(" -> false lc>rc\n");
                return false;
            }
        };
        //printf(" -> false def\n");
        return false;
    }
/*
    bool operator<( const AMCore::AMStringStrippedStart& lhs, const AMCore::AMStringStrippedStart& rhs )
    {
        AMCore::AMStringStrippedStart::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStrippedStart::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        return false;
    }
    bool operator<( const std::string& lhs, const AMCore::AMStringStrippedStart& rhs )
    {
        AMCore::AMStringStrippedStart::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStrippedStart::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        return false;
    }
    bool operator<( const AMCore::AMStringStrippedStart& lhs, const std::string& rhs )
    {
        AMCore::AMStringStrippedStart::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStrippedStart::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        if (lit == lhs.cend() && rit != rhs.cend()) {
            return true;
        }
        return false;
    }
    bool operator<( const AMCore::AMStringStrippedStartReverse& lhs, const AMCore::AMStringStrippedStartReverse& rhs )
    {
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator lit = lhs.crbegin();
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator rit = rhs.crbegin();
        while (lit != lhs.crend() && rit != rhs.crend()) {
            std::string::const_iterator clit = (lit + 1).base();
            std::string::const_iterator crit = (rit + 1).base();
            unsigned int lc = AMStringToLowercaseStripDiaGet(clit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(crit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        return false;
    }
    bool operator<( const std::string& lhs, const AMCore::AMStringStrippedStartReverse& rhs )
    {
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator lit = lhs.crbegin();
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator rit = rhs.crbegin();
        while (lit != lhs.crend() && rit != rhs.crend()) {
            std::string::const_iterator clit = (lit + 1).base();
            std::string::const_iterator crit = (rit + 1).base();
            unsigned int lc = AMStringToLowercaseStripDiaGet(clit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(crit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        return false;
    }
    bool operator<( const AMCore::AMStringStrippedStartReverse& lhs, const std::string& rhs )
    {
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator lit = lhs.crbegin();
        AMCore::AMStringStrippedStartReverse::const_reverse_iterator rit = rhs.crbegin();
        while (lit != lhs.crend() && rit != rhs.crend()) {
            std::string::const_iterator clit = (lit + 1).base();
            std::string::const_iterator crit = (rit + 1).base();
            unsigned int lc = AMStringToLowercaseStripDiaGet(clit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(crit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        if (lit == lhs.crend() && rit != rhs.crend()) {
            return true;
        }
        return false;
    }*/
    /*
    int AMStringStripDiaCompare(const std::string lhs, const std::string rhs)
    {
        AMCore::AMStringStripped::const_iterator lit = lhs.cbegin();
        AMCore::AMStringStripped::const_iterator rit = rhs.cbegin();
        while (lit != lhs.cend() && rit != rhs.cend()) {
            unsigned int lc = AMStringToLowercaseStripDiaGet(lit, lhs);
            unsigned int rc = AMStringToLowercaseStripDiaGet(rit, rhs);
            if (lc < rc) {
                return true;
            }
            if (lc > rc) {
                return false;
            }
        };
        //if (lit == lhs.cend() && rit != rhs.cend()) {
        //    return true;
        //}
        return false;
    }*/

    std::string AMImplode(std::string delimiter, const std::map<int, std::string>& m)
    {
        std::ostringstream ost;
        bool nofirst = false;
        for(auto it: m) {
            if (nofirst) {
                ost<<delimiter;
            } else {
                nofirst = true;
            }
            ost<<it.second;
        }
        return ost.str();
    }

    bool AMStringContainsOnlyCharacters(std::string characters, std::string& str)
    {
        auto it = str.cbegin();
        while(it != str.cend()) {
            auto itc = characters.cbegin();
            bool is = false;
            while(itc != characters.cend()) {
                if (*itc == '%') {
                    itc++;
                    switch (*itc) {
                        case 'a': {
                            if (*it >= 'a' && *it <= 'z') {
                                is = true;
                                it++;
                            }
                            break;
                        }
                        case 'A': {
                            if (*it >= 'A' && *it <= 'Z') {
                                is = true;
                                it++;
                            }
                            break;
                        }
                        case '0': {
                            if (*it >= '0' && *it <= '9') {
                                is = true;
                                it++;
                            }
                            break;
                        }
                        case 's': {
                            if (*it == ' ' || *it == '\t' || *it == '\n' || *it == '\r') {
                                is = true;
                                it++;
                            }
                            break;
                        }
                        case 'l': {
                            auto itt = it;
                            unsigned int c = AMCore::AMStringToLowercaseStripDiaGet(itt, str);
                            if (c >= 'a' && c <= 'z') {
                                is = true;
                                it = itt;
                            }
                            break;
                        }
                        default: {
                            if (*it == *itc) {
                                is = true;
                                it++;
                            }
                        }
                    }
                } else {
                    if (*it == *itc) {
                        is = true;
                        it++;
                    }
                }
                if (is) {
                    break;
                }
                itc++;
            }
            if (!is) {
                return false;
            }
        }
        return true;
    }

    std::string AMEscape(const std::string &s, const std::string escapables, char escape)
    {
        std::string::const_iterator it = s.cbegin();
        while(it != s.cend()) {
            if (escapables.find(*it) != std::string::npos) {
                std::string buf;
                buf = s;
                int sz = it - s.cbegin();
                int escs = 0;
                std::string::iterator itd = buf.begin() + sz;
                //*itd = escape;
                while(it != s.cend()) {
                    if (sz >= buf.size()) {
                        buf.resize(s.size() + escs);
                        itd = buf.begin() + sz ;
                    }
                    if (escapables.find(*it) != std::string::npos) {
                        sz++;
                        escs++;
                        if (sz >= buf.size()) {
                            buf.resize(s.size() + escs);
                            itd = buf.begin() + sz -1;
                        }
                        *itd = escape;
                        itd++;
                    }
                    *itd = *it;
                    it++;
                    itd++;
                    sz++;
                }
                return buf;
            }
            it++;
        }
        return s;
    }

    std::string AMUnEscape(const std::string &s, char escape)
    {
        std::string::const_iterator it = s.cbegin();
        char lastc = '\0';
        while(it != s.cend()) {
            if (*it == escape && lastc == escape) {
                std::string buf = s;
                int sz = it - s.cbegin();
                int escs = 0;
                std::string::iterator itd = buf.begin() + sz;
                while(it != s.cend()) {
                    if (*it == escape && lastc == escape) {
                        it++;
                        escs++;
                        lastc = '\0';
                        continue;
                    }
                    *itd = *it;
                    lastc = *it;
                    it++;
                    itd++;
                    //sz++;
                }
                buf.resize(buf.size() - escs);
                return buf;
            }
            lastc = *it;
            it++;
        }
        return s;
    }

    template unsigned int AMStringToLowercaseStripDiaGet(std::string::const_iterator &it, const std::string &s);
    template unsigned int AMStringToLowercaseStripDiaGet(std::string::const_reverse_iterator &it, const std::string &s);
    template unsigned int AMStringToLowercaseStripDiaUnget(std::string::const_iterator &it, const std::string &s);
    template unsigned int AMStringToLowercaseStripDiaUnget(std::string::const_reverse_iterator &it, const std::string &s);

    AMStringStripped::AMStringStripped(const AMStringCZ& s): std::string((std::string&)s) {AMStringToLowercaseStripDia(*this);};
}