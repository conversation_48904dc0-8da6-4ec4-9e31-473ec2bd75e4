//
// Created by <PERSON><PERSON><PERSON> on 19.1.23.
//
#include "../AMDataType.h"
#include <string>
#include <ctime>
#include <map>
#include <vector>
#include "../AMNullable.h"
#include "../AMNameBase.h"
#include "../AMAddress.h"
#include "../AMAuthorTag.h"
#include "../AMDatetimeus.h"

namespace AMCore {


    template<typename TEnumType, typename TStringType>
    int _AMDataTypeSizeTable[(int) AMDataType_void] = {
        sizeof( TStringType ), //AMDataType_string,
        sizeof( AMNameBase<TEnumType, TStringType> ), //AMDataType_name,
        sizeof( AMAddress<TStringType> ), //AMDataType_address,
        sizeof( AMAuthorTag<TStringType> ), //AMDataType_author_tag,
        sizeof( TEnumType ), //AMDataType_big_enum,
        sizeof( TEnumType ), //AMDataType_name_enum,
        sizeof( int ), //AMDataType_integer,
        sizeof( long long), //AMDataType_long,
        sizeof( unsigned int ), //AMDataType_natural,
        sizeof( unsigned long long), //AMDataType_natural_long,
        sizeof( float ), //AMDataType_float,
        sizeof( double ), //AMDataType_double,
        sizeof( TEnumType ), //AMDataType_enum,
        sizeof( bool ), //AMDataType_bool,
        sizeof( std::tm ), //AMDataType_datetime,
        sizeof( AMDatetimeus ), //AMDataType_datetimeus,
        sizeof( std::tm ), //AMDataType_date,
        sizeof( std::tm ), //AMDataType_time,
        sizeof( AMNullable<TStringType> ), //AMDataType_string_nullable,
        sizeof( AMNullable<AMNameBase<TEnumType, TStringType> > ), //AMDataType_name_nullable,
        sizeof( AMNullable<AMAddress<TStringType> > ), //AMDataType_address_nullable,
        sizeof( AMNullable<AMAuthorTag<TStringType> > ), //AMDataType_author_tag_nullable,
        sizeof( AMNullable<TEnumType> ), //AMDataType_big_enum_nullable,
        sizeof( AMNullable<TEnumType> ), //AMDataType_name_enum_nullable,
        sizeof( AMNullable<int> ), //AMDataType_integer_nullable,
        sizeof( AMNullable<long long> ), //AMDataType_long_nullable,
        sizeof( AMNullable<unsigned int> ), //AMDataType_natural_nullable,
        sizeof( AMNullable<unsigned long long> ), //AMDataType_natural_long_nullable,
        sizeof( AMNullable<float> ), //AMDataType_float_nullable,
        sizeof( AMNullable<double> ), //AMDataType_double_nullable,
        sizeof( AMNullable<TEnumType> ), //AMDataType_enum_nullable,
        sizeof( AMNullable<bool> ), //AMDataType_bool_nullable,
        sizeof( AMNullable<std::tm> ), //AMDataType_datetime_nullable,
        sizeof( AMNullable<AMDatetimeus> ), //AMDataType_datetimeus_nullable,
        sizeof( AMNullable<std::tm> ), //AMDataType_date_nullable,
        sizeof( AMNullable<std::tm> ), //AMDataType_time_nullable,
        sizeof( std::map<TEnumType, int> ),//AMDataType_enum_map,
        sizeof( std::vector<int> )//AMDataType_integer_vector,
        //-1, //AMDataType_void, //must be last item !!!
    };

    //extern int _AMDataTypeSizeTable[(int)AMDataType_void];
    template<typename TEnumType, typename TStringType>
    int AMDataTypeSize(AMDataType type)
    {
        return _AMDataTypeSizeTable<TEnumType, TStringType>[(int)type];
    }
}