cmake_minimum_required(VERSION 3.12)

set(${CMAKE_RUNTIME_LIBRARY_OUTPUT_DIRECTORY} "build")

# set the project name and version
project(AMCore VERSION 201.0)
file(GLOB SOURCES "src/*.cpp")

# specify the C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

include(../3rdparty/DownloadProject/DownloadProject.cmake)
download_project(
        PROJ                googletest
        GIT_REPOSITORY      https://github.com/google/googletest.git
        GIT_TAG             main
        UPDATE_DISCONNECTED 1
)

add_subdirectory(${googletest_SOURCE_DIR} ${googletest_BINARY_DIR} EXCLUDE_FROM_ALL)
message(STATUS "foo googletest_SOURCE_DIR dir: ${googletest_SOURCE_DIR}")
message(STATUS "foo googletest_BINARY_DIR dir: ${googletest_BINARY_DIR}")
message(STATUS "foo CMAKE_CURRENT_BINARY_DIR dir: ${CMAKE_CURRENT_BINARY_DIR}")
include_directories(${googletest_SOURCE_DIR}/googlemock/include)

include_directories(../)
#set(CMAKE_CXX_FLAGS --coverage)
#set(CMAKE_CXX_FLAGS -fexceptions)s
configure_file(src/AMCoreConfig.h.in AMCoreConfig.h)

#include_directories(include ../3rdparty/include)
#include_directories(include)
#link_directories(../3rdparty/lib )
add_library(AMCore SHARED
        src/AMDataType.hpp
        src/AMNullable.cpp
        src/AMNameBase.cpp
        src/AMTextUtils.cpp
        )
set_target_properties(AMCore
    PROPERTIES
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/../../lib
)


add_executable(TEST_AMFNV1a test/Hash/test_AMFNV1a.cpp)
target_link_libraries(TEST_AMFNV1a gtest pthread)
add_custom_target(Tests_3 ALL COMMAND TEST_AMFNV1a)

add_executable(TEST_AMRange test/Range/test_AMRange.cpp)
target_link_libraries(TEST_AMRange gtest pthread)
add_custom_target(Tests_8 ALL COMMAND TEST_AMRange)

add_executable(TEST_AMInteger test/Integer/test_AMInteger.cpp)
target_link_libraries(TEST_AMInteger gtest pthread)
add_custom_target(Tests_9 ALL COMMAND TEST_AMInteger)

add_executable(TEST_AMTypeId test/TypeId/test_AMCETypeId.cpp)
target_link_libraries(TEST_AMTypeId gtest pthread)
add_custom_target(Tests_4 ALL COMMAND TEST_AMTypeId)

add_executable(TEST_AMLString src/AMLString.cpp test/LString/test_AMLString.cpp)
target_link_libraries(TEST_AMLString gtest pthread AMCore)
add_custom_target(Tests_5 ALL COMMAND TEST_AMLString)

add_executable(TEST_AMException src/AMLString.cpp test/Exception/test_AMException.cpp)
target_link_libraries(TEST_AMException gtest pthread AMCore)
add_custom_target(Tests_6 ALL COMMAND TEST_AMException)

add_executable(TEST_AMVariant test/Variant/test_AMVariant.cpp)
target_link_libraries(TEST_AMVariant gtest pthread AMCore)
add_custom_target(Tests_7 ALL COMMAND TEST_AMVariant)

add_executable(TEST_AMTextUtils test/TextUtils/test_AMTextUtils.cpp src/AMTextUtils.cpp src/AMNameBase.cpp)
target_link_libraries(TEST_AMTextUtils gtest pthread)
#target_link_libraries(TEST_AMParsers GTest::gtest GTest::gtest_main pthread)
add_custom_target(Tests_11 ALL COMMAND TEST_AMTextUtils)


add_executable(TEST_AMTwoWayTable test/TwoWayTable/test_AMTwoWayTable.cpp)
target_link_libraries(TEST_AMTwoWayTable gtest pthread)
#target_link_libraries(TEST_AMParsers GTest::gtest GTest::gtest_main pthread)
add_custom_target(Tests_12 ALL COMMAND TEST_AMTwoWayTable)

# first we can indicate the documentation build as an option and set it to ON by default
option(BUILD_DOC "Build documentation" OFF)
# check if Doxygen is installed
find_package(Doxygen)
if (DOXYGEN_FOUND)
    # set input and output files
    set(DOXYGEN_IN  ${CMAKE_CURRENT_SOURCE_DIR}/docs/doxyfile)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/html/doxyfile)

    # request to configure the file
    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
    message("Doxygen build started")

    # note the option ALL which allows to build the docs together with the application
    add_custom_target( doc_doxygen ALL
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/..
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM )
else (DOXYGEN_FOUND)
  message("Doxygen need to be installed to generate the doxygen documentation")
endif (DOXYGEN_FOUND)
