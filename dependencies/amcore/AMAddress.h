//
// Created by <PERSON><PERSON><PERSON> on 3.1.23.
//

#ifndef AMCORE_AMADDRESS_H
#define AMCORE_AMADDRESS_H

#include <string>

namespace AMCore {

    template<typename TStringType>
    class AMAddress {

    public:
        TStringType place;
        TStringType street;
        int         parcelNum;
        TStringType orientationNum;
        TStringType city;
        TStringType zip;
        TStringType country;
        AMAddress()
            : place(),
            street(),
            parcelNum(-1),
            orientationNum(),
            city(),
            zip(),
            country()
        {};
        AMAddress(TStringType _place, TStringType _street, int _parcelNum, TStringType _orientationNum, TStringType _city, TStringType _zip, TStringType _country)
            : place(_place),
              street(_street),
              parcelNum(_parcelNum),
              orientationNum(_orientationNum),
              city(_city),
              zip(_zip),
              country(_country)
        {};

    };
}


#endif //AMCORE_AMADDRESS_H
