//
// Created by <PERSON><PERSON><PERSON> on 19.1.23.
//

#ifndef AMCORE_AMAUTHORTAG_H
#define AMCORE_AMAUTHORTAG_H

#include "AMDatetimeus.h"

namespace AMCore {
    template<typename TStringType>
    struct AMAuthorTag {
        AMDatetimeus timeMin;
        AMDatetimeus timeMax;
        TStringType time;
        TStringType author;
        int     timeState;
        int     authorState;
        AMAuthorTag()
            : timeMin(),
              timeMax(),
              time(),
              author(),
              timeState(0),
              authorState(0)
              {}
    };
}

#endif //AMCORE_AMAUTHORTAG_H
