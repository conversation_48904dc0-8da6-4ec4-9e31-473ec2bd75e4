//
// Created by <PERSON><PERSON><PERSON> on 25.11.22.
//

#ifndef AMCORE_AMUINAME_H
#define AMCORE_AMUINAME_H


#include <string>
#include "AMNullable.h"
#include "AMTwoWayTable.h"

namespace AMCore {

    template<typename TEnumType, typename TStringType>
    class AMNameBase {
    public:
        AMCore::AMNullable<TEnumType> degreeBefore;
        TStringType name;
        TStringType surname;
        AMCore::AMNullable<TEnumType> degreeAfter;
        AMNameBase(AMCore::AMNullable<TEnumType> degreeBefore, TStringType name, TStringType surname, AMCore::AMNullable<TEnumType> degreeAfter);
        AMNameBase();
        virtual ~AMNameBase();
        static void refreshDegreeTables();
        static AMTwoWayTable<TEnumType, TStringType>& degreeBeforeTable();
        static AMTwoWayTable<TEnumType, TStringType>& degreeAfterTable();
        AMCore::AMTwoWayTable<TEnumType, TStringType>& mdegreeBeforeTable() {return *g_degreeBeforeTable;};
        AMCore::AMTwoWayTable<TEnumType, TStringType>& mdegreeAfterTable() {return *g_degreeAfterTable;};
    protected:
        static AMTwoWayTable<TEnumType, TStringType>* g_degreeBeforeTable;
        static AMTwoWayTable<TEnumType, TStringType>* g_degreeAfterTable;
        virtual void _refreshDegreeTables();
        virtual void _discardDegreeTables();
        static int g_instances;
    };

    template<typename TEnumType, typename TStringType>
    AMTwoWayTable<TEnumType, TStringType>* AMNameBase<TEnumType, TStringType>::g_degreeBeforeTable = nullptr;
    template<typename TEnumType, typename TStringType>
    AMTwoWayTable<TEnumType, TStringType>* AMNameBase<TEnumType, TStringType>::g_degreeAfterTable = nullptr;
    template<typename TEnumType, typename TStringType>
    AMTwoWayTable<TEnumType, TStringType> AMUIAddressBaseEmptyTable{};
    template<typename TEnumType, typename TStringType>
    int AMNameBase<TEnumType, TStringType>::g_instances = 0;

    template<typename TEnumType, typename TStringType>
    AMNameBase<TEnumType, TStringType>::AMNameBase()
        :degreeBefore(),
         name(),
         surname(),
         degreeAfter()
    {
        if (g_instances == 0) {
            _refreshDegreeTables();
        }
        g_instances++;
    }

    template<typename TEnumType, typename TStringType>
    AMNameBase<TEnumType, TStringType>::AMNameBase(AMCore::AMNullable<TEnumType> _degreeBefore, TStringType _name, TStringType _surname, AMCore::AMNullable<TEnumType> _degreeAfter)
        :degreeBefore(_degreeBefore),
         name(_name),
         surname(_surname),
         degreeAfter(_degreeAfter)
    {
        if (!g_degreeBeforeTable) {
            _refreshDegreeTables();
        }
        g_instances++;
    }

    template<typename TEnumType, typename TStringType>
    AMNameBase<TEnumType, TStringType>::~AMNameBase()
    {
        g_instances--;
        if (g_instances == 0) {
            _discardDegreeTables();
        }
    }

    template<typename TEnumType, typename TStringType>
    void AMNameBase<TEnumType, TStringType>::refreshDegreeTables()
    {
        AMNameBase<TEnumType, TStringType> temp;
        temp._refreshDegreeTables();
    }

    template<typename TEnumType, typename TStringType>
    AMTwoWayTable<TEnumType, TStringType>& AMNameBase<TEnumType, TStringType>::degreeBeforeTable()
    {
        if (!g_degreeBeforeTable) {
            AMNameBase<TEnumType, TStringType> temp;
            temp._refreshDegreeTables();
            if (!g_degreeBeforeTable) {
                return AMUIAddressBaseEmptyTable<TEnumType, TStringType>;
            }
        }
        return *g_degreeBeforeTable;
    }

    template<typename TEnumType, typename TStringType>
    AMTwoWayTable<TEnumType, TStringType>& AMNameBase<TEnumType, TStringType>::degreeAfterTable()
    {
        if (!g_degreeAfterTable) {
            AMNameBase<TEnumType, TStringType> temp;
            temp._refreshDegreeTables();
            if (!g_degreeAfterTable) {
                return AMUIAddressBaseEmptyTable<TEnumType, TStringType>;
            }
        }
        return *g_degreeAfterTable;
    }

    template<typename TEnumType, typename TStringType>
    void AMNameBase<TEnumType, TStringType>::_refreshDegreeTables() {

    }

    template<typename TEnumType, typename TStringType>
    void AMNameBase<TEnumType, TStringType>::_discardDegreeTables() {

    }
}



#endif //AMCORE_AMUINAME_H
