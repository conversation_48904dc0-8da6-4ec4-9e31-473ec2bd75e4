//
// Created by <PERSON><PERSON><PERSON> on 27.11.22.
//

#ifndef AMCORE_AMDATATYPE_H
#define AMCORE_AMDATATYPE_H

namespace AMCore {

    enum AMDataType {
        AMDataType_string,
        AMDataType_name,
        AMDataType_address,
        AMDataType_author_tag,
        AMDataType_big_enum,
        AMDataType_name_enum,
        AMDataType_integer,
        AMDataType_long,
        AMDataType_natural,
        AMDataType_natural_long,
        AMDataType_float,
        AMDataType_double,
        AMDataType_enum,
        AMDataType_bool,
        AMDataType_datetime,
        AMDataType_datetimeus,
        AMDataType_date,
        AMDataType_time,
        AMDataType_string_nullable,
        AMDataType_name_nullable,
        AMDataType_address_nullable,
        AMDataType_author_tag_nullable,
        AMDataType_big_enum_nullable,
        AMDataType_name_enum_nullable,
        AMDataType_integer_nullable,
        AMDataType_long_nullable,
        AMDataType_natural_nullable,
        AMDataType_natural_long_nullable,
        AMDataType_float_nullable,
        AMDataType_double_nullable,
        AMDataType_enum_nullable,
        AMDataType_bool_nullable,
        AMDataType_datetime_nullable,
        AMDataType_datetimeus_nullable,
        AMDataType_date_nullable,
        AMDataType_time_nullable,
        AMDataType_enum_map,
        AMDataType_integer_vector,
        AMDataType_void, //must be last item !!!

    };

    inline bool AMDataTypeIsNullable(AMDataType type)
    {
        return type >= AMDataType_string_nullable && type <= AMDataType_time_nullable;
    }


    template<typename TEnumType, typename TStringType>
    int AMDataTypeSize(AMDataType type);
}

#include "src/AMDataType.hpp"

#endif //AMCORE_AMDATATYPE_H
