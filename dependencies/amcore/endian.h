/**
 * @file: endian.h
 * Standard C++ endianes from C++ < 20
 *
 * <AUTHOR>  &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;
 */
#ifndef AMCORE_ENDIAN_H
#define AMCORE_ENDIAN_H

#include <cstdint>
#include <type_traits>
/**
 *  @ingroup Common
 *  @{
 */

namespace AMCore {

	/**
	 *  @ingroup Common
	 *  @brief Indicates the endianness of all scalar types
	 *  If all scalar types are little-endian, AMCore::endian::native equals AMCore::endian::little.<br/>
	 *  If all scalar types are big-endian, AMCore::endian::native equals AMCore::endian::big.<br/>
	 *  Other type of  endiannes does not suported.<br/>
	 *  This header is replacement for &lt;bit&gt; header in C++ vesion < 20.<br/>
	 *  <br/>
	 *  If endianess is little endian, AMENDIAN_LITTLE is defined.<br/>
	 *  If endianess is big endian, AMENDIAN_BIG is defined.<br/>
	 *  <br/>
	 *  Example:
	 *	@code{.cpp}
	 *  #include <AMCore/endian.h>
	 *  #include <iostream>
	 *
	 *  int main() {
	 *
	 *  if constexpr (AMCore::endian::native == AMCore::endian::big) {
	 *       std::cout << "big-endian" << '\n';
	 *  }
	 *  else if constexpr (AMCore::endian::native == AMCore::endian::little) {
	 *       std::cout << "little-endian"  << '\n';
	 *  }
	 *  @endcode
	 */
	enum class endian
	{
		little = 0,
	    big    = 1,


#if  (defined(__BYTE_ORDER__) && defined(__ORDER_LITTLE_ENDIAN__) && __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__) || \
    defined(__LITTLE_ENDIAN__) || \
    defined(_MSC_VER) || defined(__i386__) || defined(__x86_64__)

	    native = 0
		#define AMENDIAN_LITTLE

#elif (defined(__BYTE_ORDER__) && defined(__ORDER_BIG_ENDIAN__) && __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__) || \
      defined(__BIG_ENDIAN__)

	    native = 1
		#define AMENDIAN_BIG

#elif defined(__BYTE_ORDER__) && defined(__ORDER_PDP_ENDIAN__) && __BYTE_ORDER__ == __ORDER_PDP_ENDIAN__

# error The Andromeda library does not support platforms with PDP endianness.

#else

# error The Andromeda library could not determine the endianness of this platform.

#endif

	};

#ifdef DOXYGEN
    /**
     *  @ingroup Common
     *  @def AMENDIAN_LITTLE
     *  @brief If endianess is little endian, AMENDIAN_LITTLE is defined
     */
    #define AMENDIAN_LITTLE
    /**
     *  @ingroup Common
     *  @def AMENDIAN_BIG
     *  @brief If endianess is big endian, AMENDIAN_BIG is defined
     */
    #define AMENDIAN_BIG
    /**
     *  @ingroup Common
     *  @def AMBSwap16
     *  @brief Swap bytes. 16-bit variant.
     */
    #define AMBSwap16
    /**
     *  @ingroup Common
     *  @def AMBSwap32
     *  @brief Swap bytes. 32-bit variant.
     */
    #define AMBSwap32
	/**
     *  @ingroup Common
     *  @def AMBSwap64
     *  @brief Swap bytes. 64-bit variant.
     */
    #define AMBSwap64
	/**
     *  @ingroup Common
	 *  @brief Byte swap for native endian to little endian.
	 */
	#define AMToLittleEndian
	/**
	 *  @ingroup Common
	 *  @brief Byte swap for native endian to big endian.
	 */
	#define AMToBigEndian
#endif


#if defined(_MSC_VER)
	#define AMBSwap16 _byteswap_ushort
	#define AMBSwap32 _byteswap_ulong
	#define AMBSwap64 _byteswap_uint64
#elif defined(__GNUC__)
	#define AMBSwap16(x) (((unsigned short)x << 8) | ((unsigned short)x >> 8))
	#define AMBSwap32 __builtin_bswap32
	#define AMBSwap64 __builtin_bswap64
#elif defined(__clang__)
	#define AMBSwap16(x) (((unsigned short)x << 8) | ((unsigned short)x >> 8))
	#define AMBSwap32 __builtin_bswap32
	#define AMBSwap64 __builtin_bswap64
	#error FUJJJJJJJJ
#elif defined(__llvm__)
	#define AMBSwap16(x) (((unsigned short)x << 8) | ((unsigned short)x >> 8))
	#define AMBSwap32 __builtin_bswap32
	#define AMBSwap64 __builtin_bswap64
	#error FUJJJJJJJJMMMMMMMMMMMMMMMMMMMMMMMM
#else
#error This compiler is not supported yet.
#endif

/**
 *  @ingroup Common
 *  @brief Byte swaps for 16,32,64 bit basic integer types.
 */
template<typename T>
inline T AMBSwap(T input) {
	static_assert(
			std::is_same<T, unsigned short>::value
		||  std::is_same<T, signed short>::value
		||  std::is_same<T, uint32_t>::value
		||  std::is_same<T, int32_t>::value
		||  std::is_same<T, uint64_t>::value
		||  std::is_same<T, int64_t>::value
		, "AMBSwap: This type is not defined yet.");
	return input;
};

template<> inline unsigned short AMBSwap(unsigned short input) {return AMBSwap16(input);}
template<> inline signed short AMBSwap(signed short input) {return AMBSwap16(input);}
template<> inline uint32_t AMBSwap(uint32_t input) {return AMBSwap32(input);}
template<> inline int32_t AMBSwap(int32_t input) {return AMBSwap32(input);}
template<> inline uint64_t AMBSwap(uint64_t input) {return AMBSwap64(input);}
template<> inline int64_t AMBSwap(int64_t input) {return AMBSwap64(input);}

#ifndef DOXYGEN
	#if defined(AMENDIAN_LITTLE)
		#define AMToLittleEndian(input) (input)
		#define AMToBigEndian(input) AMBSwap(input)
	#else
		#define AMToLittleEndian(input) AMBSwap(input)
		#define AMToBigEndian(input) (input)
	#endif
#endif

}//namespace

/** @} */

#endif //AMCORE_ENDIAN_H
