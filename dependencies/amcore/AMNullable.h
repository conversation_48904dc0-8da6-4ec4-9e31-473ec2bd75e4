//
// Created by <PERSON><PERSON><PERSON> on 14.10.22.
//

#ifndef AMCORE_AMNULLABLE_H
#define AMCORE_AMNULLABLE_H

#include <cstddef>
#include <stdexcept>

namespace AMCore {

    template<class T>
    class AMNullable {
    public:
        AMNullable(const T &_t);

        AMNullable();

        inline bool isNull() const;

        inline void set();

        inline void set(const T &_t);

        inline T get() const;

        inline AMNullable &operator=(const T &_t);

        inline AMNullable &operator=(std::nullptr_t _p);

        inline operator T() const;

        inline bool operator==(const AMNullable<T> &right) const;

        typedef T valueType;

    protected:
        T m_data;
        bool m_isNull;
    };

    template<class T>
    bool AMNullable<T>::operator==(const AMNullable<T> &right) const
    {
        if (m_isNull == true && right.m_isNull == true) {
            return true;
        }
        if (m_isNull != right.m_isNull) {
            return false;
        }
        return m_data == right.m_data;
    }

    extern const char* AMNullString;

    template <class, template <class> class>
    struct AMIsTemplateInstance : public std::false_type {};

    template <class T, template <class> class U>
    struct AMIsTemplateInstance<U<T>, U> : public std::true_type {};


    template<class T>
    AMNullable<T>::AMNullable()
        : m_data(),
          m_isNull(true) {};

    template<class T>
    AMNullable<T>::AMNullable(const T &_t)
        : m_data(_t),
          m_isNull(false) {};

    template<class T>
    bool AMNullable<T>::isNull() const {
        return m_isNull;
    };

    template<class T>
    void AMNullable<T>::set() {
        m_data = T();
        m_isNull = true;
    };

    template<class T>
    void AMNullable<T>::set(const T &_t) {
        m_data = _t;
        m_isNull = false;
    };

    template<class T>
    T AMNullable<T>::get() const {
        if (m_isNull) {
            throw std::runtime_error("Access to null nullable variable.");
        }
        return m_data;
    };

    template<class T>
    AMNullable<T> &AMNullable<T>::operator=(const T &_t) {
        set(_t);
        return *this;
    }

    template<class T>
    AMNullable<T> &AMNullable<T>::operator=(std::nullptr_t _p) {
        set(nullptr);
        return *this;
    }

    template<class T>
    AMNullable<T>::operator T() const {
        return get();
    }

}

#endif //AMCORE_AMNULLABLE_H
