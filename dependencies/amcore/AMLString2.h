/*!
*	@file AMLString.h
*   This file is interface for definition static stringtable with import functions from <b>.mo</b> files.
*   For more informations, see @ref Strings.
*   <AUTHOR>  &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;
*/
#ifndef AMLSTRING_H
#define AMLSTRING_H

#include <utility>
#include "AMCEFNV1a.h"

/**
 *  @ingroup Strings
 *  @{
 */

namespace AMCore {

	class _T_AM_StringItemBase;

    class _T_AM_StringList
	{
		_T_AM_StringList*        	_M_next;
        static _T_AM_StringList*	_M_root;
        const uint64_t				_M_name_hash;
        _T_AM_StringList*			_M_next_chunk;
/*
           virtual bool SaveContent(void* vpnode)=0;
           virtual int  LoadContent(void* vpdoc, void* vpnode)=0;*/
	public:
		_T_AM_StringItemBase*     _M_first_item;
    //    int                       _M_length;
    public:
    	_T_AM_StringList(const uint64_t tableHash);
        ~_T_AM_StringList();
        static _T_AM_StringList* GetStringTable(const uint64_t nameHash);
		static _T_AM_StringList* GetStringTable(const char* name);
		_T_AM_StringList& registerItem(_T_AM_StringItemBase* item);
        void Add(_T_AM_StringList& second);
			/*
           bool Save(const char* name);
           int  Load(const char* name);
		   */
    };

	template<uint64_t tableHash>
	class _T_AM_StringListHolder
	{
		static _T_AM_StringList _M_list;
	public:
		_T_AM_StringList& list() {return _M_list;}
		static _T_AM_StringList& registerItem(_T_AM_StringItemBase* item)
		{
			return _M_list.registerItem(item);
		}
	};
	template<uint64_t tableHash>
	_T_AM_StringList _T_AM_StringListHolder<tableHash>::_M_list(tableHash);

	struct _T_AM_StringItemBase
  	{
  		const char*                 _M_str;
		int							_M_length;
  		const char*                 _M_original_str;
  		_T_AM_StringItemBase*		_M_next;
	public:
		constexpr static int ceLength(const char* src)
		{
			int len = 0;
			while(src[len] != '\0') len++;
			return len + 1;
		}
  		constexpr _T_AM_StringItemBase(const char* str)
  			:_M_str(str)
			,_M_length(_T_AM_StringItemBase::ceLength(str))
		    ,_M_original_str(str)
		    ,_M_next(nullptr) {}
		constexpr _T_AM_StringItemBase()
  			:_M_str(nullptr)
			,_M_length(0)
		    ,_M_original_str(nullptr)
		    ,_M_next(nullptr) {}

		const char* getTranslatedString() const {return _M_str;}
		const char* getOriginalString() const {return _M_original_str;}
		int getTranslatedLength() const {return _M_length;}
  	};

	template<uint64_t tableHash, const char... chars>
	struct _T_AM_StringItemStatic: public _T_AM_StringItemBase
  	{
		static constexpr const char _M_original_str_data[sizeof...(chars)] = {chars...};
	public:
  		constexpr _T_AM_StringItemStatic()
  			:_T_AM_StringItemBase(_T_AM_StringItemStatic::_M_original_str_data) {}
  	};

	class _T_AM_String;

	class AMLString
	{
		const _T_AM_StringItemBase* _M_string_item;
		constexpr AMLString(_T_AM_StringItemBase* stringItem)
			:_M_string_item(stringItem) {}
		friend class _T_AM_String;
	public:
		const char* c_str() const
		{
			return _M_string_item->getTranslatedString();
		}
		const char* c_orig_str() const
		{
			return _M_string_item->getOriginalString();
		}
		int length() const
		{
			return _M_string_item->getTranslatedLength();
		}
	};

	template<uint64_t tableHash, const char... chars>
	class _T_AM_StringItemWrapper
	{
		static _T_AM_StringItemStatic<tableHash, chars...> _M_static_item;
		static _T_AM_StringList& _M_table;
	public:
		static constexpr _T_AM_StringItemBase* getTranslationObject()
		{
		    return static_cast<_T_AM_StringItemBase*>(&_M_static_item);
            _T_AM_StringList& tab = _M_table;
		}
	};
	template<uint64_t tableHash, const char... chars>
	_T_AM_StringItemStatic<tableHash, chars...> _T_AM_StringItemWrapper<tableHash, chars...>::_M_static_item =
		_T_AM_StringItemStatic<tableHash, chars...>();
	template<uint64_t tableHash, const char... chars>
	_T_AM_StringList& _T_AM_StringItemWrapper<tableHash, chars...>::_M_table =
		_T_AM_StringListHolder<tableHash>::registerItem(static_cast<_T_AM_StringItemBase*>(&_M_static_item));

	class _T_AM_String
	{
		template<uint64_t tableHash, typename T, std::size_t... ints>
		constexpr static _T_AM_StringItemBase* getTextImpl(T data, std::index_sequence<ints...> int_seq)
		{
			constexpr const char* src = data();
			return _T_AM_StringItemWrapper<tableHash, (src[ints])...>::getTranslationObject();
		}
	public:
		template<typename T>
		constexpr static AMLString getTextImpl(T data)
		{
			_T_AM_StringItemBase* stringItem = _T_AM_String::getTextImpl<
				AMCEFNV1aAlgorithm::fnv1a64("default")>
				(data, std::make_index_sequence<_T_AM_StringItemBase::ceLength(data())>{});
			return AMLString(stringItem);
		}
	};


     /**
	 *  @def gettext
     *  @brief Adds string to localization stringtable
     *  @param string string
     *  @returns translated string
     */


}//namespace

//#define gettext(string) AMCore::_T_AM_String::getTextImpl([]()constexpr{ return string;})
#define _(string) AMCore::_T_AM_String::getTextImpl([]()constexpr{ return string;})

/** @} */

#endif /* AMLSTRING_H */
