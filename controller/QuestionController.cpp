//
// Created by <PERSON><PERSON><PERSON> on 28.8.22.
//

#include "QuestionController.h"

#include "../model/UserDegree.h"
#include "../model/UserDegrees.h"
#include "SAWSettings.h"
#include "view/Resources.h"

QuestionController::QuestionController(std::string defaultQuery)
    : AgendaWithTableData("Testové otázky", defaultQuery, App::instance().openedCustomer()),
    m_main_icon(-1)
{
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, UserDegrees, UserDegree>(TAB_ALL ,defaultQuery, this, m_strVoidParam));
}

void QuestionController::onResume()
{
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onResume();

    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(Bookmark_Add_png),
        &m_main_icon,
        nullptr,
        nullptr
                                                        );
    AMAssert(rv);
}

void QuestionController::onPause()
{
    AMEliteUI::AMEliteImageManager::unloadTexture(m_main_icon);
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onPause();
    m_main_icon = -1;
}

GLuint QuestionController::mainIcon()
{
    return m_main_icon;
}
