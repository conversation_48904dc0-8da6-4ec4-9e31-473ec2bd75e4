//
// Created by <PERSON><PERSON><PERSON> on 12.4.23.
//

#include "UserController.h"

#include "../model/User.h"
#include "../model/Users.h"
#include "SAWSettings.h"
#include "view/Resources.h"

UserController::UserController(std::string defaultQuery)
    : AgendaWithTableData("Uživatelé", defaultQuery, App::instance().openedCustomer()),
      m_main_icon(-1)
{
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User>(TAB_ALL ,defaultQuery, this, m_strVoidParam));
}

void UserController::onResume()
{
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onResume();

    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(User_png),
        &m_main_icon,
        nullptr,
        nullptr
                                                                   );
    AMAssert(rv);
}

void UserController::onPause()
{
    AMEliteUI::AMEliteImageManager::unloadTexture(m_main_icon);
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onPause();
    m_main_icon = -1;
}

GLuint UserController::mainIcon()
{
    return m_main_icon;
}
