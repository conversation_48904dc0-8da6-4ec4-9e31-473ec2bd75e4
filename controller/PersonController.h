//
// Created by <PERSON><PERSON><PERSON> on 12.4.23.
//

#ifndef SAW_PERSONCONTROLLER_H
#define SAW_PERSONCONTROLLER_H

#include "model/AgendaWithTableData.h"

class AMUIFormView;
class TableView;
class UserDegree;
class UserDegrees;
class TableCache;
class AMSvrFilter;

const std::string defaultPersonQuery =
    "id=&employer_=&centre=&degree_before=&name=&surname=&degree_after=&profession_=&state=1,2,3,4,5,6,7,8,9,10,11,12,13&is_leader=&e_relationship=&sex=&modified=";
const std::string defaultPersonDataQuery =
    "id=@@@SECONDARY_ID@@@&full_name=&citizenship=&date_birth=&permanent_address=&onboard_date=&leave_date=&driving_licences=&modified=";
const std::string defaultPersonUserQuery =
    "id=&name=&roles=&valid=true&modified=";
const std::string defaultPersonalRiskQuery =
    "id=&.person=@@@SECONDARY_ID@@@&occup_hazard=&r_f_working_cond=&legal_req=&appear=&disappear=&modified=";
const std::string defaultWMSSpecificsQuery =
    "id=@@@SECONDARY_ID@@@&full_name=&occupational_disease=&approved_disability=&new_valuation=&hearing_loss=&mc_req_employer=&mc_req_employee=&mc_req_following=&modified=";
const std::string defaultHealthyRestrictionsPersonQuery =
    "id=&.person__=@@@SECONDARY_ID@@@&type=&name=&appear=&disappear=&modified=";
class PersonController: public AgendaWithTableData {
    static const int TAB_DATA = 1;
    static const int TAB_USER = 2;
    static const int TAB_RISKS = 3;
    static const int TAB_WMSSPECIFIC2 = 4;
    static const int TAB_RESTRICTIONS = 5;
public:
    PersonController(std::string defaultQuery = defaultPersonQuery);

    void onResume() override;
    void onPause() override;
    GLuint mainIcon() override;
    SAWString selectedSavedString();
    void onReturnRecord() override;


    void onUserSectionInsert();



    std::string nameForSection(int n) override;

    AMUI::AMUIIView *viewForSection(int n) override;

    AMUI::AMUIIView *detailView() override;

    void onNewRecord() override;

    bool canNewRecord() override;

    bool canCopyRecord() override;

    bool canDeleteRecord() override;


    //void onSaveRecord() override;

    //AMUITable::AMUITableForm<SAWEnum, SAWString> *createRecord(int section) override;

protected:
    GLuint m_main_icon;
    SAWString m_returnString;
};


#endif //SAW_PERSONCONTROLLER_H
