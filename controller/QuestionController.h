//
// Created by <PERSON><PERSON><PERSON> on 28.8.22.
//

#ifndef SAW_ALL_QUESTIONCONTROLLER_H
#define SAW_ALL_QUESTIONCONTROLLER_H

#include "model/AgendaWithTableData.h"

class AMUIFormView;
class TableView;
class UserDegree;
class UserDegrees;
class TableCache;
class AMSvrFilter;

const std::string defaultUserDegreesQuery = "id=&degree=&after_name=&note=&modified=";

class QuestionController: public AgendaWithTableData
{
public:
    QuestionController(std::string defaultQuery = defaultUserDegreesQuery);
    void onResume() override;
    void onPause() override;
    GLuint mainIcon() override;
protected:
    GLuint m_main_icon;
};


#endif //SAW_ALL_QUESTIONCONTROLLER_H
