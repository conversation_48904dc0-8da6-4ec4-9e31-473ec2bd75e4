//
// Created for categories table adaptation
//

#ifndef SAW_ALL_CATEGORYCONTROLLER_H
#define SAW_ALL_CATEGORYCONTROLLER_H

#include "model/AgendaWithTableData.h"

class AMUIFormView;
class TableView;
class Category;
class Categories;
class TableCache;
class AMSvrFilter;

const std::string defaultCategoriesQuery = "id=&name=&weight=&paid=&supercategory=&note=&modified=";

class CategoryController: public AgendaWithTableData
{
public:
    CategoryController(std::string defaultQuery = defaultCategoriesQuery);
    void onResume() override;
    void onPause() override;
    GLuint mainIcon() override;
protected:
    GLuint m_main_icon;
};

#endif //SAW_ALL_CATEGORYCONTROLLER_H
