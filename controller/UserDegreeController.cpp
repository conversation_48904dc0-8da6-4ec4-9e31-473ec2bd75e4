//
// Created by <PERSON><PERSON><PERSON> on 28.8.22.
//

#include "UserDegreeController.h"

#include "../model/UserDegree.h"
#include "../model/UserDegrees.h"
#include "SAWSettings.h"
#include "view/Resources.h"

UserDegreeController::UserDegreeController(std::string defaultQuery)
    : AgendaWithTableData("Tituly uživatelů", defaultQuery, App::instance().openedCustomer()),
    m_main_icon(-1)
{
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, UserDegrees, UserDegree>(TAB_ALL ,defaultQuery, this, m_strVoidParam));
}

void UserDegreeController::onResume()
{
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onResume();

    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(Bookmark_Add_png),
        &m_main_icon,
        nullptr,
        nullptr
                                                        );
    AMAssert(rv);
}

void UserDegreeController::onPause()
{
    AMEliteUI::AMEliteImageManager::unloadTexture(m_main_icon);
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onPause();
    m_main_icon = -1;
}

GLuint UserDegreeController::mainIcon()
{
    return m_main_icon;
}
