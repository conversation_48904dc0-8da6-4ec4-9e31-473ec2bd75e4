//
// Created for categories table adaptation
//

#include "CategoryController.h"

#include "../model/Category.h"
#include "../model/Categories.h"
#include "SAWSettings.h"
#include "../view/Resources.h"

CategoryController::CategoryController(std::string defaultQuery)
    : AgendaWithTableData("Kategorie", defaultQuery, App::instance().openedCustomer()),
    m_main_icon(-1)
{
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, Categories, Category>(TAB_ALL ,defaultQuery, this, m_strVoidParam));
}

void CategoryController::onResume()
{
    AgendaWithTableData::onResume();

    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(Boxes_png),
        &m_main_icon,
        nullptr,
        nullptr
                                                                   );
    AMAssert(rv);
}

void CategoryController::onPause()
{
    AMEliteUI::AMEliteImageManager::unloadTexture(m_main_icon);
    AgendaWithTableData::onPause();
    m_main_icon = -1;
}

GLuint CategoryController::mainIcon()
{
    return m_main_icon;
}
