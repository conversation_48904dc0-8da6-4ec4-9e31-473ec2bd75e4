//
// Created by <PERSON><PERSON><PERSON> on 12.4.23.
//

#include "PersonController.h"

#include "model/Person.h"
#include "model/Users.h"
#include "model/User.h"
#include "SAWSettings.h"
#include "view/Resources.h"

PersonController::PersonController(std::string defaultQuery)
    : AgendaWithTableData("Osoby", defaultQuery, App::instance().openedCustomer()),
      m_main_icon(-1),
      m_returnString()
{
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person>(TAB_ALL, defaultQuery, this, m_strVoidParam));
    this->m_sections.push_back(new AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User>(TAB_USER ,defaultPersonUserQuery, this, m_strVoidParam));
}

void PersonController::onResume()
{
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onResume();

    bool rv = AMEliteUI::AMEliteImageManager::loadTextureFromMemory(
        MEMIMG(User_Group_Filled_png),
        &m_main_icon,
        nullptr,
        nullptr
                                                                   );
    AMAssert(rv);

}

void PersonController::onPause()
{
    AMEliteUI::AMEliteImageManager::unloadTexture(m_main_icon);
    AMEliteAgendaWithTableData<SAWEnum, SAWString>::onPause();
    m_main_icon = -1;
}

GLuint PersonController::mainIcon()
{
    return m_main_icon;
}

SAWString PersonController::selectedSavedString()
{
    return m_returnString;
}

void PersonController::onReturnRecord()
{
    if ( m_mainSectionIndex >= 0) {
        AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *sec = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *>(this->m_sections[TAB_ALL]);
        Person *ps = static_cast<Person *>(sec->recordProvider());
        if (!ps) {
            return;
        }
        m_returnString =  ps->name.buffer();
    } else {
        m_returnString = "";
    }
    AgendaWithTableData::onReturnRecord();
}

std::string PersonController::nameForSection(int n)
{
    if (n == TAB_USER) {
        return "Uživatel";
    }
    return AMEliteAgendaWithTableData::nameForSection(n);
}

AMUI::AMUIIView *PersonController::viewForSection(int n)
{
    int ls = m_lastSection;

    AMUI::AMUIIView *rv = AMEliteAgendaWithTableData::viewForSection(n);

    if (m_lastSection == TAB_USER && m_lastSection != ls) {
        AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *sec = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *>(this->m_sections[TAB_ALL]);
        Person *ps = static_cast<Person *>(sec->recordProvider());
        if (!ps) {
            return rv;
        }
        int id = ps->loadingId();
        if (id != AMUI::DEFAULT_INT_ID) {
            AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *secd = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *>(this->m_sections[TAB_USER]);
            AMUITable::AMEliteAgendaWithTableData<SAWEnum, SAWString>::orderRecord(id, secd->cache());
            if(secd->cache()) {
                secd->cache()->setHighlightedRow(id);
            }
        }
    }

    return rv;
}

AMUI::AMUIIView *PersonController::detailView()
{
    /*
    if (m_lastSection == TAB_USER) {
        AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *sec = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *>(this->m_sections[TAB_USER]);
        User *pd = static_cast<User *>(sec->recordProvider());
        if (pd && pd->failWithNoRecord()) {
            return &m_failbackViewUser;
        }
    }*/
    return AMEliteAgendaWithTableData::detailView();
}


void PersonController::onUserSectionInsert()
{
    AMEliteAgendaWithTableData::onNewRecord();
    AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *sec = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, People, Person> *>(this->m_sections[TAB_ALL]);
    Person *ps = static_cast<Person *>(sec->recordProvider());
    if (!ps) {
        return;
    }
    int id = ps->loadingId();
    AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *secd = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *>(this->m_sections[TAB_USER]);
    User *pd = static_cast<User *>(secd->recordProvider());
    if (!pd) {
        return;
    }
    pd->id.setValue(id);
}



void PersonController::onNewRecord()
{
   if (m_lastSection == TAB_USER) {
        onUserSectionInsert();
        return;
    }
    AMEliteAgendaWithTableData::onNewRecord();
}

bool PersonController::canNewRecord()
{
    if (m_lastSection == TAB_USER) {
        AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *sec = static_cast<AMUITable::AMUITableSection<SAWEnum, SAWString, Users, User> *>(this->m_sections[TAB_USER]);
        if (!AMAcl(sec->tableId(), AMUITableAclOperation::acl_insert, m_strVoidParam)) {
            return false;
        }
        User *pd = static_cast<User *>(sec->recordProvider());
        if (!pd) {
            return false;
        }
        return pd->failWithNoRecord();
    }
    return AMEliteAgendaWithTableData::canNewRecord();
}

bool PersonController::canCopyRecord()
{
    if (m_lastSection == TAB_USER) {
        return false;
    }
    return AMEliteAgendaWithTableData::canCopyRecord();
}

bool PersonController::canDeleteRecord()
{
    if (m_lastSection == TAB_ALL) {
        return false;
    }
    return AMEliteAgendaWithTableData::canDeleteRecord();
}



