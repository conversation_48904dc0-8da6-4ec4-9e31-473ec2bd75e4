//
// Created by <PERSON><PERSON><PERSON> on 12.4.23.
//

#ifndef SAW_USERCONTROLLER_H
#define SAW_USERCONTROLLER_H

#include "model/AgendaWithTableData.h"

class AMUIFormView;
class TableView;
class UserDegree;
class UserDegrees;
class TableCache;
class AMSvrFilter;

const std::string defaultUserQuery = "id=&name=&roles=&valid=true&valid_person=&modified=";

class UserController: public AgendaWithTableData {
public:
    UserController(std::string defaultQuery = defaultUserQuery);
    void onResume() override;
    void onPause() override;
    GLuint mainIcon() override;
protected:
    GLuint m_main_icon;
};


#endif //SAW_USERCONTROLLER_H
