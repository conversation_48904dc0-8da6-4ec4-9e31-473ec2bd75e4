cmake_minimum_required(VERSION 3.12)
project(saw)
set(CMAKE_BUILD_FILES_DIRECTORY ${PROJECT_SOURCE_DIR}gen)
if(APP_NO_MULTITHREAD MATCHES yes)
    set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -DAPP_NO_MULTITHREAD")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DAPP_NO_MULTITHREAD")
#    set(SOURCES_SQLITE)
    set(LIB_THREAD)
else()
#    set(SOURCES_SQLITE)
##    set(SOURCES_SQLITE ../dependencies/include/sqlite/sqlite3.c)
    set(LIB_THREAD pthread)
endif()

set(SOURCES
        #main
        main.cpp
        App.h
        App.cpp
        AppLogin.hpp
        SAWSettings.h
        SAWSettings.cpp
        #amcore
        dependencies/amcore/src/AMDataType.hpp
        dependencies/amcore/src/AMNullable.cpp
        dependencies/amcore/src/AMNameBase.cpp
        dependencies/amcore/src/AMTextUtils.cpp
        dependencies/amcore/AMAddress.h
        dependencies/amcore/AMAuthorTag.h
        dependencies/amcore/AMCoreConfig.h
        dependencies/amcore/AMDataType.h
        dependencies/amcore/AMException.h
        dependencies/amcore/AMInteger.h
        dependencies/amcore/AMNameBase.h
        dependencies/amcore/AMNullable.h
        dependencies/amcore/AMRange.h
        dependencies/amcore/AMTextUtils.h
        dependencies/amcore/AMTwoWayTable.h
        #amloglite
        dependencies/amloglite/src/AMLogger.cpp
        dependencies/amloglite/AMLogger.h
        #amui
        dependencies/amui/src/AMFuture.cpp
        dependencies/amui/src/AMUIAgenda.cpp
        dependencies/amui/src/AMUIAgendaView.cpp
        dependencies/amui/src/AMUIApp.cpp
        dependencies/amui/src/AMUIAppView.cpp
        dependencies/amui/src/AMUIConfig.cpp
        dependencies/amui/src/AMUIController.cpp
        dependencies/amui/src/AMUIControllerView.cpp
        dependencies/amui/src/AMUIDirectoryUtils.cpp
        dependencies/amui/src/AMUIFonts.cpp
        dependencies/amui/src/AMUIGarbageManager.cpp
        dependencies/amui/src/AMUIGLWrappers.cpp
        dependencies/amui/src/AMUIIView.cpp
        dependencies/amui/src/AMUIRenderer.cpp
        dependencies/amui/src/AMUISystemWindow.cpp
        dependencies/amui/src/AMUIUrlUtils.cpp
        dependencies/amui/src/AMUIUrlUtilsInt.cpp
        dependencies/amui/src/AMUILoggerController.cpp
        dependencies/amui/src/AMUILoggerView.cpp
        dependencies/amui/AMFuture.h
        dependencies/amui/AMUIAgenda.h
        dependencies/amui/AMUIAgendaView.h
        dependencies/amui/AMUIApp.h
        dependencies/amui/AMUIAppView.h
        dependencies/amui/AMUIConfig.h
        dependencies/amui/AMUIController.h
        dependencies/amui/AMUIControllerView.h
        dependencies/amui/AMUIDirectoryUtils.h
        dependencies/amui/AMUIFonts.h
        dependencies/amui/AMUIGarbageManager.h
        dependencies/amui/AMUIGLWrappers.h
        dependencies/amui/AMUIIView.h
        dependencies/amui/AMUILoggerController.h
        dependencies/amui/AMUILoggerView.h
        dependencies/amui/AMUIRect.h
        dependencies/amui/AMUIRenderer.h
        dependencies/amui/AMUISystemWindow.h
        dependencies/amui/AMUIUrlUtils.h
        #ameltiteui
        dependencies/ameliteui/src/AMEliteAgenda.cpp
        dependencies/ameliteui/src/AMEliteAgendaView.cpp
        dependencies/ameliteui/src/AMEliteApp.cpp
        dependencies/ameliteui/src/AMEliteAppView.cpp
        dependencies/ameliteui/src/AMEliteConfig.cpp
        dependencies/ameliteui/src/AMEliteController.cpp
        dependencies/ameliteui/src/AMEliteControllerView.cpp
        dependencies/ameliteui/src/AMEliteHorizontalTaskBarView.cpp
        dependencies/ameliteui/src/AMEliteImageManager.cpp
        dependencies/ameliteui/src/AMEliteLoggerController.cpp
        dependencies/ameliteui/src/AMEliteLoggerMenuView.cpp
        dependencies/ameliteui/src/AMEliteResources.cpp
        dependencies/ameliteui/src/AMEliteStatus.cpp
        dependencies/ameliteui/src/AMEliteStatusView.cpp
        dependencies/ameliteui/src/AMEliteStbIncluder.cpp
        dependencies/ameliteui/src/AMEliteVerticalTaskBarView.cpp
        dependencies/ameliteui/AMEliteAgenda.h
        dependencies/ameliteui/AMEliteAgendaView.h
        dependencies/ameliteui/AMEliteApp.h
        dependencies/ameliteui/AMEliteAppView.h
        dependencies/ameliteui/AMEliteConfig.h
        dependencies/ameliteui/AMEliteController.h
        dependencies/ameliteui/AMEliteControllerView.h
        dependencies/ameliteui/AMEliteHorizontalTaskBarView.h
        dependencies/ameliteui/AMEliteImageManager.h
        dependencies/ameliteui/AMEliteLoggerController.h
        dependencies/ameliteui/AMEliteLoggerMenuView.h
        dependencies/amui/AMUIRecordOrderer.h
        dependencies/ameliteui/AMEliteResources.h
        dependencies/ameliteui/AMEliteStatus.h
        dependencies/ameliteui/AMEliteStatusView.h
        dependencies/ameliteui/AMEliteVerticalTaskBarView.h
        #amdialogs
        dependencies/amdialogs/src/AMUIDialog.cpp
        dependencies/amdialogs/src/AMUIDialogAddress.hpp
        dependencies/amdialogs/src/AMUIDialogAddressView.hpp
        dependencies/amdialogs/src/AMUIDialogAuthorTag.hpp
        dependencies/amdialogs/src/AMUIDialogAuthorTagView.hpp
        dependencies/amdialogs/src/AMUIDialogBoolTristate.hpp
        dependencies/amdialogs/src/AMUIDialogBoolTristateView.hpp
        dependencies/amdialogs/src/AMUIDialogChangePassword.hpp
        dependencies/amdialogs/src/AMUIDialogChangePasswordView.hpp
        dependencies/amdialogs/src/AMUIDialogDatetime.cpp
        dependencies/amdialogs/src/AMUIDialogDatetimeView.cpp
        dependencies/amdialogs/src/AMUIDialogEnumLine.hpp
        dependencies/amdialogs/src/AMUIDialogEnumLineView.hpp
        dependencies/amdialogs/src/AMUIDialogEnumLineNullable.hpp
        dependencies/amdialogs/src/AMUIDialogEnumLineNullableView.hpp
        dependencies/amdialogs/src/AMUIDialogExpression.hpp
        dependencies/amdialogs/src/AMUIDialogExpressionView.hpp
        dependencies/amdialogs/src/AMUIDialogExpressionNullable.hpp
        dependencies/amdialogs/src/AMUIDialogExpressionNullableView.hpp
        dependencies/amdialogs/src/AMUIDialogLogin.hpp
        dependencies/amdialogs/src/AMUIDialogLoginView.hpp
        dependencies/amdialogs/src/AMUIDialogManager.cpp
        dependencies/amdialogs/src/AMUIDialogName.hpp
        dependencies/amdialogs/src/AMUIDialogNameView.hpp
        dependencies/amdialogs/src/AMUIDialogView.cpp
        dependencies/amdialogs/src/AMUIForm.cpp
        dependencies/amdialogs/src/AMUIFormElement.cpp
        dependencies/amdialogs/src/AMUIFormElementAddress.hpp
        dependencies/amdialogs/src/AMUIFormElementAddressNullable.hpp
        dependencies/amdialogs/src/AMUIFormElementAuthorTag.hpp
        dependencies/amdialogs/src/AMUIFormElementBool.hpp
        dependencies/amdialogs/src/AMUIFormElementBoolTristate.hpp
        dependencies/amdialogs/src/AMUIFormElementButton.cpp
        dependencies/amdialogs/src/AMUIFormElementDatetime.cpp
        dependencies/amdialogs/src/AMUIFormElementDatetimeNullable.cpp
        dependencies/amdialogs/src/AMUIFormElementEnum.hpp
        dependencies/amdialogs/src/AMUIFormElementEnumMap.hpp
        dependencies/amdialogs/src/AMUIFormElementFloat.cpp
        dependencies/amdialogs/src/AMUIFormElementInteger.cpp
        dependencies/amdialogs/src/AMUIFormElementNatural.cpp
        dependencies/amdialogs/src/AMUIFormElementPassword.cpp
        dependencies/amdialogs/src/AMUIFormElementRadios.cpp
        dependencies/amdialogs/src/AMUIFormElementSelectBox.cpp
        dependencies/amdialogs/src/AMUIFormElementString.cpp
        dependencies/amdialogs/src/AMUIFormElementStringInitiator.cpp
        dependencies/amdialogs/src/AMUIFormElementStringMultiline.cpp
        dependencies/amdialogs/src/AMUIFormElementStringNullable.cpp
        dependencies/amdialogs/src/AMUIFormCallbacks.cpp
        dependencies/amdialogs/src/AMUIFormConfig.cpp
        dependencies/amdialogs/src/AMUIFormResources.cpp
        dependencies/amdialogs/src/AMUIFormView.cpp
        dependencies/amdialogs/src/AMUIParsers.cpp
        dependencies/amdialogs/src/AMUIParsers.hpp
        dependencies/amdialogs/src/AMUISynthesizers.cpp
        dependencies/amdialogs/src/AMUIWidgetState.cpp
        dependencies/amdialogs/AMUIDialog.h
        dependencies/amdialogs/AMUIDialogAddress.h
        dependencies/amdialogs/AMUIDialogAddressView.h
        dependencies/amdialogs/AMUIDialogAuthorTag.h
        dependencies/amdialogs/AMUIDialogAuthorTagView.h
        dependencies/amdialogs/AMUIDialogBoolTristate.h
        dependencies/amdialogs/AMUIDialogBoolTristateView.h
        dependencies/amdialogs/AMUIDialogChangePassword.h
        dependencies/amdialogs/AMUIDialogChangePasswordView.h
        dependencies/amdialogs/AMUIDialogDatetime.h
        dependencies/amdialogs/AMUIDialogDatetimeView.h
        dependencies/amdialogs/AMUIDialogEnumLine.h
        dependencies/amdialogs/AMUIDialogEnumLineView.h
        dependencies/amdialogs/AMUIDialogEnumLineNullable.h
        dependencies/amdialogs/AMUIDialogEnumLineNullableView.h
        dependencies/amdialogs/AMUIDialogExpression.h
        dependencies/amdialogs/AMUIDialogExpressionView.h
        dependencies/amdialogs/AMUIDialogExpressionNullable.h
        dependencies/amdialogs/AMUIDialogExpressionNullableView.h
        dependencies/amdialogs/AMUIDialogLogin.h
        dependencies/amdialogs/AMUIDialogLoginView.h
        dependencies/amdialogs/AMUIDialogManager.h
        dependencies/amdialogs/AMUIDialogName.h
        dependencies/amdialogs/AMUIDialogNameView.h
        dependencies/amdialogs/AMUIDialogView.h
        dependencies/amdialogs/AMUIForm.h
        dependencies/amdialogs/AMUIFormCallbacks.h
        dependencies/amdialogs/AMUIFormConfig.h
        dependencies/amdialogs/AMUIFormElement.h
        dependencies/amdialogs/AMUIFormElementAddress.h
        dependencies/amdialogs/AMUIFormElementAddressNullable.h
        dependencies/amdialogs/AMUIFormElementAuthorTag.h
        dependencies/amdialogs/AMUIFormElementBool.h
        dependencies/amdialogs/AMUIFormElementBoolTristate.h
        dependencies/amdialogs/AMUIFormElementButton.h
        dependencies/amdialogs/AMUIFormElementDatetime.h
        dependencies/amdialogs/AMUIFormElementDatetimeNullable.h
        dependencies/amdialogs/AMUIFormElementEnum.h
        dependencies/amdialogs/AMUIFormElementEnumMap.h
        dependencies/amdialogs/AMUIFormElementFloat.h
        dependencies/amdialogs/AMUIFormElementInteger.h
        dependencies/amdialogs/AMUIFormElementNatural.h
        dependencies/amdialogs/AMUIFormElementPassword.h
        dependencies/amdialogs/AMUIFormElementRadios.h
        dependencies/amdialogs/AMUIFormElements.h
        dependencies/amdialogs/AMUIFormElementSelectBox.h
        dependencies/amdialogs/AMUIFormElementString.h
        dependencies/amdialogs/AMUIFormElementStringInitiator.h
        dependencies/amdialogs/AMUIFormElementStringMultiline.h
        dependencies/amdialogs/AMUIFormElementStringNullable.h
        dependencies/amdialogs/AMUIEnum.h
        dependencies/amdialogs/AMUIFormResources.h
        dependencies/amdialogs/AMUIFormView.h
        dependencies/amdialogs/AMUIParsers.h
        dependencies/amdialogs/AMUISynthesizers.h
        dependencies/amdialogs/AMUIWidgetState.h
        #amuitable
        dependencies/amuitable/src/AMUIDialogEnumBig.hpp
        dependencies/amuitable/src/AMUIDialogEnumBigView.hpp
        dependencies/amuitable/src/AMUIDialogEnumName.hpp
        dependencies/amuitable/src/AMUIDialogEnumNameView.hpp
        dependencies/amuitable/src/AMUIDialogEnumNameNullable.hpp
        dependencies/amuitable/src/AMUIDialogEnumNameNullableView.hpp
        dependencies/amuitable/src/AMUIFormElementDownloads.hpp
        dependencies/amuitable/src/AMUIFormElementEnumBig.hpp
        dependencies/amuitable/src/AMUIFormElementEnumDepended.hpp
        dependencies/amuitable/src/AMUIFormElementEnumName.hpp
        dependencies/amuitable/src/AMUIFormElementEnumOrdered.hpp
        dependencies/amuitable/src/AMEliteTableProviderImpl.hpp
        dependencies/amuitable/src/AMUIHeartbeatProvider.cpp
        dependencies/amuitable/src/AMUIRefresher.hpp
        dependencies/amuitable/src/AMUITableCache.hpp
        dependencies/amuitable/src/AMUITableCacheFlashback.hpp
        dependencies/amuitable/src/AMUITableCacheFlashbackView.hpp
        dependencies/amuitable/src/AMUITableEditable.hpp
        dependencies/amuitable/src/AMUITableEditableView.hpp
        dependencies/amuitable/src/AMUITableFixed.hpp
        dependencies/amuitable/src/AMUITableEnumLoader.hpp
        dependencies/amuitable/AMUITableColumn.hpp
        dependencies/amuitable/src/AMUITableFilter.hpp
        dependencies/amuitable/src/AMUITableFilterBase.hpp
        dependencies/amuitable/src/AMUITableForm.hpp
        dependencies/amuitable/src/AMUITableHistory.hpp
        dependencies/amuitable/src/AMUITableHistoryView.hpp
        dependencies/amuitable/src/AMUITableIDetailProvider.cpp
        dependencies/amuitable/src/AMUITableImport.cpp
        dependencies/amuitable/src/AMUITableImport.hpp
        dependencies/amuitable/src/AMUITableIResultProvider.cpp
        dependencies/amuitable/src/AMUITableJsonParser.hpp
        dependencies/amuitable/src/AMUITableView.hpp
        dependencies/amuitable/AMUIDialogEnumBig.h
        dependencies/amuitable/AMUIDialogEnumBigView.h
        dependencies/amuitable/AMUIDialogEnumName.h
        dependencies/amuitable/AMUIDialogEnumNameView.h
        dependencies/amuitable/AMUIDialogEnumNameNullable.h
        dependencies/amuitable/AMUIDialogEnumNameNullableView.h
        dependencies/amuitable/AMUIFormElementDownloads.h
        dependencies/amuitable/AMUIFormElementEnumBig.h
        dependencies/amuitable/AMUIFormElementEnumDepended.h
        dependencies/amuitable/AMUIFormElementEnumName.h
        dependencies/amuitable/AMUIFormElementEnumOrdered.h
        dependencies/amuitable/AMEliteAgendaWithTableData.h
        dependencies/amuitable/AMEliteTableProviderImpl.h
        dependencies/amuitable/AMUIHeartbeatProvider.h
        dependencies/amuitable/AMUIHeartbeatProvider_.h
        dependencies/amuitable/AMUIRefresher.h
        dependencies/amuitable/AMUITableCache.h
        dependencies/amuitable/AMUITableCacheFlashback.h
        dependencies/amuitable/AMUITableCacheFlashbackView.h
        dependencies/amuitable/AMUITableColumn.h
        dependencies/amuitable/AMUITableConfig.h
        dependencies/amuitable/AMUITableEditable.h
        dependencies/amuitable/AMUITableEditableView.h
        dependencies/amuitable/AMUITableEditableSubform.h
        dependencies/amuitable/AMUITableFixed.h
        dependencies/amuitable/AMUITableFixedSubform.h
        dependencies/amuitable/AMUITableEnumLoader.h
        dependencies/amuitable/AMUITableFilter.h
        dependencies/amuitable/AMUITableFilterBase.h
        dependencies/amuitable/AMUITableForm.h
        dependencies/amuitable/AMUITableHistory.h
        dependencies/amuitable/AMUITableHistoryView.h
        dependencies/amuitable/AMUITableIDetailProvider.h
        dependencies/amuitable/AMUITableImport.h
        dependencies/amuitable/AMUITableIResultProvider.h
        dependencies/amuitable/AMUITableITableProvider.h
        dependencies/amuitable/AMUITableJsonParser.h
        dependencies/amuitable/AMUITableView.h
        #imgui
        dependencies/imgui/imgui.cpp
        dependencies/imgui/imgui_draw.cpp
        dependencies/imgui/imgui_demo.cpp
        dependencies/imgui/imgui_widgets.cpp
        dependencies/imgui/imgui_tables.cpp
        dependencies/imgui/backends/imgui_impl_sdl2.cpp
        #model

        model/Acl.cpp
        model/AclClient.cpp
        model/Citizenships.cpp
        model/Citizenships.h
        model/Citizenships_.h

        model/Enumerations.cpp
        model/Enumerations.h

        model/Menu.cpp
        model/Menu.h

        model/People.cpp
        model/People.h
        model/People_.h
        model/Person.cpp
        model/Person.h

        model/HeartbeatRefresher.cpp
        model/Sex.cpp
        model/Sex.h
        model/Sex_.h
        model/TwoWayTable.cpp
        model/User.cpp
        model/User.h
        model/Users.cpp
        model/Users.h
        model/Users_.h
        model/UserDegree.cpp
        model/UserDegree.h
        model/UserDegrees.cpp
        model/UserDegrees.h
        model/UserDegrees_.h
        model/UserRoles.cpp
        model/UserRoles.h
        model/UserRoles_.h

        model/AMUITableConfig.cpp
        #view
        view/AppView.cpp
        view/AppView.h
        view/MenuView.cpp
        view/MenuView.h


        view/Resources.cpp
        view/Resources.h
        view/UserDegreeView.cpp
        view/UserDegreeView.h
        #controller

        controller/PersonController.cpp
        controller/PersonController.h
        controller/UserDegreeController.cpp
        controller/UserDegreeController.h
        controller/UserController.cpp
        controller/UserController.h


        #generated
        generated/acl.cpp
        generated/acl_tables.cpp
        generated/TableCS.cpp

        #sha
        dependencies/sha-2/sha-256.c
        dependencies/sha-2/sha-256.h

        #zlib
        dependencies/zlib/adler32.c
        dependencies/zlib/crc32.c
        dependencies/zlib/deflate.c
        dependencies/zlib/infback.c
        dependencies/zlib/inffast.c
        dependencies/zlib/inflate.c
        dependencies/zlib/inftrees.c
        dependencies/zlib/trees.c
        dependencies/zlib/zutil.c
        dependencies/zlib/compress.c
        dependencies/zlib/uncompr.c
        dependencies/zlib/gzclose.c
        dependencies/zlib/gzlib.c
        dependencies/zlib/gzread.c
        dependencies/zlib/gzwrite.c
        dependencies/zlib/contrib/minizip/ioapi.c
        dependencies/zlib/contrib/minizip/zip.c

        ${SOURCES_SQLITE}
)


if (TARGET_BUILD_VARIANT MATCHES debug_wasm)
    include(CMakeWasm.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES release_wasm)
    include(CMakeWasm.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES release_live)
    include(CMakeLive.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES debug_stand)
    include(CMakeStand.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES release_stand)
    include(CMakeStand.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES debug_svr)
    include(CMakeSvr.cmake)
elseif(TARGET_BUILD_VARIANT MATCHES release_svr)
    include(CMakeSvr.cmake)
endif()