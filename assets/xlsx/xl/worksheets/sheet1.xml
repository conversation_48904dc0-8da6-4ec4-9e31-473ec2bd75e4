<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing" xmlns:x14="http://schemas.microsoft.com/office/spreadsheetml/2009/9/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"><sheetPr filterMode="false"><pageSetUpPr fitToPage="false"/></sheetPr><dimension ref="A1:E7"/><sheetViews><sheetView showFormulas="false" showGridLines="true" showRowColHeaders="true" showZeros="true" rightToLeft="false" tabSelected="true" showOutlineSymbols="true" defaultGridColor="true" view="normal"         topLeftCell="A1" colorId="64" zoomScale="100" zoomScaleNormal="100" zoomScalePageLayoutView="100" workbookViewId="0"><selection pane="topLeft" activeCell="D3" activeCellId="0" sqref="D3"/></sheetView></sheetViews><sheetFormatPr defaultColWidth="11.53515625" defaultRowHeight="12.8" zeroHeight="false" outlineLevelRow="0" outlineLevelCol="0"></sheetFormatPr><cols><col collapsed="false" customWidth="true" hidden="false" outlineLevel="0" max="1" min="1" style="0" width="6.38"/><col collapsed="false" customWidth="true" hidden="false" outlineLevel="0" max="2" min="2" style="0" width="35.72"/><col collapsed="false" customWidth="true" hidden="false" outlineLevel="0" max="3" min="3" style="0" width="28.06"/><col collapsed="false" customWidth="true" hidden="false" outlineLevel="0" max="4" min="4" style="0" width="41.26"/><col collapsed="false" customWidth="true" hidden="false" outlineLevel="0" max="5" min="5" style="0" width="33.16"/></cols><sheetData><row r="1" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A1" s="1" t="s"><v>0</v></c><c r="B1" s="1" t="s"><v>1</v></c><c r="C1" s="1" t="s"><v>2</v></c><c r="D1" s="1" t="s"><v>3</v></c><c r="E1" s="1" t="s"><v>4</v></c></row><row r="2" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A2" s="2" t="s"><v>5</v></c><c r="B2" s="2" t="s"><v>5</v></c><c r="C2" s="2" t="s"><v>5</v></c><c r="D2" s="2" t="s"><v>5</v></c><c r="E2" s="2" t="s"><v>5</v></c></row><row r="3" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A3" s="0" t="n"><v>4</v></c><c r="B3" s="0" t="s"><v>6</v></c><c r="C3" s="0" t="s"><v>7</v></c><c r="D3" s="7" t="n"><v>45619.9860648148</v></c><c r="E3" s="0" t="s"><v>8</v></c></row><row r="4" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A4" s="0" t="n"><v>3</v></c><c r="B4" s="0" t="s"><v>9</v></c><c r="C4" s="0" t="s"><v>10</v></c><c r="D4" s="8" t="n"><v>0.986064814814815</v></c><c r="E4" s="0" t="s"><v>11</v></c></row><row r="5" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A5" s="0" t="n"><v>2</v></c><c r="B5" s="0" t="s"><v>12</v></c><c r="C5" s="0" t="s"><v>13</v></c><c r="D5" s="9" t="n"><v>45621</v></c><c r="E5" s="0" t="s"><v>14</v></c></row><row r="6" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="A6" s="0" t="n"><v>1</v></c><c r="B6" s="0" t="s"><v>15</v></c><c r="C6" s="0" t="s"><v>16</v></c><c r="D6" s="10" t="n"><v>45622.9860662437</v></c><c r="E6" s="0" t="s"><v>17</v></c></row><row r="7" customFormat="false" ht="12.8" hidden="false" customHeight="false" outlineLevel="0" collapsed="false"><c r="D7" s="10" t="n"><v>45623.9860662437</v></c></row></sheetData><printOptions headings="false" gridLines="false" gridLinesSet="true" horizontalCentered="false" verticalCentered="false"/><pageMargins left="0.7875" right="0.7875" top="1.025" bottom="1.025" header="0.7875" footer="0.7875"/><pageSetup paperSize="9" scale="100" firstPageNumber="1" fitToWidth="1" fitToHeight="1" pageOrder="downThenOver" orientation="portrait" blackAndWhite="false" draft="false" cellComments="none"     useFirstPageNumber="true" horizontalDpi="300" verticalDpi="300" copies="1"/><headerFooter differentFirst="false" differentOddEven="false"><oddHeader>&amp;C&amp;A</oddHeader><oddFooter>&amp;CStránka &amp;P</oddFooter></headerFooter></worksheet>
