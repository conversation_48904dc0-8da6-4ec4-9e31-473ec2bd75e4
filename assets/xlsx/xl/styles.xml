<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
    <numFmts count="5">
        <numFmt numFmtId="164" formatCode="General"/>
        <numFmt numFmtId="165" formatCode="yyyy/mm/dd\ hh:mm:ss"/>
        <numFmt numFmtId="166" formatCode="hh:mm:ss"/>
        <numFmt numFmtId="167" formatCode="d/\ m/\ yyyy"/>
        <numFmt numFmtId="168" formatCode="yyyy/mm/dd\ hh:mm:ss.000000"/>
    </numFmts>
    <fonts count="5">
        <font>
            <sz val="10"/>
            <name val="Arial"/>
            <family val="2"/>
        </font>
        <font>
            <sz val="10"/>
            <name val="Arial"/>
            <family val="0"/>
        </font>
        <font>
            <sz val="10"/>
            <name val="Arial"/>
            <family val="0"/>
        </font>
        <font>
            <sz val="10"/>
            <name val="Arial"/>
            <family val="0"/>
        </font>
        <font>
            <b val="true"/>
            <sz val="10"/>
            <name val="Arial"/>
            <family val="2"/>
        </font>
    </fonts>
    <fills count="8">
        <fill>
            <patternFill patternType="none"/>
        </fill>
        <fill>
            <patternFill patternType="gray125"/>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFC7DEFA"/>
                <bgColor rgb="FFCCFFFF"/>
            </patternFill>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFFFFACD"/>
                <bgColor rgb="FFFFFFFF"/>
            </patternFill>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFB8FBB8"/>
                <bgColor rgb="FFFFFFFF"/>
            </patternFill>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFFFE4E1"/>
                <bgColor rgb="FFFFFFFF"/>
            </patternFill>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFFFE0B0"/>
                <bgColor rgb="FFFFFFFF"/>
            </patternFill>
        </fill>
        <fill>
            <patternFill patternType="solid">
                <fgColor rgb="FFD8E2ED"/>
                <bgColor rgb="FFFFFFFF"/>
            </patternFill>
        </fill>
    </fills>
    <borders count="1">
        <border diagonalUp="false" diagonalDown="false">
            <left/>
            <right/>
            <top/>
            <bottom/>
            <diagonal/>
        </border>
    </borders>
    <cellStyleXfs count="20">
        <xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="true" applyAlignment="true"
            applyProtection="true">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="43" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="41" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="44" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="42" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
        <xf numFmtId="9" fontId="1" fillId="0" borderId="0" applyFont="true" applyBorder="false" applyAlignment="false"
            applyProtection="false"></xf>
    </cellStyleXfs>
    <cellXfs count="11">
        <xf numFmtId="164" fontId="0" fillId="0" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="4" fillId="2" borderId="0" xfId="0" applyFont="true" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="0" fillId="3" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="true" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="true" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="0" fillId="4" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="true" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="true" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="0" fillId="5" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="true" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="true" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="0" fillId="6" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="true" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="true" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="164" fontId="0" fillId="7" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="true" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="true" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="165" fontId="0" fillId="0" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="166" fontId="0" fillId="0" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="167" fontId="0" fillId="0" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
        <xf numFmtId="168" fontId="0" fillId="0" borderId="0" xfId="0" applyFont="false" applyBorder="false"
            applyAlignment="false" applyProtection="false">
            <alignment horizontal="general" vertical="bottom" textRotation="0" wrapText="false" indent="0"
                       shrinkToFit="false"/>
            <protection locked="true" hidden="false"/>
        </xf>
    </cellXfs>
    <cellStyles count="6">
        <cellStyle name="Normal" xfId="0" builtinId="0"/>
        <cellStyle name="Comma" xfId="15" builtinId="3"/>
        <cellStyle name="Comma [0]" xfId="16" builtinId="6"/>
        <cellStyle name="Currency" xfId="17" builtinId="4"/>
        <cellStyle name="Currency [0]" xfId="18" builtinId="7"/>
        <cellStyle name="Percent" xfId="19" builtinId="5"/>
    </cellStyles>
    <colors>
        <indexedColors>
            <rgbColor rgb="FF000000"/>
            <rgbColor rgb="FFFFFFFF"/>
            <rgbColor rgb="FFFF0000"/>
            <rgbColor rgb="FF00FF00"/>
            <rgbColor rgb="FF0000FF"/>
            <rgbColor rgb="FFFFFF00"/>
            <rgbColor rgb="FFFF00FF"/>
            <rgbColor rgb="FF00FFFF"/>
            <rgbColor rgb="FF800000"/>
            <rgbColor rgb="FF008000"/>
            <rgbColor rgb="FF000080"/>
            <rgbColor rgb="FF808000"/>
            <rgbColor rgb="FF800080"/>
            <rgbColor rgb="FF008080"/>
            <rgbColor rgb="FFC0C0C0"/>
            <rgbColor rgb="FF808080"/>
            <rgbColor rgb="FF9999FF"/>
            <rgbColor rgb="FF993366"/>
            <rgbColor rgb="FFFFFACD"/>
            <rgbColor rgb="FFCCFFFF"/>
            <rgbColor rgb="FF660066"/>
            <rgbColor rgb="FFFF8080"/>
            <rgbColor rgb="FF0066CC"/>
            <rgbColor rgb="FFC7DEFA"/>
            <rgbColor rgb="FF000080"/>
            <rgbColor rgb="FFFF00FF"/>
            <rgbColor rgb="FFFFFF00"/>
            <rgbColor rgb="FF00FFFF"/>
            <rgbColor rgb="FF800080"/>
            <rgbColor rgb="FF800000"/>
            <rgbColor rgb="FF008080"/>
            <rgbColor rgb="FF0000FF"/>
            <rgbColor rgb="FF00CCFF"/>
            <rgbColor rgb="FFCCFFFF"/>
            <rgbColor rgb="FFCCFFCC"/>
            <rgbColor rgb="FFFFFF99"/>
            <rgbColor rgb="FF99CCFF"/>
            <rgbColor rgb="FFFF99CC"/>
            <rgbColor rgb="FFCC99FF"/>
            <rgbColor rgb="FFFFCC99"/>
            <rgbColor rgb="FF3366FF"/>
            <rgbColor rgb="FF33CCCC"/>
            <rgbColor rgb="FF99CC00"/>
            <rgbColor rgb="FFFFCC00"/>
            <rgbColor rgb="FFFF9900"/>
            <rgbColor rgb="FFFF6600"/>
            <rgbColor rgb="FF666699"/>
            <rgbColor rgb="FF969696"/>
            <rgbColor rgb="FF003366"/>
            <rgbColor rgb="FF339966"/>
            <rgbColor rgb="FF003300"/>
            <rgbColor rgb="FF333300"/>
            <rgbColor rgb="FF993300"/>
            <rgbColor rgb="FF993366"/>
            <rgbColor rgb="FF333399"/>
            <rgbColor rgb="FF333333"/>
        </indexedColors>
    </colors>
</styleSheet>
